import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Input, Select, SelectItem, Chip, Avatar } from '@heroui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { toast } from 'react-hot-toast';

/**
 * Freelance Marketplace Component - Public Gig Discovery Platform
 * 
 * Features:
 * - Public freelance marketplace for external gigs
 * - Client-freelancer matching and proposal system
 * - Bento grid layout with marketplace statistics
 * - Advanced filtering by budget, skills, timeline
 * - Proposal submission and bidding system
 * - Real-time gig updates and notifications
 * - Secure payment and escrow integration
 */
const FreelanceMarketplace = ({ className = "" }) => {
  const { currentUser } = useContext(UserContext);
  
  // State management
  const [gigs, setGigs] = useState([]);
  const [filteredGigs, setFilteredGigs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedGig, setSelectedGig] = useState(null);
  const [showGigModal, setShowGigModal] = useState(false);
  const [showProposalModal, setShowProposalModal] = useState(false);
  
  // Filter states
  const [filters, setFilters] = useState({
    budget: 'all',
    timeline: 'all',
    category: 'all',
    skillLevel: 'all',
    remote: 'all'
  });
  
  // Marketplace statistics
  const [marketplaceStats, setMarketplaceStats] = useState({
    totalGigs: 0,
    totalValue: 0,
    activeGigs: 0,
    myProposals: 0,
    myActiveWork: 0
  });

  // Load gigs from marketplace (mock data for now)
  const loadGigs = async () => {
    try {
      setLoading(true);
      
      // Mock freelance marketplace data
      const mockGigs = [
        {
          id: '1',
          title: 'React Component Bug Fix',
          description: 'Fix critical rendering issue in dashboard components that affects user experience and causes performance problems.',
          budget: 500,
          budgetType: 'fixed',
          timeline: '3 days',
          category: 'development',
          skillLevel: 'expert',
          remote: true,
          skillsRequired: ['React', 'TypeScript', 'Debugging', 'Performance'],
          client: {
            id: 'client1',
            name: 'TechCorp',
            rating: 4.9,
            reviewCount: 127,
            verified: true,
            location: 'San Francisco, CA'
          },
          proposalCount: 3,
          viewCount: 12,
          postedAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
          featured: true,
          urgent: true,
          tags: ['React', 'Bug Fix', 'Frontend']
        },
        {
          id: '2',
          title: 'API Documentation Project',
          description: 'Create comprehensive API documentation with examples and tutorials for developer onboarding. Must include interactive examples.',
          budget: 300,
          budgetType: 'hourly',
          budgetRange: [150, 300],
          timeline: '1 week',
          category: 'writing',
          skillLevel: 'intermediate',
          remote: true,
          skillsRequired: ['Technical Writing', 'API Design', 'Documentation', 'Markdown'],
          client: {
            id: 'client2',
            name: 'StartupXYZ',
            rating: 4.7,
            reviewCount: 45,
            verified: true,
            location: 'Remote'
          },
          proposalCount: 1,
          viewCount: 8,
          postedAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
          featured: false,
          urgent: false,
          tags: ['Documentation', 'API', 'Technical Writing']
        },
        {
          id: '3',
          title: 'Mobile App UI Design',
          description: 'Design complete mobile interface for gaming platform with modern UI and engaging user experience. Include prototypes.',
          budget: 1000,
          budgetType: 'project',
          budgetRange: [800, 1200],
          timeline: '2 weeks',
          category: 'design',
          skillLevel: 'advanced',
          remote: true,
          skillsRequired: ['Figma', 'Mobile UI', 'User Research', 'Prototyping'],
          client: {
            id: 'client3',
            name: 'GameStudio',
            rating: 4.8,
            reviewCount: 89,
            verified: true,
            location: 'Austin, TX'
          },
          proposalCount: 7,
          viewCount: 25,
          postedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
          featured: true,
          urgent: false,
          tags: ['UI Design', 'Mobile', 'Gaming']
        },
        {
          id: '4',
          title: 'E-commerce Website Development',
          description: 'Build a modern e-commerce website with payment integration, inventory management, and admin dashboard.',
          budget: 2500,
          budgetType: 'project',
          budgetRange: [2000, 3000],
          timeline: '4 weeks',
          category: 'development',
          skillLevel: 'expert',
          remote: true,
          skillsRequired: ['React', 'Node.js', 'Payment APIs', 'Database'],
          client: {
            id: 'client4',
            name: 'RetailCo',
            rating: 4.6,
            reviewCount: 23,
            verified: true,
            location: 'New York, NY'
          },
          proposalCount: 12,
          viewCount: 45,
          postedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
          featured: false,
          urgent: false,
          tags: ['E-commerce', 'Full Stack', 'Payment']
        },
        {
          id: '5',
          title: 'Brand Identity Design',
          description: 'Create complete brand identity including logo, color palette, typography, and brand guidelines for tech startup.',
          budget: 800,
          budgetType: 'fixed',
          timeline: '10 days',
          category: 'design',
          skillLevel: 'advanced',
          remote: true,
          skillsRequired: ['Brand Design', 'Logo Design', 'Adobe Creative Suite', 'Typography'],
          client: {
            id: 'client5',
            name: 'InnovateTech',
            rating: 4.9,
            reviewCount: 67,
            verified: true,
            location: 'Seattle, WA'
          },
          proposalCount: 5,
          viewCount: 18,
          postedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
          featured: true,
          urgent: true,
          tags: ['Branding', 'Logo', 'Identity']
        }
      ];
      
      setGigs(mockGigs);
      setFilteredGigs(mockGigs);
      updateMarketplaceStats(mockGigs);
      
    } catch (error) {
      console.error('Error loading gigs:', error);
      toast.error('Failed to load marketplace data');
    } finally {
      setLoading(false);
    }
  };

  // Update marketplace statistics
  const updateMarketplaceStats = (gigList) => {
    const stats = {
      totalGigs: gigList.length,
      totalValue: gigList.reduce((sum, gig) => sum + gig.budget, 0),
      activeGigs: gigList.filter(g => g.proposalCount < 10).length, // Still accepting proposals
      myProposals: 3, // Mock data - would come from user's proposal history
      myActiveWork: 2 // Mock data - would come from user's active contracts
    };
    setMarketplaceStats(stats);
  };

  // Format budget display
  const formatBudget = (gig) => {
    if (gig.budgetType === 'hourly') {
      return `$${gig.budgetRange[0]}-${gig.budgetRange[1]}/hr`;
    } else if (gig.budgetType === 'project' && gig.budgetRange) {
      return `$${gig.budgetRange[0]}-${gig.budgetRange[1]}`;
    } else {
      return `$${gig.budget} Fixed`;
    }
  };

  // Get skill level color
  const getSkillLevelColor = (level) => {
    const colors = {
      'beginner': 'success',
      'intermediate': 'primary',
      'advanced': 'warning',
      'expert': 'danger'
    };
    return colors[level] || 'default';
  };

  // Get category icon
  const getCategoryIcon = (category) => {
    const icons = {
      'development': '💻',
      'design': '🎨',
      'writing': '✍️',
      'marketing': '📈',
      'testing': '🧪'
    };
    return icons[category] || '💼';
  };

  // Get time since posted
  const getTimeSincePosted = (postedAt) => {
    const now = new Date();
    const diffMs = now - postedAt;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffDays > 0) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    if (diffHours > 0) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    return 'Just posted';
  };

  // Apply filters and search
  const applyFiltersAndSearch = () => {
    let filtered = [...gigs];
    
    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(gig => 
        gig.title?.toLowerCase().includes(searchLower) ||
        gig.description?.toLowerCase().includes(searchLower) ||
        gig.skillsRequired.some(skill => skill.toLowerCase().includes(searchLower)) ||
        gig.client.name?.toLowerCase().includes(searchLower) ||
        gig.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }
    
    // Apply budget filter
    if (filters.budget !== 'all') {
      if (filters.budget === 'low') {
        filtered = filtered.filter(gig => gig.budget < 500);
      } else if (filters.budget === 'medium') {
        filtered = filtered.filter(gig => gig.budget >= 500 && gig.budget < 1500);
      } else if (filters.budget === 'high') {
        filtered = filtered.filter(gig => gig.budget >= 1500);
      }
    }
    
    // Apply category filter
    if (filters.category !== 'all') {
      filtered = filtered.filter(gig => gig.category === filters.category);
    }
    
    // Apply skill level filter
    if (filters.skillLevel !== 'all') {
      filtered = filtered.filter(gig => gig.skillLevel === filters.skillLevel);
    }
    
    // Apply remote filter
    if (filters.remote !== 'all') {
      filtered = filtered.filter(gig => gig.remote === (filters.remote === 'yes'));
    }
    
    setFilteredGigs(filtered);
  };

  // Handle proposal submission
  const submitProposal = async (gigId, proposalData) => {
    try {
      // Mock proposal submission
      toast.success('Proposal submitted successfully!');
      
      // Update gig proposal count
      setGigs(prevGigs => 
        prevGigs.map(gig => 
          gig.id === gigId 
            ? { ...gig, proposalCount: gig.proposalCount + 1 }
            : gig
        )
      );
      
      setShowProposalModal(false);
      
    } catch (error) {
      console.error('Error submitting proposal:', error);
      toast.error('Failed to submit proposal');
    }
  };

  // Initialize component
  useEffect(() => {
    loadGigs();
  }, []);

  // Apply filters when they change
  useEffect(() => {
    applyFiltersAndSearch();
  }, [gigs, searchTerm, filters]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-default-600">Loading marketplace...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`freelance-marketplace ${className}`}>
      {/* Marketplace Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-2">
          💼 Freelance Marketplace
        </h1>
        <p className="text-default-600">
          Discover gigs, submit proposals, and build your freelance career
        </p>
      </div>

      {/* Marketplace Statistics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
          <CardBody className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{marketplaceStats.totalGigs}</div>
            <div className="text-sm text-default-600">Live Gigs</div>
          </CardBody>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
          <CardBody className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">${(marketplaceStats.totalValue / 1000).toFixed(1)}K</div>
            <div className="text-sm text-default-600">Total Value</div>
          </CardBody>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
          <CardBody className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">{marketplaceStats.activeGigs}</div>
            <div className="text-sm text-default-600">Accepting Proposals</div>
          </CardBody>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
          <CardBody className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">{marketplaceStats.myProposals}</div>
            <div className="text-sm text-default-600">My Proposals</div>
          </CardBody>
        </Card>

        <Card className="bg-gradient-to-br from-pink-50 to-pink-100 dark:from-pink-900/20 dark:to-pink-800/20">
          <CardBody className="p-4 text-center">
            <div className="text-2xl font-bold text-pink-600">{marketplaceStats.myActiveWork}</div>
            <div className="text-sm text-default-600">Active Work</div>
          </CardBody>
        </Card>
      </div>

      {/* Search and Filter Controls */}
      <Card className="mb-6">
        <CardBody className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search Input */}
            <div className="flex-1">
              <Input
                placeholder="Search gigs by title, skills, client, or tags..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                startContent={<span className="text-default-400">🔍</span>}
                className="w-full"
              />
            </div>

            {/* Filter Controls */}
            <div className="flex gap-2 flex-wrap">
              <Select
                placeholder="Budget"
                selectedKeys={[filters.budget]}
                onSelectionChange={(keys) => setFilters({...filters, budget: Array.from(keys)[0]})}
                className="w-32"
                size="sm"
              >
                <SelectItem key="all">All Budgets</SelectItem>
                <SelectItem key="low">Under $500</SelectItem>
                <SelectItem key="medium">$500-$1500</SelectItem>
                <SelectItem key="high">$1500+</SelectItem>
              </Select>

              <Select
                placeholder="Category"
                selectedKeys={[filters.category]}
                onSelectionChange={(keys) => setFilters({...filters, category: Array.from(keys)[0]})}
                className="w-32"
                size="sm"
              >
                <SelectItem key="all">All Categories</SelectItem>
                <SelectItem key="development">Development</SelectItem>
                <SelectItem key="design">Design</SelectItem>
                <SelectItem key="writing">Writing</SelectItem>
                <SelectItem key="marketing">Marketing</SelectItem>
              </Select>

              <Select
                placeholder="Level"
                selectedKeys={[filters.skillLevel]}
                onSelectionChange={(keys) => setFilters({...filters, skillLevel: Array.from(keys)[0]})}
                className="w-32"
                size="sm"
              >
                <SelectItem key="all">All Levels</SelectItem>
                <SelectItem key="beginner">Beginner</SelectItem>
                <SelectItem key="intermediate">Intermediate</SelectItem>
                <SelectItem key="advanced">Advanced</SelectItem>
                <SelectItem key="expert">Expert</SelectItem>
              </Select>

              <Select
                placeholder="Remote"
                selectedKeys={[filters.remote]}
                onSelectionChange={(keys) => setFilters({...filters, remote: Array.from(keys)[0]})}
                className="w-32"
                size="sm"
              >
                <SelectItem key="all">All</SelectItem>
                <SelectItem key="yes">Remote</SelectItem>
                <SelectItem key="no">On-site</SelectItem>
              </Select>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Gig Grid - Bento Layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <AnimatePresence>
          {filteredGigs.map((gig, index) => (
            <GigCard
              key={gig.id}
              gig={gig}
              index={index}
              onProposal={() => {
                setSelectedGig(gig);
                setShowProposalModal(true);
              }}
              onView={() => {
                setSelectedGig(gig);
                setShowGigModal(true);
              }}
              currentUser={currentUser}
              formatBudget={formatBudget}
              getSkillLevelColor={getSkillLevelColor}
              getCategoryIcon={getCategoryIcon}
              getTimeSincePosted={getTimeSincePosted}
            />
          ))}
        </AnimatePresence>
      </div>

      {/* Empty State */}
      {filteredGigs.length === 0 && (
        <Card className="mt-8">
          <CardBody className="p-8 text-center">
            <div className="text-6xl mb-4">💼</div>
            <h3 className="text-xl font-semibold mb-2">No gigs found</h3>
            <p className="text-default-600 mb-4">
              {searchTerm || Object.values(filters).some(f => f !== 'all')
                ? 'Try adjusting your search or filters'
                : 'No gigs available at the moment'
              }
            </p>
            {(searchTerm || Object.values(filters).some(f => f !== 'all')) && (
              <Button
                color="primary"
                variant="flat"
                onClick={() => {
                  setSearchTerm('');
                  setFilters({
                    budget: 'all',
                    timeline: 'all',
                    category: 'all',
                    skillLevel: 'all',
                    remote: 'all'
                  });
                }}
              >
                Clear Filters
              </Button>
            )}
          </CardBody>
        </Card>
      )}
    </div>
  );
};

// Gig Card Component
const GigCard = ({ gig, index, onProposal, onView, currentUser, formatBudget, getSkillLevelColor, getCategoryIcon, getTimeSincePosted }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3, delay: index * 0.05 }}
      whileHover={{ scale: 1.02 }}
      className="h-full"
    >
      <Card className={`h-full hover:shadow-lg transition-shadow duration-200 ${gig.featured ? 'ring-2 ring-yellow-400' : ''}`}>
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start w-full">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                {gig.featured && <span className="text-yellow-500">⭐</span>}
                {gig.urgent && <span className="text-red-500">🔥</span>}
                <h3 className="text-lg font-semibold line-clamp-2">
                  {gig.title}
                </h3>
              </div>
              <div className="flex items-center gap-2 mb-2">
                <Chip
                  color="primary"
                  size="sm"
                  variant="flat"
                  startContent={getCategoryIcon(gig.category)}
                >
                  {gig.category}
                </Chip>
                <Chip
                  color={getSkillLevelColor(gig.skillLevel)}
                  size="sm"
                  variant="flat"
                >
                  {gig.skillLevel}
                </Chip>
              </div>
            </div>
            <div className="text-right">
              <div className="text-lg font-bold text-green-600">
                {formatBudget(gig)}
              </div>
              <div className="text-xs text-default-500">
                {gig.timeline}
              </div>
            </div>
          </div>
        </CardHeader>

        <CardBody className="pt-0">
          {/* Client Info */}
          <div className="flex items-center gap-2 mb-3">
            <Avatar
              name={gig.client.name}
              size="sm"
            />
            <div>
              <div className="text-sm font-medium">{gig.client.name}</div>
              <div className="text-xs text-default-500">
                ⭐ {gig.client.rating} ({gig.client.reviewCount} reviews)
                {gig.client.verified && <span className="text-green-500 ml-1">✓</span>}
              </div>
            </div>
          </div>

          {/* Gig Description */}
          <p className="text-sm text-default-600 line-clamp-3 mb-3">
            {gig.description}
          </p>

          {/* Skills Required */}
          {gig.skillsRequired.length > 0 && (
            <div className="mb-3">
              <div className="text-xs text-default-500 mb-1">Skills Required:</div>
              <div className="flex flex-wrap gap-1">
                {gig.skillsRequired.slice(0, 3).map((skill, idx) => (
                  <Chip key={idx} size="sm" variant="bordered" className="text-xs">
                    {skill}
                  </Chip>
                ))}
                {gig.skillsRequired.length > 3 && (
                  <Chip size="sm" variant="bordered" className="text-xs">
                    +{gig.skillsRequired.length - 3}
                  </Chip>
                )}
              </div>
            </div>
          )}

          {/* Gig Metrics */}
          <div className="grid grid-cols-2 gap-2 mb-3 text-sm">
            <div className="flex items-center gap-1">
              <span className="text-blue-500">💬</span>
              <span className="font-medium">{gig.proposalCount} proposals</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="text-gray-500">👁️</span>
              <span>{gig.viewCount} views</span>
            </div>
          </div>

          {/* Posted Time */}
          <div className="mb-3">
            <div className="text-xs text-default-500">
              Posted {getTimeSincePosted(gig.postedAt)}
            </div>
          </div>

          {/* Remote Work Badge */}
          {gig.remote && (
            <div className="mb-3">
              <Chip size="sm" color="secondary" variant="flat">
                📍 Remote Work
              </Chip>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 mt-auto">
            <Button
              size="sm"
              variant="flat"
              onClick={onView}
              className="flex-1"
            >
              View Details
            </Button>

            <Button
              size="sm"
              color="primary"
              onClick={onProposal}
              className="flex-1"
            >
              Submit Proposal
            </Button>
          </div>
        </CardBody>
      </Card>
    </motion.div>
  );
};

export default FreelanceMarketplace;
