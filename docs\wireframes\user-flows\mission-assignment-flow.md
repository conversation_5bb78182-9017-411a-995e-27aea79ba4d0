# Mission Assignment Flow Wireframe
**Intuitive Task Creation and Assignment Process**

## 📋 Flow Information
- **Flow Type**: Mission (Task) Creation and Assignment
- **User Types**: Project Leaders, Alliance Members with task creation permissions
- **Entry Points**: Venture Dashboard, Mission Board, Quick Actions
- **Target UX**: **Simple, game-like mission creation**
- **Maps to**: Enhanced task/contribution creation system
- **Outcome**: Well-defined missions with clear rewards and assignments

---

## 🎯 **Design Philosophy**

### **Game-Like Mission Creation**
- **Mission terminology** instead of "tasks" or "tickets"
- **Reward-focused** - emphasize what contributors get
- **Clear difficulty** indicators (1-10 scale)
- **Visual mission types** - different icons and styles
- **Progress tracking** built into mission structure

### **Integration with Existing System**
- **Maps to**: Existing task/contribution tracking
- **Enhances**: Current milestone and task creation
- **Bridges**: Simple mission creation → detailed task specifications
- **Connects**: With existing royalty/payment calculation

---

## 🔄 **Complete Mission Assignment Flow**

```mermaid
flowchart TD
    A[Create Mission Button] --> B[Mission Type Selection]
    B --> C[Quick Mission Setup]
    C --> D[Mission Details]
    D --> E[Reward & Difficulty]
    E --> F[Assignment Options]
    F --> G[Review & Post]
    G --> H[Mission Created!]
    
    H --> I[Notify Team]
    H --> J[Add to Mission Board]
    H --> K[Update Venture Progress]
    
    B --> L[Mission Templates]
    L --> M[Customize Template]
    M --> D
    
    F --> N[Open Bounty]
    F --> O[Direct Assignment]
    F --> P[Team Selection]
```

---

## 📱 **Mission Creation Wireframes**

### **Mission Type Selection**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back to Venture                              Create Mission│
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              What kind of mission is this?                  │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  ⚔️ DEVELOPMENT MISSION                             │ │
│    │  Code, build, implement features                   │ │
│    │  Typical reward: $200-2000 • Difficulty: 5-9      │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🎨 CREATIVE MISSION                                │ │
│    │  Design, art, content creation                     │ │
│    │  Typical reward: $100-1500 • Difficulty: 3-7      │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🧪 TESTING MISSION                                 │ │
│    │  QA, bug hunting, user testing                     │ │
│    │  Typical reward: $50-500 • Difficulty: 2-5        │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  📋 RESEARCH MISSION                                │ │
│    │  Analysis, planning, documentation                 │ │
│    │  Typical reward: $100-800 • Difficulty: 3-6       │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🎯 CUSTOM MISSION                                  │ │
│    │  Define your own mission type                      │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Quick Mission Setup**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back                                    ⚔️ Development Mission│
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              Quick Mission Setup                            │
│                                                             │
│    Mission Title                                            │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ Implement user authentication system               │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    What needs to be done? (Brief description)              │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ Build login/signup flow with email verification    │ │
│    │ and password reset functionality                   │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    How long should this take?                              │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ ○ Quick (1-3 days)    ● Medium (1-2 weeks)         │ │
│    │ ○ Long (2-4 weeks)    ○ Epic (1+ months)           │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    How difficult is this?                                  │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ Difficulty: ⭐⭐⭐⭐⭐⭐⭐☆☆☆ (7/10)                │ │
│    │ Requires: Advanced React, Authentication APIs      │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│                        ┌─────────────┐                     │
│                        │ [Continue]  │                     │
│                        └─────────────┘                     │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Mission Details**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back                                    ⚔️ Development Mission│
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              Mission Details                                │
│                                                             │
│    📋 Requirements (What must be delivered?)               │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ • Login page with email/password fields            │ │
│    │ • Signup page with validation                      │ │
│    │ • Email verification system                        │ │
│    │ • Password reset functionality                     │ │
│    │ • Integration with Supabase Auth                   │ │
│    │ • Unit tests for auth functions                    │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    🎯 Success Criteria (How do we know it's done?)        │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ • Users can create accounts and log in             │ │
│    │ • Email verification works end-to-end              │ │
│    │ • Password reset sends working emails              │ │
│    │ • All tests pass                                   │ │
│    │ • Code review approved                             │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    🔧 Resources & References                               │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ • Supabase Auth Documentation                      │ │
│    │ • Design mockups in Figma                          │ │
│    │ • Existing user flow wireframes                    │ │
│    │ • [Add Resource] [Add Link]                        │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│                        ┌─────────────┐                     │
│                        │ [Continue]  │                     │
│                        └─────────────┘                     │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Reward & Difficulty**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back                                    ⚔️ Development Mission│
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              Reward & Difficulty                            │
│                                                             │
│    💰 Mission Reward                                        │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ Fixed Payment: $1,200                               │ │
│    │ ○ Fixed amount  ● Revenue share  ○ Hybrid           │ │
│    │                                                     │ │
│    │ Revenue Share: 2.5% of venture revenue              │ │
│    │ Estimated value: $1,200 (based on projections)     │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ⭐ Difficulty Assessment                                 │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ Overall Difficulty: ⭐⭐⭐⭐⭐⭐⭐☆☆☆ (7/10)          │ │
│    │                                                     │ │
│    │ Technical Complexity: ████████░░ (8/10)             │ │
│    │ Time Investment: ██████░░░░ (6/10)                  │ │
│    │ Learning Curve: ████████░░ (8/10)                   │ │
│    │ Risk Level: ████░░░░░░ (4/10)                       │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    🏷️ Required Skills                                      │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ [React] [JavaScript] [Authentication] [APIs]       │ │
│    │ [Testing] [Supabase] [+ Add Skill]                 │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ⏰ Deadline                                              │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ Due Date: [Feb 15, 2025] (2 weeks from now)        │ │
│    │ ○ Flexible  ● Fixed deadline  ○ ASAP               │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│                        ┌─────────────┐                     │
│                        │ [Continue]  │                     │
│                        └─────────────┘                     │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Assignment Options**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back                                    ⚔️ Development Mission│
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              Who should work on this?                       │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  👤 ASSIGN TO SPECIFIC PERSON                       │ │
│    │  Choose someone from your Alliance                  │ │
│    │                                                     │ │
│    │  Available Alliance Members:                        │ │
│    │  ┌─────┐ Sarah Chen (React Expert) ⭐⭐⭐⭐⭐⭐⭐⭐☆☆│ │
│    │  │ SC  │ Available now • Perfect skill match       │ │
│    │  └─────┘ [Assign to Sarah]                          │ │
│    │                                                     │ │
│    │  ┌─────┐ Mike Rodriguez (Full Stack) ⭐⭐⭐⭐⭐⭐⭐☆☆☆│ │
│    │  │ MR  │ Available next week • Good skill match    │ │
│    │  └─────┘ [Assign to Mike]                           │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🌍 POST AS OPEN BOUNTY                             │ │
│    │  Let anyone from the community claim this mission  │ │
│    │  • Higher visibility                               │ │
│    │  • Competitive selection                           │ │
│    │  • May take longer to assign                       │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  👥 TEAM MISSION                                    │ │
│    │  Multiple people work together on this             │ │
│    │  • Collaborative effort                            │ │
│    │  • Shared reward                                   │ │
│    │  • Requires coordination                           │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│                        ┌─────────────┐                     │
│                        │ [Continue]  │                     │
│                        └─────────────┘                     │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Review & Post Mission**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back                                    ⚔️ Development Mission│
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              Review Your Mission                            │
│                                                             │
│    ⚔️ Implement User Authentication System                  │
│    💰 Reward: $1,200 (Fixed) + 2.5% revenue share         │
│    ⭐ Difficulty: 7/10 • ⏰ Due: Feb 15, 2025              │
│    👤 Assigned to: Sarah Chen                              │
│                                                             │
│    📋 Quick Summary:                                        │
│    Build login/signup flow with email verification and     │
│    password reset functionality using Supabase Auth.       │
│                                                             │
│    🎯 Key Deliverables:                                     │
│    • Login/signup pages • Email verification               │
│    • Password reset • Unit tests • Code review             │
│                                                             │
│    🏷️ Skills: React, JavaScript, Authentication, APIs      │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │              [Create Mission]                       │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    This will:                                              │
│    • Notify Sarah Chen about the assignment               │
│    • Add mission to the Mission Board                     │
│    • Update venture progress tracking                     │
│    • Generate mission agreement (if needed)               │
│                                                             │
│    [Save as Draft] [Preview Agreement]                     │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

---

## 🧠 **Smart Mission Features**

### **Intelligent Suggestions**
- **Reward recommendations** based on similar missions
- **Difficulty auto-assessment** from requirements
- **Skill matching** with Alliance members
- **Timeline suggestions** based on scope

### **Template System**
- **Common mission types** with pre-filled details
- **Project-specific templates** based on venture type
- **Learning from history** - improve templates over time
- **Quick mission creation** for routine tasks

### **Integration Points**
- **Maps to existing**: Task/contribution tracking system
- **Enhances**: Current milestone and task creation
- **Connects with**: Royalty calculation and payment system
- **Feeds into**: Progress tracking and analytics

---

## 📱 **Mobile Responsive Design**

### **Mobile Mission Creation**
```
┌─────────────────────────┐
│ ← Back    Create Mission│
├─────────────────────────┤
│                         │
│ Mission Type:           │
│ ┌─────────────────────┐ │
│ │ ⚔️ Development      │ │
│ │ Code & features     │ │
│ └─────────────────────┘ │
│                         │
│ ┌─────────────────────┐ │
│ │ 🎨 Creative         │ │
│ │ Design & content    │ │
│ └─────────────────────┘ │
│                         │
│ [More Types ▼]          │
│                         │
└─────────────────────────┘
```

---

## 🎯 **Integration with Existing System**

### **Database Mapping**
- **Mission** → Enhanced task/contribution entry
- **Mission Types** → Task categories (new field)
- **Difficulty Rating** → Task complexity (new field)
- **Reward Structure** → Existing royalty/payment calculation
- **Assignment** → Existing contributor assignment

### **Component Enhancement**
- **Enhances**: Existing task creation forms
- **Replaces**: Complex task entry with intuitive mission creation
- **Integrates**: With existing milestone and progress tracking
- **Connects**: To existing payment and royalty systems

**This Mission Assignment Flow transforms complex task creation into an engaging, game-like experience while maintaining full integration with the existing contribution tracking and payment systems.**
