// Debug JavaScript loading
const fetch = require('node-fetch');

const SITE_URL = 'https://royalty.technology';

async function debugJSLoading() {
  try {
    console.log('🔍 Fetching main page HTML...');
    const response = await fetch(SITE_URL);
    const html = await response.text();
    
    console.log('✅ HTML received, length:', html.length);
    
    // Look for script tags
    const scriptMatches = html.match(/<script[^>]*src="[^"]*"[^>]*>/g);
    console.log('\n📜 Script tags found:', scriptMatches ? scriptMatches.length : 0);
    
    if (scriptMatches) {
      scriptMatches.forEach((script, index) => {
        console.log(`${index + 1}. ${script}`);
      });
    }
    
    // Look for main JS file
    const mainJSMatch = html.match(/src="[^"]*main-[^"]*\.js"/);
    console.log('\n🎯 Main JS file:', mainJSMatch ? mainJSMatch[0] : 'NOT FOUND');
    
    // Look for CSS files
    const cssMatches = html.match(/<link[^>]*href="[^"]*\.css"[^>]*>/g);
    console.log('\n🎨 CSS files found:', cssMatches ? cssMatches.length : 0);
    
    if (cssMatches) {
      cssMatches.forEach((css, index) => {
        console.log(`${index + 1}. ${css}`);
      });
    }
    
    // Check if main JS file is accessible
    if (mainJSMatch) {
      const jsUrl = mainJSMatch[0].match(/src="([^"]*)"/)[1];
      const fullJSUrl = jsUrl.startsWith('/') ? `${SITE_URL}${jsUrl}` : jsUrl;
      
      console.log('\n🔍 Testing main JS file access...');
      console.log('JS URL:', fullJSUrl);
      
      try {
        const jsResponse = await fetch(fullJSUrl);
        console.log('JS file status:', jsResponse.status);
        console.log('JS file content-type:', jsResponse.headers.get('content-type'));
        
        if (jsResponse.status === 200) {
          const jsContent = await jsResponse.text();
          console.log('JS file size:', jsContent.length);
          
          // Check if it contains React
          const hasReact = jsContent.includes('React') || jsContent.includes('react');
          console.log('Contains React:', hasReact);
          
          // Check if it contains our components
          const hasAllianceManage = jsContent.includes('AllianceManage');
          console.log('Contains AllianceManage:', hasAllianceManage);
          
          // Check for any obvious errors
          const hasErrors = jsContent.includes('Error:') || jsContent.includes('SyntaxError');
          console.log('Contains errors:', hasErrors);
        }
      } catch (error) {
        console.log('❌ Error fetching JS file:', error.message);
      }
    }
    
    // Check for any inline scripts
    const inlineScripts = html.match(/<script[^>]*>[\s\S]*?<\/script>/g);
    console.log('\n📝 Inline scripts found:', inlineScripts ? inlineScripts.length : 0);
    
    // Check for any error messages in HTML
    const hasErrorMessages = html.includes('error') || html.includes('Error') || html.includes('404');
    console.log('\n❌ HTML contains error messages:', hasErrorMessages);
    
    // Check for React root element
    const hasReactRoot = html.includes('id="root"') || html.includes('id="app"');
    console.log('🎯 Has React root element:', hasReactRoot);
    
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
}

debugJSLoading();
