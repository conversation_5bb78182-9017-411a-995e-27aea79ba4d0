# Onboarding Wizard Page Wireframe
**Complete New User Setup Interface - IMMERSIVE PATTERN**

## 📋 Page Information
- **Page Type**: Onboarding Wizard
- **User Types**: New users (post-authentication)
- **Entry Points**: After signup/email verification
- **Success Outcome**: User completes first meaningful action in <5 minutes
- **Pattern**: Full-screen immersive experience
- **Integration**: Connects to Dashboard, Alliance/Venture creation

---

## 🎯 **Design Philosophy**

### **Immersive Experience Principles**
- **Full-screen focus** - No distracting navigation elements
- **One step at a time** - Single question/action per screen
- **Large touch targets** - Easy interaction on all devices
- **Progress indication** - Clear advancement through steps
- **Template shortcuts** - Quick completion for power users
- **Graceful exit** - Save progress and allow return

### **<5 Minutes to Success**
- **Skip complex setup** - Get to value immediately
- **Smart defaults** - Minimize required decisions
- **Progressive disclosure** - Reveal complexity gradually
- **Quick wins** - Show immediate results

---

## 📱 **Page Layout Structure**

### **Base Layout Pattern**
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ✕                                                                           │
│                                                                             │
│                                                                             │
│                            [MAIN CONTENT]                                  │
│                                                                             │
│                         [PRIMARY ACTION]                                   │
│                                                                             │
│                                                                             │
│                    [SECONDARY] [TEMPLATE] [BACK]                           │
│                                                                             │
│                              ● ○ ○ ○ ○                                     │
└─────────────────────────────────────────────────────────────────────────────┘
```

### **Layout Components**
- **Exit Button (✕)**: Top-left, saves progress
- **Main Content Area**: Centered, large and clear
- **Primary Action**: Prominent button for main path
- **Secondary Actions**: Less prominent options
- **Template Shortcuts**: Quick completion options
- **Progress Dots**: Show current step and total steps
- **Back Navigation**: Return to previous step

---

## 🔄 **Step Templates**

### **Welcome Step Template**
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ✕                                                                           │
│                                                                             │
│                                                                             │
│                            Welcome to Royaltea                             │
│                                                                             │
│                     Where collaboration meets compensation                  │
│                                                                             │
│                                                                             │
│                              [Continue]                                     │
│                                                                             │
│                                ● ○ ○ ○                                     │
└─────────────────────────────────────────────────────────────────────────────┘
```

### **Choice Step Template**
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ✕                                                                           │
│                                                                             │
│                        [QUESTION TEXT]                                     │
│                                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │
│  │      [ICON]     │  │      [ICON]     │  │      [ICON]     │            │
│  │                 │  │                 │  │                 │            │
│  │   [OPTION 1]    │  │   [OPTION 2]    │  │   [OPTION 3]    │            │
│  │   [SUBTITLE]    │  │   [SUBTITLE]    │  │   [SUBTITLE]    │            │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘            │
│                                                                             │
│                    [Other] [Template] [Back]                               │
│                                ○ ● ○ ○                                     │
└─────────────────────────────────────────────────────────────────────────────┘
```

### **Form Step Template**
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ✕                                                                           │
│                                                                             │
│                        [FORM TITLE]                                        │
│                                                                             │
│                    ┌─────────────────────────────────┐                     │
│                    │                                 │                     │
│                    │         [FIELD LABEL]           │                     │
│                    │    [INPUT FIELD]                │                     │
│                    │                                 │                     │
│                    │         [FIELD LABEL]           │                     │
│                    │    [INPUT FIELD]                │                     │
│                    │                                 │                     │
│                    │         [OPTIONS]               │                     │
│                    │    ☑ [CHECKBOX OPTION]          │                     │
│                    │    ☑ [CHECKBOX OPTION]          │                     │
│                    │                                 │                     │
│                    └─────────────────────────────────┘                     │
│                                                                             │
│                    [Customize] [Continue] [Back]                           │
│                                ○ ○ ● ○                                     │
└─────────────────────────────────────────────────────────────────────────────┘
```

### **Success Step Template**
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│                                   🎉                                        │
│                                                                             │
│                         [SUCCESS MESSAGE]                                  │
│                                                                             │
│                    [DESCRIPTION OF WHAT WAS CREATED]                       │
│                                                                             │
│                         ┌─────────────────────┐                           │
│                         │   [PRIMARY ACTION]  │                           │
│                         └─────────────────────┘                           │
│                                                                             │
│                              [Go to Dashboard]                             │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

---

## 🎨 **Visual Design Specifications**

### **Typography**
- **Main Titles**: 48px, bold, center-aligned
- **Questions**: 36px, medium weight, center-aligned
- **Subtitles**: 18px, regular weight, center-aligned
- **Button Text**: 20px, medium weight
- **Form Labels**: 16px, medium weight

### **Color Scheme**
- **Background**: Dark theme (#1a1a1a)
- **Text**: Light (#ffffff, #e0e0e0)
- **Primary Actions**: Bright accent color
- **Secondary Actions**: Muted colors
- **Progress Dots**: Active vs inactive states

### **Spacing**
- **Vertical rhythm**: 32px between major elements
- **Button padding**: 16px vertical, 32px horizontal
- **Card spacing**: 24px between choice cards
- **Form field spacing**: 20px between fields

### **Animation**
- **Step transitions**: Smooth slide animations
- **Button interactions**: Subtle hover/press states
- **Progress updates**: Animated dot progression
- **Success celebrations**: Confetti or particle effects

---

## 📱 **Responsive Behavior**

### **Desktop (1200px+)**
- **Three-column choice layouts**
- **Large form fields and buttons**
- **Generous whitespace**

### **Tablet (768px - 1199px)**
- **Two-column choice layouts**
- **Medium-sized interactive elements**
- **Optimized spacing**

### **Mobile (< 768px)**
- **Single-column layouts**
- **Full-width buttons**
- **Touch-optimized sizing (44px minimum)**
- **Reduced vertical spacing**

---

## ⚙️ **Technical Implementation**

### **State Management**
```javascript
const onboardingState = {
  currentStep: 1,
  totalSteps: 4,
  userChoices: {},
  timeStarted: timestamp,
  canExit: true,
  progressSaved: false
}
```

### **Step Navigation**
- **Forward progression**: Validate current step before advancing
- **Backward navigation**: Preserve previous choices
- **Exit handling**: Save progress to localStorage/database
- **Resume capability**: Restore state on return

### **Integration Points**
- **Authentication**: Receive user data from auth flow
- **Alliance Creation**: Pass team preferences
- **Venture Creation**: Pass project preferences
- **Dashboard**: Deliver completed user to main interface

---

## 🎯 **Success Metrics**

### **Performance Targets**
- **Completion Rate**: >80% reach final step
- **Time to Complete**: <5 minutes average
- **Exit and Return**: >60% of exits result in completion
- **Template Usage**: Track shortcut adoption

### **User Experience Goals**
- **Effortless progression**: No confusion about next steps
- **Clear value**: Understand benefits at each step
- **Immediate gratification**: See results of choices
- **Flexible paths**: Accommodate different user goals

---

**This onboarding wizard transforms the complex Royaltea platform into an approachable, guided experience that gets new users to their first meaningful action quickly while preserving the full power of the platform.**
