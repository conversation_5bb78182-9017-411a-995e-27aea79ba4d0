// Test script for the new agreement generator
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Import the NewAgreementGenerator class
import { NewAgreementGenerator } from './client/src/utils/agreement/newAgreementGenerator.js';

// Create an instance of the generator
const generator = new NewAgreementGenerator();

// Load the agreement template
const templatePath = path.join(__dirname, 'client/public/example-cog-contributor-agreement.md');
const templateText = fs.readFileSync(templatePath, 'utf8');

// Test different project types
const projectTypes = ['game', 'software', 'music', 'film', 'art'];

// Test different project lengths
const projectLengths = ['short', 'medium', 'long'];

// Create a function to generate test projects
function createTestProject(type, length) {
  return {
    name: `Test ${type.charAt(0).toUpperCase() + type.slice(1)} Project`,
    description: `This is a test ${type} project with ${length} duration`,
    projectType: type,
    projectLength: length,
    estimatedDuration: length === 'short' ? 2 : length === 'medium' ? 6 : 12,
    company_name: 'Test Company',
    contributors: [
      {
        permission_level: 'Owner',
        display_name: 'Test Owner',
        users: {
          display_name: 'Test Owner',
          email: '<EMAIL>',
          address: '123 Test St, Test City, Test State 12345',
          state: 'Test State',
          county: 'Test County',
          title: 'CEO'
        }
      }
    ],
    milestones: [
      {
        title: 'First Milestone',
        description: 'Complete initial setup',
        deadline: '2024-12-31'
      },
      {
        title: 'Second Milestone',
        description: 'Implement core features',
        deadline: '2025-03-31'
      }
    ]
  };
}

// Create a function to generate test royalty models
function createTestRoyaltyModel(type) {
  return {
    model_type: type,
    contributor_percentage: 33,
    min_payout: 1000,
    max_payout: 100000,
    configuration: {
      tasks_weight: 30,
      hours_weight: 30,
      difficulty_weight: 40
    }
  };
}

// Create a function to generate test options
function createTestOptions(project, royaltyModel) {
  return {
    contributors: project.contributors,
    currentUser: {
      id: 'test-user-id',
      email: '<EMAIL>',
      user_metadata: {
        full_name: 'Test Contributor'
      }
    },
    royaltyModel: royaltyModel,
    milestones: project.milestones,
    fullName: 'Test Contributor'
  };
}

// Create a function to run the tests
async function runTests() {
  console.log('=== Testing Agreement Generator ===\n');

  // Test each project type with medium length
  for (const type of projectTypes) {
    console.log(`Testing ${type.toUpperCase()} project type...`);

    const project = createTestProject(type, 'medium');
    const royaltyModel = createTestRoyaltyModel('custom');
    const options = createTestOptions(project, royaltyModel);

    try {
      // Generate the agreement
      const agreement = generator.generateAgreement(templateText, project, options);

      // Save the generated agreement to a file
      const outputPath = path.join(__dirname, `test-output-${type}.md`);
      fs.writeFileSync(outputPath, agreement);

      console.log(`  ✓ Generated agreement for ${type} project`);
      console.log(`  ✓ Saved to ${outputPath}`);

      // Check for key phrases that should be replaced based on project type
      const typeSpecificPhrases = getTypeSpecificPhrases(type);
      let allPhrasesFound = true;

      for (const phrase of typeSpecificPhrases) {
        if (agreement.includes(phrase)) {
          console.log(`  ✓ Found phrase: "${phrase}"`);
        } else {
          console.log(`  ✗ Missing phrase: "${phrase}"`);
          allPhrasesFound = false;
        }
      }

      if (allPhrasesFound) {
        console.log(`  ✓ All expected phrases found for ${type} project`);
      } else {
        console.log(`  ✗ Some expected phrases missing for ${type} project`);
      }

      // Check for phrases that should be replaced
      const phrasesToReplace = [
        'City of Gamers Inc.',
        'Village of The Ages',
        'a village simulation game where players guide communities through historical progressions',
        'Gynell Journigan'
      ];

      let allPhrasesReplaced = true;

      for (const phrase of phrasesToReplace) {
        if (agreement.includes(phrase)) {
          console.log(`  ✗ Found unreplaced phrase: "${phrase}"`);
          allPhrasesReplaced = false;
        }
      }

      if (allPhrasesReplaced) {
        console.log(`  ✓ All phrases properly replaced`);
      } else {
        console.log(`  ✗ Some phrases not properly replaced`);
      }

      // Check for exhibits
      if (agreement.includes('## EXHIBIT I') && agreement.includes('## EXHIBIT II')) {
        console.log(`  ✓ Exhibits properly generated`);
      } else {
        console.log(`  ✗ Exhibits not properly generated`);
      }

    } catch (error) {
      console.error(`  ✗ Error generating agreement for ${type} project:`, error);
    }

    console.log('');
  }

  // Test different project lengths for a game project
  for (const length of projectLengths) {
    console.log(`Testing ${length.toUpperCase()} project length...`);

    const project = createTestProject('game', length);
    const royaltyModel = createTestRoyaltyModel('custom');
    const options = createTestOptions(project, royaltyModel);

    try {
      // Generate the agreement
      const agreement = generator.generateAgreement(templateText, project, options);

      // Save the generated agreement to a file
      const outputPath = path.join(__dirname, `test-output-game-${length}.md`);
      fs.writeFileSync(outputPath, agreement);

      console.log(`  ✓ Generated agreement for ${length} project`);
      console.log(`  ✓ Saved to ${outputPath}`);

      // Check for length-specific phrases
      const lengthSpecificPhrases = getLengthSpecificPhrases(length);
      let allPhrasesFound = true;

      for (const phrase of lengthSpecificPhrases) {
        if (agreement.includes(phrase)) {
          console.log(`  ✓ Found phrase: "${phrase}"`);
        } else {
          console.log(`  ✗ Missing phrase: "${phrase}"`);
          allPhrasesFound = false;
        }
      }

      if (allPhrasesFound) {
        console.log(`  ✓ All expected phrases found for ${length} project`);
      } else {
        console.log(`  ✗ Some expected phrases missing for ${length} project`);
      }

    } catch (error) {
      console.error(`  ✗ Error generating agreement for ${length} project:`, error);
    }

    console.log('');
  }

  // Test different royalty models
  const royaltyModelTypes = ['equal', 'task', 'time', 'custom'];

  for (const modelType of royaltyModelTypes) {
    console.log(`Testing ${modelType.toUpperCase()} royalty model...`);

    const project = createTestProject('game', 'medium');
    const royaltyModel = createTestRoyaltyModel(modelType);
    const options = createTestOptions(project, royaltyModel);

    try {
      // Generate the agreement
      const agreement = generator.generateAgreement(templateText, project, options);

      // Save the generated agreement to a file
      const outputPath = path.join(__dirname, `test-output-royalty-${modelType}.md`);
      fs.writeFileSync(outputPath, agreement);

      console.log(`  ✓ Generated agreement for ${modelType} royalty model`);
      console.log(`  ✓ Saved to ${outputPath}`);

      // Check for royalty model specific phrases
      if (agreement.includes(`contributor_percentage: ${royaltyModel.contributor_percentage}`)) {
        console.log(`  ✓ Royalty percentage properly replaced`);
      } else {
        console.log(`  ✗ Royalty percentage not properly replaced`);
      }

    } catch (error) {
      console.error(`  ✗ Error generating agreement for ${modelType} royalty model:`, error);
    }

    console.log('');
  }

  console.log('=== Agreement Generator Tests Complete ===');
}

// Helper function to get type-specific phrases
function getTypeSpecificPhrases(type) {
  switch (type) {
    case 'game':
      return ['gameplay', 'players', 'game', 'levels'];
    case 'software':
      return ['software application', 'users', 'modules', 'user experience'];
    case 'music':
      return ['music project', 'listeners', 'tracks', 'listening experience'];
    case 'film':
      return ['film project', 'viewers', 'scenes', 'viewing experience'];
    default:
      return ['project', 'users', 'components', 'elements'];
  }
}

// Helper function to get length-specific phrases
function getLengthSpecificPhrases(length) {
  switch (length) {
    case 'short':
      return ['Phase 1', 'Phase 2', 'Month 1', 'Month 2'];
    case 'medium':
      return ['Phase 1', 'Phase 2', 'Phase 3', 'Phase 4'];
    case 'long':
      return ['Phase 1', 'Phase 2', 'Phase 3', 'Phase 4', 'Phase 5', 'Phase 6'];
    default:
      return ['Phase 1', 'Phase 2', 'Phase 3', 'Phase 4'];
  }
}

// Run the tests
runTests().catch(console.error);
