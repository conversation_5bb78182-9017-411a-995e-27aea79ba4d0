// Script to update roadmap timeframes in Supabase
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: './client/.env.local' });

// Initialize Supabase client with service key for admin access
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Make sure VITE_SUPABASE_URL and SUPABASE_SERVICE_KEY are set in client/.env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function updateRoadmapTimeframes() {
  console.log('=== Updating Roadmap Timeframes ===');
  
  try {
    // First, get the current roadmap data
    const { data: roadmapData, error: roadmapError } = await supabase
      .from('roadmap')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (roadmapError) {
      console.error('Error fetching roadmap data:', roadmapError);
      return;
    }
    
    if (!roadmapData || roadmapData.length === 0) {
      console.error('No roadmap data found');
      return;
    }
    
    const currentRoadmap = roadmapData[0];
    let phases = currentRoadmap.data;
    
    if (!Array.isArray(phases)) {
      console.error('Roadmap data is not an array');
      return;
    }
    
    console.log(`Current roadmap has ${phases.length} phases`);
    
    // Update timeframes for each phase to use actual timeframes instead of phase numbers
    phases.forEach(phase => {
      // Only update if the timeframe is in the format "Phase X"
      if (phase.timeframe && phase.timeframe.startsWith('Phase ')) {
        switch (phase.id) {
          case 1:
            // Keep "Completed" for Phase 1
            if (phase.timeframe !== "Completed") {
              phase.timeframe = "Completed";
            }
            break;
          case 2:
            phase.timeframe = "In Progress";
            break;
          case 3:
            phase.timeframe = "Current Focus";
            break;
          case 4:
            phase.timeframe = "Q3-Q4 2024";
            break;
          case 5:
            phase.timeframe = "Q1 2025";
            break;
          case 6:
            phase.timeframe = "Q2-Q3 2025";
            break;
          default:
            // Keep the existing timeframe for any other phases
            break;
        }
      }
    });
    
    console.log('Updated timeframes:');
    phases.forEach(phase => {
      console.log(`- Phase ${phase.id}: ${phase.title} (${phase.timeframe})`);
    });
    
    // Update the roadmap in the database
    const { data: updateData, error: updateError } = await supabase
      .from('roadmap')
      .update({
        data: phases,
        updated_at: new Date()
      })
      .eq('id', currentRoadmap.id);
    
    if (updateError) {
      console.error('Error updating roadmap data:', updateError);
    } else {
      console.log('Roadmap timeframes updated successfully');
    }
    
  } catch (error) {
    console.error('Error updating roadmap timeframes:', error);
  }
}

// Run the function
updateRoadmapTimeframes();
