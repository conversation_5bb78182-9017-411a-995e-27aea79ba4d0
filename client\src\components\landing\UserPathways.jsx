import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, Button } from '@heroui/react';

/**
 * User Pathways Component - Choose Your Creative Path
 * 
 * Features:
 * - Three distinct user segments with tailored value propositions
 * - Clear benefits and use cases for each path
 * - Direct navigation to appropriate onboarding flows
 * - Responsive card layout with hover effects
 */
const UserPathways = ({ onPathSelection }) => {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.8,
        staggerChildren: 0.2
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  };

  // User pathway data
  const pathways = [
    {
      id: 'business',
      icon: '🏢',
      title: 'ESTABLISHED BUSINESSES',
      subtitle: 'Professional tools for scaling teams',
      benefits: [
        'Professional tools',
        'Multiple revenue models',
        'Financial reporting',
        'Team management'
      ],
      metrics: '$25K+ monthly recurring revenue',
      examples: ['Enterprise teams', 'Agencies', 'Studios'],
      buttonText: 'Start Business',
      gradient: 'from-blue-600 to-cyan-600',
      bgGradient: 'from-blue-50 to-cyan-50',
      darkBgGradient: 'from-blue-900/20 to-cyan-900/20'
    },
    {
      id: 'alliance',
      icon: '🎨',
      title: 'CREATIVE COLLABORATIVES',
      subtitle: 'Fair contribution tracking for teams',
      benefits: [
        'Fair contribution tracking',
        'Transparent revenue sharing',
        'Project management',
        'Team collaboration'
      ],
      metrics: 'Game studios, Film teams, App developers',
      examples: ['🎮 Game studios', '🎬 Film teams', '📱 App developers'],
      buttonText: 'Form Alliance',
      gradient: 'from-purple-600 to-pink-600',
      bgGradient: 'from-purple-50 to-pink-50',
      darkBgGradient: 'from-purple-900/20 to-pink-900/20'
    },
    {
      id: 'individual',
      icon: '👤',
      title: 'INDIVIDUAL CREATORS',
      subtitle: 'Build your freelance career',
      benefits: [
        'Skill verification',
        'Professional networking',
        'Flexible project participation',
        'Career growth tracking'
      ],
      metrics: '🎯 Bounty hunting, 🤝 Alliance work, 💼 Mercenary roles',
      examples: ['Freelancers', 'Contractors', 'Specialists'],
      buttonText: 'Join Platform',
      gradient: 'from-green-600 to-emerald-600',
      bgGradient: 'from-green-50 to-emerald-50',
      darkBgGradient: 'from-green-900/20 to-emerald-900/20'
    }
  ];

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900 py-20">
      <motion.div
        className="max-w-7xl mx-auto px-6"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
      >
        {/* Section Header */}
        <motion.div variants={cardVariants} className="text-center mb-16">
          <h2 className="text-5xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            🎯 Choose Your Creative Path
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Whether you're running an established business, forming a creative collective, 
            or building your individual career, Royaltea has the tools you need to succeed.
          </p>
        </motion.div>

        {/* Pathway Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {pathways.map((pathway, index) => (
            <motion.div
              key={pathway.id}
              variants={cardVariants}
              whileHover={{ scale: 1.02, y: -5 }}
              transition={{ duration: 0.3 }}
            >
              <Card className={`h-full bg-gradient-to-br ${pathway.bgGradient} dark:${pathway.darkBgGradient} border-2 border-transparent hover:border-gray-200 dark:hover:border-gray-700 shadow-lg hover:shadow-xl transition-all duration-300`}>
                <CardBody className="p-8">
                  {/* Header */}
                  <div className="text-center mb-6">
                    <div className="text-6xl mb-4">{pathway.icon}</div>
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                      {pathway.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      {pathway.subtitle}
                    </p>
                  </div>

                  {/* Benefits */}
                  <div className="mb-6">
                    <ul className="space-y-3">
                      {pathway.benefits.map((benefit, idx) => (
                        <li key={idx} className="flex items-center text-gray-700 dark:text-gray-300">
                          <span className="text-green-500 mr-3">✅</span>
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Metrics/Examples */}
                  <div className="mb-6 p-4 bg-white/50 dark:bg-black/20 rounded-lg">
                    <div className="text-sm font-semibold text-gray-900 dark:text-white mb-2">
                      {pathway.metrics}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {pathway.examples.join(' • ')}
                    </div>
                  </div>

                  {/* Call to Action */}
                  <Button
                    size="lg"
                    className={`w-full bg-gradient-to-r ${pathway.gradient} text-white font-semibold py-4 text-lg shadow-lg hover:shadow-xl transition-all duration-300`}
                    onPress={() => onPathSelection(pathway.id)}
                  >
                    {pathway.buttonText}
                  </Button>
                </CardBody>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Additional Information */}
        <motion.div variants={cardVariants} className="text-center mt-16">
          <div className="max-w-4xl mx-auto">
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
              Not sure which path is right for you? All plans include our core features 
              and you can always upgrade or change your approach as you grow.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
              <div className="p-4">
                <div className="text-3xl mb-2">⚡</div>
                <div className="font-semibold text-gray-900 dark:text-white">5-Minute Setup</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Get started immediately</div>
              </div>
              <div className="p-4">
                <div className="text-3xl mb-2">🔒</div>
                <div className="font-semibold text-gray-900 dark:text-white">Secure & Compliant</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Enterprise-grade security</div>
              </div>
              <div className="p-4">
                <div className="text-3xl mb-2">🎯</div>
                <div className="font-semibold text-gray-900 dark:text-white">Fair & Transparent</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">No hidden fees or surprises</div>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default UserPathways;
