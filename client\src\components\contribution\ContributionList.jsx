import React, { useState, useEffect, useContext } from 'react';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { toast } from 'react-hot-toast';
import { format } from 'date-fns';
import { validateContribution, validateContributionsBulk } from '../../utils/validationHandler';
import BulkValidationModal from './BulkValidationModal';

const ContributionList = ({ projectId, userId = null, limit = 10, showActions = true, onEdit, onDelete }) => {
  const { currentUser } = useContext(UserContext);
  const [contributions, setContributions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [users, setUsers] = useState({});
  const [milestones, setMilestones] = useState({});

  // State for validation status filter
  const [validationFilter, setValidationFilter] = useState('all'); // 'all', 'pending', 'approved', 'rejected', 'pending_changes'

  // Fetch contributions
  useEffect(() => {
    const fetchContributions = async () => {
      try {
        setLoading(true);

        let query = supabase
          .from('contributions')
          .select('*, validation_status')
          .order('created_at', { ascending: false });

        // Filter by project if provided
        if (projectId) {
          query = query.eq('project_id', projectId);
        }

        // Filter by user if provided
        if (userId) {
          query = query.eq('user_id', userId);
        }

        // Filter by validation status if not 'all'
        if (validationFilter !== 'all') {
          query = query.eq('validation_status', validationFilter);
        }

        // Apply limit if provided
        if (limit > 0) {
          query = query.limit(limit);
        }

        const { data, error } = await query;

        if (error) throw error;

        setContributions(data || []);

        // Fetch related users and milestones
        if (data && data.length > 0) {
          await fetchRelatedData(data);
        }
      } catch (error) {
        console.error('Error fetching contributions:', error);
        toast.error('Failed to load contributions');
      } finally {
        setLoading(false);
      }
    };

    fetchContributions();
  }, [projectId, userId, limit, validationFilter]);

  // Fetch related users and milestones
  const fetchRelatedData = async (contributionsData) => {
    try {
      // Get unique user IDs
      const userIds = [...new Set(contributionsData.map(c => c.user_id))];

      // Fetch users
      if (userIds.length > 0) {
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('id, email, display_name')
          .in('id', userIds);

        if (userError) throw userError;

        // Create a map of user data
        const userMap = {};
        userData.forEach(user => {
          userMap[user.id] = user;
        });

        setUsers(userMap);
      }

      // Get unique milestone IDs
      const milestoneIds = [...new Set(contributionsData
        .filter(c => c.milestone_id)
        .map(c => c.milestone_id))];

      // Fetch milestones
      if (milestoneIds.length > 0) {
        const { data: milestoneData, error: milestoneError } = await supabase
          .from('milestones')
          .select('id, name')
          .in('id', milestoneIds);

        if (milestoneError) throw milestoneError;

        // Create a map of milestone data
        const milestoneMap = {};
        milestoneData.forEach(milestone => {
          milestoneMap[milestone.id] = milestone;
        });

        setMilestones(milestoneMap);
      }
    } catch (error) {
      console.error('Error fetching related data:', error);
    }
  };

  // Handle contribution deletion
  const handleDelete = async (id) => {
    if (!window.confirm('Are you sure you want to delete this contribution?')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('contributions')
        .delete()
        .eq('id', id);

      if (error) throw error;

      // Remove from local state
      setContributions(prev => prev.filter(c => c.id !== id));
      toast.success('Contribution deleted successfully');

      // Call onDelete callback if provided
      if (onDelete) {
        onDelete(id);
      }
    } catch (error) {
      console.error('Error deleting contribution:', error);
      toast.error('Failed to delete contribution');
    }
  };

  // Get user display name
  const getUserName = (userId) => {
    const user = users[userId];
    if (!user) return 'Unknown User';
    return user.display_name || user.email;
  };

  // Get milestone name
  const getMilestoneName = (milestoneId) => {
    if (!milestoneId) return null;
    const milestone = milestones[milestoneId];
    if (!milestone) return 'Unknown Milestone';
    return milestone.name;
  };

  // Check if current user can edit/delete a contribution
  const canModify = (contribution) => {
    if (!currentUser) return false;
    return contribution.user_id === currentUser.id;
  };

  // Check if current user is a project admin
  const [isAdmin, setIsAdmin] = useState({});

  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!currentUser || !projectId) return;

      try {
        const { data, error } = await supabase
          .from('project_contributors')
          .select('project_id, permission_level, is_admin')
          .eq('project_id', projectId)
          .eq('user_id', currentUser.id)
          .single();

        if (error) {
          console.error('Error checking admin status:', error);
          setIsAdmin(prev => ({ ...prev, [projectId]: false }));
        } else {
          setIsAdmin(prev => ({
            ...prev,
            [projectId]: data.is_admin || ['Owner', 'Admin'].includes(data.permission_level)
          }));
        }
      } catch (error) {
        console.error('Error in checkAdminStatus:', error);
        setIsAdmin(prev => ({ ...prev, [projectId]: false }));
      }
    };

    checkAdminStatus();
  }, [currentUser, projectId]);

  // State for inline feedback
  const [expandedFeedback, setExpandedFeedback] = useState({});
  const [feedbackText, setFeedbackText] = useState({});

  // State for attachment dropdowns
  const [openAttachmentDropdown, setOpenAttachmentDropdown] = useState(null);

  // State for bulk validation
  const [selectedContributions, setSelectedContributions] = useState([]);
  const [showBulkValidationModal, setShowBulkValidationModal] = useState(false);
  const [selectionMode, setSelectionMode] = useState(false);
  const [initialValidationStatus, setInitialValidationStatus] = useState('approved');

  // Toggle attachment dropdown
  const toggleAttachmentDropdown = (contributionId, event) => {
    // Prevent the click from propagating to the document
    if (event) {
      event.stopPropagation();
    }
    setOpenAttachmentDropdown(prev => prev === contributionId ? null : contributionId);
  };

  // Close attachment dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (openAttachmentDropdown && !event.target.closest('.attachment-dropdown-container')) {
        setOpenAttachmentDropdown(null);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [openAttachmentDropdown]);

  // Toggle feedback input for a contribution
  const toggleFeedbackInput = (contributionId, status) => {
    // If it's an approval, just validate directly
    if (status === 'approved') {
      handleQuickValidate(contributionId, status);
      return;
    }

    // Otherwise toggle the feedback input
    setExpandedFeedback(prev => ({
      ...prev,
      [contributionId]: prev[contributionId] ?
        { ...prev[contributionId], [status]: !prev[contributionId][status] } :
        { [status]: true }
    }));

    // Initialize feedback text if not already set
    if (!feedbackText[contributionId] || !feedbackText[contributionId][status]) {
      setFeedbackText(prev => ({
        ...prev,
        [contributionId]: {
          ...(prev[contributionId] || {}),
          [status]: ''
        }
      }));
    }
  };

  // Handle feedback text change
  const handleFeedbackChange = (contributionId, status, text) => {
    setFeedbackText(prev => ({
      ...prev,
      [contributionId]: {
        ...(prev[contributionId] || {}),
        [status]: text
      }
    }));
  };

  // Toggle selection mode
  const toggleSelectionMode = () => {
    setSelectionMode(prev => !prev);
    if (selectionMode) {
      // Clear selections when exiting selection mode
      setSelectedContributions([]);
    }
  };

  // Toggle contribution selection
  const toggleContributionSelection = (contribution, event) => {
    if (!selectionMode) return;

    // If the event is coming from the checkbox, don't toggle again
    if (event && event.target.type === 'checkbox') {
      return;
    }

    setSelectedContributions(prev => {
      const isSelected = prev.some(c => c.id === contribution.id);
      if (isSelected) {
        return prev.filter(c => c.id !== contribution.id);
      } else {
        return [...prev, contribution];
      }
    });
  };

  // Handle checkbox change directly
  const handleCheckboxChange = (contribution, checked) => {
    setSelectedContributions(prev => {
      if (checked) {
        // Add to selection if not already selected
        if (!prev.some(c => c.id === contribution.id)) {
          return [...prev, contribution];
        }
        return prev;
      } else {
        // Remove from selection
        return prev.filter(c => c.id !== contribution.id);
      }
    });
  };

  // Check if a contribution is selected
  const isContributionSelected = (contributionId) => {
    return selectedContributions.some(c => c.id === contributionId);
  };

  // Select all visible contributions
  const selectAllContributions = () => {
    // Only select pending contributions
    const pendingContributions = contributions.filter(c => c.validation_status === 'pending');
    setSelectedContributions(pendingContributions);
  };

  // Deselect all contributions
  const deselectAllContributions = () => {
    setSelectedContributions([]);
  };

  // Toggle select all
  const toggleSelectAll = () => {
    if (selectedContributions.length === contributions.filter(c => c.validation_status === 'pending').length) {
      deselectAllContributions();
    } else {
      selectAllContributions();
    }
  };

  // Handle bulk validation
  const handleBulkValidation = (initialStatus = 'approved') => {
    if (selectedContributions.length === 0) {
      toast.error('No contributions selected');
      return;
    }

    // Only allow bulk validation for pending contributions
    const nonPendingContributions = selectedContributions.filter(c => c.validation_status !== 'pending');
    if (nonPendingContributions.length > 0) {
      toast.error('Only pending contributions can be bulk validated');
      return;
    }

    // Set the initial status and show the modal
    setInitialValidationStatus(initialStatus);
    setShowBulkValidationModal(true);
  };

  // Handle bulk validation submit
  const handleBulkValidationSubmit = async (contributions, status) => {
    try {
      const result = await validateContributionsBulk(contributions, status, projectId);

      if (result.success) {
        // Update the local state
        setContributions(prev =>
          prev.map(c =>
            selectedContributions.some(sc => sc.id === c.id)
              ? { ...c, validation_status: status }
              : c
          )
        );

        // Exit selection mode and clear selections
        setSelectionMode(false);
        setSelectedContributions([]);

        return result;
      } else {
        toast.error(result.error || 'Bulk validation failed');
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Error in bulk validation:', error);
      toast.error(error.message || 'Bulk validation failed');
      return { success: false, error: error.message };
    }
  };

  // Handle quick validation
  const handleQuickValidate = async (contributionId, status, feedback = '') => {
    try {
      // For non-approval statuses, get feedback from the state if not provided
      if (status !== 'approved' && !feedback) {
        feedback = feedbackText[contributionId]?.[status] || '';
      }

      // Require feedback for rejections and change requests
      if ((status === 'rejected' || status === 'pending_changes') && !feedback.trim()) {
        toast.error(`Please provide feedback when ${status === 'rejected' ? 'rejecting' : 'requesting changes for'} a contribution`);
        return;
      }

      const result = await validateContribution(contributionId, status, feedback);

      if (result.success) {
        toast.success(`Contribution ${status.replace('_', ' ')}`);

        // Update the local state
        setContributions(prev =>
          prev.map(c =>
            c.id === contributionId
              ? { ...c, validation_status: status }
              : c
          )
        );

        // Clear the expanded state and feedback for this contribution
        setExpandedFeedback(prev => {
          const newState = { ...prev };
          delete newState[contributionId];
          return newState;
        });

        setFeedbackText(prev => {
          const newState = { ...prev };
          delete newState[contributionId];
          return newState;
        });
      } else {
        toast.error(result.error || 'Validation failed');
      }
    } catch (error) {
      console.error('Error validating contribution:', error);
      toast.error(error.message || 'Validation failed');
    }
  };

  // Get the date to display (use created_at if date_performed is not available)
  const getDisplayDate = (contribution) => {
    return contribution.date_performed || contribution.created_at;
  };

  // Render validation status badge
  const renderValidationBadge = (status) => {
    const statusClasses = {
      pending: 'badge-warning',
      approved: 'badge-success',
      rejected: 'badge-danger',
      pending_changes: 'badge-info'
    };

    const statusLabels = {
      pending: 'Pending',
      approved: 'Approved',
      rejected: 'Rejected',
      pending_changes: 'Changes Requested'
    };

    return (
      <span className={`validation-badge ${statusClasses[status] || 'badge-secondary'}`}>
        {statusLabels[status] || status || 'Pending'}
      </span>
    );
  };

  // Handle validation filter change
  const handleFilterChange = (status) => {
    setValidationFilter(status);
  };

  // Get appropriate icon for file type
  const getFileIcon = (fileType) => {
    if (!fileType) return 'bi bi-file-earmark';

    if (fileType.startsWith('image/')) {
      return 'bi bi-file-image';
    } else if (fileType === 'application/pdf') {
      return 'bi bi-file-pdf';
    } else if (fileType.includes('word') || fileType.includes('document')) {
      return 'bi bi-file-word';
    } else if (fileType.includes('excel') || fileType.includes('spreadsheet')) {
      return 'bi bi-file-excel';
    } else if (fileType.includes('powerpoint') || fileType.includes('presentation')) {
      return 'bi bi-file-ppt';
    } else if (fileType.includes('text/')) {
      return 'bi bi-file-text';
    } else {
      return 'bi bi-file-earmark';
    }
  };

  if (loading) {
    return <div className="contribution-list-loading">Loading contributions...</div>;
  }

  return (
    <div className="contribution-list-container">
      <div className="contribution-list-header">
        <div className="contribution-filters">
          <div className="filter-label">Filter by validation status:</div>
          <div className="filter-options">
            <button
              className={`filter-btn ${validationFilter === 'all' ? 'active' : ''}`}
              onClick={() => handleFilterChange('all')}
            >
              All
            </button>
            <button
              className={`filter-btn ${validationFilter === 'pending' ? 'active' : ''}`}
              onClick={() => handleFilterChange('pending')}
            >
              Pending
            </button>
            <button
              className={`filter-btn ${validationFilter === 'approved' ? 'active' : ''}`}
              onClick={() => handleFilterChange('approved')}
            >
              Approved
            </button>
            <button
              className={`filter-btn ${validationFilter === 'rejected' ? 'active' : ''}`}
              onClick={() => handleFilterChange('rejected')}
            >
              Rejected
            </button>
            <button
              className={`filter-btn ${validationFilter === 'pending_changes' ? 'active' : ''}`}
              onClick={() => handleFilterChange('pending_changes')}
            >
              Changes Requested
            </button>
          </div>
        </div>

        {isAdmin[projectId] && (
          <div className="bulk-actions">
            <button
              className={`bulk-action-btn ${selectionMode ? 'active' : ''}`}
              onClick={toggleSelectionMode}
              title={selectionMode ? 'Exit selection mode' : 'Enter selection mode'}
            >
              <i className={`bi ${selectionMode ? 'bi-check-square' : 'bi-square'}`}></i>
              {selectionMode ? 'Cancel Selection' : 'Select Multiple'}
            </button>

            {selectionMode && (
              <>
                <button
                  className="bulk-action-btn select-all"
                  onClick={toggleSelectAll}
                  title="Select/Deselect All Pending Contributions"
                >
                  <i className="bi bi-check-all"></i>
                  {selectedContributions.length === contributions.filter(c => c.validation_status === 'pending').length
                    ? 'Deselect All'
                    : 'Select All Pending'}
                </button>
                <div className="bulk-validation-buttons">
                  <button
                    className="bulk-action-btn validate approve"
                    onClick={() => handleBulkValidation('approved')}
                    disabled={selectedContributions.length === 0}
                    title="Approve selected contributions"
                  >
                    <i className="bi bi-check-circle"></i>
                    Approve
                  </button>
                  <button
                    className="bulk-action-btn validate reject"
                    onClick={() => handleBulkValidation('rejected')}
                    disabled={selectedContributions.length === 0}
                    title="Reject selected contributions"
                  >
                    <i className="bi bi-x-circle"></i>
                    Reject
                  </button>
                  <button
                    className="bulk-action-btn validate changes"
                    onClick={() => handleBulkValidation('pending_changes')}
                    disabled={selectedContributions.length === 0}
                    title="Request changes for selected contributions"
                  >
                    <i className="bi bi-pencil-square"></i>
                    Request Changes
                  </button>
                </div>
              </>
            )}
          </div>
        )}
      </div>

      {contributions.length === 0 ? (
        <div className="contribution-list-empty">No contributions found matching the selected filter.</div>
      ) : (
        <div className="contribution-list">
          {contributions.map(contribution => (
        <div
          key={contribution.id}
          className={`contribution-item ${selectionMode && isContributionSelected(contribution.id) ? 'selected' : ''}`}
          onClick={(e) => selectionMode && toggleContributionSelection(contribution, e)}
        >
          <div className="contribution-header">
            <div className="contribution-title-row">
              {selectionMode && (
                <div className="contribution-selection">
                  <input
                    type="checkbox"
                    checked={isContributionSelected(contribution.id)}
                    onChange={(e) => {
                      e.stopPropagation();
                      handleCheckboxChange(contribution, e.target.checked);
                    }}
                    className="contribution-checkbox"
                  />
                </div>
              )}
              <div className="contribution-title">{contribution.task_name}</div>
              <div className="contribution-validation-status">
                <div className="validation-status-label">Status:</div>
                {renderValidationBadge(contribution.validation_status)}
              </div>
            </div>
            <div className="contribution-meta">
              <span className="contribution-date">
                {format(new Date(getDisplayDate(contribution)), 'MMM d, yyyy')}
              </span>
              <span className="contribution-user">{getUserName(contribution.user_id)}</span>
            </div>
          </div>

          <div className="contribution-details">
            <div className="contribution-stats">
              <div className="stat">
                <span className="stat-label">Type:</span>
                <span className="stat-value">{contribution.task_type}</span>
              </div>
              <div className="stat">
                <span className="stat-label">Category:</span>
                <span className="stat-value">{contribution.category}</span>
              </div>
              <div className="stat">
                <span className="stat-label">Difficulty:</span>
                <span className="stat-value">{contribution.difficulty}</span>
              </div>
              <div className="stat">
                <span className="stat-label">Hours:</span>
                <span className="stat-value">{contribution.hours_spent}</span>
              </div>
              {contribution.milestone_id && (
                <div className="stat">
                  <span className="stat-label">Milestone:</span>
                  <span className="stat-value">{getMilestoneName(contribution.milestone_id)}</span>
                </div>
              )}
            </div>

            {contribution.description && (
              <div className="contribution-description">
                {contribution.description}
              </div>
            )}

            {/* File Attachments */}
            {contribution.has_attachments && contribution.attachments_data && contribution.attachments_data.length > 0 && (
              <div className="contribution-attachments">
                <h6>Attachments</h6>
                <div className="attachments-count">{contribution.attachments_data.length} file(s) attached</div>
              </div>
            )}
          </div>

          <div className="contribution-actions-row">
            <div className="contribution-actions-left">
              {showActions && canModify(contribution) && (
                <div className="contribution-actions">
                  <button
                    className="btn-action edit"
                    onClick={() => onEdit && onEdit(contribution)}
                    title="Edit contribution"
                  >
                    <i className="bi bi-pencil"></i>
                  </button>
                  <button
                    className="btn-action delete"
                    onClick={() => handleDelete(contribution.id)}
                    title="Delete contribution"
                  >
                    <i className="bi bi-trash"></i>
                  </button>
                </div>
              )}
            </div>

            <div className="contribution-validation-actions">
              {/* Main validation button */}
              <button
                className={`btn-validation ${contribution.validation_status === 'pending' ? 'btn-validation-pending' :
                  contribution.validation_status === 'approved' ? 'btn-validation-approved' :
                  contribution.validation_status === 'rejected' ? 'btn-validation-rejected' :
                  contribution.validation_status === 'pending_changes' ? 'btn-validation-changes' : ''}`}
                onClick={() => window.location.href = `/contribution/${contribution.id}/validate`}
                title="View or update validation status"
              >
                {contribution.validation_status === 'pending' && <i className="bi bi-hourglass"></i>}
                {contribution.validation_status === 'approved' && <i className="bi bi-check-circle-fill"></i>}
                {contribution.validation_status === 'rejected' && <i className="bi bi-x-circle-fill"></i>}
                {contribution.validation_status === 'pending_changes' && <i className="bi bi-pencil-square"></i>}
                {contribution.validation_status === 'pending' && 'Validate Now'}
                {contribution.validation_status === 'approved' && 'Approved - View Details'}
                {contribution.validation_status === 'rejected' && 'Rejected - View Details'}
                {contribution.validation_status === 'pending_changes' && 'Changes Requested - View Details'}
              </button>

              {/* Quick validation buttons for admins */}
              {isAdmin[projectId] && contribution.validation_status === 'pending' && (
                <div className="quick-validation-container">
                  <div className="quick-validation-buttons">
                    <button
                      className="quick-validate approve"
                      onClick={() => toggleFeedbackInput(contribution.id, 'approved')}
                      title="Quickly approve this contribution"
                    >
                      <i className="bi bi-check-circle"></i>
                    </button>
                    <button
                      className={`quick-validate reject ${expandedFeedback[contribution.id]?.rejected ? 'active' : ''}`}
                      onClick={() => toggleFeedbackInput(contribution.id, 'rejected')}
                      title="Reject (requires feedback)"
                    >
                      <i className="bi bi-x-circle"></i>
                    </button>
                    <button
                      className={`quick-validate changes ${expandedFeedback[contribution.id]?.pending_changes ? 'active' : ''}`}
                      onClick={() => toggleFeedbackInput(contribution.id, 'pending_changes')}
                      title="Request changes (requires feedback)"
                    >
                      <i className="bi bi-pencil-square"></i>
                    </button>
                  </div>

                  {/* Inline feedback for rejection */}
                  {expandedFeedback[contribution.id]?.rejected && (
                    <div className="inline-feedback-container">
                      <textarea
                        className="inline-feedback-textarea"
                        value={feedbackText[contribution.id]?.rejected || ''}
                        onChange={(e) => handleFeedbackChange(contribution.id, 'rejected', e.target.value)}
                        placeholder="Please explain why you are rejecting this contribution..."
                      />
                      <div className="inline-feedback-actions">
                        <button
                          className="inline-feedback-cancel"
                          onClick={() => toggleFeedbackInput(contribution.id, 'rejected')}
                        >
                          Cancel
                        </button>
                        <button
                          className="inline-feedback-submit reject"
                          onClick={() => handleQuickValidate(contribution.id, 'rejected')}
                        >
                          Reject
                        </button>
                      </div>
                    </div>
                  )}

                  {/* Inline feedback for change requests */}
                  {expandedFeedback[contribution.id]?.pending_changes && (
                    <div className="inline-feedback-container">
                      <textarea
                        className="inline-feedback-textarea"
                        value={feedbackText[contribution.id]?.pending_changes || ''}
                        onChange={(e) => handleFeedbackChange(contribution.id, 'pending_changes', e.target.value)}
                        placeholder="Please explain what changes are needed..."
                      />
                      <div className="inline-feedback-actions">
                        <button
                          className="inline-feedback-cancel"
                          onClick={() => toggleFeedbackInput(contribution.id, 'pending_changes')}
                        >
                          Cancel
                        </button>
                        <button
                          className="inline-feedback-submit changes"
                          onClick={() => handleQuickValidate(contribution.id, 'pending_changes')}
                        >
                          Request Changes
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* File attachments dropdown */}
              {contribution.has_attachments && contribution.attachments_data && contribution.attachments_data.length > 0 && (
                <div className="attachment-dropdown-container">
                  <button
                    className={`btn-attachment-download ${openAttachmentDropdown === contribution.id ? 'active' : ''}`}
                    onClick={(e) => toggleAttachmentDropdown(contribution.id, e)}
                    title={`View ${contribution.attachments_data.length} attachment(s)`}
                  >
                    <i className="bi bi-paperclip"></i>
                    <span className="attachment-count">{contribution.attachments_data.length}</span>
                  </button>

                  {openAttachmentDropdown === contribution.id && (
                    <div className="attachment-dropdown">
                      <div className="attachment-dropdown-header">
                        <h6>Attachments</h6>
                        <button
                          className="close-dropdown-btn"
                          onClick={(e) => toggleAttachmentDropdown(contribution.id, e)}
                        >
                          <i className="bi bi-x"></i>
                        </button>
                      </div>
                      <ul className="attachment-list">
                        {contribution.attachments_data.map((file, index) => (
                          <li key={index} className="attachment-item">
                            <div className="attachment-info">
                              <i className={getFileIcon(file.type)}></i>
                              <span className="attachment-name">{file.name}</span>
                            </div>
                            <div className="attachment-actions">
                              {file.type.startsWith('image/') && (
                                <a
                                  href={file.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="attachment-action preview"
                                  title="Preview"
                                >
                                  <i className="bi bi-eye"></i>
                                </a>
                              )}
                              <a
                                href={file.url}
                                download={file.name}
                                className="attachment-action download"
                                title="Download"
                              >
                                <i className="bi bi-download"></i>
                              </a>
                            </div>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
      )}

      {/* Bulk Validation Modal */}
      {showBulkValidationModal && (
        <BulkValidationModal
          selectedContributions={selectedContributions}
          onClose={() => setShowBulkValidationModal(false)}
          onValidate={handleBulkValidationSubmit}
          initialStatus={initialValidationStatus}
        />
      )}
    </div>
  );
};

export default ContributionList;
