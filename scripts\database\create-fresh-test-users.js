// Create fresh test users with email confirmation disabled
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  'https://hqqlrrqvjcetoxbdjgzx.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MjA1NTksImV4cCI6MjA1OTM5NjU1OX0.e0oaDNVbA563SZJHPm6UTFOXcLbWorKn42a_brChpKQ'
);

async function createFreshTestUsers() {
  console.log('🔑 Creating fresh test users...\n');
  
  // New test user credentials
  const testUsers = [
    {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      name: 'Test User',
      role: 'admin'
    },
    {
      email: '<EMAIL>', 
      password: 'VRCPassword123!',
      name: 'VRC Admin',
      role: 'company_admin'
    },
    {
      email: '<EMAIL>',
      password: 'SalesPassword123!',
      name: 'Sales Rep',
      role: 'sales_rep'
    }
  ];
  
  for (const user of testUsers) {
    try {
      console.log(`Creating user: ${user.email}`);
      
      // Create new user
      const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
        email: user.email,
        password: user.password,
        options: {
          data: {
            full_name: user.name,
            role: user.role
          }
        }
      });
      
      if (signUpData?.user) {
        console.log(`✅ User ${user.email} created successfully`);
        console.log(`   User ID: ${signUpData.user.id}`);
        console.log(`   Email confirmed: ${signUpData.user.email_confirmed_at ? 'Yes' : 'No'}`);
        
        if (signUpData.session) {
          console.log(`   Access Token: ${signUpData.session.access_token.substring(0, 50)}...`);
          
          // Store the full token for testing
          console.log(`\n   🔑 FULL TOKEN FOR TESTING:`);
          console.log(`   ${signUpData.session.access_token}\n`);
          
          // Test the token immediately
          console.log(`   Testing token...`);
          const { data: userData, error: userError } = await supabase.auth.getUser(signUpData.session.access_token);
          
          if (userData?.user) {
            console.log(`   ✅ Token valid for user: ${userData.user.email}`);
          } else {
            console.log(`   ❌ Token test failed: ${userError?.message}`);
          }
          
        } else {
          console.log(`   ⚠️ No session created`);
        }
      } else {
        console.log(`❌ Failed to create user: ${signUpError?.message}`);
      }
      
      console.log('---\n');
      
    } catch (error) {
      console.log(`❌ Error with user ${user.email}: ${error.message}\n`);
    }
  }
  
  // Test companies table access
  console.log('🏢 Testing companies table access...');
  try {
    const { data, error } = await supabase
      .from('companies')
      .select('*')
      .limit(5);
      
    if (error) {
      console.log(`❌ Companies table error: ${error.message}`);
    } else {
      console.log(`✅ Companies table accessible (${data?.length || 0} records)`);
      if (data && data.length > 0) {
        console.log(`   Sample company: ${data[0].legal_name}`);
        console.log(`   Company ID: ${data[0].id}`);
      }
    }
  } catch (error) {
    console.log(`❌ Companies table connection error: ${error.message}`);
  }
}

createFreshTestUsers();
