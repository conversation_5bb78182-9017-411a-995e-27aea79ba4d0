// Simple script to check the 3 projects and their agreements
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MjA1NTksImV4cCI6MjA1OTM5NjU1OX0.e0oaDNVbA563SZJHPm6UTFOXcLbWorKn42a_brChpKQ';
const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log('Checking all projects and their agreements...\n');

    // Get all projects
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('*');

    if (projectsError) {
      throw projectsError;
    }

    console.log(`Found ${projects.length} projects in the database:`);
    projects.forEach((p, i) => {
      console.log(`${i+1}. ${p.name} (ID: ${p.id}, Type: ${p.project_type})`);
    });

    // Get all agreements
    const { data: agreements, error: agreementsError } = await supabase
      .from('contributor_agreements')
      .select('*, project:project_id(name)');

    if (agreementsError) {
      throw agreementsError;
    }

    console.log(`\nFound ${agreements.length} agreements in the database:`);

    // Group agreements by project
    const projectAgreements = {};
    agreements.forEach(agreement => {
      const projectId = agreement.project_id;
      if (!projectAgreements[projectId]) {
        projectAgreements[projectId] = [];
      }
      projectAgreements[projectId].push(agreement);
    });

    // Check each project's agreements
    for (const projectId in projectAgreements) {
      const projectAgreementList = projectAgreements[projectId];
      const projectName = projectAgreementList[0].project?.name || 'Unknown Project';

      console.log(`\n-----------------------------------------`);
      console.log(`Project: ${projectName} (ID: ${projectId})`);
      console.log(`Agreements: ${projectAgreementList.length}`);

      for (const agreement of projectAgreementList) {
        console.log(`\n  Agreement ID: ${agreement.id}, Version: ${agreement.version || 1}`);

        // Check for Village references
        if (agreement.agreement_text && agreement.agreement_text.toLowerCase().includes('village')) {
          console.log(`  ⚠️ CONTAINS VILLAGE REFERENCE`);

          // Find the context of the reference
          const lines = agreement.agreement_text.split('\n');
          let foundCount = 0;

          for (let i = 0; i < lines.length; i++) {
            if (lines[i].toLowerCase().includes('village')) {
              foundCount++;
              const start = Math.max(0, i - 1);
              const end = Math.min(lines.length - 1, i + 1);

              console.log(`\n  Context (line ${i + 1}):`);
              for (let j = start; j <= end; j++) {
                console.log(`    ${j === i ? '→' : ' '} ${lines[j]}`);
              }

              // Only show the first few occurrences
              if (foundCount >= 3) {
                const remainingCount = lines.filter(line =>
                  line.toLowerCase().includes('village')).length - foundCount;
                if (remainingCount > 0) {
                  console.log(`\n  ... and ${remainingCount} more occurrences`);
                }
                break;
              }
            }
          }
        } else {
          console.log(`  ✓ No Village references found`);
        }
      }
    }

    // Get all milestones
    const { data: milestones, error: milestonesError } = await supabase
      .from('milestones')
      .select('*, project:project_id(name)');

    if (milestonesError) {
      throw milestonesError;
    }

    console.log(`\n-----------------------------------------`);
    console.log(`\nFound ${milestones.length} milestones in the database:`);

    // Group milestones by project
    const projectMilestones = {};
    milestones.forEach(milestone => {
      const projectId = milestone.project_id;
      if (!projectMilestones[projectId]) {
        projectMilestones[projectId] = [];
      }
      projectMilestones[projectId].push(milestone);
    });

    // Check each project's milestones
    for (const projectId in projectMilestones) {
      const projectMilestoneList = projectMilestones[projectId];
      const projectName = projectMilestoneList[0].project?.name || 'Unknown Project';

      console.log(`\n-----------------------------------------`);
      console.log(`Project: ${projectName} (ID: ${projectId})`);
      console.log(`Milestones: ${projectMilestoneList.length}`);

      for (const milestone of projectMilestoneList) {
        console.log(`\n  Milestone ID: ${milestone.id}, Name: ${milestone.name}`);
        console.log(`  Description: ${milestone.description || 'No description'}`);

        // Check for Village references
        if ((milestone.name && milestone.name.toLowerCase().includes('village')) ||
            (milestone.description && milestone.description.toLowerCase().includes('village'))) {
          console.log(`  ⚠️ CONTAINS VILLAGE REFERENCE`);
        } else {
          console.log(`  ✓ No Village references found`);
        }
      }
    }

    console.log('\nDatabase check completed.');

  } catch (error) {
    console.error('Error:', error);
  }
}

main();
