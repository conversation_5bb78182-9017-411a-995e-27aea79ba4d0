// Debug what the 425-character content actually is
const fetch = require('node-fetch');

const SITE_URL = 'https://royalty.technology';

async function debugPageContent() {
  const routes = [
    '/',
    '/teams',
    '/alliances',
    '/teams/test-id/manage'
  ];
  
  for (const route of routes) {
    try {
      console.log(`\n🔍 Testing route: ${route}`);
      const response = await fetch(`${SITE_URL}${route}`);
      const content = await response.text();
      
      console.log(`Status: ${response.status}`);
      console.log(`Content-Type: ${response.headers.get('content-type')}`);
      console.log(`Content length: ${content.length}`);
      console.log(`Content preview: ${content.substring(0, 200)}...`);
      
      // Check if it's the same content
      if (content.length === 425) {
        console.log('⚠️ This is the 425-character content!');
        console.log('Full content:');
        console.log(content);
      }
      
      // Check if it contains React root
      const hasReactRoot = content.includes('id="root"');
      console.log(`Has React root: ${hasReactRoot}`);
      
      // Check if it's HTML
      const isHTML = content.includes('<html') || content.includes('<!DOCTYPE');
      console.log(`Is HTML: ${isHTML}`);
      
    } catch (error) {
      console.log(`❌ Error testing ${route}: ${error.message}`);
    }
  }
}

debugPageContent();
