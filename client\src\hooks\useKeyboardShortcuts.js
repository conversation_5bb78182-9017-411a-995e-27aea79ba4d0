import { useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';

/**
 * Keyboard Shortcuts Hook
 * 
 * Provides comprehensive keyboard shortcuts and accessibility improvements
 * throughout the navigation system and application features.
 */

const useKeyboardShortcuts = ({ 
  onNavigateToTab, 
  onOpenSearch, 
  onToggleFilters,
  onCreateNew,
  currentCanvas = null 
}) => {
  const navigate = useNavigate();

  // Keyboard shortcut handler
  const handleKeyDown = useCallback((event) => {
    // Don't trigger shortcuts when typing in inputs
    if (event.target.tagName === 'INPUT' || 
        event.target.tagName === 'TEXTAREA' || 
        event.target.contentEditable === 'true') {
      return;
    }

    const { key, ctrlKey, metaKey, altKey, shiftKey } = event;
    const isModifierPressed = ctrlKey || metaKey;

    // Global navigation shortcuts
    if (isModifierPressed) {
      switch (key) {
        case '1':
          event.preventDefault();
          navigate('/start');
          toast.success('Navigated to Start');
          break;
        case '2':
          event.preventDefault();
          navigate('/contributions');
          toast.success('Navigated to Track');
          break;
        case '3':
          event.preventDefault();
          navigate('/earn');
          toast.success('Navigated to Earn');
          break;
        case 'k':
          event.preventDefault();
          onOpenSearch?.();
          break;
        case 'f':
          event.preventDefault();
          onToggleFilters?.();
          break;
        case 'n':
          event.preventDefault();
          onCreateNew?.();
          break;
        case '/':
          event.preventDefault();
          onOpenSearch?.();
          break;
        default:
          break;
      }
    }

    // Canvas-specific shortcuts
    if (!isModifierPressed) {
      switch (key) {
        case '1':
        case '2':
        case '3':
        case '4':
        case '5':
          if (onNavigateToTab) {
            event.preventDefault();
            const tabIndex = parseInt(key) - 1;
            onNavigateToTab(tabIndex);
          }
          break;
        case 'Escape':
          // Close modals, clear search, etc.
          event.preventDefault();
          document.dispatchEvent(new CustomEvent('closeModals'));
          break;
        case '?':
          event.preventDefault();
          showKeyboardShortcuts();
          break;
        default:
          break;
      }
    }

    // Alt + key shortcuts for quick actions
    if (altKey && !isModifierPressed) {
      switch (key) {
        case 't':
          event.preventDefault();
          if (currentCanvas === 'contributions') {
            onNavigateToTab?.(0); // Time tracker tab
            toast.success('Switched to Time Tracker');
          }
          break;
        case 's':
          event.preventDefault();
          if (currentCanvas === 'contributions') {
            onNavigateToTab?.(1); // Submit tab
            toast.success('Switched to Submit Work');
          }
          break;
        case 'p':
          event.preventDefault();
          if (currentCanvas === 'contributions') {
            onNavigateToTab?.(2); // Progress tab
            toast.success('Switched to Progress');
          }
          break;
        case 'a':
          event.preventDefault();
          if (currentCanvas === 'contributions') {
            onNavigateToTab?.(3); // Analytics tab
            toast.success('Switched to Analytics');
          }
          break;
        default:
          break;
      }
    }
  }, [navigate, onNavigateToTab, onOpenSearch, onToggleFilters, onCreateNew, currentCanvas]);

  // Show keyboard shortcuts help
  const showKeyboardShortcuts = () => {
    const shortcuts = [
      { key: 'Ctrl/Cmd + 1', description: 'Navigate to Start' },
      { key: 'Ctrl/Cmd + 2', description: 'Navigate to Track' },
      { key: 'Ctrl/Cmd + 3', description: 'Navigate to Earn' },
      { key: 'Ctrl/Cmd + K', description: 'Open search' },
      { key: 'Ctrl/Cmd + F', description: 'Toggle filters' },
      { key: 'Ctrl/Cmd + N', description: 'Create new item' },
      { key: '1-5', description: 'Switch between tabs' },
      { key: 'Alt + T', description: 'Time tracker (Track page)' },
      { key: 'Alt + S', description: 'Submit work (Track page)' },
      { key: 'Alt + P', description: 'Progress view (Track page)' },
      { key: 'Alt + A', description: 'Analytics view (Track page)' },
      { key: 'Escape', description: 'Close modals/dialogs' },
      { key: '?', description: 'Show this help' }
    ];

    const helpText = shortcuts
      .map(s => `${s.key}: ${s.description}`)
      .join('\n');

    toast.success(
      `Keyboard Shortcuts:\n\n${helpText}`,
      { 
        duration: 8000,
        style: {
          whiteSpace: 'pre-line',
          textAlign: 'left',
          maxWidth: '400px'
        }
      }
    );
  };

  // Set up event listeners
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  // Return utility functions
  return {
    showKeyboardShortcuts
  };
};

/**
 * Accessibility Improvements Hook
 * 
 * Provides additional accessibility features like focus management,
 * screen reader support, and keyboard navigation enhancements.
 */
export const useAccessibility = () => {
  // Focus management
  const focusElement = useCallback((selector) => {
    const element = document.querySelector(selector);
    if (element) {
      element.focus();
    }
  }, []);

  // Announce to screen readers
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }, []);

  // Skip to main content
  const skipToMainContent = useCallback(() => {
    const mainContent = document.querySelector('main, [role="main"], #main-content');
    if (mainContent) {
      mainContent.focus();
      mainContent.scrollIntoView();
    }
  }, []);

  // High contrast mode detection
  const isHighContrastMode = useCallback(() => {
    return window.matchMedia('(prefers-contrast: high)').matches;
  }, []);

  // Reduced motion detection
  const prefersReducedMotion = useCallback(() => {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }, []);

  // Color scheme detection
  const getColorScheme = useCallback(() => {
    if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'dark';
    }
    return 'light';
  }, []);

  return {
    focusElement,
    announceToScreenReader,
    skipToMainContent,
    isHighContrastMode,
    prefersReducedMotion,
    getColorScheme
  };
};

export default useKeyboardShortcuts;
