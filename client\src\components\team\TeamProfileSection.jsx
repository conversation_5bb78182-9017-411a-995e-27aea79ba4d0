import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * TeamProfileSection component for displaying teams in the user profile
 * @param {Object} props - Component props
 * @param {string} props.userId - The user ID
 */
const TeamProfileSection = ({ userId }) => {
  const [teams, setTeams] = useState([]);
  const [invitations, setInvitations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newTeam, setNewTeam] = useState({
    name: '',
    description: ''
  });
  const navigate = useNavigate();

  // Fetch teams and invitations on component mount
  useEffect(() => {
    if (userId) {
      fetchUserTeamsAndInvitations();
    }
  }, [userId]);

  // Fetch teams the user belongs to and pending invitations
  const fetchUserTeamsAndInvitations = async () => {
    try {
      setLoading(true);

      // Get teams where the user is a member
      const { data: teamMembers, error: memberError } = await supabase
        .from('team_members')
        .select('team_id, role, is_admin')
        .eq('user_id', userId);

      if (memberError) throw memberError;

      let userTeams = [];
      if (teamMembers && teamMembers.length > 0) {
        // Get team details
        const teamIds = teamMembers.map(member => member.team_id);
        const { data: teamData, error: teamError } = await supabase
          .from('teams')
          .select('*')
          .in('id', teamIds);

        if (teamError) throw teamError;

        // Combine team data with member role
        userTeams = teamData.map(team => {
          const membership = teamMembers.find(member => member.team_id === team.id);
          return {
            ...team,
            role: membership?.role || 'member',
            is_admin: membership?.is_admin || false
          };
        });
      }

      // Get pending team invitations
      const { data: invitationsData, error: invitationsError } = await supabase
        .from('team_invitations')
        .select(`
          id,
          team_id,
          role,
          status,
          created_at,
          teams:team_id(name)
        `)
        .eq('invited_user_id', userId)
        .eq('status', 'pending');

      if (invitationsError) throw invitationsError;

      setTeams(userTeams);
      setInvitations(invitationsData || []);
    } catch (error) {
      console.error('Error fetching teams and invitations:', error);
      toast.error('Failed to load teams');
    } finally {
      setLoading(false);
    }
  };

  // Handle creating a new team
  const handleCreateTeam = async (e) => {
    e.preventDefault();

    if (!newTeam.name.trim()) {
      toast.error('Team name is required');
      return;
    }

    try {
      setLoading(true);

      // Create the team
      const { data: teamData, error: teamError } = await supabase
        .from('teams')
        .insert([{
          name: newTeam.name,
          description: newTeam.description,
          created_by: userId
        }])
        .select()
        .single();

      if (teamError) throw teamError;

      // Add the creator as an admin member
      const { error: memberError } = await supabase
        .from('team_members')
        .insert([{
          team_id: teamData.id,
          user_id: userId,
          role: 'owner',
          is_admin: true
        }]);

      if (memberError) throw memberError;

      toast.success('Team created successfully');
      setNewTeam({ name: '', description: '' });
      setShowCreateForm(false);
      fetchUserTeamsAndInvitations();
    } catch (error) {
      console.error('Error creating team:', error);
      toast.error('Failed to create team');
    } finally {
      setLoading(false);
    }
  };

  // Handle accepting a team invitation
  const handleAcceptInvitation = async (invitationId, teamId, teamName) => {
    try {
      setLoading(true);

      // First, update the invitation status
      const { error: updateError } = await supabase
        .from('team_invitations')
        .update({ status: 'accepted' })
        .eq('id', invitationId);

      if (updateError) throw updateError;

      // Then, add the user as a team member
      const { error: memberError } = await supabase
        .from('team_members')
        .insert([{
          team_id: teamId,
          user_id: userId,
          role: 'member',
          is_admin: false
        }]);

      if (memberError) throw memberError;

      toast.success(`You have joined ${teamName}`);
      fetchUserTeamsAndInvitations();
    } catch (error) {
      console.error('Error accepting invitation:', error);
      toast.error('Failed to accept invitation');
    } finally {
      setLoading(false);
    }
  };

  // Handle declining a team invitation
  const handleDeclineInvitation = async (invitationId, teamName) => {
    try {
      setLoading(true);

      // Update the invitation status
      const { error: updateError } = await supabase
        .from('team_invitations')
        .update({ status: 'declined' })
        .eq('id', invitationId);

      if (updateError) throw updateError;

      toast.success(`You have declined the invitation to join ${teamName}`);
      fetchUserTeamsAndInvitations();
    } catch (error) {
      console.error('Error declining invitation:', error);
      toast.error('Failed to decline invitation');
    } finally {
      setLoading(false);
    }
  };

  // Navigate to team management page
  const goToTeamManagement = () => {
    navigate('/teams');
  };

  return (
    <div className="team-profile-section">
      {loading ? (
        <div className="loading-indicator">Loading teams...</div>
      ) : (
        <>
          {/* Teams List */}
          {teams.length > 0 && (
            <div className="teams-list">
              <h3 className="subsection-title">Your Teams</h3>
              <div className="teams-grid">
                {teams.slice(0, 3).map(team => (
                  <div key={team.id} className="team-card">
                    <div className="team-card-header">
                      <h4>{team.name}</h4>
                      {team.is_admin && <span className="admin-badge">Admin</span>}
                    </div>
                    <p className="team-description">{team.description || 'No description'}</p>
                    <Link to={`/teams/${team.id}`} className="view-team-link">
                      View Team <i className="bi bi-arrow-right"></i>
                    </Link>
                  </div>
                ))}
              </div>
              {teams.length > 3 && (
                <div className="view-all-link">
                  <Link to="/teams">View all teams ({teams.length})</Link>
                </div>
              )}
            </div>
          )}

          {/* Invitations List */}
          {invitations.length > 0 && (
            <div className="invitations-list">
              <h3 className="subsection-title">Pending Invitations</h3>
              {invitations.map(invitation => (
                <div key={invitation.id} className="invitation-card">
                  <div className="invitation-info">
                    <span className="invitation-team">{invitation.teams.name}</span>
                    <span className="invitation-role">Role: {invitation.role}</span>
                  </div>
                  <div className="invitation-actions">
                    <button 
                      className="accept-button"
                      onClick={() => handleAcceptInvitation(invitation.id, invitation.team_id, invitation.teams.name)}
                    >
                      Accept
                    </button>
                    <button 
                      className="decline-button"
                      onClick={() => handleDeclineInvitation(invitation.id, invitation.teams.name)}
                    >
                      Decline
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Quick Create Team Form */}
          {showCreateForm ? (
            <div className="create-team-form">
              <h3 className="subsection-title">Create New Team</h3>
              <form onSubmit={handleCreateTeam}>
                <div className="form-group">
                  <input
                    type="text"
                    value={newTeam.name}
                    onChange={(e) => setNewTeam({ ...newTeam, name: e.target.value })}
                    placeholder="Team Name"
                    className="form-control"
                    required
                  />
                </div>
                <div className="form-group">
                  <textarea
                    value={newTeam.description}
                    onChange={(e) => setNewTeam({ ...newTeam, description: e.target.value })}
                    placeholder="Description (optional)"
                    className="form-control"
                    rows="2"
                  />
                </div>
                <div className="form-actions">
                  <button type="submit" className="btn btn-primary">Create Team</button>
                  <button 
                    type="button" 
                    className="btn btn-secondary"
                    onClick={() => setShowCreateForm(false)}
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          ) : (
            <div className="team-actions">
              <button 
                className="btn btn-primary"
                onClick={() => setShowCreateForm(true)}
              >
                <i className="bi bi-plus-circle"></i> Create Team
              </button>
              <button 
                className="btn btn-outline-primary"
                onClick={goToTeamManagement}
              >
                <i className="bi bi-people"></i> Manage Teams
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default TeamProfileSection;
