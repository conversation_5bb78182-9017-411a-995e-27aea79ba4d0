import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Button, Input, Textarea, Card, CardBody } from '@heroui/react';

/**
 * VentureQuestionStep Component
 * 
 * Renders individual question steps in the immersive flow
 * Handles different question types: selection, details, team roles
 * Follows wireframe design with large touch targets and clear hierarchy
 */
const VentureQuestionStep = ({ 
  question, 
  answer, 
  onAnswer, 
  onNext, 
  isAnswered 
}) => {
  const [localValues, setLocalValues] = useState({
    ventureName: answer.ventureName || '',
    ventureDescription: answer.ventureDescription || '',
    ventureIcon: answer.ventureIcon || '🚀',
    ventureTags: answer.ventureTags || []
  });

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        staggerChildren: 0.1
      }
    },
    exit: {
      opacity: 0,
      y: -30,
      transition: { duration: 0.3 }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4 }
    }
  };

  // Handle option selection
  const handleOptionSelect = (value) => {
    onAnswer(question.id, value);
    // Auto-advance for selection questions
    setTimeout(() => {
      onNext();
    }, 300);
  };

  // Handle venture details form
  const handleDetailsChange = (field, value) => {
    const updated = { ...localValues, [field]: value };
    setLocalValues(updated);
    onAnswer(question.id, updated);
  };

  // Handle tag input
  const handleTagsChange = (value) => {
    const tags = value.split(/[#\s,]+/).filter(tag => tag.trim().length > 0);
    handleDetailsChange('ventureTags', tags);
  };

  // Render different question types
  const renderQuestionContent = () => {
    switch (question.type) {
      case 'category_selection':
      case 'subtype_selection':
      case 'audience_selection':
      case 'timeline_selection':
      case 'budget_selection':
      case 'success_selection':
        return renderSelectionOptions();
      
      case 'venture_details':
        return renderVentureDetails();
      
      case 'team_roles':
        return renderTeamRoles();
      
      default:
        return null;
    }
  };

  // Render selection options
  const renderSelectionOptions = () => (
    <div className="space-y-4 max-w-2xl mx-auto">
      {question.options.map((option, index) => (
        <motion.div
          key={option.value}
          variants={itemVariants}
          custom={index}
        >
          <Card
            isPressable
            onPress={() => handleOptionSelect(option.value)}
            className="bg-white/10 backdrop-blur-md border border-white/20 hover:bg-white/20 transition-colors cursor-pointer"
          >
            <CardBody className="p-6">
              <div className="flex items-center space-x-4">
                <div className="text-4xl">{option.icon}</div>
                <div className="flex-1 text-left">
                  <h3 className="text-xl font-semibold text-white mb-1">
                    {option.title}
                  </h3>
                  <p className="text-white/70">
                    {option.description}
                  </p>
                </div>
                <div className="text-white/40">
                  <i className="bi bi-chevron-right text-xl"></i>
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>
      ))}
    </div>
  );

  // Render venture details form
  const renderVentureDetails = () => (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Venture Name */}
      <motion.div variants={itemVariants}>
        <label className="block text-white text-lg font-medium mb-3">
          Venture Name
        </label>
        <Input
          value={localValues.ventureName}
          onChange={(e) => handleDetailsChange('ventureName', e.target.value)}
          placeholder="TaskMaster Pro"
          size="lg"
          variant="bordered"
          classNames={{
            input: "text-white placeholder:text-white/50 text-lg",
            inputWrapper: "bg-white/10 border-white/30 hover:border-white/50 focus-within:border-white/70 h-14"
          }}
        />
      </motion.div>

      {/* Suggestions */}
      <motion.div variants={itemVariants}>
        <div className="text-white/80 text-sm">
          💡 Suggestions based on your project:
          <div className="mt-2 space-x-2">
            {getSuggestions().map((suggestion, index) => (
              <Button
                key={index}
                size="sm"
                variant="bordered"
                className="text-white/70 border-white/30 hover:bg-white/10"
                onPress={() => handleDetailsChange('ventureName', suggestion)}
              >
                {suggestion}
              </Button>
            ))}
          </div>
        </div>
      </motion.div>

      {/* Description */}
      <motion.div variants={itemVariants}>
        <label className="block text-white text-lg font-medium mb-3">
          Short Description
        </label>
        <Textarea
          value={localValues.ventureDescription}
          onChange={(e) => handleDetailsChange('ventureDescription', e.target.value)}
          placeholder="A mobile app that helps teams manage tasks and track productivity with smart automation..."
          minRows={3}
          variant="bordered"
          classNames={{
            input: "text-white placeholder:text-white/50",
            inputWrapper: "bg-white/10 border-white/30 hover:border-white/50 focus-within:border-white/70"
          }}
        />
      </motion.div>

      {/* Icon Picker */}
      <motion.div variants={itemVariants}>
        <label className="block text-white text-lg font-medium mb-3">
          🎨 Choose an icon:
        </label>
        <div className="flex space-x-3">
          {question.fields.find(f => f.name === 'ventureIcon')?.options.map((icon) => (
            <Button
              key={icon}
              size="lg"
              variant={localValues.ventureIcon === icon ? "solid" : "bordered"}
              className={`text-2xl ${
                localValues.ventureIcon === icon 
                  ? "bg-white text-primary" 
                  : "text-white border-white/30 hover:bg-white/10"
              }`}
              onPress={() => handleDetailsChange('ventureIcon', icon)}
            >
              {icon}
            </Button>
          ))}
        </div>
      </motion.div>

      {/* Tags */}
      <motion.div variants={itemVariants}>
        <label className="block text-white text-lg font-medium mb-3">
          🏷️ Tags (help others find your project):
        </label>
        <Input
          value={localValues.ventureTags.map(tag => `#${tag}`).join(' ')}
          onChange={(e) => handleTagsChange(e.target.value)}
          placeholder="#mobile #productivity #automation #startup"
          size="lg"
          variant="bordered"
          classNames={{
            input: "text-white placeholder:text-white/50",
            inputWrapper: "bg-white/10 border-white/30 hover:border-white/50 focus-within:border-white/70 h-14"
          }}
        />
      </motion.div>

      {/* Continue Button */}
      <motion.div variants={itemVariants} className="pt-6">
        <Button
          size="lg"
          className="w-full bg-white text-primary font-semibold py-4 text-lg"
          onPress={onNext}
          isDisabled={!localValues.ventureName || !localValues.ventureDescription}
        >
          Continue
        </Button>
      </motion.div>
    </div>
  );

  // Render team roles assignment
  const renderTeamRoles = () => (
    <div className="max-w-2xl mx-auto">
      <motion.div variants={itemVariants} className="text-center mb-8">
        <p className="text-white/80 text-lg mb-6">
          {question.description}
        </p>
        
        <div className="bg-white/10 backdrop-blur-md rounded-lg p-6 border border-white/20 text-left">
          <div className="space-y-4 text-white">
            <div className="flex items-center space-x-3">
              <span className="text-2xl">👑</span>
              <div>
                <div className="font-semibold">Project Lead: You</div>
                <div className="text-sm text-white/70">Overall project management and final decision making</div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <span className="text-2xl">💻</span>
              <div>
                <div className="font-semibold">Lead Developer: [Assign to team member]</div>
                <div className="text-sm text-white/70">Technical architecture and code review</div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <span className="text-2xl">🎨</span>
              <div>
                <div className="font-semibold">UI/UX Designer: [Assign to team member]</div>
                <div className="text-sm text-white/70">User interface design and experience optimization</div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <span className="text-2xl">🧪</span>
              <div>
                <div className="font-semibold">QA Tester: [Assign to team member]</div>
                <div className="text-sm text-white/70">Quality assurance and bug testing</div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      <motion.div variants={itemVariants} className="flex space-x-4">
        <Button
          size="lg"
          variant="bordered"
          className="flex-1 text-white border-white/30 hover:bg-white/10"
          onPress={() => {
            onAnswer(question.id, { assignLater: true });
            onNext();
          }}
        >
          Assign Roles Later
        </Button>
        <Button
          size="lg"
          variant="bordered"
          className="flex-1 text-white border-white/30 hover:bg-white/10"
          onPress={() => {
            onAnswer(question.id, { customize: true });
            onNext();
          }}
        >
          Customize Roles
        </Button>
      </motion.div>
    </div>
  );

  // Get name suggestions based on previous answers
  const getSuggestions = () => {
    const suggestions = [];
    
    if (answer.projectCategory === 'software') {
      suggestions.push('My Awesome App', 'Team Productivity Tool', 'Smart Solution');
    } else if (answer.projectCategory === 'creative') {
      suggestions.push('Creative Studio Project', 'Art Collaboration', 'Design Workshop');
    } else if (answer.projectCategory === 'business') {
      suggestions.push('Business Solutions', 'Professional Services', 'Consulting Venture');
    }
    
    return suggestions.slice(0, 3);
  };

  return (
    <motion.div
      className="text-center"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
    >
      {/* Question Title */}
      <motion.h1 
        variants={itemVariants}
        className="text-4xl md:text-5xl font-bold text-white mb-12"
      >
        {question.title}
      </motion.h1>

      {/* Question Content */}
      {renderQuestionContent()}
    </motion.div>
  );
};

export default VentureQuestionStep;
