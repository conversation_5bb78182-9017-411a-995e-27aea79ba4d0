import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { useNavigate, useParams, Link } from 'react-router-dom';
import ValidationMetricsDashboard from '../../components/validation/ValidationMetricsDashboard';
import { Card, CardBody, CardHeader, Button } from '../../components/ui/heroui';

/**
 * ValidationMetricsPage Component
 *
 * Displays validation metrics for all projects or a specific project
 */
const ValidationMetricsPage = () => {
  const { currentUser } = useContext(UserContext);
  const navigate = useNavigate();
  const { projectId } = useParams();
  const [loading, setLoading] = useState(true);
  const [project, setProject] = useState(null);
  const [userProjects, setUserProjects] = useState([]);
  const [selectedProjectId, setSelectedProjectId] = useState(projectId || 'all');

  // Fetch user's projects
  useEffect(() => {
    const fetchUserProjects = async () => {
      if (!currentUser) return;

      try {
        // Get projects where user is a contributor
        const { data: contributorData, error: contributorError } = await supabase
          .from('project_contributors')
          .select(`
            project_id,
            role,
            projects(
              id,
              name,
              description,
              created_by
            )
          `)
          .eq('user_id', currentUser.id);

        if (contributorError) throw contributorError;

        // Extract projects from contributor data
        const projects = contributorData
          .filter(item => item.projects)
          .map(item => ({
            id: item.projects.id,
            name: item.projects.name,
            description: item.projects.description,
            role: item.role,
            isAdmin: item.role === 'admin' || item.projects.created_by === currentUser.id
          }));

        setUserProjects(projects);

        // If projectId is provided, check if it exists in user's projects
        if (projectId) {
          const projectExists = projects.some(p => p.id === projectId);
          if (!projectExists) {
            // Project not found or user doesn't have access
            navigate('/validation/metrics');
            return;
          }

          // Fetch project details
          const { data: projectData, error: projectError } = await supabase
            .from('projects')
            .select('*')
            .eq('id', projectId)
            .single();

          if (projectError) throw projectError;
          setProject(projectData);
        }
      } catch (error) {
        console.error('Error fetching projects:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUserProjects();
  }, [currentUser, projectId, navigate]);

  // Handle project selection change
  const handleProjectChange = (e) => {
    const newProjectId = e.target.value;
    setSelectedProjectId(newProjectId);

    if (newProjectId === 'all') {
      navigate('/validation/metrics');
    } else {
      navigate(`/validation/metrics/${newProjectId}`);
    }
  };

  if (!currentUser) {
    return (
      <div className="validation-metrics-page">
        <div className="auth-required">
          <h2>Authentication Required</h2>
          <p>Please log in to view validation metrics.</p>
          <button
            className="btn btn-primary"
            onClick={() => navigate('/login')}
          >
            Log In
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="validation-metrics-page">
      <div className="page-header">
        <div className="back-link">
          <Link to="/track">
            <i className="bi bi-arrow-left"></i> Back to Track Dashboard
          </Link>
        </div>
        <h1>Validation Metrics</h1>
        <div className="project-selector">
          <label htmlFor="project-select">Project:</label>
          <select
            id="project-select"
            value={selectedProjectId}
            onChange={handleProjectChange}
            disabled={loading}
          >
            <option value="all">All Projects</option>
            {userProjects.map(project => (
              <option key={project.id} value={project.id}>
                {project.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {project && (
        <div className="project-info">
          <h2>{project.name}</h2>
          {project.description && <p>{project.description}</p>}
        </div>
      )}

      <ValidationMetricsDashboard
        projectId={selectedProjectId === 'all' ? null : selectedProjectId}
      />
    </div>
  );
};

export default ValidationMetricsPage;
