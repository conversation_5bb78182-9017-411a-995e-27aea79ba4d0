// JavaScript Error Detection Test
// Day 2 - Finding the specific errors preventing <PERSON><PERSON> from loading

import { test, expect } from '@playwright/test';

const SITE_URL = 'https://royalty.technology';

test.describe('JavaScript Error Detection', () => {
  test('should capture all JavaScript errors on page load', async ({ page }) => {
    const errors = [];
    const warnings = [];
    const logs = [];
    
    // Capture all console messages
    page.on('console', msg => {
      const message = {
        type: msg.type(),
        text: msg.text(),
        location: msg.location()
      };
      
      if (msg.type() === 'error') {
        errors.push(message);
      } else if (msg.type() === 'warning') {
        warnings.push(message);
      } else {
        logs.push(message);
      }
    });
    
    // Capture page errors (uncaught exceptions)
    page.on('pageerror', error => {
      errors.push({
        type: 'pageerror',
        text: error.message,
        stack: error.stack
      });
    });
    
    // Navigate to the site
    console.log('🔍 Loading site and capturing errors...');
    await page.goto(SITE_URL);
    
    // Wait for page to fully load
    await page.waitForLoadState('networkidle');
    
    // Wait additional time for any async operations
    await page.waitForTimeout(5000);
    
    // Report all errors
    console.log('\n❌ ERRORS FOUND:', errors.length);
    errors.forEach((error, index) => {
      console.log(`\nERROR ${index + 1}:`);
      console.log(`Type: ${error.type}`);
      console.log(`Message: ${error.text}`);
      if (error.location) {
        console.log(`Location: ${error.location.url}:${error.location.lineNumber}:${error.location.columnNumber}`);
      }
      if (error.stack) {
        console.log(`Stack: ${error.stack.substring(0, 200)}...`);
      }
    });
    
    // Report warnings
    console.log('\n⚠️ WARNINGS FOUND:', warnings.length);
    warnings.forEach((warning, index) => {
      console.log(`WARNING ${index + 1}: ${warning.text}`);
    });
    
    // Check if React actually mounted
    const reactMounted = await page.evaluate(() => {
      // Check if React root has content
      const root = document.getElementById('root');
      return root && root.children.length > 0;
    });
    
    console.log('\n🎯 React mounted successfully:', reactMounted);
    
    // Check if any React components are rendered
    const hasReactComponents = await page.evaluate(() => {
      // Look for React-specific attributes
      return document.querySelector('[data-reactroot]') !== null ||
             document.querySelector('[data-react-helmet]') !== null ||
             document.querySelector('.react-component') !== null;
    });
    
    console.log('🔍 React components detected:', hasReactComponents);
    
    // Check for specific error patterns
    const criticalErrors = errors.filter(error => 
      error.text.includes('Cannot read') ||
      error.text.includes('undefined') ||
      error.text.includes('is not a function') ||
      error.text.includes('Module') ||
      error.text.includes('import') ||
      error.text.includes('export')
    );
    
    console.log('\n🚨 CRITICAL ERRORS:', criticalErrors.length);
    criticalErrors.forEach((error, index) => {
      console.log(`CRITICAL ${index + 1}: ${error.text}`);
    });
  });

  test('should test React initialization specifically', async ({ page }) => {
    // Navigate to site
    await page.goto(SITE_URL);
    await page.waitForLoadState('networkidle');
    
    // Check if React is available globally
    const reactAvailable = await page.evaluate(() => {
      return typeof window.React !== 'undefined';
    });
    
    console.log('React globally available:', reactAvailable);
    
    // Check if ReactDOM is available
    const reactDOMAvailable = await page.evaluate(() => {
      return typeof window.ReactDOM !== 'undefined';
    });
    
    console.log('ReactDOM globally available:', reactDOMAvailable);
    
    // Check if the root element has been populated
    const rootContent = await page.evaluate(() => {
      const root = document.getElementById('root');
      return {
        exists: !!root,
        hasChildren: root ? root.children.length : 0,
        innerHTML: root ? root.innerHTML.substring(0, 200) : 'NO ROOT'
      };
    });
    
    console.log('Root element status:', rootContent);
    
    // Try to manually check if React would work
    const manualReactTest = await page.evaluate(() => {
      try {
        // Try to create a simple React element
        if (window.React) {
          const element = window.React.createElement('div', null, 'Test');
          return { success: true, element: !!element };
        }
        return { success: false, reason: 'React not available' };
      } catch (error) {
        return { success: false, reason: error.message };
      }
    });
    
    console.log('Manual React test:', manualReactTest);
  });

  test('should check for module loading errors', async ({ page }) => {
    const networkErrors = [];
    
    // Capture failed network requests
    page.on('requestfailed', request => {
      networkErrors.push({
        url: request.url(),
        failure: request.failure()
      });
    });
    
    await page.goto(SITE_URL);
    await page.waitForLoadState('networkidle');
    
    console.log('Network errors:', networkErrors.length);
    networkErrors.forEach((error, index) => {
      console.log(`NETWORK ERROR ${index + 1}:`);
      console.log(`URL: ${error.url}`);
      console.log(`Failure: ${error.failure?.errorText}`);
    });
    
    // Check if all expected assets loaded
    const expectedAssets = [
      '/assets/main-CknBnnwR.js',
      '/assets/main-D3nc2AJO.css'
    ];
    
    for (const asset of expectedAssets) {
      const response = await page.request.get(`${SITE_URL}${asset}`);
      console.log(`Asset ${asset}: ${response.status()}`);
    }
  });

  test('should test alliance routes specifically for errors', async ({ page }) => {
    const routeErrors = [];
    
    page.on('pageerror', error => {
      routeErrors.push({
        message: error.message,
        stack: error.stack
      });
    });
    
    const routes = ['/teams', '/alliances', '/teams/test/manage', '/alliances/test/manage'];
    
    for (const route of routes) {
      console.log(`\n🔍 Testing route: ${route}`);
      
      await page.goto(`${SITE_URL}${route}`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      const routeSpecificErrors = routeErrors.filter(error => 
        !routeErrors.slice(0, routeErrors.indexOf(error)).includes(error)
      );
      
      console.log(`Errors on ${route}:`, routeSpecificErrors.length);
      routeSpecificErrors.forEach(error => {
        console.log(`ERROR: ${error.message}`);
      });
      
      // Check if page has any content
      const hasContent = await page.locator('body *').count();
      console.log(`Content elements on ${route}:`, hasContent);
    }
  });
});
