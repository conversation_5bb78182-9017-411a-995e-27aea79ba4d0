import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { supabase } from '../../../utils/supabase/supabase.utils';
import DatePicker from 'react-datepicker';
import { toast } from 'react-hot-toast';
import { logProjectActivity } from '../../utils/activity-logger';
import { Button } from '../ui/heroui';

const BulkContributionForm = ({ projectId, onSuccess, onCancel }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(false);
  const [project, setProject] = useState(null);
  const [milestones, setMilestones] = useState([]);
  const [taskTypes, setTaskTypes] = useState([]);
  const [categories, setCategories] = useState([]);
  const [difficultyLevels, setDifficultyLevels] = useState([1, 2, 3, 5, 8]);
  const [generatedContributions, setGeneratedContributions] = useState([]);
  const [showPreview, setShowPreview] = useState(false);

  const [formData, setFormData] = useState({
    task_name: '',
    task_type: '',
    category: '',
    difficulty: 3,
    hours_per_occurrence: 1,
    description: '',
    milestone_id: '',
    frequency: 'weekly',
    start_date: new Date(),
    end_date: new Date(new Date().setDate(new Date().getDate() + 28)) // Default to 4 weeks from now
  });

  // Fetch project data, milestones, and contribution tracking config
  useEffect(() => {
    const fetchProjectData = async () => {
      if (!projectId) return;

      try {
        setLoading(true);

        // Fetch project data
        let projectData;
        let configData;

        try {
          // Try to fetch project with contribution_tracking_config
          const { data, error } = await supabase
            .from('projects')
            .select('*, contribution_tracking_config(*)')
            .eq('id', projectId)
            .single();

          if (error) throw error;
          projectData = data;
          configData = data.contribution_tracking_config;
        } catch (e) {
          console.log('Error fetching project with config:', e);

          // Fallback: fetch just the project
          const { data, error } = await supabase
            .from('projects')
            .select('*')
            .eq('id', projectId)
            .single();

          if (error) throw error;
          projectData = data;

          // Fetch config separately
          try {
            const { data: configResult, error: configError } = await supabase
              .from('contribution_tracking_config')
              .select('*')
              .eq('project_id', projectId)
              .single();

            if (!configError) {
              configData = configResult;
            }
          } catch (configErr) {
            console.log('Error fetching config separately:', configErr);
          }
        }

        setProject(projectData);

        // Use default config if none exists
        const defaultConfig = {
          task_types: [
            {name: "Development", value: "development"},
            {name: "Design", value: "design"},
            {name: "Documentation", value: "documentation"},
            {name: "Testing", value: "testing"},
            {name: "Research", value: "research"},
            {name: "Management", value: "management"},
            {name: "Other", value: "other"}
          ],
          categories: [
            {name: "Frontend", value: "frontend"},
            {name: "Backend", value: "backend"},
            {name: "Database", value: "database"},
            {name: "UI/UX", value: "ui-ux"},
            {name: "DevOps", value: "devops"},
            {name: "QA", value: "qa"},
            {name: "Content", value: "content"},
            {name: "Other", value: "other"}
          ],
          difficulty_levels: [1, 2, 3, 5, 8]
        };

        // Process task types
        let processedTaskTypes = defaultConfig.task_types;
        if (configData && configData.task_types && Array.isArray(configData.task_types)) {
          if (configData.task_types.length > 0) {
            // Check if task_types is an array of objects or strings
            if (typeof configData.task_types[0] === 'object') {
              processedTaskTypes = configData.task_types;
            } else if (typeof configData.task_types[0] === 'string') {
              // Convert strings to objects
              processedTaskTypes = configData.task_types.map(type => ({ name: type, value: type }));
            }
          }
        }
        setTaskTypes(processedTaskTypes);

        // Process categories
        let processedCategories = defaultConfig.categories;
        if (configData && configData.categories && Array.isArray(configData.categories)) {
          if (configData.categories.length > 0) {
            // Check if categories is an array of objects or strings
            if (typeof configData.categories[0] === 'object') {
              processedCategories = configData.categories;
            } else if (typeof configData.categories[0] === 'string') {
              // Convert strings to objects
              processedCategories = configData.categories.map(cat => ({ name: cat, value: cat }));
            }
          }
        }
        setCategories(processedCategories);

        // Process difficulty levels
        let processedDifficultyLevels = defaultConfig.difficulty_levels;
        if (configData) {
          // Check for difficulty_scale first, then difficulty_levels
          const difficultyData = configData.difficulty_scale || configData.difficulty_levels;
          if (difficultyData && Array.isArray(difficultyData)) {
            if (difficultyData.length > 0) {
              // Check if it's an array of numbers or objects
              if (typeof difficultyData[0] === 'number') {
                processedDifficultyLevels = difficultyData;
              } else if (typeof difficultyData[0] === 'object' && difficultyData[0].multiplier) {
                // Extract multipliers from objects
                processedDifficultyLevels = difficultyData.map(level => level.multiplier);
              }
            }
          }
        }
        setDifficultyLevels(processedDifficultyLevels);

        // Fetch milestones
        const { data: milestonesData, error: milestonesError } = await supabase
          .from('milestones')
          .select('id, name, status')
          .eq('project_id', projectId)
          .order('deadline', { ascending: true });

        if (milestonesError) throw milestonesError;

        setMilestones(milestonesData || []);

      } catch (error) {
        console.error('Error fetching project data:', error);
        toast.error('Failed to load project data');
      } finally {
        setLoading(false);
      }
    };

    fetchProjectData();
  }, [projectId]);

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle start date change
  const handleStartDateChange = (date) => {
    setFormData(prev => ({
      ...prev,
      start_date: date
    }));
  };

  // Handle end date change
  const handleEndDateChange = (date) => {
    setFormData(prev => ({
      ...prev,
      end_date: date
    }));
  };

  // Handle task type selection
  const handleTaskTypeChange = (e) => {
    const selectedTaskTypeValue = e.target.value;

    // Find the selected task type and get its default difficulty
    const taskType = taskTypes.find(type =>
      (type.value || type.name) === selectedTaskTypeValue
    );

    setFormData(prev => ({
      ...prev,
      task_type: selectedTaskTypeValue,
      // Update difficulty if task type has a default difficulty
      ...(taskType && taskType.difficulty && { difficulty: taskType.difficulty })
    }));
  };

  // Generate contributions based on frequency and date range
  const generateContributions = () => {
    const {
      task_name,
      task_type,
      category,
      difficulty,
      hours_per_occurrence,
      description,
      milestone_id,
      frequency,
      start_date,
      end_date
    } = formData;

    // Validate required fields
    if (!task_name || !task_type || !category || !hours_per_occurrence || !start_date || !end_date) {
      toast.error('Please fill in all required fields');
      return;
    }

    // Validate date range
    if (new Date(end_date) <= new Date(start_date)) {
      toast.error('End date must be after start date');
      return;
    }

    const contributions = [];
    const startDate = new Date(start_date);
    const endDate = new Date(end_date);
    let currentDate = new Date(startDate);

    // Calculate interval based on frequency
    let dayInterval = 1;
    switch (frequency) {
      case 'daily':
        dayInterval = 1;
        break;
      case 'weekly':
        dayInterval = 7;
        break;
      case 'biweekly':
        dayInterval = 14;
        break;
      case 'monthly':
        dayInterval = 30;
        break;
      default:
        dayInterval = 7; // Default to weekly
    }

    // Generate contributions for each date in the range
    while (currentDate <= endDate) {
      // Create a new contribution
      const contribution = {
        task_name,
        task_type,
        category,
        difficulty: parseInt(difficulty),
        hours_spent: parseFloat(hours_per_occurrence),
        description,
        milestone_id: milestone_id || null,
        date_performed: new Date(currentDate),
        status: 'pending'
      };

      contributions.push(contribution);

      // Advance to next date
      currentDate = new Date(currentDate);
      currentDate.setDate(currentDate.getDate() + dayInterval);
    }

    setGeneratedContributions(contributions);
    setShowPreview(true);
  };

  // Submit all generated contributions
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (generatedContributions.length === 0) {
      generateContributions();
      return;
    }

    try {
      setLoading(true);

      // Prepare contributions for insertion
      const contributionsToInsert = generatedContributions.map(contribution => ({
        ...contribution,
        project_id: projectId,
        user_id: currentUser.id
      }));

      // Insert all contributions
      const { data, error } = await supabase
        .from('contributions')
        .insert(contributionsToInsert)
        .select();

      if (error) throw error;

      toast.success(`Successfully added ${data.length} contributions`);

      // Log activity for bulk contributions
      await logProjectActivity(
        projectId,
        currentUser.id,
        'bulk_contributions_added',
        {
          count: data.length,
          task: formData.task_name,
          total_hours: generatedContributions.reduce((sum, contrib) => sum + contrib.hours_spent, 0),
          start_date: formData.start_date,
          end_date: formData.end_date
        }
      );

      // Reset form
      setFormData({
        task_name: '',
        task_type: '',
        category: '',
        difficulty: 3,
        hours_per_occurrence: 1,
        description: '',
        milestone_id: '',
        frequency: 'weekly',
        start_date: new Date(),
        end_date: new Date(new Date().setDate(new Date().getDate() + 28))
      });

      setGeneratedContributions([]);
      setShowPreview(false);

      // Call success callback
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error submitting contributions:', error);
      toast.error('Failed to submit contributions');
    } finally {
      setLoading(false);
    }
  };

  // Format date for display
  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading && !project) {
    return <div className="loading-spinner">Loading...</div>;
  }

  return (
    <div className="contribution-entry-form">
      <form onSubmit={handleSubmit}>
        <h4 className="mb-4">Bulk Contribution Entry</h4>
        <p className="text-muted mb-4">
          Use this form to add multiple contributions for recurring tasks.
          Enter the task details, how often it was performed, and for how long.
        </p>

        <div className="form-grid">
          {/* Task Name */}
          <div className="form-group">
            <label htmlFor="task_name">Task Name*</label>
            <input
              type="text"
              id="task_name"
              name="task_name"
              value={formData.task_name}
              onChange={handleInputChange}
              className="form-control"
              placeholder="Brief name of the recurring task"
              required
            />
          </div>

          {/* Task Type */}
          <div className="form-group">
            <label htmlFor="task_type">Task Type*</label>
            <select
              id="task_type"
              name="task_type"
              value={formData.task_type}
              onChange={handleTaskTypeChange}
              className="form-control"
              required
            >
              <option value="">Select Task Type</option>
              {taskTypes.map((type, index) => (
                <option key={index} value={type.value || type.name}>
                  {type.name} {type.difficulty ? `(Difficulty: ${type.difficulty})` : ''}
                </option>
              ))}
            </select>
          </div>

          {/* Category */}
          <div className="form-group">
            <label htmlFor="category">Category*</label>
            <select
              id="category"
              name="category"
              value={formData.category}
              onChange={handleInputChange}
              className="form-control"
              required
            >
              <option value="">Select Category</option>
              {categories.map((category, index) => (
                <option key={index} value={typeof category === 'object' ? (category.value || category.name) : category}>
                  {typeof category === 'object' ? category.name : category}
                </option>
              ))}
            </select>
          </div>

          {/* Difficulty */}
          <div className="form-group">
            <label htmlFor="difficulty">Difficulty*</label>
            <select
              id="difficulty"
              name="difficulty"
              value={formData.difficulty}
              onChange={handleInputChange}
              className="form-control"
              required
            >
              {difficultyLevels.map((level) => (
                <option key={level} value={level}>
                  {level}
                </option>
              ))}
            </select>
          </div>

          {/* Hours Per Occurrence */}
          <div className="form-group">
            <label htmlFor="hours_per_occurrence">Hours Per Occurrence*</label>
            <input
              type="number"
              id="hours_per_occurrence"
              name="hours_per_occurrence"
              value={formData.hours_per_occurrence}
              onChange={handleInputChange}
              className="form-control"
              min="0.1"
              step="0.1"
              required
            />
            <small className="form-text text-muted">Average time spent each time this task was performed</small>
          </div>

          {/* Frequency */}
          <div className="form-group">
            <label htmlFor="frequency">Frequency*</label>
            <select
              id="frequency"
              name="frequency"
              value={formData.frequency}
              onChange={handleInputChange}
              className="form-control"
              required
            >
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="biweekly">Bi-weekly</option>
              <option value="monthly">Monthly</option>
            </select>
          </div>

          {/* Start Date */}
          <div className="form-group">
            <label htmlFor="start_date">Start Date*</label>
            <DatePicker
              id="start_date"
              selected={formData.start_date}
              onChange={handleStartDateChange}
              className="form-control"
              dateFormat="MMMM d, yyyy"
              required
            />
          </div>

          {/* End Date */}
          <div className="form-group">
            <label htmlFor="end_date">End Date*</label>
            <DatePicker
              id="end_date"
              selected={formData.end_date}
              onChange={handleEndDateChange}
              className="form-control"
              dateFormat="MMMM d, yyyy"
              required
              minDate={formData.start_date}
            />
            <small className="form-text text-muted">
              Last date for which to generate contributions
            </small>
          </div>

          {/* Milestone */}
          <div className="form-group">
            <label htmlFor="milestone_id">Milestone</label>
            <select
              id="milestone_id"
              name="milestone_id"
              value={formData.milestone_id}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="">Select Milestone (Optional)</option>
              {milestones.map((milestone) => (
                <option key={milestone.id} value={milestone.id}>
                  {milestone.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Description */}
        <div className="form-group">
          <label htmlFor="description">Description</label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            className="form-control"
            rows="3"
            placeholder="Additional details about these recurring contributions"
          />
        </div>

        {/* Preview Section */}
        {showPreview && generatedContributions.length > 0 && (
          <div className="contribution-preview mt-4">
            <h5>Preview ({generatedContributions.length} contributions)</h5>
            <div className="table-responsive">
              <table className="table table-sm">
                <thead>
                  <tr>
                    <th>#</th>
                    <th>Date</th>
                    <th>Task</th>
                    <th>Hours</th>
                  </tr>
                </thead>
                <tbody>
                  {generatedContributions.map((contribution, index) => (
                    <tr key={index}>
                      <td>{index + 1}</td>
                      <td>{formatDate(contribution.date_performed)}</td>
                      <td>{contribution.task_name}</td>
                      <td>{contribution.hours_spent}</td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr>
                    <td colSpan="3" className="text-end"><strong>Total Hours:</strong></td>
                    <td>
                      <strong>
                        {generatedContributions.reduce((sum, c) => sum + parseFloat(c.hours_spent), 0).toFixed(1)}
                      </strong>
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        )}

        <div className="flex justify-end gap-4 mt-6">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
          >
            Cancel
          </Button>
          {!showPreview ? (
            <Button
              type="button"
              onClick={generateContributions}
              disabled={loading}
            >
              {loading ? 'Generating...' : 'Generate Contributions'}
            </Button>
          ) : (
            <Button
              type="submit"
              disabled={loading}
            >
              {loading ? 'Submitting...' : 'Submit All Contributions'}
            </Button>
          )}
        </div>
      </form>
    </div>
  );
};

export default BulkContributionForm;
