// Comprehensive Navigation and Alliance Testing
// Day 2-3 - Testing navigation flow and alliance components with proper auth

import { test, expect, bypassAuth, testNavigationFlow, createTestAlliance } from './auth-setup.js';

const SITE_URL = 'https://royalty.technology';

test.describe('Comprehensive Navigation and Alliance Testing', () => {
  test('should test full navigation flow with authentication', async ({ authenticatedPage: page }) => {
    console.log('🧭 Starting comprehensive navigation test...');
    
    // Test navigation flow
    const navigationWorked = await testNavigationFlow(page);
    console.log('Navigation to teams worked:', navigationWorked);
    
    // Ensure we're on teams page
    await page.goto(`${SITE_URL}/teams`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Analyze what we actually see
    const pageContent = await page.textContent('body');
    console.log('Teams page content length:', pageContent.length);
    console.log('Teams page preview:', pageContent.substring(0, 300));
    
    // Check for team-related content
    const hasTeamContent = pageContent.toLowerCase().includes('team');
    console.log('Has team content:', hasTeamContent);
    
    // Look for interactive elements
    const buttons = await page.locator('button').count();
    const links = await page.locator('a').count();
    console.log('Interactive elements - Buttons:', buttons, 'Links:', links);
    
    // Check for create/manage functionality
    const createButton = page.locator('text=Create').first();
    const manageButton = page.locator('text=Manage').first();
    
    const hasCreate = await createButton.isVisible();
    const hasManage = await manageButton.isVisible();
    
    console.log('Has create button:', hasCreate);
    console.log('Has manage button:', hasManage);
    
    // If we have teams, try to access management
    if (hasManage) {
      console.log('🔧 Testing alliance management access...');
      await manageButton.click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);
      
      const manageContent = await page.textContent('body');
      console.log('Management page content length:', manageContent.length);
      
      // Look for our alliance management components
      const allianceIndicators = [
        'Alliance Information',
        'Business Entity',
        'Alliance Permissions',
        'Manage Alliance'
      ];
      
      for (const indicator of allianceIndicators) {
        const found = manageContent.includes(indicator);
        console.log(`"${indicator}": ${found}`);
      }
    }
  });

  test('should test alliance creation and business entity registration', async ({ authenticatedPage: page }) => {
    console.log('🏰 Testing alliance creation flow...');
    
    // Try to create a test alliance
    const allianceCreated = await createTestAlliance(page);
    console.log('Alliance creation attempted:', allianceCreated);
    
    if (allianceCreated) {
      // Navigate to alliance management
      await page.goto(`${SITE_URL}/teams`);
      await page.waitForLoadState('networkidle');
      
      // Look for our created alliance
      const allianceLink = page.locator('text=Test Alliance').first();
      if (await allianceLink.isVisible()) {
        await allianceLink.click();
        await page.waitForLoadState('networkidle');
        
        // Try to access management
        const manageButton = page.locator('text=Manage').first();
        if (await manageButton.isVisible()) {
          await manageButton.click();
          await page.waitForLoadState('networkidle');
          await page.waitForTimeout(3000);
          
          // Test business entity registration
          const registerButton = page.locator('text=Register Business').first();
          if (await registerButton.isVisible()) {
            console.log('✅ Business entity registration available');
            
            await registerButton.click();
            await page.waitForLoadState('networkidle');
            
            // Check if registration form loads
            const formContent = await page.textContent('body');
            const hasRegistrationForm = formContent.includes('Legal Name') || 
                                      formContent.includes('Tax ID') ||
                                      formContent.includes('Business Type');
            
            console.log('Business registration form loaded:', hasRegistrationForm);
          }
        }
      }
    }
  });

  test('should test API endpoints with authentication', async ({ authenticatedPage: page }) => {
    console.log('🔌 Testing API endpoints...');
    
    // Get authentication token from page context
    const authToken = await page.evaluate(() => {
      const token = localStorage.getItem('supabase.auth.token') || 
                   localStorage.getItem('sb-hqqlrrqvjcetoxbdjgzx-auth-token');
      return token ? JSON.parse(token).access_token : null;
    });
    
    console.log('Auth token available:', !!authToken);
    
    if (authToken) {
      // Test companies API with auth
      const companiesResponse = await page.request.get(`${SITE_URL}/.netlify/functions/companies`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      console.log('Companies API status:', companiesResponse.status());
      
      if (companiesResponse.status() === 200) {
        const companiesData = await companiesResponse.json();
        console.log('Companies data:', companiesData);
      }
      
      // Test financial transactions API
      const transactionsResponse = await page.request.get(`${SITE_URL}/.netlify/functions/financial-transactions`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      console.log('Financial transactions API status:', transactionsResponse.status());
      
      if (transactionsResponse.status() === 200) {
        const transactionsData = await transactionsResponse.json();
        console.log('Transactions data:', transactionsData);
      }
    }
  });

  test('should test experimental navigation comprehensively', async ({ authenticatedPage: page }) => {
    console.log('🎮 Testing experimental navigation system...');
    
    await page.goto(SITE_URL);
    await page.waitForLoadState('networkidle');
    
    // Check for canvas and navigation elements
    const canvas = page.locator('canvas').first();
    const hasCanvas = await canvas.isVisible();
    
    console.log('Experimental navigation canvas present:', hasCanvas);
    
    if (hasCanvas) {
      // Get canvas dimensions and position
      const canvasBox = await canvas.boundingBox();
      console.log('Canvas dimensions:', canvasBox);
      
      // Test mouse interactions
      console.log('Testing mouse interactions...');
      await canvas.click({ position: { x: canvasBox.width / 2, y: canvasBox.height / 2 } });
      await page.waitForTimeout(1000);
      
      // Test keyboard navigation
      console.log('Testing keyboard navigation...');
      
      // Test all arrow keys
      const keys = ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'];
      for (const key of keys) {
        await page.keyboard.press(key);
        await page.waitForTimeout(500);
        
        // Check if view changed
        const currentView = await page.evaluate(() => {
          return window.location.hash || window.location.pathname;
        });
        console.log(`After ${key}:`, currentView);
      }
      
      // Test zoom levels
      console.log('Testing zoom levels...');
      
      // Go to grid view (multiple down arrows)
      for (let i = 0; i < 3; i++) {
        await page.keyboard.press('ArrowDown');
        await page.waitForTimeout(500);
      }
      
      // Look for cards in grid view
      const cards = await page.locator('[class*="card"], [class*="grid"], [class*="item"]').count();
      console.log('Cards/items in grid view:', cards);
      
      // Look specifically for teams card
      const teamsCard = page.locator('text=Teams').first();
      const teamsVisible = await teamsCard.isVisible();
      console.log('Teams card visible in grid:', teamsVisible);
      
      if (teamsVisible) {
        console.log('Testing teams card interaction...');
        await teamsCard.click();
        await page.waitForLoadState('networkidle');
        
        const finalUrl = page.url();
        console.log('Final navigation URL:', finalUrl);
        
        // Verify we reached teams page
        const isTeamsPage = finalUrl.includes('/teams');
        console.log('Successfully navigated to teams page:', isTeamsPage);
        
        if (isTeamsPage) {
          // Test the teams page content
          const teamsContent = await page.textContent('body');
          console.log('Teams page content after navigation:', teamsContent.length);
          
          // Look for alliance management options
          const hasAllianceOptions = teamsContent.includes('Alliance') || 
                                   teamsContent.includes('Manage') ||
                                   teamsContent.includes('Create');
          console.log('Has alliance management options:', hasAllianceOptions);
        }
      }
    } else {
      console.log('❌ Experimental navigation not active - testing fallback navigation');
      
      // Test regular navigation links
      const navLinks = await page.locator('nav a, .nav a, [role="navigation"] a').count();
      console.log('Regular navigation links found:', navLinks);
      
      // Look for teams link
      const teamsLink = page.locator('a[href*="teams"]').first();
      if (await teamsLink.isVisible()) {
        await teamsLink.click();
        await page.waitForLoadState('networkidle');
        console.log('Navigated via regular link to:', page.url());
      }
    }
  });
});
