// Simple test function to verify Netlify Functions are working
exports.handler = async function(event, context) {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
  };

  // Handle OPTIONS request (preflight)
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 204,
      headers,
      body: ''
    };
  }

  // Test Supabase connection if available
  let supabaseStatus = 'Not tested';
  if (process.env.SUPABASE_URL && process.env.SUPABASE_SERVICE_KEY) {
    try {
      const { createClient } = require('@supabase/supabase-js');
      const supabase = createClient(
        process.env.SUPABASE_URL,
        process.env.SUPABASE_SERVICE_KEY
      );

      // Try a simple query
      const { data, error } = await supabase.from('roadmap').select('id').limit(1);

      if (error) {
        supabaseStatus = `Error: ${error.message}`;
      } else {
        supabaseStatus = 'Connected successfully';
      }
    } catch (error) {
      supabaseStatus = `Exception: ${error.message}`;
    }
  } else {
    supabaseStatus = 'Missing environment variables';
  }

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({
      message: "Netlify Functions are working!",
      timestamp: new Date().toISOString(),
      path: event.path,
      method: event.httpMethod,
      supabaseStatus
    })
  };
};
