// Alliance Management API
// Backend Specialist: Core alliance CRUD operations
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  // Extract user ID from JWT token (simplified)
  // In production, properly verify JWT
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Get all alliances for a user
const getAlliances = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    // Get alliances where user is a member
    const { data: userAlliances, error } = await supabase
      .from('teams')
      .select(`
        *,
        team_members!inner(
          id,
          user_id,
          role,
          status,
          joined_at
        )
      `)
      .eq('team_members.user_id', userId)
      .eq('team_members.status', 'active');

    if (error) throw error;

    // Transform teams to alliances format
    const alliances = userAlliances.map(team => ({
      id: team.id,
      name: team.name,
      description: team.description,
      alliance_type: team.alliance_type || 'emerging',
      industry: team.industry || null,
      is_business_entity: team.is_business_entity || false,
      company_id: team.company_id,
      logo_url: team.logo_url,
      created_at: team.created_at,
      updated_at: team.updated_at,
      created_by: team.created_by,
      user_role: team.team_members[0]?.role || 'member',
      member_since: team.team_members[0]?.joined_at
    }));

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ alliances })
    };

  } catch (error) {
    console.error('Get alliances error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch alliances' })
    };
  }
};

// Get single alliance details
const getAlliance = async (event) => {
  try {
    const allianceId = event.path.split('/').pop();
    const userId = getUserFromRequest(event);
    
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    // Get alliance with member details
    const { data: alliance, error: allianceError } = await supabase
      .from('teams')
      .select(`
        *,
        team_members(
          id,
          user_id,
          role,
          status,
          joined_at,
          users(
            id,
            display_name,
            email,
            avatar_url
          )
        )
      `)
      .eq('id', allianceId)
      .single();

    if (allianceError) throw allianceError;

    // Check if user is a member
    const userMembership = alliance.team_members.find(m => m.user_id === userId);
    if (!userMembership) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Access denied' })
      };
    }

    // Get alliance ventures (projects)
    const { data: ventures, error: venturesError } = await supabase
      .from('projects')
      .select('*')
      .eq('team_id', allianceId)
      .eq('is_active', true);

    if (venturesError) console.warn('Ventures fetch error:', venturesError);

    // Transform response
    const response = {
      id: alliance.id,
      name: alliance.name,
      description: alliance.description,
      alliance_type: alliance.alliance_type || 'emerging',
      industry: alliance.industry || null,
      is_business_entity: alliance.is_business_entity || false,
      company_id: alliance.company_id,
      logo_url: alliance.logo_url,
      created_at: alliance.created_at,
      updated_at: alliance.updated_at,
      created_by: alliance.created_by,
      members: alliance.team_members.map(member => ({
        id: member.id,
        user_id: member.user_id,
        role: member.role || 'member',
        status: member.status || 'active',
        joined_at: member.joined_at,
        user: member.users
      })),
      ventures: ventures || [],
      user_role: userMembership.role || 'member'
    };

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(response)
    };

  } catch (error) {
    console.error('Get alliance error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch alliance' })
    };
  }
};

// Create new alliance
const createAlliance = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const data = JSON.parse(event.body);
    
    // Validate required fields
    if (!data.name || !data.description) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Name and description are required' })
      };
    }

    // Create alliance (team)
    const allianceData = {
      name: data.name,
      description: data.description,
      alliance_type: data.alliance_type || 'emerging',
      is_business_entity: data.is_business_entity || false,
      company_id: data.company_id || null,
      created_by: userId
    };

    const { data: alliance, error: allianceError } = await supabase
      .from('teams')
      .insert([allianceData])
      .select()
      .single();

    if (allianceError) throw allianceError;

    // Add creator as founder
    const memberData = {
      team_id: alliance.id,
      user_id: userId,
      role: 'founder',
      status: 'active',
      joined_at: new Date().toISOString()
    };

    const { error: memberError } = await supabase
      .from('team_members')
      .insert([memberData]);

    if (memberError) {
      // Rollback alliance creation
      await supabase.from('teams').delete().eq('id', alliance.id);
      throw memberError;
    }

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        alliance: {
          id: alliance.id,
          name: alliance.name,
          description: alliance.description,
          alliance_type: alliance.alliance_type,
          created_at: alliance.created_at,
          user_role: 'founder'
        }
      })
    };

  } catch (error) {
    console.error('Create alliance error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to create alliance' })
    };
  }
};

// Update alliance
const updateAlliance = async (event) => {
  try {
    const allianceId = event.path.split('/').pop();
    const userId = getUserFromRequest(event);
    
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    // Check if user has admin permissions
    const { data: membership, error: memberError } = await supabase
      .from('team_members')
      .select('role')
      .eq('team_id', allianceId)
      .eq('user_id', userId)
      .single();

    if (memberError || !membership || !['founder', 'owner', 'admin'].includes(membership.role)) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Insufficient permissions' })
      };
    }

    const data = JSON.parse(event.body);
    
    // Update alliance
    const updateData = {};
    if (data.name) updateData.name = data.name;
    if (data.description) updateData.description = data.description;
    if (data.alliance_type) updateData.alliance_type = data.alliance_type;
    if (data.is_business_entity !== undefined) updateData.is_business_entity = data.is_business_entity;
    if (data.company_id !== undefined) updateData.company_id = data.company_id;
    
    updateData.updated_at = new Date().toISOString();

    const { data: alliance, error: updateError } = await supabase
      .from('teams')
      .update(updateData)
      .eq('id', allianceId)
      .select()
      .single();

    if (updateError) throw updateError;

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ alliance })
    };

  } catch (error) {
    console.error('Update alliance error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to update alliance' })
    };
  }
};

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/alliances', '');

  try {
    let response;

    if (event.httpMethod === 'GET') {
      if (path === '' || path === '/') {
        response = await getAlliances(event);
      } else {
        response = await getAlliance(event);
      }
    } else if (event.httpMethod === 'POST') {
      response = await createAlliance(event);
    } else if (event.httpMethod === 'PUT') {
      response = await updateAlliance(event);
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('Alliance API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
