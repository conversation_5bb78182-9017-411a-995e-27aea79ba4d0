// Test Payment APIs with valid authentication tokens
const fetch = require('node-fetch');

const SITE_URL = 'https://royalty.technology';

// Valid test tokens from the user creation
const TEST_TOKENS = {
  admin: 'eyJhbGciOiJIUzI1NiIsImtpZCI6IjF1bjdLRUJ1NU9lVEgwWUwiLCJ0eXAiOiJKV1QifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.nu1g3DOQW331jqw-6ki28tJe7yJTYtoH4IVJ3kycgZs',
  vrc: 'eyJhbGciOiJIUzI1NiIsImtpZCI6IjF1bjdLRUJ1NU9lVEgwWUwiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.H07-KcHX5bzlugu5XXWfOws08OysH0j0nlKmfK70ICg',
  sales: 'eyJhbGciOiJIUzI1NiIsImtpZCI6IjF1bjdLRUJ1NU9lVEgwWUwiLCJ0eXAiOiJKV1QifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.TdMxhJ2xYdkEdqQ8PaRmQ0G2Iz7IUrlo2-v-ugxM_jI'
};

const VRC_COMPANY_ID = '2bdd6331-37cd-41ed-886a-b48cb47f9409';

async function testPaymentAPIsWithAuth() {
  console.log('💰 Testing Payment APIs with valid authentication...\n');
  
  const endpoints = [
    '/.netlify/functions/companies',
    '/.netlify/functions/financial-transactions',
    '/.netlify/functions/commission-payments',
    '/.netlify/functions/recurring-fees'
  ];
  
  // Test with admin token
  console.log('🔑 Testing with Admin Token...\n');
  
  for (const endpoint of endpoints) {
    try {
      console.log(`Testing ${endpoint}...`);
      
      const response = await fetch(`${SITE_URL}${endpoint}`, {
        headers: {
          'Authorization': `Bearer ${TEST_TOKENS.admin}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log(`Status: ${response.status}`);
      
      if (response.ok) {
        try {
          const data = await response.json();
          console.log(`✅ Success:`, data);
        } catch (e) {
          const text = await response.text();
          console.log(`✅ Success (text):`, text.substring(0, 200));
        }
      } else {
        try {
          const errorData = await response.json();
          console.log(`⚠️ Error:`, errorData);
        } catch (e) {
          const errorText = await response.text();
          console.log(`⚠️ Error (text):`, errorText.substring(0, 200));
        }
      }
      
      console.log('---\n');
      
    } catch (error) {
      console.log(`❌ Network Error: ${error.message}\n`);
    }
  }
  
  // Test creating a commission payment
  console.log('💼 Testing Commission Payment Creation...\n');
  
  const commissionData = {
    company_id: VRC_COMPANY_ID,
    sales_rep_id: 'ab69ee38-02a0-4744-bbe2-4f6d0bc40e61', // admin user ID
    sales_amount: 10000.00,
    commission_rate: 15.0,
    product_or_service: 'Film Production Services',
    sale_date: '2025-01-15',
    client_reference: 'CLIENT-001',
    payment_terms: 'net_30'
  };
  
  try {
    const response = await fetch(`${SITE_URL}/.netlify/functions/commission-payments`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${TEST_TOKENS.admin}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(commissionData)
    });
    
    console.log(`Commission creation status: ${response.status}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log(`✅ Commission created successfully:`, result);
    } else {
      const error = await response.json();
      console.log(`⚠️ Commission creation failed:`, error);
    }
    
  } catch (error) {
    console.log(`❌ Commission creation error: ${error.message}`);
  }
  
  console.log('\n---\n');
  
  // Test creating a recurring fee
  console.log('🔄 Testing Recurring Fee Creation...\n');
  
  const recurringFeeData = {
    company_id: VRC_COMPANY_ID,
    payee_user_id: '243623f2-b20a-44e0-bd0f-cc0fd76b6482', // sales rep user ID
    fee_type: 'talent_fee',
    amount: 2500.00,
    frequency: 'monthly',
    start_date: '2025-02-01',
    description: 'Monthly talent retainer fee for lead actor'
  };
  
  try {
    const response = await fetch(`${SITE_URL}/.netlify/functions/recurring-fees`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${TEST_TOKENS.admin}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(recurringFeeData)
    });
    
    console.log(`Recurring fee creation status: ${response.status}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log(`✅ Recurring fee created successfully:`, result);
    } else {
      const error = await response.json();
      console.log(`⚠️ Recurring fee creation failed:`, error);
    }
    
  } catch (error) {
    console.log(`❌ Recurring fee creation error: ${error.message}`);
  }
  
  console.log('\n---\n');
  
  // Test listing data after creation
  console.log('📋 Testing Data Retrieval After Creation...\n');
  
  const listEndpoints = [
    `/.netlify/functions/commission-payments?company_id=${VRC_COMPANY_ID}`,
    `/.netlify/functions/recurring-fees?company_id=${VRC_COMPANY_ID}`,
    `/.netlify/functions/financial-transactions?company_id=${VRC_COMPANY_ID}`
  ];
  
  for (const endpoint of listEndpoints) {
    try {
      console.log(`Listing ${endpoint}...`);
      
      const response = await fetch(`${SITE_URL}${endpoint}`, {
        headers: {
          'Authorization': `Bearer ${TEST_TOKENS.admin}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log(`Status: ${response.status}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ Retrieved ${data.commissions?.length || data.recurringFees?.length || data.transactions?.length || 0} records`);
        
        if (data.commissions && data.commissions.length > 0) {
          console.log(`   Sample commission: $${data.commissions[0].commission_amount} for ${data.commissions[0].product_or_service}`);
        }
        
        if (data.recurringFees && data.recurringFees.length > 0) {
          console.log(`   Sample recurring fee: $${data.recurringFees[0].amount} ${data.recurringFees[0].frequency} for ${data.recurringFees[0].description}`);
        }
        
        if (data.transactions && data.transactions.length > 0) {
          console.log(`   Sample transaction: $${data.transactions[0].gross_amount} - ${data.transactions[0].description}`);
        }
      } else {
        const error = await response.json();
        console.log(`⚠️ Error:`, error);
      }
      
      console.log('---\n');
      
    } catch (error) {
      console.log(`❌ Error: ${error.message}\n`);
    }
  }
}

testPaymentAPIsWithAuth();
