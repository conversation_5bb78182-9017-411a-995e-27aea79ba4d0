// Auth debugging function
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Content-Type': 'application/json'
  };

  // Handle preflight OPTIONS request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ message: 'CORS preflight response' })
    };
  }

  try {
    // Collect debug information
    const debugInfo = {
      timestamp: new Date().toISOString(),
      environment: {
        NODE_VERSION: process.version,
        SUPABASE_URL_EXISTS: !!process.env.SUPABASE_URL,
        SUPABASE_SERVICE_KEY_EXISTS: !!process.env.SUPABASE_SERVICE_KEY,
        SITE_URL_EXISTS: !!process.env.SITE_URL,
        NETLIFY_ENV: process.env.NETLIFY_ENV || 'not set',
        DEPLOY_URL: process.env.DEPLOY_URL || 'not set',
        URL: process.env.URL || 'not set',
        DEPLOY_PRIME_URL: process.env.DEPLOY_PRIME_URL || 'not set',
      },
      request: {
        method: event.httpMethod,
        path: event.path,
        headers: event.headers,
        queryParams: event.queryStringParameters || {},
        body: event.body ? '(body exists)' : '(no body)'
      }
    };

    // Check Supabase connection
    let supabaseStatus = 'unknown';
    let authSettings = null;
    let error = null;

    try {
      // Try to fetch a simple query to test connection
      const { data, error: queryError } = await supabase
        .from('users')
        .select('count(*)', { count: 'exact', head: true });
      
      if (queryError) {
        supabaseStatus = 'error';
        error = queryError.message;
      } else {
        supabaseStatus = 'connected';
        
        // Try to get auth settings
        const { data: authData, error: authError } = await supabase
          .rpc('get_auth_settings');
        
        if (!authError) {
          authSettings = authData;
        }
      }
    } catch (e) {
      supabaseStatus = 'exception';
      error = e.message;
    }

    // Add Supabase status to debug info
    debugInfo.supabase = {
      status: supabaseStatus,
      error: error,
      authSettings: authSettings
    };

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify(debugInfo)
    };
  } catch (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ 
        message: 'Error collecting debug information',
        error: error.message
      })
    };
  }
};
