import { supabase } from '../../utils/supabase/supabase.utils.js';

/**
 * Activity Logger Service
 *
 * Comprehensive logging system for tracking user navigation, interactions,
 * and debugging information to help improve the experimental navigation system.
 */
class ActivityLogger {
  constructor() {
    this.sessionId = this.generateSessionId();
    this.userId = null;
    this.isEnabled = true;
    this.batchQueue = [];
    this.batchSize = 10;
    this.batchTimeout = 5000; // 5 seconds
    this.currentFlow = null;
    this.performanceObserver = null;

    this.initializeLogger();
  }

  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  initializeLogger() {
    // Get user ID when available
    supabase.auth.onAuthStateChange((event, session) => {
      this.userId = session?.user?.id || null;

      if (event === 'SIGNED_IN') {
        this.logEvent('debug', 'session', 'user_signed_in', {
          sessionId: this.sessionId,
          userId: this.userId
        });
      }
    });

    // Set up batch processing
    setInterval(() => {
      this.flushBatch();
    }, this.batchTimeout);

    // Set up performance monitoring
    this.initializePerformanceMonitoring();

    // Set up error tracking
    this.initializeErrorTracking();

    // Log initial page load
    this.logPageLoad();
  }

  initializePerformanceMonitoring() {
    if ('PerformanceObserver' in window) {
      this.performanceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.logPerformanceMetric(entry);
        }
      });

      this.performanceObserver.observe({ entryTypes: ['navigation', 'paint', 'largest-contentful-paint'] });
    }
  }

  initializeErrorTracking() {
    window.addEventListener('error', (event) => {
      this.logError('javascript_error', event.error?.message || 'Unknown error', {
        error_stack: event.error?.stack,
        metadata: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        }
      });
    });

    window.addEventListener('unhandledrejection', (event) => {
      this.logError('promise_rejection', event.reason?.message || 'Unhandled promise rejection', {
        error_stack: event.reason?.stack,
        metadata: {
          reason: event.reason
        }
      });
    });
  }

  // Core logging method
  async logEvent(eventType, eventCategory, eventAction, data = {}) {
    if (!this.isEnabled) return;

    // Define valid database columns to prevent schema mismatches
    const validColumns = [
      'user_id', 'session_id', 'timestamp', 'event_type', 'event_category', 'event_action',
      'from_page', 'to_page', 'navigation_method', 'view_mode', 'zoom_level',
      'element_type', 'element_id', 'mouse_position', 'viewport_size',
      'debug_data', 'user_agent', 'url', 'referrer',
      'page_load_time', 'interaction_duration',
      'error_message', 'error_stack', 'metadata'
    ];

    // Base log entry with required fields
    const baseEntry = {
      user_id: this.userId,
      session_id: this.sessionId,
      timestamp: new Date().toISOString(),
      event_type: eventType,
      event_category: eventCategory,
      event_action: eventAction,
      user_agent: navigator.userAgent,
      url: window.location.href,
      referrer: document.referrer,
      viewport_size: {
        width: window.innerWidth,
        height: window.innerHeight
      }
    };

    // Filter additional data to only include valid columns
    const filteredData = {};
    const metadataFields = {};

    Object.entries(data).forEach(([key, value]) => {
      if (validColumns.includes(key)) {
        filteredData[key] = value;
      } else {
        // Put unknown fields in metadata
        metadataFields[key] = value;
      }
    });

    // Merge metadata
    const existingMetadata = filteredData.metadata || {};
    const finalMetadata = { ...existingMetadata, ...metadataFields };

    const logEntry = {
      ...baseEntry,
      ...filteredData,
      metadata: Object.keys(finalMetadata).length > 0 ? finalMetadata : {}
    };

    // Add to batch queue
    this.batchQueue.push(logEntry);

    // Flush immediately for errors or if batch is full
    if (eventType === 'error' || this.batchQueue.length >= this.batchSize) {
      await this.flushBatch();
    }
  }

  // Navigation specific logging
  logNavigation(fromPage, toPage, method, viewMode, additionalData = {}) {
    this.logEvent('navigation', 'page_transition', 'navigate', {
      from_page: fromPage,
      to_page: toPage,
      navigation_method: method,
      view_mode: viewMode,
      ...additionalData
    });

    // Update current navigation flow
    this.updateNavigationFlow(fromPage, toPage, method);
  }

  logViewModeChange(fromMode, toMode, trigger, additionalData = {}) {
    this.logEvent('navigation', 'view_mode_change', 'mode_transition', {
      from_mode: fromMode,
      to_mode: toMode,
      trigger: trigger,
      ...additionalData
    });
  }

  logCardInteraction(cardId, action, position, additionalData = {}) {
    this.logEvent('interaction', 'card_interaction', action, {
      element_type: 'card',
      element_id: cardId,
      mouse_position: position,
      ...additionalData
    });
  }

  logKeyboardNavigation(key, fromPage, toPage, success, additionalData = {}) {
    this.logEvent('interaction', 'keyboard_navigation', 'key_press', {
      key_pressed: key,
      from_page: fromPage,
      to_page: toPage,
      navigation_success: success,
      ...additionalData
    });
  }

  logDragInteraction(startPos, endPos, duration, elementType, additionalData = {}) {
    this.logEvent('interaction', 'drag_interaction', 'drag_complete', {
      start_position: startPos,
      end_position: endPos,
      interaction_duration: duration,
      element_type: elementType,
      ...additionalData
    });
  }

  logDebugAction(action, command, result, additionalData = {}) {
    this.logEvent('debug', 'console_command', action, {
      debug_command: command,
      command_result: result,
      ...additionalData
    });
  }

  logError(errorType, message, additionalData = {}) {
    this.logEvent('error', errorType, 'error_occurred', {
      error_message: message,
      error_stack: additionalData.error_stack,
      metadata: additionalData.metadata || {},
      ...additionalData
    });
  }

  logPerformanceMetric(entry) {
    const metricData = {
      page_name: window.location.pathname,
      metric_type: entry.entryType,
      metric_name: entry.name,
      start_time: entry.startTime,
      duration: entry.duration
    };

    if (entry.entryType === 'navigation') {
      metricData.load_time = entry.loadEventEnd - entry.loadEventStart;
      metricData.dom_content_loaded = entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart;
    }

    this.logEvent('performance', 'page_metrics', 'performance_measured', metricData);
  }

  // Navigation flow tracking
  startNavigationFlow(startPage, intent = null) {
    this.currentFlow = {
      id: `flow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      start_page: startPage,
      user_intent: intent,
      steps: [],
      start_time: Date.now(),
      interaction_count: 0,
      error_count: 0
    };
  }

  updateNavigationFlow(fromPage, toPage, method) {
    if (!this.currentFlow) {
      this.startNavigationFlow(fromPage);
    }

    this.currentFlow.steps.push({
      from: fromPage,
      to: toPage,
      method: method,
      timestamp: Date.now()
    });

    this.currentFlow.interaction_count++;
  }

  endNavigationFlow(endPage, successful = true) {
    if (!this.currentFlow) return;

    const flowData = {
      ...this.currentFlow,
      end_page: endPage,
      successful_completion: successful,
      total_duration: Date.now() - this.currentFlow.start_time,
      total_steps: this.currentFlow.steps.length
    };

    this.logEvent('navigation', 'flow_completion', 'flow_ended', {
      navigation_flow: flowData
    });

    this.currentFlow = null;
  }

  // Batch processing
  async flushBatch() {
    if (this.batchQueue.length === 0) return;

    const batch = [...this.batchQueue];
    this.batchQueue = [];

    try {
      const { error } = await supabase
        .from('user_activity_logs')
        .insert(batch);

      if (error) {
        console.error('Failed to log activity batch:', error);
        // Re-add failed items to queue for retry
        this.batchQueue.unshift(...batch);
      }
    } catch (err) {
      console.error('Activity logging error:', err);
      // Re-add failed items to queue for retry
      this.batchQueue.unshift(...batch);
    }
  }

  // Utility methods
  logPageLoad() {
    this.logEvent('navigation', 'page_load', 'initial_load', {
      page_name: window.location.pathname,
      load_time: performance.now()
    });
  }

  setUserId(userId) {
    this.userId = userId;
  }

  enable() {
    this.isEnabled = true;
  }

  disable() {
    this.isEnabled = false;
  }

  // Debug helpers
  getSessionInfo() {
    return {
      sessionId: this.sessionId,
      userId: this.userId,
      isEnabled: this.isEnabled,
      queueSize: this.batchQueue.length,
      currentFlow: this.currentFlow
    };
  }

  // Export session data for debugging
  async exportSessionData() {
    const { data, error } = await supabase
      .from('user_activity_logs')
      .select('*')
      .eq('session_id', this.sessionId)
      .order('timestamp', { ascending: true });

    if (error) {
      console.error('Failed to export session data:', error);
      return null;
    }

    return data;
  }
}

// Create singleton instance
const activityLogger = new ActivityLogger();

export default activityLogger;
