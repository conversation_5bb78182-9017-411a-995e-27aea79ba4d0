import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { useNavigate, useParams, Link } from 'react-router-dom';
import RoyaltyCalculator from '../../components/royalty/RoyaltyCalculator';

/**
 * RoyaltyCalculatorPage Component
 * 
 * Page for calculating royalty distributions for a project
 */
const RoyaltyCalculatorPage = () => {
  const { currentUser } = useContext(UserContext);
  const navigate = useNavigate();
  const { projectId, revenueId } = useParams();
  const [loading, setLoading] = useState(true);
  const [project, setProject] = useState(null);
  const [revenue, setRevenue] = useState(null);
  const [hasAccess, setHasAccess] = useState(false);

  // Check if user has access to the project
  useEffect(() => {
    const checkAccess = async () => {
      if (!currentUser || !projectId) return;

      try {
        setLoading(true);
        
        // Check if user is a contributor to the project
        const { data: contributorData, error: contributorError } = await supabase
          .from('project_contributors')
          .select('role')
          .eq('project_id', projectId)
          .eq('user_id', currentUser.id)
          .eq('status', 'active')
          .single();
          
        if (contributorError && contributorError.code !== 'PGRST116') {
          throw contributorError;
        }
        
        // Check if user is the project creator
        const { data: projectData, error: projectError } = await supabase
          .from('projects')
          .select('*')
          .eq('id', projectId)
          .single();
          
        if (projectError) throw projectError;
        
        const isCreator = projectData.created_by === currentUser.id;
        const isAdmin = contributorData?.role === 'admin';
        
        // Set access based on role
        setHasAccess(isCreator || isAdmin);
        setProject(projectData);
        
        // If revenueId is provided, fetch revenue data
        if (revenueId) {
          const { data: revenueData, error: revenueError } = await supabase
            .from('revenue')
            .select('*')
            .eq('id', revenueId)
            .eq('project_id', projectId)
            .single();
            
          if (revenueError) throw revenueError;
          setRevenue(revenueData);
        }
      } catch (error) {
        console.error('Error checking access:', error);
        setHasAccess(false);
      } finally {
        setLoading(false);
      }
    };
    
    checkAccess();
  }, [currentUser, projectId, revenueId]);

  // Handle successful save
  const handleSaveSuccess = () => {
    if (revenueId) {
      // Navigate back to revenue page
      navigate(`/project/${projectId}/revenue`);
    } else {
      // Stay on calculator page
      navigate(`/project/${projectId}/royalty-calculator`);
    }
  };

  if (loading) {
    return (
      <div className="royalty-calculator-page loading">
        <div className="loading-spinner">
          <i className="bi bi-arrow-repeat spinning"></i>
          <span>Loading...</span>
        </div>
      </div>
    );
  }

  if (!currentUser) {
    return (
      <div className="royalty-calculator-page">
        <div className="auth-required">
          <h2>Authentication Required</h2>
          <p>Please log in to access the royalty calculator.</p>
          <button 
            className="btn btn-primary"
            onClick={() => navigate('/login')}
          >
            Log In
          </button>
        </div>
      </div>
    );
  }

  if (!hasAccess) {
    return (
      <div className="royalty-calculator-page">
        <div className="access-denied">
          <h2>Access Denied</h2>
          <p>You don't have permission to access the royalty calculator for this project.</p>
          <Link to="/track" className="btn btn-primary">
            Go to Projects
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="royalty-calculator-page">
      <div className="page-header">
        <div className="header-content">
          <h1>Royalty Calculator</h1>
          {project && (
            <div className="project-info">
              <span className="project-name">{project.name}</span>
              {revenue && (
                <span className="revenue-info">
                  Revenue: {new Intl.NumberFormat('en-US', { 
                    style: 'currency', 
                    currency: revenue.currency || 'USD' 
                  }).format(revenue.amount)}
                </span>
              )}
            </div>
          )}
        </div>
        
        <div className="header-actions">
          <Link to={`/project/${projectId}/revenue`} className="back-btn">
            <i className="bi bi-arrow-left"></i>
            Back to Revenue
          </Link>
        </div>
      </div>
      
      <div className="page-content">
        <RoyaltyCalculator 
          projectId={projectId}
          revenueId={revenueId}
          onSave={handleSaveSuccess}
          onCancel={() => navigate(`/project/${projectId}/revenue`)}
        />
      </div>
    </div>
  );
};

export default RoyaltyCalculatorPage;
