// Execute Profile Migration API function
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const initSupabase = () => {
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_ANON_KEY;
  
  if (!supabaseUrl || !supabaseKey) {
    throw new Error('Missing Supabase credentials');
  }
  
  return createClient(supabaseUrl, supabaseKey);
};

exports.handler = async (event, context) => {
  // Check if this is a POST request
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  // Check for admin authorization
  const authHeader = event.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return {
      statusCode: 401,
      body: JSON.stringify({ error: 'Unauthorized' })
    };
  }

  try {
    // Initialize Supabase client
    const supabase = initSupabase();
    
    // Verify the user is an admin
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return {
        statusCode: 401,
        body: JSON.stringify({ error: 'Invalid token' })
      };
    }
    
    // Check if user is admin
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single();
    
    if (userError || !userData || !userData.is_admin) {
      return {
        statusCode: 403,
        body: JSON.stringify({ error: 'Unauthorized - Admin access required' })
      };
    }
    
    // Execute the migration
    const migrationResult = await executeMigration(supabase);
    
    return {
      statusCode: 200,
      body: JSON.stringify(migrationResult)
    };
  } catch (error) {
    console.error('Error executing migration:', error);
    
    return {
      statusCode: 500,
      body: JSON.stringify({ 
        error: 'Internal server error', 
        details: error.message 
      })
    };
  }
};

// Function to execute the migration
async function executeMigration(supabase) {
  const results = {
    steps: [],
    success: true
  };
  
  try {
    // Step 1: Add new columns to the users table
    results.steps.push({ step: 'Adding new columns to users table', status: 'started' });
    
    // Add headline column
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: 'ALTER TABLE public.users ADD COLUMN IF NOT EXISTS headline TEXT;' 
      });
      results.steps.push({ step: 'Added headline column', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Adding headline column', status: 'error', details: error.message });
      results.success = false;
    }
    
    // Add location column
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: 'ALTER TABLE public.users ADD COLUMN IF NOT EXISTS location TEXT;' 
      });
      results.steps.push({ step: 'Added location column', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Adding location column', status: 'error', details: error.message });
      results.success = false;
    }
    
    // Add website column
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: 'ALTER TABLE public.users ADD COLUMN IF NOT EXISTS website TEXT;' 
      });
      results.steps.push({ step: 'Added website column', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Adding website column', status: 'error', details: error.message });
      results.success = false;
    }
    
    // Add cover_image_url column
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: 'ALTER TABLE public.users ADD COLUMN IF NOT EXISTS cover_image_url TEXT;' 
      });
      results.steps.push({ step: 'Added cover_image_url column', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Adding cover_image_url column', status: 'error', details: error.message });
      results.success = false;
    }
    
    // Add status_message column
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: 'ALTER TABLE public.users ADD COLUMN IF NOT EXISTS status_message TEXT;' 
      });
      results.steps.push({ step: 'Added status_message column', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Adding status_message column', status: 'error', details: error.message });
      results.success = false;
    }
    
    // Add availability_status column
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: 'ALTER TABLE public.users ADD COLUMN IF NOT EXISTS availability_status TEXT;' 
      });
      results.steps.push({ step: 'Added availability_status column', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Adding availability_status column', status: 'error', details: error.message });
      results.success = false;
    }
    
    // Add profile_views column
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: 'ALTER TABLE public.users ADD COLUMN IF NOT EXISTS profile_views INTEGER DEFAULT 0;' 
      });
      results.steps.push({ step: 'Added profile_views column', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Adding profile_views column', status: 'error', details: error.message });
      results.success = false;
    }
    
    // Add theme_settings column
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: `ALTER TABLE public.users ADD COLUMN IF NOT EXISTS theme_settings JSONB DEFAULT '{
          "theme": "default",
          "colors": {
            "background": "#f8fafc",
            "primary": "#3b82f6",
            "secondary": "#f0f2f5",
            "text": "#1c1e21",
            "accent": "#6c5ce7",
            "links": "#3b82f6",
            "borders": "#e2e8f0"
          },
          "fonts": {
            "heading": "Inter",
            "body": "Inter"
          },
          "layout": "standard"
        }'::jsonb;` 
      });
      results.steps.push({ step: 'Added theme_settings column', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Adding theme_settings column', status: 'error', details: error.message });
      results.success = false;
    }
    
    // Add custom_css column
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: 'ALTER TABLE public.users ADD COLUMN IF NOT EXISTS custom_css TEXT;' 
      });
      results.steps.push({ step: 'Added custom_css column', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Adding custom_css column', status: 'error', details: error.message });
      results.success = false;
    }
    
    // Add profile_song_url column
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: 'ALTER TABLE public.users ADD COLUMN IF NOT EXISTS profile_song_url TEXT;' 
      });
      results.steps.push({ step: 'Added profile_song_url column', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Adding profile_song_url column', status: 'error', details: error.message });
      results.success = false;
    }
    
    // Add privacy_settings column
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: `ALTER TABLE public.users ADD COLUMN IF NOT EXISTS privacy_settings JSONB DEFAULT '{
          "profile_visibility": "public",
          "section_visibility": {
            "personal_info": true,
            "contact_details": false,
            "skills": true,
            "projects": true,
            "contribution_details": true,
            "contribution_percentages": false,
            "royalty_info": false,
            "profile_song": true,
            "top_collaborators": true,
            "comments": true,
            "profile_visitors": false
          },
          "verification_display": "level_only"
        }'::jsonb;` 
      });
      results.steps.push({ step: 'Added privacy_settings column', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Adding privacy_settings column', status: 'error', details: error.message });
      results.success = false;
    }
    
    // Step 2: Create the increment_profile_views function
    results.steps.push({ step: 'Creating increment_profile_views function', status: 'started' });
    
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: `
        CREATE OR REPLACE FUNCTION increment_profile_views(profile_id UUID)
        RETURNS void
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        BEGIN
          -- Update the profile_views counter
          UPDATE public.users
          SET profile_views = COALESCE(profile_views, 0) + 1
          WHERE id = profile_id;
        END;
        $$;
        ` 
      });
      results.steps.push({ step: 'Created increment_profile_views function', status: 'success' });
    } catch (error) {
      results.steps.push({ step: 'Creating increment_profile_views function', status: 'error', details: error.message });
      results.success = false;
    }
    
    return results;
  } catch (error) {
    results.success = false;
    results.error = error.message;
    return results;
  }
}
