// Ally Management API
// Handles friend requests, connections, and ally network management
// Based on docs/design-system/systems/social-system.md

const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' };
  }

  try {
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Get user from authorization header
    const authHeader = event.headers.authorization;
    if (!authHeader) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Authorization header required' })
      };
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Invalid authentication token' })
      };
    }

    const { httpMethod, path } = event;
    const pathParts = path.split('/').filter(part => part);
    
    switch (httpMethod) {
      case 'GET':
        return await handleGet(supabase, user, pathParts, event.queryStringParameters);
      case 'POST':
        return await handlePost(supabase, user, pathParts, JSON.parse(event.body || '{}'));
      case 'PUT':
        return await handlePut(supabase, user, pathParts, JSON.parse(event.body || '{}'));
      case 'DELETE':
        return await handleDelete(supabase, user, pathParts);
      default:
        return {
          statusCode: 405,
          headers,
          body: JSON.stringify({ error: 'Method not allowed' })
        };
    }
  } catch (error) {
    console.error('API Error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error', details: error.message })
    };
  }
};

// GET handlers
async function handleGet(supabase, user, pathParts, queryParams) {
  const endpoint = pathParts[pathParts.length - 1];
  
  switch (endpoint) {
    case 'allies':
      return await getAllies(supabase, user.id, queryParams);
    case 'requests':
      return await getRequests(supabase, user.id, queryParams);
    case 'suggestions':
      return await getSuggestions(supabase, user.id, queryParams);
    default:
      return {
        statusCode: 404,
        headers,
        body: JSON.stringify({ error: 'Endpoint not found' })
      };
  }
}

// Get user's ally network
async function getAllies(supabase, userId, queryParams) {
  const { status = 'accepted', limit = 50, offset = 0 } = queryParams || {};
  
  const { data, error } = await supabase
    .from('user_allies')
    .select(`
      id,
      status,
      connection_strength,
      connection_reason,
      accepted_at,
      last_interaction_at,
      mutual_connections_count,
      collaboration_count,
      ally:ally_id(id, display_name, email),
      user:user_id(id, display_name, email)
    `)
    .or(`user_id.eq.${userId},ally_id.eq.${userId}`)
    .eq('status', status)
    .order('last_interaction_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to fetch allies', details: error.message })
    };
  }

  // Format the response to always show the ally (not the current user)
  const formattedAllies = data.map(connection => {
    const isUserInitiator = connection.user.id === userId;
    return {
      id: connection.id,
      ally: isUserInitiator ? connection.ally : connection.user,
      status: connection.status,
      connection_strength: connection.connection_strength,
      connection_reason: connection.connection_reason,
      accepted_at: connection.accepted_at,
      last_interaction_at: connection.last_interaction_at,
      mutual_connections_count: connection.mutual_connections_count,
      collaboration_count: connection.collaboration_count
    };
  });

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({
      allies: formattedAllies,
      total: formattedAllies.length,
      hasMore: formattedAllies.length === limit
    })
  };
}

// Get pending ally requests
async function getRequests(supabase, userId, queryParams) {
  const { type = 'received', limit = 20, offset = 0 } = queryParams || {};
  
  let query = supabase
    .from('user_allies')
    .select(`
      id,
      request_message,
      requested_at,
      connection_reason,
      created_by,
      requester:created_by(id, display_name, email),
      target:${type === 'received' ? 'user_id' : 'ally_id'}(id, display_name, email)
    `)
    .eq('status', 'pending')
    .order('requested_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (type === 'received') {
    query = query.or(`user_id.eq.${userId},ally_id.eq.${userId}`).neq('created_by', userId);
  } else {
    query = query.eq('created_by', userId);
  }

  const { data, error } = await query;

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to fetch requests', details: error.message })
    };
  }

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({
      requests: data,
      total: data.length,
      hasMore: data.length === limit
    })
  };
}

// Get AI-suggested allies
async function getSuggestions(supabase, userId, queryParams) {
  const { limit = 10, skills, industry } = queryParams || {};
  
  // Simple suggestion algorithm - find users with mutual connections or similar skills
  // This is a basic implementation - in production, you'd want more sophisticated AI
  
  const { data, error } = await supabase
    .from('users')
    .select(`
      id,
      display_name,
      email,
      user_skills(skill_name),
      user_profiles(professional_title, bio, location)
    `)
    .neq('id', userId)
    .not('id', 'in', `(
      SELECT CASE 
        WHEN user_id = '${userId}' THEN ally_id 
        ELSE user_id 
      END 
      FROM user_allies 
      WHERE (user_id = '${userId}' OR ally_id = '${userId}') 
      AND status IN ('accepted', 'pending')
    )`)
    .limit(limit);

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to fetch suggestions', details: error.message })
    };
  }

  // Add suggestion score (simplified)
  const suggestions = data.map(user => ({
    ...user,
    suggestion_score: Math.random() * 100, // Placeholder for AI scoring
    match_reasons: ['Similar skills', 'Industry match'] // Placeholder
  }));

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({
      suggestions: suggestions.sort((a, b) => b.suggestion_score - a.suggestion_score),
      total: suggestions.length
    })
  };
}

// POST handlers
async function handlePost(supabase, user, pathParts, body) {
  const endpoint = pathParts[pathParts.length - 1];
  
  if (endpoint === 'request') {
    return await sendAllyRequest(supabase, user.id, body);
  }
  
  return {
    statusCode: 404,
    headers,
    body: JSON.stringify({ error: 'Endpoint not found' })
  };
}

// Send ally request
async function sendAllyRequest(supabase, userId, body) {
  const { ally_id, message, connection_reason } = body;
  
  if (!ally_id) {
    return {
      statusCode: 400,
      headers,
      body: JSON.stringify({ error: 'ally_id is required' })
    };
  }

  // Check if connection already exists
  const { data: existing } = await supabase
    .from('user_allies')
    .select('id, status')
    .or(`and(user_id.eq.${userId},ally_id.eq.${ally_id}),and(user_id.eq.${ally_id},ally_id.eq.${userId})`);

  if (existing && existing.length > 0) {
    return {
      statusCode: 409,
      headers,
      body: JSON.stringify({ error: 'Connection already exists', status: existing[0].status })
    };
  }

  const { data, error } = await supabase
    .from('user_allies')
    .insert({
      user_id: userId,
      ally_id: ally_id,
      created_by: userId,
      request_message: message,
      connection_reason: connection_reason || 'networking',
      status: 'pending'
    })
    .select()
    .single();

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to send ally request', details: error.message })
    };
  }

  return {
    statusCode: 201,
    headers,
    body: JSON.stringify({ message: 'Ally request sent successfully', request: data })
  };
}

// PUT handlers
async function handlePut(supabase, user, pathParts, body) {
  const requestId = pathParts[pathParts.length - 2];
  const action = pathParts[pathParts.length - 1];

  if (action === 'accept') {
    return await acceptAllyRequest(supabase, user.id, requestId);
  } else if (action === 'decline') {
    return await declineAllyRequest(supabase, user.id, requestId);
  }

  return {
    statusCode: 404,
    headers,
    body: JSON.stringify({ error: 'Action not found' })
  };
}

// Accept ally request
async function acceptAllyRequest(supabase, userId, requestId) {
  // Verify the user is the target of this request
  const { data: request, error: fetchError } = await supabase
    .from('user_allies')
    .select('*')
    .eq('id', requestId)
    .or(`user_id.eq.${userId},ally_id.eq.${userId}`)
    .neq('created_by', userId)
    .eq('status', 'pending')
    .single();

  if (fetchError || !request) {
    return {
      statusCode: 404,
      headers,
      body: JSON.stringify({ error: 'Request not found or not authorized' })
    };
  }

  const { data, error } = await supabase
    .from('user_allies')
    .update({
      status: 'accepted',
      accepted_at: new Date().toISOString()
    })
    .eq('id', requestId)
    .select()
    .single();

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to accept request', details: error.message })
    };
  }

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({ message: 'Ally request accepted', connection: data })
  };
}

// Decline ally request
async function declineAllyRequest(supabase, userId, requestId) {
  // Verify the user is the target of this request
  const { data: request, error: fetchError } = await supabase
    .from('user_allies')
    .select('*')
    .eq('id', requestId)
    .or(`user_id.eq.${userId},ally_id.eq.${userId}`)
    .neq('created_by', userId)
    .eq('status', 'pending')
    .single();

  if (fetchError || !request) {
    return {
      statusCode: 404,
      headers,
      body: JSON.stringify({ error: 'Request not found or not authorized' })
    };
  }

  const { error } = await supabase
    .from('user_allies')
    .update({ status: 'declined' })
    .eq('id', requestId);

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to decline request', details: error.message })
    };
  }

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({ message: 'Ally request declined' })
  };
}

// DELETE handlers
async function handleDelete(supabase, user, pathParts) {
  const connectionId = pathParts[pathParts.length - 1];
  return await removeAllyConnection(supabase, user.id, connectionId);
}

// Remove ally connection
async function removeAllyConnection(supabase, userId, connectionId) {
  // Verify the user is part of this connection
  const { data: connection, error: fetchError } = await supabase
    .from('user_allies')
    .select('*')
    .eq('id', connectionId)
    .or(`user_id.eq.${userId},ally_id.eq.${userId}`)
    .single();

  if (fetchError || !connection) {
    return {
      statusCode: 404,
      headers,
      body: JSON.stringify({ error: 'Connection not found or not authorized' })
    };
  }

  const { error } = await supabase
    .from('user_allies')
    .delete()
    .eq('id', connectionId);

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to remove connection', details: error.message })
    };
  }

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({ message: 'Ally connection removed successfully' })
  };
}
