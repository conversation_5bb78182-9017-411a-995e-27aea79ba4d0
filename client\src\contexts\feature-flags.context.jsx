import React, { createContext, useState, useEffect, useContext } from 'react';
import { supabase } from '../../utils/supabase/supabase.utils';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';

// Create the context
export const FeatureFlagContext = createContext();

// Default feature flags
const DEFAULT_FLAGS = {
  'new-ui': true,           // General UI components using HeroUI (✅ COMPLETED)
  'tremor-charts': false,   // Data visualization with Tremor
  'tanstack-query': false,  // API layer with TanStack Query
  'new-forms': false,       // Form components with React Hook Form + Zod
  'new-auth': false,        // Authentication system with <PERSON>
};

// Provider component
export const FeatureFlagProvider = ({ children }) => {
  const [flags, setFlags] = useState(DEFAULT_FLAGS);
  const [loading, setLoading] = useState(true);
  const { currentUser } = useContext(UserContext);

  // Load feature flags from localStorage or database
  useEffect(() => {
    const loadFeatureFlags = async () => {
      try {
        // First check localStorage for any overrides
        const localFlags = localStorage.getItem('featureFlags');
        if (localFlags) {
          setFlags(JSON.parse(localFlags));
          setLoading(false);
          return;
        }

        // If user is logged in, check database for user-specific flags
        if (currentUser) {
          try {
            // Try to get the user's preferences
            const { data, error } = await supabase
              .from('user_preferences')
              .select('feature_flags')
              .eq('user_id', currentUser.id)
              .maybeSingle(); // Use maybeSingle instead of single to avoid errors when no rows are found

            if (error) {
              // If there's an error, the table might not exist or there's another issue
              console.log('Error fetching user preferences:', error);
              setFlags(DEFAULT_FLAGS);
              setLoading(false);
              return;
            }

            // If data exists and has feature_flags, use it
            if (data && data.feature_flags) {
              setFlags({
                ...DEFAULT_FLAGS,
                ...data.feature_flags
              });
              setLoading(false);
              return;
            }

            // If we get here, the user doesn't have preferences yet, so create them
            if (currentUser) {
              try {
                const { error: insertError } = await supabase
                  .from('user_preferences')
                  .insert({
                    user_id: currentUser.id,
                    feature_flags: DEFAULT_FLAGS,
                    updated_at: new Date()
                  });

                if (insertError) {
                  // If there's an error inserting, the table might not exist
                  console.log('Error creating user preferences:', insertError);
                }
              } catch (insertErr) {
                console.log('Error creating user preferences:', insertErr);
              }
            }
          } catch (err) {
            console.log('Error in user preferences logic:', err);
          }

          // No additional code needed here, everything is handled in the try/catch block above
        }

        // If we get here, use default flags
        setFlags(DEFAULT_FLAGS);
      } catch (error) {
        console.error('Error loading feature flags:', error);
        setFlags(DEFAULT_FLAGS);
      } finally {
        setLoading(false);
      }
    };

    loadFeatureFlags();
  }, [currentUser]);

  // Function to check if a feature flag is enabled
  const isFeatureEnabled = (flagName) => {
    return flags[flagName] === true;
  };

  // Function to enable a feature flag
  const enableFeature = async (flagName) => {
    if (!flags.hasOwnProperty(flagName)) {
      console.warn(`Feature flag "${flagName}" does not exist`);
      return;
    }

    const newFlags = { ...flags, [flagName]: true };
    setFlags(newFlags);

    // Save to localStorage for persistence
    localStorage.setItem('featureFlags', JSON.stringify(newFlags));

    // If user is logged in, save to database
    if (currentUser) {
      try {
        const { error } = await supabase
          .from('user_preferences')
          .upsert({
            user_id: currentUser.id,
            feature_flags: newFlags,
            updated_at: new Date()
          }, {
            onConflict: 'user_id'
          });

        if (error) {
          // If there's an error, the table might not exist or there's another issue
          // Just use localStorage in this case
          console.log('Error saving feature flags to database, using localStorage only:', error);
        }
      } catch (error) {
        console.log('Error saving feature flags to database, using localStorage only:', error);
      }
    }
  };

  // Function to disable a feature flag
  const disableFeature = async (flagName) => {
    if (!flags.hasOwnProperty(flagName)) {
      console.warn(`Feature flag "${flagName}" does not exist`);
      return;
    }

    const newFlags = { ...flags, [flagName]: false };
    setFlags(newFlags);

    // Save to localStorage for persistence
    localStorage.setItem('featureFlags', JSON.stringify(newFlags));

    // If user is logged in, save to database
    if (currentUser) {
      try {
        const { error } = await supabase
          .from('user_preferences')
          .upsert({
            user_id: currentUser.id,
            feature_flags: newFlags,
            updated_at: new Date()
          }, {
            onConflict: 'user_id'
          });

        if (error) {
          // If there's an error, the table might not exist or there's another issue
          // Just use localStorage in this case
          console.log('Error saving feature flags to database, using localStorage only:', error);
        }
      } catch (error) {
        console.log('Error saving feature flags to database, using localStorage only:', error);
      }
    }
  };

  // Function to toggle a feature flag
  const toggleFeature = (flagName) => {
    if (isFeatureEnabled(flagName)) {
      disableFeature(flagName);
    } else {
      enableFeature(flagName);
    }
  };

  // Function to reset all feature flags to default
  const resetFeatures = async () => {
    setFlags(DEFAULT_FLAGS);
    localStorage.removeItem('featureFlags');

    // If user is logged in, update database
    if (currentUser) {
      try {
        const { error } = await supabase
          .from('user_preferences')
          .upsert({
            user_id: currentUser.id,
            feature_flags: DEFAULT_FLAGS,
            updated_at: new Date()
          }, {
            onConflict: 'user_id'
          });

        if (error) {
          // If there's an error, the table might not exist or there's another issue
          // Just use localStorage in this case
          console.log('Error resetting feature flags in database, using localStorage only:', error);
        }
      } catch (error) {
        console.log('Error resetting feature flags in database, using localStorage only:', error);
      }
    }
  };

  return (
    <FeatureFlagContext.Provider value={{
      flags,
      loading,
      isFeatureEnabled,
      enableFeature,
      disableFeature,
      toggleFeature,
      resetFeatures
    }}>
      {children}
    </FeatureFlagContext.Provider>
  );
};

// Custom hook for using feature flags
export const useFeatureFlags = () => {
  const context = useContext(FeatureFlagContext);
  if (context === undefined) {
    throw new Error('useFeatureFlags must be used within a FeatureFlagProvider');
  }
  return context;
};

export default FeatureFlagProvider;
