// Script to check if the Retro Profile migration has been applied
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY; // We can use the anon key for this check

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please set SUPABASE_URL and SUPABASE_ANON_KEY environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);
console.log('Supabase client initialized');

async function checkMigration() {
  try {
    console.log('Checking if Retro Profile migration has been applied...');
    
    // Check if the new columns exist in the users table
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, headline, location, website, cover_image_url, status_message, availability_status, profile_views, theme_settings, custom_css, profile_song_url, privacy_settings')
      .limit(1);
    
    if (userError) {
      if (userError.message.includes('column') && userError.message.includes('does not exist')) {
        console.error('Migration not applied: Some columns are missing from the users table');
        console.error('Error:', userError.message);
        return false;
      } else {
        console.error('Error checking users table:', userError.message);
        return false;
      }
    }
    
    console.log('✅ Users table columns exist');
    
    // Check if the profile_comments table exists
    const { data: commentsData, error: commentsError } = await supabase
      .from('profile_comments')
      .select('id')
      .limit(1);
    
    if (commentsError && !commentsError.message.includes('no rows returned')) {
      console.error('Migration not applied: profile_comments table may not exist');
      console.error('Error:', commentsError.message);
      return false;
    }
    
    console.log('✅ profile_comments table exists');
    
    // Check if the profile_views table exists
    const { data: viewsData, error: viewsError } = await supabase
      .from('profile_views')
      .select('id')
      .limit(1);
    
    if (viewsError && !viewsError.message.includes('no rows returned')) {
      console.error('Migration not applied: profile_views table may not exist');
      console.error('Error:', viewsError.message);
      return false;
    }
    
    console.log('✅ profile_views table exists');
    
    // Check if the top_collaborators table exists
    const { data: collaboratorsData, error: collaboratorsError } = await supabase
      .from('top_collaborators')
      .select('id')
      .limit(1);
    
    if (collaboratorsError && !collaboratorsError.message.includes('no rows returned')) {
      console.error('Migration not applied: top_collaborators table may not exist');
      console.error('Error:', collaboratorsError.message);
      return false;
    }
    
    console.log('✅ top_collaborators table exists');
    
    console.log('\n✅ Migration has been successfully applied!');
    return true;
  } catch (error) {
    console.error('Error checking migration:', error.message);
    return false;
  }
}

// Run the check
checkMigration();
