import React, { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { supabase } from '../../../utils/supabase/supabase.utils';
import LoadingAnimation from '../../components/layout/LoadingAnimation';
import UserSkillsList from '../../components/skills/UserSkillsList';
import { toast } from 'react-hot-toast';

const PublicProfile = () => {
  const { userId } = useParams();
  const { currentUser } = useContext(UserContext);
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [profileData, setProfileData] = useState(null);
  const [error, setError] = useState(null);
  const [isOwnProfile, setIsOwnProfile] = useState(false);

  // Log the userId for debugging
  useEffect(() => {
    console.log('Public Profile - userId param:', userId);

    // If userId is undefined or null, redirect to home
    if (!userId) {
      console.error('No userId provided');
      toast.error('User profile not found');
      navigate('/');
    }
  }, [userId, navigate]);

  useEffect(() => {
    const fetchProfileData = async () => {
      if (!userId) return; // Skip if no userId

      try {
        setLoading(true);
        console.log('Fetching profile data for userId:', userId);

        // Check if this is the current user's profile
        if (currentUser && userId === currentUser.id) {
          setIsOwnProfile(true);
          console.log('This is the user\'s own profile');
        }

        // Get user data from Supabase
        const { data, error } = await supabase
          .from('users')
          .select(`
            id,
            display_name,
            bio,
            avatar_url,
            certifications,
            awards,
            stats,
            social_links,
            is_premium
          `)
          .eq('id', userId)
          .single();

        if (error) {
          console.error('Supabase error fetching profile:', error);
          throw error;
        }

        if (!data) {
          console.error('No data returned for userId:', userId);
          throw new Error('User not found');
        }

        console.log('Profile data loaded successfully:', data.display_name);

        // Fetch user's teams
        const { data: teamMembers, error: teamError } = await supabase
          .from('team_members')
          .select('team_id, role, is_admin')
          .eq('user_id', userId);

        if (teamError) {
          console.error('Error fetching team memberships:', teamError);
        }

        let userTeams = [];
        if (teamMembers && teamMembers.length > 0) {
          // Get team details
          const teamIds = teamMembers.map(member => member.team_id);
          const { data: teamData, error: teamsError } = await supabase
            .from('teams')
            .select('*')
            .in('id', teamIds);

          if (teamsError) {
            console.error('Error fetching teams:', teamsError);
          } else {
            // Combine team data with member role
            userTeams = teamData.map(team => {
              const membership = teamMembers.find(member => member.team_id === team.id);
              return {
                ...team,
                role: membership?.role || 'member',
                is_admin: membership?.is_admin || false
              };
            });
          }
        }

        // Set profile data with teams
        setProfileData({
          ...data,
          teams: userTeams
        });

        toast.success(`Viewing ${data.display_name}'s profile`);
      } catch (error) {
        console.error('Error fetching profile data:', error);
        setError(error.message || 'Failed to load profile');
        toast.error('Could not load user profile');

        // Redirect to home after a delay if profile can't be loaded
        setTimeout(() => {
          navigate('/');
        }, 3000);
      } finally {
        setLoading(false);
      }
    };

    fetchProfileData();
  }, [userId, currentUser, navigate]);

  if (loading) {
    return <LoadingAnimation />;
  }

  if (error) {
    return (
      <div className="public-profile-page">
        <div className="container">
          <div className="error-container">
            <i className="bi bi-exclamation-triangle-fill error-icon"></i>
            <h2>Profile Not Found</h2>
            <p>{error}</p>
            <a href="/" className="btn btn-primary">Go Home</a>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="public-profile-page">
      <div className="container">
        <div className="profile-card">
          <div className="profile-header">
            <div className="profile-avatar">
              <img
                src={profileData.avatar_url || (profileData.is_premium ? '/default-avatar-crown.png' : '/default-avatar-specs.png')}
                alt={`${profileData.display_name}'s avatar`}
              />
            </div>
            <div className="profile-info">
              <h1 className="profile-name">{profileData.display_name}</h1>
              {profileData.bio && (
                <p className="profile-bio">{profileData.bio}</p>
              )}

              <div className="profile-action-buttons">
                {isOwnProfile && (
                  <a href="/profile" className="btn btn-outline-primary btn-sm edit-profile-btn">
                    <i className="bi bi-pencil"></i> Edit Profile
                  </a>
                )}
                <a href={`/retro-profile/${userId}`} className="btn btn-outline-secondary btn-sm retro-profile-btn">
                  <i className="bi bi-stars"></i> View Retro Profile
                </a>
              </div>
            </div>
          </div>

          <div className="profile-stats">
            <div className="stat-card">
              <div className="stat-value">{profileData.stats?.projects_completed || 0}</div>
              <div className="stat-label">Projects</div>
            </div>
            <div className="stat-card">
              <div className="stat-value">{profileData.stats?.contributions || 0}</div>
              <div className="stat-label">Contributions</div>
            </div>
            <div className="stat-card">
              <div className="stat-value">{profileData.stats?.hours_tracked || 0}</div>
              <div className="stat-label">Hours</div>
            </div>
          </div>

          {profileData.social_links && Object.values(profileData.social_links).some(link => link) && (
            <div className="profile-social">
              {profileData.social_links.github && (
                <a href={profileData.social_links.github} target="_blank" rel="noopener noreferrer" className="social-link">
                  <i className="bi bi-github"></i>
                </a>
              )}
              {profileData.social_links.twitter && (
                <a href={profileData.social_links.twitter} target="_blank" rel="noopener noreferrer" className="social-link">
                  <i className="bi bi-twitter"></i>
                </a>
              )}
              {profileData.social_links.linkedin && (
                <a href={profileData.social_links.linkedin} target="_blank" rel="noopener noreferrer" className="social-link">
                  <i className="bi bi-linkedin"></i>
                </a>
              )}
              {profileData.social_links.website && (
                <a href={profileData.social_links.website} target="_blank" rel="noopener noreferrer" className="social-link">
                  <i className="bi bi-globe"></i>
                </a>
              )}
            </div>
          )}
        </div>

        {profileData.awards && profileData.awards.length > 0 && (
          <div className="profile-section">
            <h2 className="section-title">
              <i className="bi bi-trophy-fill section-icon"></i> Awards & Achievements
            </h2>
            <div className="awards-grid">
              {profileData.awards.map((award, index) => (
                <div key={index} className="award-item">
                  <div className="award-icon">
                    <i className={`bi ${award.icon || 'bi-award-fill'}`}></i>
                  </div>
                  <div className="award-info">
                    <h3 className="award-title">{award.name}</h3>
                    <p className="award-description">{award.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {profileData.certifications && profileData.certifications.length > 0 && (
          <div className="profile-section">
            <h2 className="section-title">
              <i className="bi bi-patch-check-fill section-icon"></i> Certifications
            </h2>
            <div className="certifications-grid">
              {profileData.certifications.map((cert, index) => (
                <div key={index} className="certification-item">
                  <div className="certification-icon">
                    <i className={`bi ${cert.icon || 'bi-mortarboard-fill'}`}></i>
                  </div>
                  <div className="certification-info">
                    <h3 className="certification-title">{cert.name}</h3>
                    <p className="certification-date">
                      Earned on {new Date(cert.earned_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="profile-section">
          <h2 className="section-title">
            <i className="bi bi-stars section-icon"></i> Skills
          </h2>
          <UserSkillsList userId={userId} isOwnProfile={isOwnProfile} />
        </div>

        {profileData.teams && profileData.teams.length > 0 && (
          <div className="profile-section">
            <h2 className="section-title">
              <i className="bi bi-people-fill section-icon"></i> Teams
            </h2>
            <div className="teams-grid">
              {profileData.teams.map((team) => (
                <div key={team.id} className="team-item">
                  <div className="team-info">
                    <h3 className="team-title">{team.name}</h3>
                    {team.description && (
                      <p className="team-description">{team.description}</p>
                    )}
                  </div>
                  <div className="team-role">
                    <span className={`role-badge ${team.role}`}>{team.role}</span>
                    {team.is_admin && <span className="admin-badge">Admin</span>}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="profile-section">
          <h2 className="section-title">
            <i className="bi bi-bar-chart-fill section-icon"></i> Activity
          </h2>

          {/* This would be replaced with actual activity data in the future */}
          <div className="empty-activity">
            <i className="bi bi-calendar3 empty-icon"></i>
            <p>No activity data available yet.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PublicProfile;
