// Script to create a test invitation
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzgyMDU1OSwiZXhwIjoyMDU5Mzk2NTU5fQ.k5xvFBzBxNMCtPd0d8Ko9dAcT5Y3op3ZgFvlAf85LLs';

console.log('Using Supabase URL:', supabaseUrl);

const supabase = createClient(supabaseUrl, supabaseKey);

// Function to clean up all notifications
async function cleanupNotifications() {
  try {
    console.log('\nCleaning up all notifications...');
    
    // Delete all notifications
    const { error: notificationsError } = await supabase
      .from('notifications')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Dummy condition to delete all
    
    if (notificationsError) {
      console.error('Error deleting notifications:', notificationsError);
    } else {
      console.log('Deleted all notifications successfully');
    }
  } catch (error) {
    console.error('Error in cleanupNotifications:', error);
  }
}

// Function to create a test project
async function createTestProject() {
  try {
    console.log('\nCreating a test project...');
    
    // Create a test project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .insert({
        name: 'RLS Test Project',
        description: 'This is a test project to verify RLS is disabled',
        created_by: '93cbbbed-2772-4922-b7d7-d07fdc1aa62b', // Owner's user ID
        date_created: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_active: true,
        is_public: true,
        estimated_duration: 3,
        start_date: new Date().toISOString().split('T')[0]
      })
      .select()
      .single();
    
    if (projectError) {
      console.error('Error creating test project:', projectError);
      return null;
    }
    
    console.log('Created test project:', project);
    
    // Add the owner as a contributor
    const { data: ownerContributor, error: ownerError } = await supabase
      .from('project_contributors')
      .insert({
        project_id: project.id,
        user_id: '93cbbbed-2772-4922-b7d7-d07fdc1aa62b', // Owner's user ID
        email: '<EMAIL>',
        display_name: 'Gynell Journigan',
        role: 'Owner',
        permission_level: 'Owner',
        is_admin: true,
        status: 'active',
        invitation_sent_at: new Date().toISOString(),
        joined_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (ownerError) {
      console.error('Error adding owner as contributor:', ownerError);
    } else {
      console.log('Added owner as contributor:', ownerContributor);
    }
    
    return project;
  } catch (error) {
    console.error('Error in createTestProject:', error);
    return null;
  }
}

// Function to create a test invitation
async function createTestInvitation(projectId) {
  try {
    console.log('\nCreating a test invitation...');
    
    // Create a test invitation
    const { data: invitation, error: invitationError } = await supabase
      .from('project_contributors')
      .insert({
        project_id: projectId,
        user_id: '2a033231-d173-4292-aa36-90f4d735bcf3', // Gynell's user ID
        email: '<EMAIL>',
        display_name: 'Gynell Journigan',
        role: 'Contributor',
        permission_level: 'Contributor',
        is_admin: false,
        status: 'pending',
        invitation_sent_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (invitationError) {
      console.error('Error creating test invitation:', invitationError);
      return null;
    }
    
    console.log('Created test invitation:', invitation);
    return invitation;
  } catch (error) {
    console.error('Error in createTestInvitation:', error);
    return null;
  }
}

// Main function
async function main() {
  try {
    console.log('=== Creating Test Invitation ===');
    
    // Clean up existing notifications
    await cleanupNotifications();
    
    // Create a test project
    const project = await createTestProject();
    if (!project) {
      console.error('Failed to create test project');
      return;
    }
    
    // Create a test invitation
    const invitation = await createTestInvitation(project.id);
    if (!invitation) {
      console.error('Failed to create test invitation');
      return;
    }
    
    // Wait for the notification to be created by the trigger
    console.log('\nWaiting for notification to be created by trigger...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check if notification was created
    const { data: notifications, error: notificationsError } = await supabase
      .from('notifications')
      .select('*')
      .eq('related_id', invitation.id)
      .order('created_at', { ascending: false });
    
    if (notificationsError) {
      console.error('Error fetching notifications:', notificationsError);
    } else {
      console.log(`Found ${notifications.length} notifications for invitation:`);
      notifications.forEach((notification, index) => {
        console.log(`${index + 1}. ID: ${notification.id}, Type: ${notification.type}, Title: ${notification.title}, Created: ${notification.created_at}`);
        console.log(`   Message: ${notification.message}`);
        console.log(`   Metadata: ${JSON.stringify(notification.metadata)}`);
      });
    }
    
    console.log('\n=== Test Invitation Created Successfully ===');
    console.log('Project ID:', project.id);
    console.log('Invitation ID:', invitation.id);
    
    console.log('\nYou can now test accepting this invitation in the UI.');
    console.log('\nTesting Instructions:');
    console.log('1. Log in as the invited user (<EMAIL>)');
    console.log('2. Click on the notification bell icon');
    console.log('3. Accept the invitation to "RLS Test Project"');
    console.log('4. You should be redirected to the project page');
    console.log('5. You should be able to view the project page without access errors');
  } catch (error) {
    console.error('Error in main function:', error);
  }
}

// Run the main function
main();
