// Test script for the enhanced agreement generation system
const fs = require('fs');
const path = require('path');

// Mock the template processor
const templateProcessor = {
  processTemplate: (template, data) => {
    // Simple variable replacement for testing
    let processed = template;
    
    // Replace {{variable}} with data.variable
    const variableRegex = /\{\{([^}]+)\}\}/g;
    processed = processed.replace(variableRegex, (match, path) => {
      const parts = path.trim().split('.');
      let value = data;
      
      for (const part of parts) {
        if (value === undefined || value === null) return match;
        value = value[part];
      }
      
      return value !== undefined ? value : match;
    });
    
    return processed;
  },
  
  getValueByPath: (obj, path) => {
    if (!obj || !path) return undefined;
    
    const parts = path.split('.');
    let current = obj;
    
    for (const part of parts) {
      if (current === null || current === undefined) return undefined;
      current = current[part];
    }
    
    return current;
  },
  
  getFallbackValue: (path, projectType) => {
    // Project-type specific fallbacks
    const projectTypeFallbacks = {
      'game': {
        'description': 'A collaborative game development project',
        'overview': 'A game where players interact with engaging gameplay mechanics and systems',
        'core_feature_1': 'Core Gameplay Mechanics',
        'core_feature_2': 'Visual and Audio Elements',
        'core_feature_3': 'User Interface and Experience',
        'core_feature_4': 'Content and Progression',
        'development_term': 'Development',
        'participant_term': 'Players',
        'content_term': 'Game Content'
      },
      'music': {
        'description': 'A collaborative music production project',
        'overview': 'A music project focused on creating original compositions and recordings',
        'core_feature_1': 'Composition and Arrangement',
        'core_feature_2': 'Recording and Production',
        'core_feature_3': 'Mixing and Mastering',
        'core_feature_4': 'Distribution and Promotion',
        'development_term': 'Production',
        'participant_term': 'Listeners',
        'content_term': 'Audio Content'
      }
    };
    
    // Check if we have a project-type specific fallback
    if (projectType && projectTypeFallbacks[projectType.toLowerCase()] && 
        projectTypeFallbacks[projectType.toLowerCase()][path]) {
      return projectTypeFallbacks[projectType.toLowerCase()][path];
    }
    
    // Common fallbacks
    const commonFallbacks = {
      'name': 'Untitled Project',
      'description': 'A collaborative project',
      'company_name': 'Project Owner'
    };
    
    // Check if we have a common fallback
    if (commonFallbacks[path]) {
      return commonFallbacks[path];
    }
    
    return '[Not specified]';
  }
};

// Import the fallback content functions
const { 
  getDefaultProjectOverview, 
  getDefaultTechnicalRequirements, 
  getDefaultProjectPhases, 
  getDefaultMilestones 
} = require('./client/src/utils/agreement/fallbackContent');

// Import the agreement generator functions
const { 
  generateAgreement, 
  generateExhibitI, 
  generateExhibitII, 
  removeSpecificInformation 
} = require('./client/src/utils/agreement/agreementGenerator');

// Mock projects for testing
const mockProjects = [
  {
    name: 'Dream Project',
    description: 'A collaborative game development project',
    project_type: 'game',
    company_name: 'Dream Team',
    features: [
      { title: 'Core Gameplay', description: 'Engaging gameplay mechanics' },
      { title: 'Visual Design', description: 'Stunning visuals and effects' },
      { title: 'Audio', description: 'Immersive sound design' },
      { title: 'Multiplayer', description: 'Cooperative and competitive modes' }
    ]
  },
  {
    name: 'Symphony',
    description: 'A collaborative music production project',
    project_type: 'music',
    company_name: 'Harmony Studios',
    features: [
      { title: 'Composition', description: 'Original musical compositions' },
      { title: 'Recording', description: 'Professional studio recording' },
      { title: 'Mixing', description: 'Expert audio mixing' },
      { title: 'Distribution', description: 'Digital and physical distribution' }
    ]
  },
  {
    name: 'Screenplay',
    description: 'A collaborative film production project',
    project_type: 'film',
    company_name: 'Vision Films',
    features: [
      { title: 'Script Development', description: 'Compelling storytelling' },
      { title: 'Production', description: 'Professional filming and direction' },
      { title: 'Post-Production', description: 'Editing and visual effects' },
      { title: 'Distribution', description: 'Festival submissions and distribution' }
    ]
  }
];

// Mock contributors for testing
const mockContributors = [
  {
    permission_level: 'Owner',
    display_name: 'John Doe',
    email: '<EMAIL>'
  },
  {
    permission_level: 'Contributor',
    display_name: 'Jane Smith',
    email: '<EMAIL>'
  }
];

// Mock current user for testing
const mockCurrentUser = {
  email: '<EMAIL>',
  user_metadata: {
    full_name: 'Test User'
  }
};

// Test the agreement generation
async function testAgreementGeneration() {
  try {
    console.log('Testing enhanced agreement generation system...\n');
    
    // Read the template file
    const templatePath = './client/public/example-cog-contributor-agreement.md';
    const templateText = fs.readFileSync(templatePath, 'utf8');
    
    // Test with each mock project
    for (const project of mockProjects) {
      console.log(`\nTesting with project: ${project.name} (${project.project_type})`);
      
      // Generate the agreement
      const agreement = generateAgreement(
        templateText,
        project,
        {
          contributors: mockContributors,
          currentUser: mockCurrentUser,
          royaltyModel: null,
          milestones: [],
          fullName: mockCurrentUser.user_metadata.full_name
        }
      );
      
      // Save the generated agreement
      const outputPath = `./test-output-${project.project_type}.md`;
      fs.writeFileSync(outputPath, agreement);
      console.log(`Agreement saved to ${outputPath}`);
      
      // Check for Village references
      const villageReferences = checkForVillageReferences(agreement);
      
      if (villageReferences.length > 0) {
        console.log(`\n⚠️ Found ${villageReferences.length} Village references:`);
        villageReferences.forEach((ref, index) => {
          if (index < 5) { // Only show the first 5 references
            console.log(`\n${index + 1}. Line ${ref.lineNumber}: ${ref.line}`);
            console.log(`   Context: ${ref.context}`);
          }
        });
        
        if (villageReferences.length > 5) {
          console.log(`\n... and ${villageReferences.length - 5} more references`);
        }
      } else {
        console.log(`✓ No Village references found in the agreement!`);
      }
      
      // Check for project-specific content
      const projectTypeContent = checkForProjectTypeContent(agreement, project.project_type);
      console.log(`\nProject-specific content check:`);
      Object.entries(projectTypeContent).forEach(([key, found]) => {
        console.log(`  ${found ? '✓' : '✗'} ${key}`);
      });
    }
    
    console.log('\nTest completed!');
    
  } catch (error) {
    console.error('Error:', error);
  }
}

// Helper function to check for Village references
function checkForVillageReferences(text) {
  if (!text) return [];
  
  const lines = text.split('\n');
  const references = [];
  
  const villagePatterns = [
    /village/i,
    /Village of The Ages/i,
    /Village of the Ages/i,
    /VOTA/i
  ];
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    for (const pattern of villagePatterns) {
      if (pattern.test(line)) {
        // Get context (1 line before and after)
        const start = Math.max(0, i - 1);
        const end = Math.min(lines.length - 1, i + 1);
        const context = lines.slice(start, end + 1).join('\n');
        
        references.push({
          lineNumber: i + 1,
          line: line,
          context: context
        });
        
        // Only add each line once, even if it matches multiple patterns
        break;
      }
    }
  }
  
  return references;
}

// Helper function to check for project-type specific content
function checkForProjectTypeContent(text, projectType) {
  const projectTypePatterns = {
    'game': {
      'gameplay': /gameplay/i,
      'players': /players/i,
      'game mechanics': /game mechanics/i,
      'game development': /game development/i
    },
    'music': {
      'composition': /composition/i,
      'recording': /recording/i,
      'listeners': /listeners/i,
      'music production': /music production/i
    },
    'film': {
      'production': /production/i,
      'filming': /filming/i,
      'viewers': /viewers/i,
      'film project': /film project/i
    }
  };
  
  const patterns = projectTypePatterns[projectType] || {};
  const results = {};
  
  Object.entries(patterns).forEach(([key, pattern]) => {
    results[key] = pattern.test(text);
  });
  
  return results;
}

// Run the test
testAgreementGeneration();
