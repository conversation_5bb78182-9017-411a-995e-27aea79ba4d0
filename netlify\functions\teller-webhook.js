const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// CORS headers
const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
};

// Webhook event handlers
const handleTransferUpdate = async (webhookData) => {
  try {
    const { transfer_id, transfer_status, failure_reason } = webhookData;
    
    console.log('Processing transfer update:', { transfer_id, transfer_status });
    
    // Update transaction status in database
    const { data: transaction, error: updateError } = await supabase
      .from('financial_transactions')
      .update({ 
        status: mapTellerStatusToInternal(transfer_status),
        updated_at: new Date().toISOString()
      })
      .eq('reference_number', transfer_id)
      .select()
      .single();

    if (updateError) {
      console.error('Failed to update transaction:', updateError.message);
      return false;
    }

    // Send notification to user if transaction completed or failed
    if (transfer_status === 'posted' || transfer_status === 'failed') {
      await sendTransactionNotification(transaction, transfer_status, failure_reason);
    }

    console.log('Transfer update processed successfully');
    return true;
  } catch (error) {
    console.error('Handle transfer update error:', error);
    return false;
  }
};

const sendTransactionNotification = async (transaction, status, failureReason = null) => {
  try {
    const notificationData = {
      user_id: transaction.to_user_id,
      type: status === 'posted' ? 'payment_received' : 'payment_failed',
      title: status === 'posted' ? 'Payment Received' : 'Payment Failed',
      message: status === 'posted' 
        ? `You received $${transaction.amount} from ${transaction.from_user_name || 'a user'}`
        : `Payment of $${transaction.amount} failed: ${failureReason || 'Unknown error'}`,
      data: { transaction_id: transaction.id }
    };

    await supabase.from('notifications').insert([notificationData]);
  } catch (error) {
    console.error('Send notification error:', error);
  }
};

const handleAccountUpdate = async (webhookData) => {
  try {
    const { item_id, account_id, new_balance } = webhookData;
    
    console.log('Processing account update:', { item_id, account_id });
    
    // Update account balance if teller_accounts table exists
    // For now, log the event
    console.log('Account balance updated:', new_balance);
    
    return true;
  } catch (error) {
    console.error('Handle account update error:', error);
    return false;
  }
};

const handleItemError = async (webhookData) => {
  try {
    const { item_id, error_code, error_message } = webhookData;
    
    console.log('Processing item error:', { item_id, error_code, error_message });
    
    // Log error and potentially notify user
    // For now, just log it
    console.error('Teller item error:', { item_id, error_code, error_message });
    
    return true;
  } catch (error) {
    console.error('Handle item error error:', error);
    return false;
  }
};

// Map Teller transfer status to internal status
const mapTellerStatusToInternal = (tellerStatus) => {
  const statusMap = {
    'pending': 'processing',
    'posted': 'completed',
    'cancelled': 'cancelled',
    'failed': 'failed',
    'returned': 'failed',
    'reversed': 'reversed'
  };
  
  return statusMap[tellerStatus] || 'processing';
};

const handleTransactionUpdate = async (webhookData) => {
  try {
    const { transaction_id, status, amount, account_id } = webhookData;
    
    console.log('Processing transaction update:', { transaction_id, status });
    
    // Update transaction in database
    const { error } = await supabase
      .from('financial_transactions')
      .update({ 
        status: mapTellerStatusToInternal(status),
        updated_at: new Date().toISOString()
      })
      .eq('teller_transaction_id', transaction_id);

    if (error) {
      console.error('Failed to update transaction:', error.message);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Handle transaction update error:', error);
    return false;
  }
};

const handleAuthUpdate = async (webhookData) => {
  try {
    const { item_id, account_id, new_accounts } = webhookData;
    
    console.log('Processing auth update:', { item_id, account_id });
    
    // Handle account authentication updates
    // This could involve re-verifying accounts or updating permissions
    
    return true;
  } catch (error) {
    console.error('Handle auth update error:', error);
    return false;
  }
};

const handleLiabilitiesUpdate = async (webhookData) => {
  try {
    const { item_id, account_id } = webhookData;
    
    console.log('Processing liabilities update:', { item_id, account_id });
    
    // Handle credit card or loan account updates
    
    return true;
  } catch (error) {
    console.error('Handle liabilities update error:', error);
    return false;
  }
};

const verifyWebhookSignature = (body, signature) => {
  // TODO: Implement webhook signature verification for production
  // This should verify the webhook came from Teller
  return true;
};

// Main webhook handler
exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    // Parse webhook data
    const webhookData = JSON.parse(event.body);
    const { webhook_type, webhook_code } = webhookData;

    console.log('Received Teller webhook:', { webhook_type, webhook_code });

    // Verify webhook signature (in production)
    // const signature = event.headers['teller-verification'];
    // if (!verifyWebhookSignature(event.body, signature)) {
    //   return {
    //     statusCode: 401,
    //     headers,
    //     body: JSON.stringify({ error: 'Invalid signature' })
    //   };
    // }

    // Log webhook for debugging
    try {
      await supabase
        .from('teller_webhooks')
        .insert([{
          webhook_type,
          webhook_code,
          item_id: webhookData.item_id || null,
          webhook_data: webhookData,
          processed: false
        }]);
    } catch (logError) {
      console.warn('Failed to log webhook:', logError.message);
    }

    let processed = false;

    // Route webhook to appropriate handler
    switch (webhook_type) {
      case 'TRANSACTIONS':
        switch (webhook_code) {
          case 'SYNC_UPDATES_AVAILABLE':
          case 'DEFAULT_UPDATE':
          case 'INITIAL_UPDATE':
            processed = await handleTransactionUpdate(webhookData);
            break;
          default:
            console.log('Unhandled transaction webhook code:', webhook_code);
            processed = true; // Mark as processed to avoid retries
        }
        break;

      case 'TRANSFER':
        switch (webhook_code) {
          case 'TRANSFER_EVENTS_UPDATE':
            processed = await handleTransferUpdate(webhookData);
            break;
          default:
            console.log('Unhandled transfer webhook code:', webhook_code);
            processed = true;
        }
        break;

      case 'AUTH':
        switch (webhook_code) {
          case 'AUTOMATICALLY_VERIFIED':
          case 'VERIFICATION_EXPIRED':
            processed = await handleAuthUpdate(webhookData);
            break;
          default:
            console.log('Unhandled auth webhook code:', webhook_code);
            processed = true;
        }
        break;

      case 'ACCOUNTS':
        switch (webhook_code) {
          case 'DEFAULT_UPDATE':
            processed = await handleAccountUpdate(webhookData);
            break;
          default:
            console.log('Unhandled accounts webhook code:', webhook_code);
            processed = true;
        }
        break;

      case 'ITEM':
        switch (webhook_code) {
          case 'ERROR':
          case 'PENDING_EXPIRATION':
            processed = await handleItemError(webhookData);
            break;
          default:
            console.log('Unhandled item webhook code:', webhook_code);
            processed = true;
        }
        break;

      case 'LIABILITIES':
        processed = await handleLiabilitiesUpdate(webhookData);
        break;

      default:
        console.log('Unhandled webhook type:', webhook_type);
        processed = true; // Mark as processed to avoid retries
    }

    // Update webhook processing status
    if (processed) {
      try {
        await supabase
          .from('teller_webhooks')
          .update({ 
            processed: true,
            processed_at: new Date().toISOString()
          })
          .eq('webhook_data', webhookData);
      } catch (updateError) {
        console.warn('Failed to update webhook status:', updateError.message);
      }
    }

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ 
        success: true, 
        processed,
        webhook_type,
        webhook_code 
      })
    };

  } catch (error) {
    console.error('Webhook processing error:', error);
    
    // Log failed webhook processing
    try {
      await supabase
        .from('teller_webhooks')
        .update({ 
          processed: false,
          processing_error: error.message,
          processed_at: new Date().toISOString()
        })
        .eq('webhook_data', JSON.parse(event.body));
    } catch (logError) {
      console.warn('Failed to log webhook error:', logError.message);
    }

    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Webhook processing failed' })
    };
  }
};
