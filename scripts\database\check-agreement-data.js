// Script to check agreement data in the database
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MjA1NTksImV4cCI6MjA1OTM5NjU1OX0.e0oaDNVbA563SZJHPm6UTFOXcLbWorKn42a_brChpKQ';
const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log('Checking database for agreement issues...');

    // 1. First, check all contributor agreements for Village references
    console.log('\n1. Checking all contributor agreements for Village references...');
    const { data: agreements, error: agreementsError } = await supabase
      .from('contributor_agreements')
      .select('*, project:project_id(id, name, project_type)');

    if (agreementsError) {
      throw agreementsError;
    }

    if (!agreements || agreements.length === 0) {
      console.log('No contributor agreements found.');
      return;
    }

    console.log(`Found ${agreements.length} total contributor agreements.`);

    // Filter for agreements with Village references
    const villageAgreements = agreements.filter(agreement =>
      agreement.agreement_text &&
      agreement.agreement_text.toLowerCase().includes('village')
    );

    if (villageAgreements.length === 0) {
      console.log('No agreements with Village references found.');
      return;
    }

    console.log(`Found ${villageAgreements.length} agreements with Village references:`);

    // Group by project
    const projectGroups = {};
    villageAgreements.forEach(agreement => {
      const projectId = agreement.project_id;
      if (!projectGroups[projectId]) {
        projectGroups[projectId] = {
          project: agreement.project,
          agreements: []
        };
      }
      projectGroups[projectId].agreements.push(agreement);
    });

    // Display results by project
    for (const projectId in projectGroups) {
      const group = projectGroups[projectId];
      const project = group.project;
      console.log(`\nProject: ${project.name} (${project.id}), Type: ${project.project_type}`);
      console.log(`Found ${group.agreements.length} agreements with Village references:`);

      for (const agreement of group.agreements) {
        console.log(`- Agreement ID: ${agreement.id}, Version: ${agreement.version}, Status: ${agreement.status}`);

        // Extract snippets with Village references
        const lines = agreement.agreement_text.split('\n');
        const villageLines = [];

        for (let i = 0; i < lines.length; i++) {
          if (lines[i].toLowerCase().includes('village')) {
            const start = Math.max(0, i - 1);
            const end = Math.min(lines.length - 1, i + 1);
            const snippet = lines.slice(start, end + 1).map((line, idx) =>
              `    ${start + idx === i ? '→' : ' '} ${line}`
            ).join('\n');
            villageLines.push(snippet);
          }
        }

        if (villageLines.length > 0) {
          console.log('  Village references found in these sections:');
          console.log(villageLines.slice(0, 5).join('\n')); // Show first 5 occurrences
          if (villageLines.length > 5) {
            console.log(`  ... and ${villageLines.length - 5} more occurrences`);
          }
        }
      }

      // 2. Check project milestones
      console.log(`\n2. Checking milestones for project: ${project.name} (${project.id})`);

      // Check project_milestones table
      const { data: milestones, error: milestonesError } = await supabase
        .from('project_milestones')
        .select('*')
        .eq('project_id', project.id);

      if (milestonesError) {
        console.error(`Error fetching milestones for ${project.name}:`, milestonesError);
        continue;
      }

      if (!milestones || milestones.length === 0) {
        console.log(`No milestones found for ${project.name} in project_milestones table.`);

        // Try the milestones table
        const { data: altMilestones, error: altMilestonesError } = await supabase
          .from('milestones')
          .select('*')
          .eq('project_id', project.id);

        if (altMilestonesError) {
          console.error(`Error fetching from milestones table for ${project.name}:`, altMilestonesError);
          continue;
        }

        if (!altMilestones || altMilestones.length === 0) {
          console.log(`No milestones found for ${project.name} in milestones table either.`);
        } else {
          console.log(`Found ${altMilestones.length} milestones in milestones table:`);
          altMilestones.forEach(milestone => {
            console.log(`- ID: ${milestone.id}, Name: ${milestone.name}, Description: ${milestone.description}`);
            // Check for Village Sim references
            const hasVillageRef =
              (milestone.name && milestone.name.toLowerCase().includes('village')) ||
              (milestone.description && milestone.description.toLowerCase().includes('village'));

            if (hasVillageRef) {
              console.log(`  ⚠️ FOUND VILLAGE REFERENCE in milestone: ${milestone.id}`);
            }
          });
        }
      } else {
        console.log(`Found ${milestones.length} milestones in project_milestones table:`);
        milestones.forEach(milestone => {
          console.log(`- ID: ${milestone.id}, Name: ${milestone.name}, Description: ${milestone.description}`);
          // Check for Village Sim references
          const hasVillageRef =
            (milestone.name && milestone.name.toLowerCase().includes('village')) ||
            (milestone.description && milestone.description.toLowerCase().includes('village'));

          if (hasVillageRef) {
            console.log(`  ⚠️ FOUND VILLAGE REFERENCE in milestone: ${milestone.id}`);
          }
        });
      }
    }

    console.log('\nDatabase check completed.');

  } catch (error) {
    console.error('Error:', error);
  }
}

main();
