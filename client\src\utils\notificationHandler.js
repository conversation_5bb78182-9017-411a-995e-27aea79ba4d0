// Notification handler utility
import { supabase } from '../../utils/supabase/supabase.utils';

/**
 * Client-side utility for working with notifications
 * Note: Actual notification creation is handled by database triggers
 * This file provides utility functions for working with notifications on the client side
 */

/**
 * Mark a notification as read
 * @param {string} notificationId - The ID of the notification to mark as read
 * @returns {Promise} - The result of the operation
 */
export async function markNotificationAsRead(notificationId) {
  try {
    const { data, error } = await supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('id', notificationId);

    if (error) {
      console.error('Error marking notification as read:', error);
      return { success: false, error: error.message };
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error in markNotificationAsRead:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Mark all notifications as read for the current user
 * @returns {Promise} - The result of the operation
 */
export async function markAllNotificationsAsRead() {
  try {
    const { data: user } = await supabase.auth.getUser();

    if (!user) {
      return { success: false, error: 'User not authenticated' };
    }

    const { data, error } = await supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('user_id', user.user.id)
      .eq('is_read', false);

    if (error) {
      console.error('Error marking all notifications as read:', error);
      return { success: false, error: error.message };
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error in markAllNotificationsAsRead:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Get notification types for contribution validations
 * @returns {Object} - Object containing notification types
 */
export const NOTIFICATION_TYPES = {
  CONTRIBUTION_APPROVED: 'contribution_approved',
  CONTRIBUTION_REJECTED: 'contribution_rejected',
  CONTRIBUTION_CHANGES_REQUESTED: 'contribution_changes_requested',
  CONTRIBUTION_STATUS_UPDATE: 'contribution_status_update'
};

export default {
  markNotificationAsRead,
  markAllNotificationsAsRead,
  NOTIFICATION_TYPES
};
