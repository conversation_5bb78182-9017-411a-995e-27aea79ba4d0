// Authenticated Alliance Testing
// Day 3 - Proper authentication testing

import { test, expect } from '@playwright/test';

const SITE_URL = 'https://royalty.technology';

test.describe('Authenticated Alliance Testing', () => {
  test('should test with proper authentication', async ({ page }) => {
    console.log('🔑 Starting authenticated test...');
    
    // Navigate to site
    await page.goto(SITE_URL);
    await page.waitForLoadState('networkidle');
    
    // Check if we need to authenticate
    const emailInput = page.locator('input[type="email"]').first();
    const needsAuth = await emailInput.isVisible();
    
    console.log('Needs authentication:', needsAuth);
    
    if (needsAuth) {
      // Perform authentication
      await emailInput.fill('<EMAIL>');
      await page.fill('input[type="password"]', 'TestPassword123!');
      await page.click('button[type="submit"]');
      
      // Wait for auth to complete
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(5000);
      
      // Check if auth succeeded
      const stillNeedsAuth = await page.locator('input[type="email"]').isVisible();
      console.log('Authentication successful:', !stillNeedsAuth);
    }
    
    // Now test teams page
    console.log('🏰 Testing teams page...');
    await page.goto(`${SITE_URL}/teams`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    const content = await page.textContent('body');
    console.log('Teams page content length:', content.length);
    console.log('Content preview:', content.substring(0, 400));
    
    // Check for team-related content
    const hasTeamContent = content.toLowerCase().includes('team') || 
                          content.toLowerCase().includes('alliance') ||
                          content.toLowerCase().includes('create') ||
                          content.toLowerCase().includes('manage');
    
    console.log('Has team/alliance content:', hasTeamContent);
    
    // Test alliance management route
    console.log('⚔️ Testing alliance management...');
    await page.goto(`${SITE_URL}/teams/test-id/manage`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    const manageContent = await page.textContent('body');
    console.log('Management page content length:', manageContent.length);
    
    // Look for our alliance components
    const allianceIndicators = [
      'Alliance Information',
      'Business Entity', 
      'Alliance Permissions',
      'Manage Alliance',
      'Register Business'
    ];
    
    console.log('🔍 Checking for alliance management features...');
    for (const indicator of allianceIndicators) {
      const found = manageContent.includes(indicator);
      console.log(`"${indicator}": ${found}`);
    }
    
    // Check for CSS classes
    const cssClasses = [
      'alliance-manage-container',
      'business-entity-section',
      'alliance-permissions-section'
    ];
    
    console.log('🎨 Checking for CSS classes...');
    for (const className of cssClasses) {
      const hasClass = await page.locator(`.${className}`).count() > 0;
      console.log(`"${className}": ${hasClass}`);
    }
    
    // Test experimental navigation
    console.log('🎮 Testing experimental navigation...');
    await page.goto(SITE_URL);
    await page.waitForLoadState('networkidle');
    
    const hasCanvas = await page.locator('canvas').isVisible();
    console.log('Has experimental navigation canvas:', hasCanvas);
    
    if (hasCanvas) {
      // Test keyboard navigation
      await page.keyboard.press('ArrowDown');
      await page.waitForTimeout(1000);
      await page.keyboard.press('ArrowDown');
      await page.waitForTimeout(1000);
      
      // Look for teams card
      const teamsCard = page.locator('text=Teams').first();
      const teamsVisible = await teamsCard.isVisible();
      console.log('Teams card visible in grid view:', teamsVisible);
      
      if (teamsVisible) {
        await teamsCard.click();
        await page.waitForLoadState('networkidle');
        console.log('Navigated to:', page.url());
      }
    }
  });
});
