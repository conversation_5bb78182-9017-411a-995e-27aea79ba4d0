-- Create RPC Functions for Retro Profile System
-- This script creates RPC functions to handle the data fetching for the Retro Profile system
-- Run this in the Supabase SQL Editor

-- Function to get profile comments with author details
CREATE OR REPLACE FUNCTION get_profile_comments(
    profile_id_param UUID,
    limit_param INTEGER DEFAULT 10,
    offset_param INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    profile_id UUID,
    author_id UUID,
    content TEXT,
    is_approved BOOLEAN,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    author_display_name TEXT,
    author_avatar_url TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        pc.id,
        pc.profile_id,
        pc.author_id,
        pc.content,
        pc.is_approved,
        pc.created_at,
        pc.updated_at,
        u.display_name as author_display_name,
        u.avatar_url as author_avatar_url
    FROM
        public.profile_comments pc
    LEFT JOIN
        public.users u ON pc.author_id = u.id
    WHERE
        pc.profile_id = profile_id_param
        AND pc.is_approved = true
    ORDER BY
        pc.created_at DESC
    LIMIT limit_param
    OFFSET offset_param;
END;
$$;

-- Function to get top collaborators with user details
CREATE OR REPLACE FUNCTION get_top_collaborators(
    user_id_param UUID
)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    collaborator_id UUID,
    display_order INTEGER,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    collaborator_display_name TEXT,
    collaborator_avatar_url TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        tc.id,
        tc.user_id,
        tc.collaborator_id,
        tc.display_order,
        tc.created_at,
        tc.updated_at,
        u.display_name as collaborator_display_name,
        u.avatar_url as collaborator_avatar_url
    FROM
        public.top_collaborators tc
    LEFT JOIN
        public.users u ON tc.collaborator_id = u.id
    WHERE
        tc.user_id = user_id_param
    ORDER BY
        tc.display_order ASC;
END;
$$;

-- Grant permissions on the functions
GRANT EXECUTE ON FUNCTION get_profile_comments TO authenticated;
GRANT EXECUTE ON FUNCTION get_top_collaborators TO authenticated;

-- Done! These RPC functions can now be used to fetch the data with the relationships.
