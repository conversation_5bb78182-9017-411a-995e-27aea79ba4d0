// Emergency Debug Test
const fetch = require('node-fetch');

const SITE_URL = 'https://royalty.technology';

async function emergencyDebug() {
  console.log('🚨 Emergency debugging - checking what broke...\n');
  
  const testUrls = [
    `${SITE_URL}/`,
    `${SITE_URL}/?test_mode=true`,
    `${SITE_URL}/teams`,
    `${SITE_URL}/teams?test_mode=true`,
    `${SITE_URL}/track`,
    `${SITE_URL}/track?test_mode=true`
  ];
  
  for (const url of testUrls) {
    try {
      console.log(`Testing: ${url}`);
      
      const response = await fetch(url);
      const html = await response.text();
      
      console.log(`Status: ${response.status}`);
      console.log(`Content length: ${html.length}`);
      console.log(`Content preview: "${html.substring(0, 200)}"`);
      
      // Check for specific patterns
      const hasHTML = html.includes('<html');
      const hasReact = html.includes('id="root"');
      const hasScript = html.includes('<script');
      const hasError = html.includes('error') || html.includes('Error');
      
      console.log(`  Has HTML: ${hasHTML}`);
      console.log(`  Has React root: ${hasReact}`);
      console.log(`  Has scripts: ${hasScript}`);
      console.log(`  Has errors: ${hasError}`);
      
      console.log('---\n');
      
    } catch (error) {
      console.log(`❌ Error: ${error.message}\n`);
    }
  }
}

emergencyDebug();
