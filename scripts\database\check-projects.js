// Script to check all projects and their agreements
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MjA1NTksImV4cCI6MjA1OTM5NjU1OX0.e0oaDNVbA563SZJHPm6UTFOXcLbWorKn42a_brChpKQ';
const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log('Checking all projects and their agreements...\n');
    
    // Get all projects
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('*');
    
    if (projectsError) {
      throw projectsError;
    }
    
    if (!projects || projects.length === 0) {
      console.log('No projects found in the database.');
      return;
    }
    
    console.log(`Found ${projects.length} projects in the database.`);
    
    // Check each project
    for (const project of projects) {
      console.log(`\n-----------------------------------------`);
      console.log(`Project: ${project.name} (ID: ${project.id})`);
      console.log(`Type: ${project.project_type}`);
      console.log(`Description: ${project.description || 'No description'}`);
      
      // Get project milestones
      const { data: milestones, error: milestonesError } = await supabase
        .from('project_milestones')
        .select('*')
        .eq('project_id', project.id);
      
      if (milestonesError) {
        console.error(`Error fetching milestones:`, milestonesError);
      } else {
        console.log(`\nMilestones: ${milestones?.length || 0}`);
        if (milestones && milestones.length > 0) {
          milestones.forEach((milestone, index) => {
            console.log(`  ${index + 1}. ${milestone.name}: ${milestone.description || 'No description'}`);
            
            // Check for Village references
            if ((milestone.name && milestone.name.toLowerCase().includes('village')) || 
                (milestone.description && milestone.description.toLowerCase().includes('village'))) {
              console.log(`    ⚠️ CONTAINS VILLAGE REFERENCE`);
            }
          });
        }
      }
      
      // Get agreements
      const { data: agreements, error: agreementsError } = await supabase
        .from('contributor_agreements')
        .select('*')
        .eq('project_id', project.id);
      
      if (agreementsError) {
        console.error(`Error fetching agreements:`, agreementsError);
      } else {
        console.log(`\nAgreements: ${agreements?.length || 0}`);
        if (agreements && agreements.length > 0) {
          agreements.forEach((agreement, index) => {
            console.log(`  ${index + 1}. Agreement ID: ${agreement.id}, Version: ${agreement.version || 1}`);
            
            // Check for Village references
            if (agreement.agreement_text && agreement.agreement_text.toLowerCase().includes('village')) {
              console.log(`    ⚠️ CONTAINS VILLAGE REFERENCE`);
              
              // Find the context of the reference
              const lines = agreement.agreement_text.split('\n');
              const villageLines = [];
              
              for (let i = 0; i < lines.length; i++) {
                if (lines[i].toLowerCase().includes('village')) {
                  const start = Math.max(0, i - 1);
                  const end = Math.min(lines.length - 1, i + 1);
                  const context = lines.slice(start, end + 1).join('\n');
                  villageLines.push(`    Context (line ${i + 1}):\n      ${context.replace(/\n/g, '\n      ')}`);
                  
                  // Only show the first few occurrences
                  if (villageLines.length >= 3) {
                    villageLines.push(`    ... and more occurrences`);
                    break;
                  }
                }
              }
              
              console.log(villageLines.join('\n'));
            }
          });
        }
      }
    }
    
    console.log('\nDatabase check completed.');
    
  } catch (error) {
    console.error('Error:', error);
  }
}

main();
