import React, { useState, useEffect, useContext } from 'react';
import { useParams } from 'react-router-dom';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { toast } from 'react-hot-toast';
import LoadingAnimation from '../../components/layout/LoadingAnimation';
import ProfileHeader from '../../components/profile/ProfileHeader';
import ProfileSidebar from '../../components/profile/ProfileSidebar';
import ProfileSongPlayer from '../../components/profile/ProfileSongPlayer';
import ProfileComments from '../../components/profile/ProfileComments';
import UserSkillsList from '../../components/skills/UserSkillsList';
import ThemeCustomizer from '../../components/profile/ThemeCustomizer';
import SkillForm from '../../components/skills/SkillForm';
import VerificationForm from '../../components/skills/VerificationForm';
import {
  getUserProfile,
  updateUserProfile,
  uploadProfileImage,
  uploadProfileSong,
  recordProfileView,
  getTopCollaborators
} from '../../utils/profile/profile.utils';
import {
  applyTheme,
  loadThemeFonts
} from '../../utils/profile/theme.utils';

const RetroProfilePage = () => {
  const { userId } = useParams();
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [profile, setProfile] = useState(null);
  const [isOwnProfile, setIsOwnProfile] = useState(false);
  const [topCollaborators, setTopCollaborators] = useState([]);
  const [topSkills, setTopSkills] = useState([]);

  // Modal states
  const [showThemeCustomizer, setShowThemeCustomizer] = useState(false);
  const [showSkillForm, setShowSkillForm] = useState(false);
  const [showVerificationForm, setShowVerificationForm] = useState(false);
  const [selectedUserSkill, setSelectedUserSkill] = useState(null);

  // Edit states
  const [editingAvatar, setEditingAvatar] = useState(false);
  const [editingCover, setEditingCover] = useState(false);
  const [editingStatus, setEditingStatus] = useState(false);
  const [editingSong, setEditingSong] = useState(false);
  const [newStatus, setNewStatus] = useState('');
  const [newSongFile, setNewSongFile] = useState(null);

  // Load profile data
  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        setLoading(true);

        // Determine which profile to load
        const profileId = userId || currentUser?.id;

        if (!profileId) {
          toast.error('No profile ID provided');
          return;
        }

        // Check if this is the user's own profile
        setIsOwnProfile(currentUser && currentUser.id === profileId);

        // Fetch profile data
        const profileData = await getUserProfile(profileId);
        setProfile(profileData);

        // Fetch top collaborators
        const collaborators = await getTopCollaborators(profileId);
        setTopCollaborators(collaborators);

        // Record profile view if not own profile
        if (currentUser && currentUser.id !== profileId) {
          recordProfileView(profileId, currentUser.id);
        }

        // Apply theme
        if (profileData.theme_settings) {
          applyTheme(profileData.theme_settings, profileData.custom_css);
          loadThemeFonts(profileData.theme_settings);
        }

        // TODO: Fetch top skills
        // For now, use placeholder data
        setTopSkills([
          {
            id: '1',
            name: 'Unity Development',
            proficiency_score: 80,
            verification_level: 4,
            verification_label: 'Expert Verified',
            verification_icon: 'bi-award-fill'
          },
          {
            id: '2',
            name: 'C# Programming',
            proficiency_score: 90,
            verification_level: 5,
            verification_label: 'Industry Recognized',
            verification_icon: 'bi-trophy-fill'
          },
          {
            id: '3',
            name: '3D Modeling',
            proficiency_score: 60,
            verification_level: 3,
            verification_label: 'Project Verified',
            verification_icon: 'bi-briefcase-fill'
          }
        ]);
      } catch (error) {
        console.error('Error fetching profile data:', error);
        toast.error('Failed to load profile data');
      } finally {
        setLoading(false);
      }
    };

    fetchProfileData();

    // Clean up theme when component unmounts
    return () => {
      const existingStyle = document.getElementById('profile-theme-style');
      if (existingStyle) {
        existingStyle.remove();
      }

      const existingFonts = document.getElementById('profile-theme-fonts');
      if (existingFonts) {
        existingFonts.remove();
      }
    };
  }, [userId, currentUser]);

  // Handle profile image upload
  const handleProfileImageUpload = async (type, file) => {
    if (!file || !currentUser || !isOwnProfile) return;

    try {
      const imageUrl = await uploadProfileImage(currentUser.id, file, type);

      if (imageUrl) {
        // Update profile state
        setProfile({
          ...profile,
          [type === 'avatar' ? 'avatar_url' : 'cover_image_url']: imageUrl
        });

        // Update in database
        await updateUserProfile(currentUser.id, {
          [type === 'avatar' ? 'avatar_url' : 'cover_image_url']: imageUrl
        });

        toast.success(`${type === 'avatar' ? 'Profile picture' : 'Cover image'} updated successfully`);
      }
    } catch (error) {
      console.error(`Error uploading ${type}:`, error);
      toast.error(`Failed to upload ${type === 'avatar' ? 'profile picture' : 'cover image'}`);
    } finally {
      setEditingAvatar(false);
      setEditingCover(false);
    }
  };

  // Handle status update
  const handleStatusUpdate = async () => {
    if (!currentUser || !isOwnProfile || !newStatus.trim()) return;

    try {
      // Update profile state
      setProfile({
        ...profile,
        status_message: newStatus.trim()
      });

      // Update in database
      await updateUserProfile(currentUser.id, {
        status_message: newStatus.trim()
      });

      toast.success('Status updated successfully');
      setEditingStatus(false);
      setNewStatus('');
    } catch (error) {
      console.error('Error updating status:', error);
      toast.error('Failed to update status');
    }
  };

  // Handle song upload
  const handleSongUpload = async () => {
    if (!newSongFile || !currentUser || !isOwnProfile) return;

    try {
      const songUrl = await uploadProfileSong(currentUser.id, newSongFile);

      if (songUrl) {
        // Update profile state
        setProfile({
          ...profile,
          profile_song_url: songUrl
        });

        // Update in database
        await updateUserProfile(currentUser.id, {
          profile_song_url: songUrl
        });

        toast.success('Profile song updated successfully');
      }
    } catch (error) {
      console.error('Error uploading song:', error);
      toast.error('Failed to upload profile song');
    } finally {
      setEditingSong(false);
      setNewSongFile(null);
    }
  };

  // Handle theme save
  const handleThemeSave = async (themeSettings) => {
    if (!currentUser || !isOwnProfile) return;

    try {
      // Apply theme
      applyTheme(themeSettings, themeSettings.customCss);
      loadThemeFonts(themeSettings);

      // Update profile state
      setProfile({
        ...profile,
        theme_settings: {
          theme: themeSettings.theme,
          colors: themeSettings.colors,
          fonts: themeSettings.fonts,
          layout: themeSettings.layout
        },
        custom_css: themeSettings.customCss
      });

      // Update in database
      await updateUserProfile(currentUser.id, {
        theme_settings: {
          theme: themeSettings.theme,
          colors: themeSettings.colors,
          fonts: themeSettings.fonts,
          layout: themeSettings.layout
        },
        custom_css: themeSettings.customCss
      });

      toast.success('Theme updated successfully');
    } catch (error) {
      console.error('Error updating theme:', error);
      toast.error('Failed to update theme');
    }
  };

  // Handle edit profile section
  const handleEditSection = (section) => {
    switch (section) {
      case 'theme':
        setShowThemeCustomizer(true);
        break;
      case 'about':
        // TODO: Implement about section editing
        toast.info('About section editing coming soon');
        break;
      case 'social':
        // TODO: Implement social links editing
        toast.info('Social links editing coming soon');
        break;
      case 'skills':
        setShowSkillForm(true);
        break;
      case 'collaborators':
        // TODO: Implement collaborators editing
        toast.info('Collaborators editing coming soon');
        break;
      default:
        break;
    }
  };

  // Handle edit profile
  const handleEditProfile = (type) => {
    switch (type) {
      case 'avatar':
        setEditingAvatar(true);
        document.getElementById('avatar-upload').click();
        break;
      case 'cover':
        setEditingCover(true);
        document.getElementById('cover-upload').click();
        break;
      case 'profile':
        // TODO: Implement full profile editing
        toast.info('Profile editing coming soon');
        break;
      default:
        break;
    }
  };

  // Handle avatar file selection
  const handleAvatarFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      handleProfileImageUpload('avatar', file);
    }
  };

  // Handle cover file selection
  const handleCoverFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      handleProfileImageUpload('cover', file);
    }
  };

  // Handle song file selection
  const handleSongFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setNewSongFile(file);
      handleSongUpload();
    }
  };

  if (loading) {
    return <LoadingAnimation />;
  }

  if (!profile) {
    return (
      <div className="profile-not-found">
        <h2>Profile Not Found</h2>
        <p>The profile you're looking for doesn't exist or you don't have permission to view it.</p>
      </div>
    );
  }

  return (
    <div className="retro-profile-page">
      <div className="container">
        {/* Hidden file inputs */}
        <input
          type="file"
          id="avatar-upload"
          accept="image/*"
          style={{ display: 'none' }}
          onChange={handleAvatarFileChange}
        />
        <input
          type="file"
          id="cover-upload"
          accept="image/*"
          style={{ display: 'none' }}
          onChange={handleCoverFileChange}
        />
        <input
          type="file"
          id="song-upload"
          accept="audio/*"
          style={{ display: 'none' }}
          onChange={handleSongFileChange}
        />

        {/* Profile Song Player */}
        {profile.profile_song_url && (
          <ProfileSongPlayer
            songUrl={profile.profile_song_url}
            songTitle={profile.song_title}
            isOwnProfile={isOwnProfile}
            onChangeSong={() => document.getElementById('song-upload').click()}
          />
        )}

        {/* Profile Header */}
        <ProfileHeader
          profile={profile}
          isOwnProfile={isOwnProfile}
          onEditProfile={handleEditProfile}
          onEditStatus={() => setEditingStatus(true)}
        />

        {/* Main Content */}
        <div className="profile-content">
          {/* Sidebar */}
          <div className="profile-sidebar-container">
            <ProfileSidebar
              profile={profile}
              topCollaborators={topCollaborators}
              topSkills={topSkills}
              isOwnProfile={isOwnProfile}
              onEditSection={handleEditSection}
            />
          </div>

          {/* Main Content Area */}
          <div className="profile-main-content">
            {/* Skills Section */}
            <div className="profile-main-section">
              <UserSkillsList
                userId={profile.id}
                isOwnProfile={isOwnProfile}
                onAddSkill={() => setShowSkillForm(true)}
              />
            </div>

            {/* Comments Section */}
            <div className="profile-main-section">
              <ProfileComments
                profileId={profile.id}
                currentUser={currentUser}
                isOwnProfile={isOwnProfile}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      <ThemeCustomizer
        show={showThemeCustomizer}
        onHide={() => setShowThemeCustomizer(false)}
        currentTheme={profile.theme_settings}
        onSaveTheme={handleThemeSave}
        isPremium={profile.is_premium}
      />

      <SkillForm
        show={showSkillForm}
        onHide={() => setShowSkillForm(false)}
        userId={currentUser?.id}
        userSkill={selectedUserSkill}
        onSave={() => {
          toast.success('Skill saved successfully');
          setShowSkillForm(false);
        }}
      />

      {selectedUserSkill && (
        <VerificationForm
          show={showVerificationForm}
          onHide={() => setShowVerificationForm(false)}
          userSkill={selectedUserSkill}
          onSave={() => {
            toast.success('Verification added successfully');
            setShowVerificationForm(false);
          }}
        />
      )}
    </div>
  );
};

export default RetroProfilePage;
