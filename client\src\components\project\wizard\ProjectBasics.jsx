import React, { useState, useEffect } from 'react';
import { supabase } from '../../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import DatePicker from 'react-datepicker';
import projectTemplates from '../../../data/project-templates';
import {
  Accordion,
  AccordionItem,
  Card,
  CardBody,
  CardHeader,
  Button,
  Input,
  Textarea,
  Select,
  SelectItem
} from '../../ui/heroui';

const ProjectBasics = ({ projectData, setProjectData }) => {
  const [uploading, setUploading] = useState(false);

  // Project types
  const projectTypes = [
    { value: 'game', label: 'Game' },
    { value: 'app', label: 'App' },
    { value: 'website', label: 'Website' },
    { value: 'plugin', label: 'Plugin/Extension' },
    { value: 'art', label: 'Art/Asset Pack' },
    { value: 'music', label: 'Music/Sound' },
    { value: 'other', label: 'Other' }
  ];

  // State for template selection
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(null);

  // Apply template when project type changes
  useEffect(() => {
    // Only suggest template if this is a new project (name is empty)
    if (projectData.project_type && !projectData.name) {
      setShowTemplateModal(true);
    }
  }, [projectData.project_type]);

  // Apply selected template
  const applyTemplate = (templateKey) => {
    const template = projectTemplates[templateKey];
    if (!template) {
      toast.error('Template not found');
      return;
    }

    // Keep the current project type and any existing data
    const updatedData = {
      ...template,
      project_type: projectData.project_type,
      // Preserve any existing data that shouldn't be overwritten
      thumbnail_url: projectData.thumbnail_url || template.thumbnail_url || '',
      start_date: projectData.start_date || template.start_date || new Date(),
      launch_date: projectData.launch_date || template.launch_date || null
    };

    setProjectData(updatedData);
    setShowTemplateModal(false);
    toast.success(`Applied ${templateKey} template`);
  };

  // Skip template
  const skipTemplate = () => {
    setShowTemplateModal(false);
    toast.info('Template skipped');
  };

  // Handle thumbnail upload
  const handleThumbnailUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file type
    const fileExt = file.name.split('.').pop();
    const allowedExts = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    if (!allowedExts.includes(fileExt.toLowerCase())) {
      toast.error('Invalid file type. Please upload an image file.');
      return;
    }

    // Validate file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      toast.error('File size too large. Maximum size is 2MB.');
      return;
    }

    setUploading(true);
    const uploadToastId = toast.loading('Uploading thumbnail...');

    try {
      // Create a unique file name
      const fileName = `project-${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
      const filePath = `project-thumbnails/${fileName}`;

      // Use the avatars bucket
      const bucketName = 'avatars';

      // Upload to Supabase Storage
      const { data, error: uploadError } = await supabase.storage
        .from(bucketName)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: true
        });

      if (uploadError) {
        // If the error is because the bucket doesn't exist, try to create it
        if (uploadError.message.includes('bucket') && uploadError.message.includes('not found')) {
          toast.error('Storage bucket not found. Please contact an administrator.', { id: uploadToastId });
          setUploading(false);
          return;
        }
        throw new Error(`Upload error: ${uploadError.message}`);
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from(bucketName)
        .getPublicUrl(filePath);

      if (!urlData || !urlData.publicUrl) {
        throw new Error('Failed to get public URL for uploaded file');
      }

      // Update project data
      setProjectData({
        ...projectData,
        thumbnail_url: urlData.publicUrl
      });

      toast.success('Thumbnail uploaded successfully', { id: uploadToastId });
    } catch (error) {
      console.error('Error uploading thumbnail:', error);
      toast.error(`Failed to upload thumbnail: ${error.message}`, { id: uploadToastId });
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="wizard-step-content">
      {/* Template Selection Modal */}
      {showTemplateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-4">
            <CardHeader>
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Use Project Template?</h3>
                <Button
                  variant="light"
                  size="sm"
                  onClick={skipTemplate}
                  aria-label="Close"
                  isIconOnly
                >
                  <i className="bi bi-x-lg"></i>
                </Button>
              </div>
            </CardHeader>
            <CardBody className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Would you like to use a template for your {projectData.project_type} project?
                This will pre-populate settings, milestones, and contribution tracking.
              </p>

              <div className="p-4 bg-muted rounded-md">
                <h4 className="font-medium mb-2">Template includes:</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Pre-configured royalty model</li>
                  <li>• Relevant contribution categories and task types</li>
                  <li>• Standard milestones for {projectData.project_type} projects</li>
                  <li>• Recommended revenue tranches</li>
                </ul>
              </div>

              <div className="flex gap-2 pt-4">
                <Button
                  onClick={() => applyTemplate(projectData.project_type)}
                  className="flex-1"
                >
                  Use Template
                </Button>
                <Button
                  variant="outline"
                  onClick={skipTemplate}
                  className="flex-1"
                >
                  Start from Scratch
                </Button>
              </div>
            </CardBody>
          </Card>
        </div>
      )}

      <h2 className="step-title">Project Basics</h2>
      <p className="step-description">
        Let's start with the basic information about your project.
      </p>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-4">
        <div className="lg:col-span-2 space-y-6">
          <div className="space-y-2">
            <Input
              label="Project Name"
              placeholder="Enter project name"
              value={projectData.name || ""}
              onValueChange={(value) => setProjectData({ ...projectData, name: value })}
              isRequired
              description="Choose a clear, descriptive name for your project."
            />
          </div>

          <div className="space-y-2">
            <Textarea
              label="Description"
              placeholder="Describe your project"
              value={projectData.description || ""}
              onValueChange={(value) => setProjectData({ ...projectData, description: value })}
              minRows={4}
              description="Provide a brief description of what your project is about."
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex gap-2">
                <Select
                  label="Project Type"
                  placeholder="Select project type"
                  selectedKeys={projectData.project_type ? [projectData.project_type] : []}
                  onSelectionChange={(keys) => {
                    const value = Array.from(keys)[0];
                    setProjectData({ ...projectData, project_type: value });
                  }}
                  className="flex-1"
                >
                  {projectTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </Select>

                {projectData.project_type && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowTemplateModal(true)}
                    title="Apply project template"
                  >
                    <i className="bi bi-file-earmark-text mr-2"></i> Template
                  </Button>
                )}
              </div>
              {projectData.project_type && (
                <p className="text-sm text-muted-foreground">
                  <i className="bi bi-info-circle mr-1"></i>
                  You can apply a template for {projectData.project_type} projects.
                </p>
              )}
            </div>

            <div className="space-y-2">
              <div>
                <label className="text-sm font-medium">Start Date</label>
                <DatePicker
                  selected={projectData.start_date ? new Date(projectData.start_date) : new Date()}
                  onChange={(date) => setProjectData({ ...projectData, start_date: date })}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  dateFormat="MMMM d, yyyy"
                />
                <p className="text-sm text-muted-foreground">
                  When did/will this project start?
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <Input
                type="number"
                label="Estimated Duration (months)"
                min={1}
                max={60}
                value={projectData.estimated_duration?.toString() || "6"}
                onValueChange={(value) => setProjectData({
                  ...projectData,
                  estimated_duration: parseInt(value) || 6
                })}
                description="How long do you expect this project to take?"
              />
            </div>

            <div className="space-y-2">
              <div>
                <label className="text-sm font-medium">Launch Date (optional)</label>
                <DatePicker
                  selected={projectData.launch_date ? new Date(projectData.launch_date) : null}
                  onChange={(date) => setProjectData({ ...projectData, launch_date: date })}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  placeholderText="Select launch date"
                  dateFormat="MMMM d, yyyy"
                  minDate={projectData.start_date || new Date()}
                />
                <p className="text-sm text-muted-foreground">
                  When do you plan to launch? (Can be updated later)
                </p>
              </div>
            </div>
          </div>

          {/* Company Information Section */}
          <Accordion className="mt-4 mb-4">
            <AccordionItem
              key="company-info"
              aria-label="Company Information"
              title={
                <div className="flex items-center">
                  <i className="bi bi-building me-2"></i> Company Information
                  <span className="text-muted-foreground ms-2">(Required for Agreement Generation)</span>
                </div>
              }
            >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Input
                      label="Company Name"
                      value={projectData.company_name || ''}
                      onValueChange={(value) => setProjectData({ ...projectData, company_name: value })}
                      placeholder="Enter company name"
                      description="Name of the company that owns the project."
                    />
                  </div>

                  <div className="space-y-2">
                    <Input
                      label="Company Address"
                      value={projectData.company_address || ''}
                      onValueChange={(value) => setProjectData({ ...projectData, company_address: value })}
                      placeholder="Enter company address"
                      description="Full address of the company."
                    />
                  </div>

                  <div className="space-y-2">
                    <Input
                      label="State/Province"
                      value={projectData.company_state || ''}
                      onValueChange={(value) => setProjectData({ ...projectData, company_state: value })}
                      placeholder="Enter state/province"
                    />
                  </div>

                  <div className="space-y-2">
                    <Input
                      label="County/Region"
                      value={projectData.company_county || ''}
                      onValueChange={(value) => setProjectData({ ...projectData, company_county: value })}
                      placeholder="Enter county/region"
                    />
                  </div>
                </div>

                <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
                  <div className="flex items-start">
                    <i className="bi bi-info-circle-fill text-blue-500 mr-2 mt-0.5"></i>
                    <p className="text-sm text-blue-700">
                      This information is required for generating legal agreements. It will be used to identify the company in the contributor agreement.
                    </p>
                  </div>
                </div>
            </AccordionItem>
          </Accordion>

          {/* Project-Specific Fields Section */}
          {projectData.project_type && (
            <Accordion className="mt-4 mb-4">
              <AccordionItem
                key="project-specific"
                aria-label="Project Specific Details"
                title={
                  <div className="flex items-center">
                    <i className="bi bi-gear me-2"></i> {projectData.project_type.charAt(0).toUpperCase() + projectData.project_type.slice(1)} Specific Details
                    <span className="text-muted-foreground ms-2">(Required for Agreement Generation)</span>
                  </div>
                }
              >
                  {projectData.project_type === 'game' && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Input
                          label="Game Engine"
                          value={projectData.engine || ''}
                          onValueChange={(value) => setProjectData({ ...projectData, engine: value })}
                          placeholder="e.g. Unity, Unreal Engine, Godot"
                          description="The game engine or framework used for development."
                        />
                      </div>

                      <div className="space-y-2">
                        <Input
                          label="Platforms"
                          value={projectData.platforms || ''}
                          onValueChange={(value) => setProjectData({ ...projectData, platforms: value })}
                          placeholder="e.g. PC, Mac, Mobile, Console"
                          description="The platforms the game will be released on."
                        />
                      </div>
                    </div>
                  )}

                  {projectData.project_type === 'music' && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Input
                          label="Genre"
                          value={projectData.genre || ''}
                          onValueChange={(value) => setProjectData({ ...projectData, genre: value })}
                          placeholder="e.g. Rock, Pop, Electronic"
                        />
                      </div>

                      <div className="space-y-2">
                        <Input
                          label="Distribution Platforms"
                          value={projectData.distribution_platforms || ''}
                          onValueChange={(value) => setProjectData({ ...projectData, distribution_platforms: value })}
                          placeholder="e.g. Spotify, Apple Music, Bandcamp"
                        />
                      </div>
                    </div>
                  )}

                  {(projectData.project_type === 'app' || projectData.project_type === 'website' || projectData.project_type === 'plugin') && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Input
                          label="Technology Stack"
                          value={projectData.technology_stack || ''}
                          onValueChange={(value) => setProjectData({ ...projectData, technology_stack: value })}
                          placeholder="e.g. React, Node.js, MongoDB"
                        />
                      </div>

                      <div className="space-y-2">
                        <Input
                          label="Platforms"
                          value={projectData.platforms || ''}
                          onValueChange={(value) => setProjectData({ ...projectData, platforms: value })}
                          placeholder="e.g. Web, iOS, Android, Windows"
                        />
                      </div>
                    </div>
                  )}

                  <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
                    <div className="flex items-start">
                      <i className="bi bi-info-circle-fill text-blue-500 mr-2 mt-0.5"></i>
                      <p className="text-sm text-blue-700">
                        These details will be included in the project specifications section of the contributor agreement.
                      </p>
                    </div>
                  </div>
              </AccordionItem>
            </Accordion>
          )}
        </div>

        <div className="space-y-6">
          <div className="space-y-2">
            <label className="text-sm font-medium">Project Thumbnail</label>
            <div className="thumbnail-upload-container">
              {projectData.thumbnail_url ? (
                <div className="thumbnail-preview">
                  <img
                    src={projectData.thumbnail_url}
                    alt="Project thumbnail"
                    className="w-full h-auto rounded"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="thumbnail-remove"
                    onClick={() => setProjectData({ ...projectData, thumbnail_url: '' })}
                  >
                    <i className="bi bi-x-lg"></i>
                  </Button>
                </div>
              ) : (
                <div className="thumbnail-placeholder">
                  <i className="bi bi-image"></i>
                  <span>Upload Thumbnail</span>
                </div>
              )}

              <input
                type="file"
                className="thumbnail-input"
                onChange={handleThumbnailUpload}
                accept="image/*"
                disabled={uploading}
              />

              {uploading && (
                <div className="thumbnail-uploading">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary" role="status">
                    <span className="sr-only">Loading...</span>
                  </div>
                  <span>Uploading...</span>
                </div>
              )}
            </div>
            <p className="text-sm text-muted-foreground">
              Upload a thumbnail image for your project (optional).
              <br />
              Recommended size: 800x600 pixels.
            </p>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Project Privacy</label>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="projectPrivacy"
                checked={projectData.is_public}
                onChange={(e) => setProjectData({ ...projectData, is_public: e.target.checked })}
                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
              />
              <label htmlFor="projectPrivacy" className="text-sm font-normal">
                {projectData.is_public ? 'Public' : 'Private'}
              </label>
            </div>
            <p className="text-sm text-muted-foreground">
              {projectData.is_public
                ? 'Public projects are visible to all users.'
                : 'Private projects are only visible to contributors.'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectBasics;
