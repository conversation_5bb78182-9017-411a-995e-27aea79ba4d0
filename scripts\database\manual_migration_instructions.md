# Manual Migration Instructions for File Attachments

Since we're having issues with the Supabase CLI, you can run the migration manually through the Supabase dashboard. Follow these steps:

## Step 1: Access the SQL Editor

1. Log in to your Supabase dashboard
2. Select your project
3. Click on "SQL Editor" in the left sidebar
4. Click "New Query" to create a new SQL query

## Step 2: Run the Migration SQL

Copy and paste the following SQL into the editor:

```sql
-- Add file attachment fields to contributions table
DO $$
DECLARE
  has_attachments_exists BOOLEAN;
  attachments_url_exists BOOLEAN;
  attachments_data_exists BOOLEAN;
BEGIN
  -- Check if has_attachments column exists
  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'contributions'
    AND column_name = 'has_attachments'
  ) INTO has_attachments_exists;

  -- Check if attachments_url column exists
  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'contributions'
    AND column_name = 'attachments_url'
  ) INTO attachments_url_exists;

  -- Check if attachments_data column exists
  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'contributions'
    AND column_name = 'attachments_data'
  ) INTO attachments_data_exists;

  -- Add has_attachments column if it doesn't exist
  IF NOT has_attachments_exists THEN
    ALTER TABLE public.contributions ADD COLUMN has_attachments BOOLEAN DEFAULT false;
    RAISE NOTICE 'Added has_attachments column to contributions table';
  ELSE
    RAISE NOTICE 'has_attachments column already exists in contributions table';
  END IF;

  -- Add attachments_url column if it doesn't exist
  IF NOT attachments_url_exists THEN
    ALTER TABLE public.contributions ADD COLUMN attachments_url TEXT;
    RAISE NOTICE 'Added attachments_url column to contributions table';
  ELSE
    RAISE NOTICE 'attachments_url column already exists in contributions table';
  END IF;

  -- Add attachments_data column if it doesn't exist
  IF NOT attachments_data_exists THEN
    ALTER TABLE public.contributions ADD COLUMN attachments_data JSONB DEFAULT '[]'::jsonb;
    RAISE NOTICE 'Added attachments_data column to contributions table';
  ELSE
    RAISE NOTICE 'attachments_data column already exists in contributions table';
  END IF;
END $$;

-- Create storage bucket for contribution attachments if it doesn't exist
DO $$
BEGIN
  INSERT INTO storage.buckets (id, name, public)
  VALUES ('contribution-attachments', 'contribution-attachments', true)
  ON CONFLICT (id) DO NOTHING;
  
  RAISE NOTICE 'Created or confirmed contribution-attachments storage bucket';
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error creating contribution-attachments bucket: %', SQLERRM;
END $$;

-- Set up RLS policies for the contribution-attachments bucket
DO $$
DECLARE
  policy_exists BOOLEAN;
BEGIN
  -- Check if read policy exists
  SELECT EXISTS (
    SELECT FROM pg_policies
    WHERE tablename = 'objects'
    AND schemaname = 'storage'
    AND policyname = 'Contribution Attachments Read Policy'
  ) INTO policy_exists;
  
  -- Create read policy if it doesn't exist
  IF NOT policy_exists THEN
    CREATE POLICY "Contribution Attachments Read Policy"
      ON storage.objects
      FOR SELECT
      USING (bucket_id = 'contribution-attachments');
    
    RAISE NOTICE 'Created Contribution Attachments Read Policy';
  ELSE
    RAISE NOTICE 'Contribution Attachments Read Policy already exists';
  END IF;
  
  -- Check if insert policy exists
  SELECT EXISTS (
    SELECT FROM pg_policies
    WHERE tablename = 'objects'
    AND schemaname = 'storage'
    AND policyname = 'Contribution Attachments Insert Policy'
  ) INTO policy_exists;
  
  -- Create insert policy if it doesn't exist
  IF NOT policy_exists THEN
    CREATE POLICY "Contribution Attachments Insert Policy"
      ON storage.objects
      FOR INSERT
      WITH CHECK (
        bucket_id = 'contribution-attachments'
        AND auth.role() = 'authenticated'
      );
    
    RAISE NOTICE 'Created Contribution Attachments Insert Policy';
  ELSE
    RAISE NOTICE 'Contribution Attachments Insert Policy already exists';
  END IF;
  
  -- Check if update policy exists
  SELECT EXISTS (
    SELECT FROM pg_policies
    WHERE tablename = 'objects'
    AND schemaname = 'storage'
    AND policyname = 'Contribution Attachments Update Policy'
  ) INTO policy_exists;
  
  -- Create update policy if it doesn't exist
  IF NOT policy_exists THEN
    CREATE POLICY "Contribution Attachments Update Policy"
      ON storage.objects
      FOR UPDATE
      USING (
        bucket_id = 'contribution-attachments'
        AND auth.role() = 'authenticated'
      );
    
    RAISE NOTICE 'Created Contribution Attachments Update Policy';
  ELSE
    RAISE NOTICE 'Contribution Attachments Update Policy already exists';
  END IF;
  
  -- Check if delete policy exists
  SELECT EXISTS (
    SELECT FROM pg_policies
    WHERE tablename = 'objects'
    AND schemaname = 'storage'
    AND policyname = 'Contribution Attachments Delete Policy'
  ) INTO policy_exists;
  
  -- Create delete policy if it doesn't exist
  IF NOT policy_exists THEN
    CREATE POLICY "Contribution Attachments Delete Policy"
      ON storage.objects
      FOR DELETE
      USING (
        bucket_id = 'contribution-attachments'
        AND auth.role() = 'authenticated'
      );
    
    RAISE NOTICE 'Created Contribution Attachments Delete Policy';
  ELSE
    RAISE NOTICE 'Contribution Attachments Delete Policy already exists';
  END IF;
END $$;
```

## Step 3: Execute the Query

Click the "Run" button to execute the SQL query. You should see a success message in the results panel.

## Step 4: Verify the Changes

### Check the Contributions Table

1. Go to the "Table Editor" in the left sidebar
2. Select the "contributions" table
3. Click on "Edit" to view the table structure
4. Verify that the following columns have been added:
   - `has_attachments` (boolean)
   - `attachments_url` (text)
   - `attachments_data` (jsonb)

### Check the Storage Buckets

1. Go to the "Storage" section in the left sidebar
2. Verify that a bucket named "contribution-attachments" has been created

### Check the RLS Policies

1. Go to the "Storage" section
2. Click on "Policies"
3. Verify that the following policies have been created for the "contribution-attachments" bucket:
   - "Contribution Attachments Read Policy"
   - "Contribution Attachments Insert Policy"
   - "Contribution Attachments Update Policy"
   - "Contribution Attachments Delete Policy"

## Step 5: Deploy the Frontend Code

After successfully running the migration, you can deploy the frontend code that implements the file attachment feature.

## Troubleshooting

If you encounter any issues:

1. Check the error messages in the SQL Editor results panel
2. Make sure you have the necessary permissions to create tables, columns, and policies
3. If a specific part of the migration fails, you can run that part separately

For more help, refer to the Supabase documentation on [migrations](https://supabase.com/docs/guides/database/migrations) and [storage](https://supabase.com/docs/guides/storage).
