// Venture Management API
// Backend Specialist: Core venture CRUD operations
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Get all ventures for a user
const getVentures = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    // Get ventures where user is involved (created by user or member of alliance)
    const { data: ventures, error } = await supabase
      .from('projects')
      .select(`
        *,
        teams(
          id,
          name,
          alliance_type
        ),
        tasks(
          id,
          title,
          status,
          priority
        )
      `)
      .or(`created_by.eq.${userId},team_id.in.(select team_id from team_members where user_id = '${userId}')`)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) throw error;

    // Transform projects to ventures format
    const venturesResponse = ventures.map(project => ({
      id: project.id,
      name: project.name || project.title,
      title: project.title,
      description: project.description,
      venture_type: project.project_type || 'software',
      status: project.is_complete ? 'completed' : (project.has_started ? 'active' : 'planning'),
      alliance: project.teams ? {
        id: project.teams.id,
        name: project.teams.name,
        type: project.teams.alliance_type
      } : null,
      created_by: project.created_by,
      created_at: project.created_at,
      updated_at: project.updated_at,
      estimated_duration: project.estimated_duration,
      start_date: project.start_date,
      launch_date: project.launch_date,
      is_public: project.is_public,
      thumbnail_url: project.thumbnail_url,
      tasks_summary: {
        total: project.tasks?.length || 0,
        completed: project.tasks?.filter(t => t.status === 'done').length || 0,
        in_progress: project.tasks?.filter(t => t.status === 'in_progress').length || 0
      }
    }));

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ ventures: venturesResponse })
    };

  } catch (error) {
    console.error('Get ventures error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch ventures' })
    };
  }
};

// Get single venture details
const getVenture = async (event) => {
  try {
    const ventureId = event.path.split('/').pop();
    const userId = getUserFromRequest(event);
    
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    // Get venture with full details
    const { data: venture, error: ventureError } = await supabase
      .from('projects')
      .select(`
        *,
        teams(
          id,
          name,
          alliance_type,
          team_members(
            user_id,
            role,
            users(
              id,
              display_name,
              avatar_url
            )
          )
        ),
        tasks(
          id,
          title,
          description,
          status,
          priority,
          task_type,
          difficulty_level,
          estimated_hours,
          logged_hours,
          assignee_id,
          created_at,
          updated_at
        ),
        contributions(
          id,
          user_id,
          hours_logged,
          description,
          status,
          created_at
        )
      `)
      .eq('id', ventureId)
      .single();

    if (ventureError) throw ventureError;

    // Check if user has access (created venture or member of alliance)
    const hasAccess = venture.created_by === userId || 
      (venture.teams && venture.teams.team_members.some(m => m.user_id === userId));

    if (!hasAccess && !venture.is_public) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Access denied' })
      };
    }

    // Transform response
    const response = {
      id: venture.id,
      name: venture.name || venture.title,
      title: venture.title,
      description: venture.description,
      venture_type: venture.project_type || 'software',
      status: venture.is_complete ? 'completed' : (venture.has_started ? 'active' : 'planning'),
      alliance: venture.teams ? {
        id: venture.teams.id,
        name: venture.teams.name,
        type: venture.teams.alliance_type,
        members: venture.teams.team_members.map(m => ({
          user_id: m.user_id,
          role: m.role,
          user: m.users
        }))
      } : null,
      created_by: venture.created_by,
      created_at: venture.created_at,
      updated_at: venture.updated_at,
      estimated_duration: venture.estimated_duration,
      start_date: venture.start_date,
      launch_date: venture.launch_date,
      is_public: venture.is_public,
      thumbnail_url: venture.thumbnail_url,
      wizard_progress: venture.wizard_progress,
      tasks: venture.tasks || [],
      contributions: venture.contributions || [],
      metrics: {
        total_tasks: venture.tasks?.length || 0,
        completed_tasks: venture.tasks?.filter(t => t.status === 'done').length || 0,
        total_hours_logged: venture.contributions?.reduce((sum, c) => sum + (c.hours_logged || 0), 0) || 0,
        active_contributors: [...new Set(venture.contributions?.map(c => c.user_id))].length || 0
      }
    };

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(response)
    };

  } catch (error) {
    console.error('Get venture error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch venture' })
    };
  }
};

// Create new venture
const createVenture = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const data = JSON.parse(event.body);
    
    // Validate required fields
    if (!data.name || !data.description) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Name and description are required' })
      };
    }

    // If alliance_id provided, check user has permission
    if (data.alliance_id) {
      const { data: membership, error: memberError } = await supabase
        .from('team_members')
        .select('role')
        .eq('team_id', data.alliance_id)
        .eq('user_id', userId)
        .single();

      if (memberError || !membership) {
        return {
          statusCode: 403,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ error: 'Not a member of specified alliance' })
        };
      }
    }

    // Create venture (project)
    const ventureData = {
      name: data.name,
      title: data.name, // projects table uses title
      description: data.description,
      project_type: data.venture_type || 'software',
      team_id: data.alliance_id || null,
      created_by: userId,
      is_active: true,
      is_public: data.is_public || false,
      estimated_duration: data.estimated_duration || 6,
      start_date: data.start_date || new Date().toISOString().split('T')[0],
      wizard_progress: { current_step: 1, completed_steps: [] }
    };

    const { data: venture, error: ventureError } = await supabase
      .from('projects')
      .insert([ventureData])
      .select()
      .single();

    if (ventureError) throw ventureError;

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        venture: {
          id: venture.id,
          name: venture.name,
          title: venture.title,
          description: venture.description,
          venture_type: venture.project_type,
          alliance_id: venture.team_id,
          created_at: venture.created_at,
          status: 'planning'
        }
      })
    };

  } catch (error) {
    console.error('Create venture error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to create venture' })
    };
  }
};

// Update venture
const updateVenture = async (event) => {
  try {
    const ventureId = event.path.split('/').pop();
    const userId = getUserFromRequest(event);
    
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    // Check if user has permission to update
    const { data: venture, error: ventureError } = await supabase
      .from('projects')
      .select(`
        *,
        teams(
          team_members(user_id, role)
        )
      `)
      .eq('id', ventureId)
      .single();

    if (ventureError) throw ventureError;

    const hasPermission = venture.created_by === userId ||
      (venture.teams && venture.teams.team_members.some(m => 
        m.user_id === userId && ['founder', 'owner', 'admin'].includes(m.role)
      ));

    if (!hasPermission) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Insufficient permissions' })
      };
    }

    const data = JSON.parse(event.body);
    
    // Update venture
    const updateData = {};
    if (data.name) {
      updateData.name = data.name;
      updateData.title = data.name;
    }
    if (data.description) updateData.description = data.description;
    if (data.venture_type) updateData.project_type = data.venture_type;
    if (data.is_public !== undefined) updateData.is_public = data.is_public;
    if (data.estimated_duration) updateData.estimated_duration = data.estimated_duration;
    if (data.start_date) updateData.start_date = data.start_date;
    if (data.launch_date) updateData.launch_date = data.launch_date;
    if (data.has_started !== undefined) updateData.has_started = data.has_started;
    if (data.is_complete !== undefined) updateData.is_complete = data.is_complete;
    
    updateData.updated_at = new Date().toISOString();

    const { data: updatedVenture, error: updateError } = await supabase
      .from('projects')
      .update(updateData)
      .eq('id', ventureId)
      .select()
      .single();

    if (updateError) throw updateError;

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ venture: updatedVenture })
    };

  } catch (error) {
    console.error('Update venture error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to update venture' })
    };
  }
};

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/ventures', '');

  try {
    let response;

    if (event.httpMethod === 'GET') {
      if (path === '' || path === '/') {
        response = await getVentures(event);
      } else {
        response = await getVenture(event);
      }
    } else if (event.httpMethod === 'POST') {
      response = await createVenture(event);
    } else if (event.httpMethod === 'PUT') {
      response = await updateVenture(event);
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('Venture API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
