// Configuration for Augment Agent to access Supabase
// Replace these values with your actual Supabase credentials

const supabaseConfig = {
  supabaseUrl: "https://auth.royalty.technology", // e.g., https://abcdefghijklmnopqrst.supabase.co
  supabaseKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzgyMDU1OSwiZXhwIjoyMDU5Mzk2NTU5fQ.k5xvFBzBxNMCtPd0d8Ko9dAcT5Y3op3ZgFvlAf85LLs", // The anon key or service role key
  
  // Optional: Specific table access permissions
  // This helps document what the assistant should have access to
  tableAccess: {
    // List tables and their access levels (read, write, etc.)
    "roadmap": "read",
    "roadmap_sections": "read",
    "roadmap_tasks": "read",
    "projects": "read",
    "users": "read",
    "tasks": "read",
    // Add other tables as needed
  }
};

// Do not modify below this line
module.exports = supabaseConfig;
