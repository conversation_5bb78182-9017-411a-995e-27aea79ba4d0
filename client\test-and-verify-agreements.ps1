Write-Host "Running Agreement Generation Tests and Verification..." -ForegroundColor Cyan
Write-Host ""
Write-Host "Step 1: Generating test agreements..." -ForegroundColor Yellow
node scripts/run-agreement-tests.mjs
Write-Host ""
Write-Host "Step 2: Verifying generated agreements..." -ForegroundColor Yellow
node scripts/verify-agreements.mjs
Write-Host ""
Write-Host "All tests and verification completed. Check the test-output/agreements directory for results." -ForegroundColor Green
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
