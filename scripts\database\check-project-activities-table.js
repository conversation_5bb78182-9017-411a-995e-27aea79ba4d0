const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || process.argv[2];

if (!supabaseKey) {
  console.error('Error: Supabase key is required. Please provide it as an environment variable or command line argument.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkProjectActivitiesTable() {
  try {
    // Try to query the project_activities table
    const { data, error } = await supabase
      .from('project_activities')
      .select('id')
      .limit(1);

    if (error) {
      if (error.code === '42P01') {
        console.error('The project_activities table does not exist.');
        return false;
      } else {
        console.error('Error checking project_activities table:', error);
        return false;
      }
    }

    console.log('The project_activities table exists.');
    return true;
  } catch (error) {
    console.error('Unexpected error:', error);
    return false;
  }
}

async function applyMigration() {
  try {
    // Read the migration SQL
    const fs = require('fs');
    const path = require('path');
    const migrationPath = path.join(__dirname, 'supabase/migrations/20240601000002_create_project_activities_table.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Execute the migration SQL
    const { error } = await supabase.rpc('exec_sql', { sql: migrationSQL });

    if (error) {
      console.error('Error applying migration:', error);
      return false;
    }

    console.log('Migration applied successfully.');
    return true;
  } catch (error) {
    console.error('Unexpected error applying migration:', error);
    return false;
  }
}

async function main() {
  const tableExists = await checkProjectActivitiesTable();

  if (!tableExists) {
    console.log('Attempting to apply migration...');
    const migrationApplied = await applyMigration();
    
    if (migrationApplied) {
      console.log('Migration applied successfully. The project_activities table should now exist.');
    } else {
      console.error('Failed to apply migration. Please apply it manually.');
    }
  }

  process.exit(0);
}

main();
