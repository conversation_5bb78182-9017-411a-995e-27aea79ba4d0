// Script to apply the Retro Profile migration using direct SQL queries
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please set SUPABASE_URL and SUPABASE_SERVICE_KEY environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);
console.log('Supabase client initialized');

// Function to execute a SQL query
async function executeSQL(query) {
  try {
    // Use the Supabase client to execute the query
    const { data, error } = await supabase.rpc('exec_sql', { sql: query });
    
    if (error) {
      return { success: false, error: error.message };
    }
    
    return { success: true, data };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Function to split SQL into individual statements
function splitSQLStatements(sql) {
  // Split on semicolons but ignore those inside quotes or comments
  const statements = [];
  let currentStatement = '';
  let inSingleQuote = false;
  let inDoubleQuote = false;
  let inComment = false;
  
  for (let i = 0; i < sql.length; i++) {
    const char = sql[i];
    const nextChar = sql[i + 1] || '';
    
    // Handle comments
    if (!inSingleQuote && !inDoubleQuote) {
      if (char === '-' && nextChar === '-') {
        inComment = true;
      } else if (inComment && char === '\n') {
        inComment = false;
      }
    }
    
    // Handle quotes
    if (!inComment) {
      if (char === "'" && sql[i - 1] !== '\\') {
        inSingleQuote = !inSingleQuote;
      } else if (char === '"' && sql[i - 1] !== '\\') {
        inDoubleQuote = !inDoubleQuote;
      }
    }
    
    // Add character to current statement
    currentStatement += char;
    
    // Check for statement end
    if (char === ';' && !inSingleQuote && !inDoubleQuote && !inComment) {
      statements.push(currentStatement.trim());
      currentStatement = '';
    }
  }
  
  // Add the last statement if it doesn't end with a semicolon
  if (currentStatement.trim()) {
    statements.push(currentStatement.trim());
  }
  
  return statements.filter(stmt => stmt.trim() !== '' && !stmt.trim().startsWith('--'));
}

// Main function to apply the migration
async function applyMigration() {
  try {
    console.log('Starting Retro Profile migration process...');
    
    // First, check if the exec_sql function exists
    const { data: functionData, error: functionError } = await supabase.rpc('exec_sql', { sql: 'SELECT 1' });
    
    if (functionError) {
      console.error('The exec_sql function does not exist. Creating it...');
      
      // Create the exec_sql function
      const createFunctionSQL = `
        CREATE OR REPLACE FUNCTION exec_sql(sql text)
        RETURNS json
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        DECLARE
          result json;
        BEGIN
          EXECUTE sql;
          result := json_build_object('success', true);
          RETURN result;
        EXCEPTION WHEN OTHERS THEN
          result := json_build_object('success', false, 'error', SQLERRM);
          RETURN result;
        END;
        $$;
      `;
      
      // Execute the function creation directly
      const { data, error } = await supabase.from('_exec_sql').select('*').limit(1);
      
      if (error) {
        console.error('Error creating exec_sql function:', error.message);
        console.log('Please create the exec_sql function manually in the Supabase SQL editor.');
        process.exit(1);
      }
    }
    
    // Read the migration SQL file
    const migrationPath = path.join(__dirname, 'retro-profile-migration.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    console.log(`Read migration file: ${migrationPath}`);
    
    // Split into individual statements
    const statements = splitSQLStatements(migrationSQL);
    console.log(`Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement
    let successCount = 0;
    let failCount = 0;
    const failedStatements = [];
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`\nExecuting statement ${i + 1}/${statements.length}...`);
      console.log(statement.substring(0, 100) + (statement.length > 100 ? '...' : ''));
      
      // Execute the statement
      const result = await executeSQL(statement);
      
      if (result.success) {
        successCount++;
        console.log('Success!');
      } else {
        // Check if the error is because the column already exists
        if (result.error.includes('already exists')) {
          console.log('Already exists, skipping...');
          successCount++;
        } else {
          failCount++;
          console.error(`Error: ${result.error}`);
          failedStatements.push({
            index: i + 1,
            statement: statement.substring(0, 100) + (statement.length > 100 ? '...' : ''),
            error: result.error
          });
        }
      }
    }
    
    // Print summary
    console.log('\n=== Migration Summary ===');
    console.log(`Total statements: ${statements.length}`);
    console.log(`Successfully executed: ${successCount}`);
    console.log(`Failed: ${failCount}`);
    
    if (failCount > 0) {
      console.log('\nFailed Statements:');
      failedStatements.forEach(failure => {
        console.log(`\n${failure.index}. ${failure.statement}`);
        console.log(`   Error: ${failure.error}`);
      });
    }
    
    // Check if the migration was successful
    if (failCount === 0) {
      console.log('\nMigration completed successfully!');
      
      // Verify that the columns were added
      console.log('\nVerifying migration...');
      const { data, error } = await supabase
        .from('users')
        .select('headline, location, website, cover_image_url')
        .limit(1);
      
      if (error) {
        console.error('Verification failed:', error.message);
      } else {
        console.log('Verification successful! The new columns are available.');
        console.log('Sample data:', data);
      }
    } else {
      console.log('\nMigration completed with errors. Please check the failed statements.');
    }
  } catch (error) {
    console.error('Migration failed:', error);
  }
}

// Run the migration
applyMigration();
