{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "parcel-dev": "parcel index.html", "parcel-build": "parcel build index.html --dist-dir dist"}, "dependencies": {"@atlaskit/pragmatic-drag-and-drop": "^1.5.2", "@formkit/auto-animate": "^0.8.2", "@headlessui/react": "^2.2.4", "@heroui/react": "^2.7.8", "@heroui/theme": "^2.4.15", "@hookform/resolvers": "^5.0.1", "@popperjs/core": "^2.11.8", "@supabase/supabase-js": "^2.39.7", "@tanstack/react-query": "^5.76.2", "@tremor/react": "^3.18.7", "axios": "^1.7.7", "chalk": "^5.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "diff": "^8.0.1", "express": "^5.1.0", "framer-motion": "^12.12.2", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lodash": "^4.17.21", "lucide-react": "^0.511.0", "moment": "^2.30.1", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-datepicker": "^8.2.1", "react-dom": "^18.3.1", "react-feature-flags": "^1.0.0", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.4.1", "react-minimal-pie-chart": "^9.1.0", "react-router-dom": "^6.26.2", "react-signature-canvas": "^1.1.0-alpha.2", "react-spinners": "^0.14.1", "react-toastify": "^11.0.5", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.17", "zod": "^3.25.28"}, "devDependencies": {"@eslint/js": "^9.9.0", "@parcel/transformer-postcss": "^2.15.1", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.5.1", "autoprefixer": "^10.4.21", "buffer": "^6.0.3", "dotenv": "^16.5.0", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "parcel": "^2.15.1", "postcss": "^8.5.3", "tailwindcss-animate": "^1.0.7", "vite": "^6.3.5"}, "packageManager": "pnpm@9.4.0+sha512.f549b8a52c9d2b8536762f99c0722205efc5af913e77835dbccc3b0b0b2ca9e7dc8022b78062c17291c48e88749c70ce88eb5a74f1fa8c4bf5e18bb46c8bd83a"}