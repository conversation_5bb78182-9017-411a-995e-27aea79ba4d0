/**
 * Project Verifier for Agreement Generator
 * 
 * This module provides functionality to verify project details from the database
 * and ensure that the agreement generator is using accurate information.
 */

import { supabase } from '../../../utils/supabase/supabase.utils';

/**
 * Fetch project details from Supabase
 * @param {string} projectId - The project ID to fetch
 * @returns {Promise<Object>} - The project data
 */
export const fetchProjectDetails = async (projectId) => {
  try {
    console.log('ProjectVerifier: Fetching project details for ID:', projectId);
    
    // Fetch project details
    const { data, error } = await supabase
      .from('projects')
      .select(`
        *,
        royalty_models(*),
        project_milestones(*)
      `)
      .eq('id', projectId)
      .single();
    
    if (error) {
      console.error('ProjectVerifier: Error fetching project details:', error);
      throw error;
    }
    
    if (!data) {
      console.error('ProjectVerifier: No project found with ID:', projectId);
      throw new Error('Project not found');
    }
    
    console.log('ProjectVerifier: Successfully fetched project details:', data);
    return data;
  } catch (error) {
    console.error('ProjectVerifier: Error in fetchProjectDetails:', error);
    throw error;
  }
};

/**
 * Fetch project by name from Supabase
 * @param {string} projectName - The project name to search for
 * @returns {Promise<Object>} - The project data
 */
export const fetchProjectByName = async (projectName) => {
  try {
    console.log('ProjectVerifier: Fetching project by name:', projectName);
    
    // Fetch project details
    const { data, error } = await supabase
      .from('projects')
      .select(`
        *,
        royalty_models(*),
        project_milestones(*)
      `)
      .ilike('name', projectName)
      .limit(1);
    
    if (error) {
      console.error('ProjectVerifier: Error fetching project by name:', error);
      throw error;
    }
    
    if (!data || data.length === 0) {
      console.error('ProjectVerifier: No project found with name:', projectName);
      throw new Error('Project not found');
    }
    
    console.log('ProjectVerifier: Successfully fetched project by name:', data[0]);
    return data[0];
  } catch (error) {
    console.error('ProjectVerifier: Error in fetchProjectByName:', error);
    throw error;
  }
};

/**
 * Verify project information in the agreement
 * @param {Object} project - The project data from the agreement
 * @returns {Promise<Object>} - Verification results
 */
export const verifyProjectInformation = async (project) => {
  try {
    console.log('ProjectVerifier: Verifying project information for:', project.name);
    
    // Try to fetch the project from the database
    let dbProject;
    try {
      if (project.id) {
        dbProject = await fetchProjectDetails(project.id);
      } else {
        dbProject = await fetchProjectByName(project.name);
      }
    } catch (error) {
      console.warn('ProjectVerifier: Could not find project in database:', error);
      return {
        verified: false,
        message: 'Project not found in database',
        project: project
      };
    }
    
    // Compare project details
    const discrepancies = [];
    
    if (project.name !== dbProject.name) {
      discrepancies.push(`Name mismatch: "${project.name}" vs "${dbProject.name}"`);
    }
    
    if (project.description !== dbProject.description) {
      discrepancies.push(`Description mismatch`);
    }
    
    if (project.projectType !== dbProject.project_type) {
      discrepancies.push(`Project type mismatch: "${project.projectType}" vs "${dbProject.project_type}"`);
    }
    
    return {
      verified: discrepancies.length === 0,
      message: discrepancies.length === 0 ? 'Project information verified' : 'Project information discrepancies found',
      discrepancies: discrepancies,
      project: project,
      dbProject: dbProject
    };
  } catch (error) {
    console.error('ProjectVerifier: Error in verifyProjectInformation:', error);
    return {
      verified: false,
      message: 'Error verifying project information',
      error: error.message,
      project: project
    };
  }
};
