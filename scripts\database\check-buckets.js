const { createClient } = require('@supabase/supabase-js');

// Create a Supabase client
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE2OTk0NzI1NzYsImV4cCI6MjAxNTA0ODU3Nn0.Wd_oYpYUQdE_RoLQdOgwcnQE4MqUCxgMjU6QKW_RQ0c';
const supabase = createClient(supabaseUrl, supabaseKey);

async function listBuckets() {
  try {
    const { data, error } = await supabase.storage.listBuckets();
    
    if (error) {
      console.error('Error listing buckets:', error);
      return;
    }
    
    console.log('Buckets:', data);
    
    // Check if contribution-attachments bucket exists
    const contributionBucket = data.find(bucket => bucket.name === 'contribution-attachments');
    if (contributionBucket) {
      console.log('contribution-attachments bucket exists');
    } else {
      console.log('contribution-attachments bucket does NOT exist');
    }
    
    // List files in each bucket
    for (const bucket of data) {
      console.log(`\nListing files in bucket: ${bucket.name}`);
      const { data: files, error: filesError } = await supabase.storage
        .from(bucket.name)
        .list();
        
      if (filesError) {
        console.error(`Error listing files in bucket ${bucket.name}:`, filesError);
        continue;
      }
      
      console.log(`Files in ${bucket.name}:`, files);
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

listBuckets();
