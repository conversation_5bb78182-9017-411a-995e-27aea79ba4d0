import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { Card, CardBody, CardHeader, <PERSON><PERSON>, <PERSON>, Badge } from '@heroui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { formatDistanceToNow } from 'date-fns';

/**
 * Notification Center Section
 * 
 * Shows recent notifications, alerts, and system messages.
 * Part of the Home canvas in the experimental navigation system.
 */
const NotificationCenter = () => {
  const { currentUser } = useContext(UserContext);
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [unreadCount, setUnreadCount] = useState(0);

  // Fetch notifications
  useEffect(() => {
    const fetchNotifications = async () => {
      if (!currentUser) return;

      try {
        // For now, we'll create mock notifications since the notifications table might not exist
        // In a real implementation, you'd fetch from a notifications table
        const mockNotifications = [
          {
            id: 1,
            type: 'contribution_approved',
            title: 'Contribution Approved',
            message: 'Your contribution to "Game Engine Project" has been approved!',
            timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
            read: false,
            icon: '✅',
            color: 'success'
          },
          {
            id: 2,
            type: 'project_invitation',
            title: 'Project Invitation',
            message: 'You\'ve been invited to join "Mobile App Development"',
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
            read: false,
            icon: '📨',
            color: 'primary'
          },
          {
            id: 3,
            type: 'revenue_update',
            title: 'Revenue Update',
            message: 'New revenue entry of $150 added to your account',
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
            read: true,
            icon: '💰',
            color: 'warning'
          },
          {
            id: 4,
            type: 'system_update',
            title: 'System Update',
            message: 'New features available in the analytics dashboard',
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2), // 2 days ago
            read: true,
            icon: '🔄',
            color: 'secondary'
          }
        ];

        setNotifications(mockNotifications);
        setUnreadCount(mockNotifications.filter(n => !n.read).length);
      } catch (error) {
        console.error('Error fetching notifications:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchNotifications();
  }, [currentUser]);

  // Mark notification as read
  const markAsRead = (notificationId) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, read: true }
          : notification
      )
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  // Mark all as read
  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, read: true }))
    );
    setUnreadCount(0);
  };

  // Get notification icon color
  const getIconColor = (type) => {
    switch (type) {
      case 'contribution_approved': return 'from-green-500 to-emerald-500';
      case 'project_invitation': return 'from-blue-500 to-cyan-500';
      case 'revenue_update': return 'from-yellow-500 to-orange-500';
      case 'system_update': return 'from-purple-500 to-pink-500';
      default: return 'from-gray-500 to-slate-500';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  return (
    <div className="p-6">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardHeader>
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center relative">
                  <span className="text-xl">🔔</span>
                  {unreadCount > 0 && (
                    <Badge
                      content={unreadCount}
                      color="danger"
                      className="absolute -top-1 -right-1"
                    />
                  )}
                </div>
                <div>
                  <h2 className="text-xl font-bold text-white">Notifications</h2>
                  <p className="text-white/60 text-sm">
                    {unreadCount > 0 ? `${unreadCount} unread notifications` : 'All caught up!'}
                  </p>
                </div>
              </div>
              {unreadCount > 0 && (
                <Button
                  size="sm"
                  variant="flat"
                  className="bg-white/10 text-white hover:bg-white/20"
                  onClick={markAllAsRead}
                >
                  Mark all read
                </Button>
              )}
            </div>
          </CardHeader>
          <CardBody>
            {notifications.length === 0 ? (
              <div className="text-center py-8">
                <span className="text-6xl mb-4 block">🔕</span>
                <h3 className="text-white text-lg font-medium mb-2">No notifications</h3>
                <p className="text-white/60">You're all caught up! Check back later for updates.</p>
              </div>
            ) : (
              <div className="space-y-3 max-h-96 overflow-y-auto">
                <AnimatePresence>
                  {notifications.map((notification, index) => (
                    <motion.div
                      key={notification.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 20 }}
                      transition={{ duration: 0.3, delay: 0.05 * index }}
                      className={`
                        flex items-start gap-4 p-4 rounded-lg transition-all duration-200 cursor-pointer
                        ${notification.read 
                          ? 'bg-white/5 hover:bg-white/10' 
                          : 'bg-white/10 hover:bg-white/15 border border-white/20'
                        }
                      `}
                      onClick={() => !notification.read && markAsRead(notification.id)}
                    >
                      {/* Notification Icon */}
                      <div className={`w-10 h-10 rounded-lg bg-gradient-to-r ${getIconColor(notification.type)} flex items-center justify-center flex-shrink-0`}>
                        <span className="text-lg">{notification.icon}</span>
                      </div>

                      {/* Notification Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-2">
                          <h4 className={`font-medium ${notification.read ? 'text-white/70' : 'text-white'}`}>
                            {notification.title}
                          </h4>
                          {!notification.read && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mt-2" />
                          )}
                        </div>
                        <p className={`text-sm mt-1 ${notification.read ? 'text-white/50' : 'text-white/70'}`}>
                          {notification.message}
                        </p>
                        <p className="text-white/40 text-xs mt-2">
                          {formatDistanceToNow(notification.timestamp, { addSuffix: true })}
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
            )}

            {/* View All Button */}
            {notifications.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.5 }}
                className="mt-4 pt-4 border-t border-white/10 text-center"
              >
                <Button
                  variant="flat"
                  className="bg-white/10 text-white hover:bg-white/20"
                  onClick={() => window.location.href = '/notifications'}
                >
                  View All Notifications
                </Button>
              </motion.div>
            )}
          </CardBody>
        </Card>
      </motion.div>
    </div>
  );
};

export default NotificationCenter;
