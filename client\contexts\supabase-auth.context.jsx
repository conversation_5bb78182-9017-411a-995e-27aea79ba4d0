import { createContext, useState, useEffect } from "react";
import { supabase } from "../utils/supabase/supabase.utils";
import axios from "axios";

// Create the context
export const UserContext = createContext({
  currentUser: null,
  setCurrentUser: () => null,
  isLoading: true,
});

// Provider component
export const UserProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  // Set isLoading to true by default to ensure we check auth before rendering protected routes
  const [isLoading, setIsLoading] = useState(true);

  // Add a custom setLoading function with debug info
  const setLoadingWithDebug = (value) => {
    console.log('🔍 AUTH CONTEXT: Setting isLoading to', value);
    console.log('🔍 AUTH CONTEXT: Called from:', new Error().stack);
    setIsLoading(value);
  };

  // Function to sync user data to the users table
  const syncUserToDatabase = async (user) => {
    if (!user) return;

    try {
      // Check if user already exists in the users table
      const { data: existingUser, error: checkError } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();

      if (checkError && checkError.code !== 'PGRST116') { // PGRST116 means not found
        console.warn('Database sync issue (this is normal if users table doesn\'t exist yet):', checkError.message);
        // Don't throw error, just skip database sync for now
        return;
      }

      if (!existingUser) {
        // User doesn't exist in the table, insert them
        const { error: insertError } = await supabase
          .from('users')
          .insert([
            {
              id: user.id,
              email: user.email,
              display_name: user.user_metadata?.full_name || user.user_metadata?.name || user.email.split('@')[0],
              avatar_url: '/default-avatar.png', // Default avatar
              date_created: new Date().toISOString(),
              is_admin: false // Default to non-admin
            }
          ]);

        if (insertError) {
          console.error('Error inserting user into database:', insertError);
        }
      } else {
        // User exists but we should update the email if it changed
        // We don't update display_name here to preserve user customizations
        if (existingUser.email !== user.email) {
          const { error: updateError } = await supabase
            .from('users')
            .update({ email: user.email })
            .eq('id', user.id);

          if (updateError) {
            console.error('Error updating user email:', updateError);
          }
        }
      }
    } catch (error) {
      console.error('Error syncing user to database:', error);
    }
  };

  useEffect(() => {
    // Check for current session on load
    const checkSession = async () => {
      try {
        console.log('🔍 AUTH CONTEXT: Checking auth session...');

        // Set global flag to prevent loading state resets during auth operations
        window.isAuthOperationInProgress = true;

        setLoadingWithDebug(true); // Explicitly set loading to true with debug

        const { data: { session } } = await supabase.auth.getSession();
        const user = session?.user || null;

        console.log('🔍 AUTH CONTEXT: Session check result:', user ? 'User authenticated' : 'No user found');
        setCurrentUser(user);

        // Sync user to database if logged in
        if (user) {
          await syncUserToDatabase(user);
        }
      } catch (error) {
        console.error('🔍 AUTH CONTEXT: Error checking auth session:', error);
        // Don't set current user if there's an error
      } finally {
        // Ensure isLoading is false
        setLoadingWithDebug(false);
        console.log('🔍 AUTH CONTEXT: Auth loading state set to false after session check');

        // Clear auth operation flag
        window.isAuthOperationInProgress = false;
      }

      // Set up auth state change listener
      try {
        const { data: { subscription } } = await supabase.auth.onAuthStateChange(
          async (event, session) => {
            console.log('🔍 AUTH CONTEXT: Auth state changed:', event);

            const user = session?.user || null;
            const previousUser = currentUser;

            console.log('🔍 AUTH CONTEXT: Previous user:', previousUser ? previousUser.id : 'none');
            console.log('🔍 AUTH CONTEXT: Current user:', user ? user.id : 'none');

            // Only show loading for significant auth state changes
            // Skip loading for SIGNED_IN and INITIAL_SESSION events when we already have a user (tab focus changes)
            // Also check if this event happened shortly after a visibility change
            const timeSinceVisibilityChange = Date.now() - (window.lastVisibilityChangeTime || 0);
            const isVisibilityChangeRecent = timeSinceVisibilityChange < 2000;

            console.log('🔍 AUTH CONTEXT: Time since last visibility change:', timeSinceVisibilityChange, 'ms');
            console.log('🔍 AUTH CONTEXT: Is visibility change recent?', isVisibilityChangeRecent);

            const sameUserCheck = (event === 'SIGNED_IN' || event === 'INITIAL_SESSION') &&
                                  previousUser && user &&
                                  previousUser.id === user.id;

            const visibilityChangeCheck = isVisibilityChangeRecent &&
                                         event === 'SIGNED_IN' &&
                                         previousUser;

            console.log('🔍 AUTH CONTEXT: Same user check:', sameUserCheck);
            console.log('🔍 AUTH CONTEXT: Visibility change check:', visibilityChangeCheck);

            const isRefreshEvent = sameUserCheck || visibilityChangeCheck;

            // ALWAYS skip loading for SIGNED_IN events after a visibility change
            // This is the most aggressive approach to prevent loading on tab focus
            if (isRefreshEvent ||
                (event === 'SIGNED_IN' && window.lastVisibilityChangeTime &&
                 Date.now() - window.lastVisibilityChangeTime < 5000)) {
              console.log('🔍 AUTH CONTEXT: Skipping loading for refresh event - user already signed in');
              // Just update the user without showing loading
              setCurrentUser(user);
            } else {
              // Set global flag to prevent loading state resets during auth operations
              window.isAuthOperationInProgress = true;

              // Set loading to true during significant auth state changes
              console.log('🔍 AUTH CONTEXT: Significant auth state change, showing loading');
              setLoadingWithDebug(true);

              setCurrentUser(user);

              // Sync user to database on sign in or user update
              if (user && (event === 'SIGNED_IN' || event === 'USER_UPDATED')) {
                await syncUserToDatabase(user);
              }

              // Set loading back to false after processing
              setLoadingWithDebug(false);
              console.log('🔍 AUTH CONTEXT: Auth loading state set to false after auth state change');

              // Clear auth operation flag
              window.isAuthOperationInProgress = false;
            }
          }
        );

        // Clean up subscription
        return () => {
          if (subscription && typeof subscription.unsubscribe === 'function') {
            subscription.unsubscribe();
          }
        };
      } catch (error) {
        console.error('Error setting up auth listener:', error);
        return () => {}; // Empty cleanup function
      }
    };

    checkSession();
  }, []);

  // Set up axios interceptor to include the JWT token in requests
  useEffect(() => {
    if (currentUser) {
      // Add token to all axios requests
      const interceptor = axios.interceptors.request.use(async (config) => {
        const { data: { session } } = await supabase.auth.getSession();
        if (session) {
          config.headers.Authorization = `Bearer ${session.access_token}`;
        }
        return config;
      });

      // Clean up interceptor
      return () => axios.interceptors.request.eject(interceptor);
    }
  }, [currentUser]);

  // Auth methods
  const login = async (email, password) => {
    try {
      console.log('🔍 AUTH CONTEXT: Starting login process');

      // Set global flag to prevent loading state resets during auth operations
      window.isAuthOperationInProgress = true;

      setLoadingWithDebug(true);

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;
      console.log('🔍 AUTH CONTEXT: Login successful');
      return data;
    } catch (error) {
      console.error('🔍 AUTH CONTEXT: Error logging in:', error);
      throw error;
    } finally {
      setLoadingWithDebug(false);
      console.log('🔍 AUTH CONTEXT: Login process complete');

      // Clear auth operation flag
      window.isAuthOperationInProgress = false;
    }
  };

  const signup = async (email, password, metadata = {}) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata,
      },
    });

    if (error) throw error;
    return data;
  };

  const loginWithGoogle = async () => {
    try {
      console.log('🔍 AUTH CONTEXT: Starting Google login process');
      setLoadingWithDebug(true);

      // Use the deployed site URL for the redirect
      const redirectUrl = 'https://royalty.technology/auth/callback';
      console.log('🔍 AUTH CONTEXT: Initiating Google login with redirect to:', redirectUrl);

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: redirectUrl,
          skipBrowserRedirect: false,
          queryParams: {
            // Use the custom auth domain
            domain: 'auth.royalty.technology'
          }
        },
      });

      if (error) throw error;
      console.log('🔍 AUTH CONTEXT: Google login initiated successfully');
      return data;
    } catch (error) {
      console.error('🔍 AUTH CONTEXT: Error initiating Google login:', error);
      setLoadingWithDebug(false); // Make sure to reset loading state on error
      throw error;
    }
    // Note: We don't set loading to false in finally because the redirect will happen
  };

  const loginWithGithub = async () => {
    // Use the deployed site URL for the redirect
    const redirectUrl = 'https://royalty.technology/auth/callback';

    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'github',
      options: {
        redirectTo: redirectUrl,
        skipBrowserRedirect: false,
        queryParams: {
          // Use the custom auth domain
          domain: 'auth.royalty.technology'
        }
      },
    });

    if (error) throw error;
    return data;
  };

  const logout = async () => {
    try {
      console.log('🔍 AUTH CONTEXT: Starting logout process');

      // Set global flag to prevent loading state resets during auth operations
      window.isAuthOperationInProgress = true;

      setLoadingWithDebug(true);

      const { error } = await supabase.auth.signOut();
      if (error) throw error;

      console.log('🔍 AUTH CONTEXT: Logout successful');
    } catch (error) {
      console.error('🔍 AUTH CONTEXT: Error logging out:', error);
      throw error;
    } finally {
      setLoadingWithDebug(false);
      console.log('🔍 AUTH CONTEXT: Logout process complete');

      // Clear auth operation flag
      window.isAuthOperationInProgress = false;
    }
  };

  const resetPassword = async (email) => {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: window.location.origin + '/reset-password',
    });

    if (error) throw error;
  };

  // Create the value object
  const value = {
    currentUser,
    setCurrentUser,
    isLoading,
    login,
    signup,
    loginWithGoogle,
    loginWithGithub,
    logout,
    resetPassword,
  };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
};
