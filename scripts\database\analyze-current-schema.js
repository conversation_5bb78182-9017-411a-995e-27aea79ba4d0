// Analyze current database schema for Alliance & Venture migration
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

console.log('🔍 Analyzing Current Database Schema...');
console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key available:', !!supabaseKey);

const supabase = createClient(supabaseUrl, supabaseKey);

async function analyzeSchema() {
  try {
    console.log('\n📋 Checking Core Tables...');
    
    // Check teams table (will become alliances)
    console.log('\n1. TEAMS TABLE (→ Alliances):');
    try {
      const { data: teams, error: teamsError } = await supabase
        .from('teams')
        .select('*')
        .limit(3);
      
      if (teamsError) {
        console.log('❌ Teams table error:', teamsError.message);
      } else {
        console.log('✅ Teams table exists');
        console.log('Sample data:', teams);
        if (teams.length > 0) {
          console.log('Current columns:', Object.keys(teams[0]));
        }
      }
    } catch (err) {
      console.log('❌ Teams table not accessible:', err.message);
    }

    // Check projects table (will become ventures)
    console.log('\n2. PROJECTS TABLE (→ Ventures):');
    try {
      const { data: projects, error: projectsError } = await supabase
        .from('projects')
        .select('*')
        .limit(3);
      
      if (projectsError) {
        console.log('❌ Projects table error:', projectsError.message);
      } else {
        console.log('✅ Projects table exists');
        console.log('Sample data:', projects);
        if (projects.length > 0) {
          console.log('Current columns:', Object.keys(projects[0]));
        }
      }
    } catch (err) {
      console.log('❌ Projects table not accessible:', err.message);
    }

    // Check team_members table
    console.log('\n3. TEAM_MEMBERS TABLE:');
    try {
      const { data: members, error: membersError } = await supabase
        .from('team_members')
        .select('*')
        .limit(3);
      
      if (membersError) {
        console.log('❌ Team_members table error:', membersError.message);
      } else {
        console.log('✅ Team_members table exists');
        console.log('Sample data:', members);
        if (members.length > 0) {
          console.log('Current columns:', Object.keys(members[0]));
        }
      }
    } catch (err) {
      console.log('❌ Team_members table not accessible:', err.message);
    }

    // Check tasks table (for missions/bounties)
    console.log('\n4. TASKS TABLE (→ Missions/Bounties):');
    try {
      const { data: tasks, error: tasksError } = await supabase
        .from('tasks')
        .select('*')
        .limit(3);
      
      if (tasksError) {
        console.log('❌ Tasks table error:', tasksError.message);
      } else {
        console.log('✅ Tasks table exists');
        console.log('Sample data:', tasks);
        if (tasks.length > 0) {
          console.log('Current columns:', Object.keys(tasks[0]));
        }
      }
    } catch (err) {
      console.log('❌ Tasks table not accessible:', err.message);
    }

    // Check users table
    console.log('\n5. USERS TABLE:');
    try {
      const { data: users, error: usersError } = await supabase
        .from('users')
        .select('*')
        .limit(3);
      
      if (usersError) {
        console.log('❌ Users table error:', usersError.message);
      } else {
        console.log('✅ Users table exists');
        console.log('Sample data:', users);
        if (users.length > 0) {
          console.log('Current columns:', Object.keys(users[0]));
        }
      }
    } catch (err) {
      console.log('❌ Users table not accessible:', err.message);
    }

    console.log('\n🎯 Schema Analysis Complete!');
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

analyzeSchema();
