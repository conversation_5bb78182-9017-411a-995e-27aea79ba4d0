import React, { useState, useContext } from 'react';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

const InviteUserForm = ({ onClose, inviteType = 'friend', projectId = null }) => {
  const { currentUser } = useContext(UserContext);
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!email || !email.includes('@')) {
      toast.error('Please enter a valid email address');
      return;
    }

    if (!currentUser) {
      toast.error('You must be logged in to send invites');
      return;
    }

    setIsSubmitting(true);

    try {
      // Check if the user exists in the system
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('id, display_name')
        .eq('email', email)
        .single();

      if (userError && userError.code !== 'PGRST116') {
        throw userError;
      }

      // If inviting to a project
      if (inviteType === 'project' && projectId) {
        // Check if the user is already a contributor
        const { data: existingContributor, error: checkError } = await supabase
          .from('project_contributors')
          .select('id')
          .eq('project_id', projectId)
          .eq('email', email)
          .single();

        if (checkError && checkError.code !== 'PGRST116') {
          throw checkError;
        }

        if (existingContributor) {
          toast.error(`${email} is already a contributor to this project`);
          return;
        }

        // Store the invitation in the project_contributors table with status 'pending'
        const { error: inviteError } = await supabase
          .from('project_contributors')
          .insert({
            project_id: projectId,
            user_id: userData?.id || null, // May be null if user doesn't exist yet
            email: email,
            display_name: userData?.display_name || email.split('@')[0],
            role: '',
            permission_level: 'Contributor',
            is_admin: false,
            status: 'pending',
            invitation_sent_at: new Date().toISOString(),
            created_at: new Date().toISOString()
          });

        if (inviteError) throw inviteError;

        toast.success(`Invitation sent to ${email}`);
      }
      // If sending a friend request
      else if (inviteType === 'friend') {
        // Store the friend request in a new friend_requests table
        const { error: friendError } = await supabase
          .from('friend_requests')
          .insert({
            sender_id: currentUser.id,
            recipient_email: email,
            recipient_id: userData?.id || null, // May be null if user doesn't exist yet
            message: message,
            status: 'pending',
            created_at: new Date().toISOString()
          });

        if (friendError) throw friendError;

        toast.success(`Friend request sent to ${email}`);
      }

      // Close the form
      if (onClose) onClose();
    } catch (error) {
      console.error('Error sending invitation:', error);
      toast.error('Failed to send invitation. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="invite-user-form">
      <h2>{inviteType === 'project' ? 'Invite to Project' : 'Send Friend Request'}</h2>
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="email">Email Address</label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Enter email address"
            required
          />
          <small className="form-text">
            {inviteType === 'project'
              ? 'Invite someone to join this project by email'
              : 'Send a friend request by email'}
          </small>
        </div>

        <div className="form-group">
          <label htmlFor="message">Personal Message (Optional)</label>
          <textarea
            id="message"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Add a personal message..."
            rows={3}
          />
        </div>

        <div className="form-actions">
          <button
            type="button"
            className="btn-cancel"
            onClick={onClose}
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn-submit"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Sending...' : 'Send Invitation'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default InviteUserForm;
