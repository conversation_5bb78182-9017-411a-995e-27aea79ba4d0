// Update Static Roadmap Function
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Initialize Supabase client
const initSupabase = () => {
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_ANON_KEY;
  
  if (!supabaseUrl || !supabaseKey) {
    throw new Error('Missing Supabase credentials');
  }
  
  return createClient(supabaseUrl, supabaseKey);
};

exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Content-Type': 'application/json'
  };
  
  // Only allow POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }
  
  try {
    // Initialize Supabase client
    const supabase = initSupabase();
    
    // Get the latest roadmap data from Supabase
    const { data: roadmapData, error: roadmapError } = await supabase
      .from('roadmap')
      .select('*')
      .order('updated_at', { ascending: false })
      .limit(1);
    
    if (roadmapError) {
      console.error('Error fetching roadmap from Supabase:', roadmapError);
      throw roadmapError;
    }
    
    if (!roadmapData || roadmapData.length === 0) {
      throw new Error('No roadmap data found in Supabase');
    }
    
    // Get the roadmap data and latest feature
    const roadmap = roadmapData[0].data;
    const latestFeature = roadmapData[0].latest_feature;
    
    // Create the static roadmap data
    const staticRoadmapData = {
      data: roadmap,
      latest_feature: latestFeature,
      updated_at: new Date().toISOString()
    };
    
    // Write the data to a file
    const staticRoadmapPath = path.join(__dirname, '../../static-roadmap.json');
    fs.writeFileSync(staticRoadmapPath, JSON.stringify(staticRoadmapData, null, 2));
    
    // Also write to the netlify-deploy directory if it exists
    try {
      const deployRoadmapPath = path.join(__dirname, '../../netlify-deploy/static-roadmap.json');
      fs.writeFileSync(deployRoadmapPath, JSON.stringify(staticRoadmapData, null, 2));
    } catch (writeError) {
      console.warn('Could not write to netlify-deploy directory:', writeError);
    }
    
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        message: 'Static roadmap data updated successfully',
        data: {
          latest_feature: latestFeature,
          updated_at: staticRoadmapData.updated_at
        }
      })
    };
  } catch (error) {
    console.error('Error updating static roadmap data:', error);
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    };
  }
};
