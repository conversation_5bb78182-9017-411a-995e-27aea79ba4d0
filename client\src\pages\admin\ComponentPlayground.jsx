import React, { useState, useEffect } from 'react';
// import { Con<PERSON>er, <PERSON>, Col, Card, Tabs, Tab, Alert, Form } from 'react-bootstrap';
import FeatureFlagAdmin from '../../components/admin/FeatureFlagAdmin';
import { useFeatureFlags } from '../../contexts/feature-flags.context';
import ModernButton from '../../components/ui/modern/ModernButton';
import ModernCard from '../../components/ui/modern/ModernCard';
import FeatureFlagDebug from '../../components/debug/FeatureFlagDebug';
import {
  ModernFormGroup,
  ModernInput,
  ModernTextarea,
  ModernSelect,
  ModernCheckbox,
  ModernRadio
} from '../../components/ui/modern/ModernFormControls';

/**
 * ComponentPlayground
 *
 * A development environment for testing new UI components with feature flags.
 * This page allows developers to toggle between old and new components and
 * see them side by side.
 */
const ComponentPlayground = () => {
  const [activeTab, setActiveTab] = useState('buttons');
  const { isFeatureEnabled } = useFeatureFlags();

  // Check if the new UI is enabled
  const newUIEnabled = isFeatureEnabled('new-ui');

  // Force a re-render when feature flags change
  useEffect(() => {
    const handleStorageChange = (e) => {
      if (e.key === 'featureFlags') {
        // Force a re-render
        setActiveTab(activeTab);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [activeTab]);

  return (
    <Container className="py-4">
      <h1>Component Playground</h1>
      <p className="text-muted">
        Use this playground to test and compare old and new UI components.
      </p>

      <Row className="mb-4">
        <Col md={12}>
          <FeatureFlagAdmin />
        </Col>
      </Row>

      <FeatureFlagDebug />

      <Row>
        <Col md={12}>
          <Card>
            <Card.Header>
              <Tabs
                activeKey={activeTab}
                onSelect={(k) => setActiveTab(k)}
                className="mb-3"
              >
                <Tab eventKey="buttons" title="Buttons" />
                <Tab eventKey="cards" title="Cards" />
                <Tab eventKey="forms" title="Forms" />
                <Tab eventKey="typography" title="Typography" />
                <Tab eventKey="layout" title="Layout" />
              </Tabs>
            </Card.Header>
            <Card.Body>
              {newUIEnabled ? (
                <Alert variant="success">
                  <strong>New UI is enabled!</strong> You're seeing the Tailwind CSS components.
                </Alert>
              ) : (
                <Alert variant="info">
                  <strong>New UI is disabled.</strong> You're seeing the Bootstrap components.
                </Alert>
              )}

              {activeTab === 'buttons' && (
                <ButtonsPlayground />
              )}

              {activeTab === 'cards' && (
                <CardsPlayground />
              )}

              {activeTab === 'forms' && (
                <FormsPlayground />
              )}

              {activeTab === 'typography' && (
                <TypographyPlayground />
              )}

              {activeTab === 'layout' && (
                <LayoutPlayground />
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

// Button components playground
const ButtonsPlayground = () => {
  const { isFeatureEnabled } = useFeatureFlags();
  const newUIEnabled = isFeatureEnabled('new-ui');

  return (
    <div>
      <h2>Buttons</h2>

      <Row className="mb-4">
        <Col md={6}>
          <Card>
            <Card.Header>Bootstrap Buttons</Card.Header>
            <Card.Body>
              <div className="d-flex flex-wrap gap-2">
                <button className="btn btn-primary">Primary</button>
                <button className="btn btn-secondary">Secondary</button>
                <button className="btn btn-success">Success</button>
                <button className="btn btn-danger">Danger</button>
                <button className="btn btn-warning">Warning</button>
                <button className="btn btn-info">Info</button>
              </div>

              <div className="mt-3 d-flex flex-wrap gap-2">
                <button className="btn btn-outline-primary">Primary</button>
                <button className="btn btn-outline-secondary">Secondary</button>
                <button className="btn btn-outline-success">Success</button>
                <button className="btn btn-outline-danger">Danger</button>
              </div>
            </Card.Body>
          </Card>
        </Col>

        <Col md={6}>
          <Card>
            <Card.Header>Tailwind Buttons {!newUIEnabled && '(Disabled)'}</Card.Header>
            <Card.Body>
              {newUIEnabled ? (
                <>
                  <div className="tw-flex tw-flex-wrap tw-gap-2">
                    <ModernButton variant="primary">Primary</ModernButton>
                    <ModernButton variant="secondary">Secondary</ModernButton>
                    <ModernButton variant="success">Success</ModernButton>
                    <ModernButton variant="danger">Danger</ModernButton>
                    <ModernButton variant="warning">Warning</ModernButton>
                    <ModernButton variant="info">Info</ModernButton>
                  </div>

                  <div className="tw-mt-3 tw-flex tw-flex-wrap tw-gap-2">
                    <ModernButton variant="primary" outline>Primary</ModernButton>
                    <ModernButton variant="secondary" outline>Secondary</ModernButton>
                    <ModernButton variant="success" outline>Success</ModernButton>
                    <ModernButton variant="danger" outline>Danger</ModernButton>
                  </div>

                  <div className="tw-mt-3 tw-flex tw-flex-wrap tw-gap-2">
                    <ModernButton variant="primary" size="sm">Small</ModernButton>
                    <ModernButton variant="primary">Default</ModernButton>
                    <ModernButton variant="primary" size="lg">Large</ModernButton>
                  </div>
                </>
              ) : (
                <div className="text-center py-4">
                  <p>Enable the "new-ui" feature flag to see Tailwind buttons</p>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

// Card components playground
const CardsPlayground = () => {
  const { isFeatureEnabled } = useFeatureFlags();
  const newUIEnabled = isFeatureEnabled('new-ui');

  return (
    <div>
      <h2>Cards</h2>

      <Row className="mb-4">
        <Col md={6}>
          <Card>
            <Card.Header>Bootstrap Card</Card.Header>
            <Card.Body>
              <Card>
                <Card.Header>Card Title</Card.Header>
                <Card.Body>
                  <Card.Title>Special title treatment</Card.Title>
                  <Card.Text>
                    With supporting text below as a natural lead-in to additional content.
                  </Card.Text>
                  <button className="btn btn-primary">Go somewhere</button>
                </Card.Body>
                <Card.Footer className="text-muted">2 days ago</Card.Footer>
              </Card>
            </Card.Body>
          </Card>
        </Col>

        <Col md={6}>
          <Card>
            <Card.Header>Tailwind Card {!newUIEnabled && '(Disabled)'}</Card.Header>
            <Card.Body>
              {newUIEnabled ? (
                <ModernCard
                  title="Special title treatment"
                  subtitle="With supporting text below as a natural lead-in to additional content."
                  footer={<div className="tw-text-gray-500 tw-text-sm">2 days ago</div>}
                >
                  <p className="tw-mb-4">With supporting text below as a natural lead-in to additional content.</p>
                  <ModernButton variant="primary">Go somewhere</ModernButton>
                </ModernCard>
              ) : (
                <div className="text-center py-4">
                  <p>Enable the "new-ui" feature flag to see Tailwind cards</p>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

// Forms playground component
const FormsPlayground = () => {
  const { isFeatureEnabled } = useFeatureFlags();
  const newUIEnabled = isFeatureEnabled('new-ui');

  return (
    <div>
      <h2>Forms</h2>

      <Row className="mb-4">
        <Col md={6}>
          <Card>
            <Card.Header>Bootstrap Forms</Card.Header>
            <Card.Body>
              <Form>
                <Form.Group className="mb-3">
                  <Form.Label>Email address</Form.Label>
                  <Form.Control type="email" placeholder="Enter email" />
                  <Form.Text className="text-muted">
                    We'll never share your email with anyone else.
                  </Form.Text>
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Label>Password</Form.Label>
                  <Form.Control type="password" placeholder="Password" />
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Label>Example textarea</Form.Label>
                  <Form.Control as="textarea" rows={3} />
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Label>Example select</Form.Label>
                  <Form.Select>
                    <option>Option 1</option>
                    <option>Option 2</option>
                    <option>Option 3</option>
                  </Form.Select>
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Check type="checkbox" label="Check me out" />
                </Form.Group>

                <button className="btn btn-primary" type="submit">
                  Submit
                </button>
              </Form>
            </Card.Body>
          </Card>
        </Col>

        <Col md={6}>
          <Card>
            <Card.Header>Tailwind Forms {!newUIEnabled && '(Disabled)'}</Card.Header>
            <Card.Body>
              {newUIEnabled ? (
                <form className="tw-space-y-4">
                  <ModernFormGroup
                    id="email"
                    label="Email address"
                    helpText="We'll never share your email with anyone else."
                  >
                    <ModernInput
                      type="email"
                      id="email"
                      placeholder="Enter email"
                    />
                  </ModernFormGroup>

                  <ModernFormGroup
                    id="password"
                    label="Password"
                  >
                    <ModernInput
                      type="password"
                      id="password"
                      placeholder="Password"
                    />
                  </ModernFormGroup>

                  <ModernFormGroup
                    id="textarea"
                    label="Example textarea"
                  >
                    <ModernTextarea
                      id="textarea"
                      rows={3}
                    />
                  </ModernFormGroup>

                  <ModernFormGroup
                    id="select"
                    label="Example select"
                  >
                    <ModernSelect id="select">
                      <option>Option 1</option>
                      <option>Option 2</option>
                      <option>Option 3</option>
                    </ModernSelect>
                  </ModernFormGroup>

                  <div className="tw-mb-4">
                    <ModernCheckbox
                      id="checkbox"
                      label="Check me out"
                    />
                  </div>

                  <ModernButton variant="primary" type="submit">
                    Submit
                  </ModernButton>
                </form>
              ) : (
                <div className="text-center py-4">
                  <p>Enable the "new-ui" feature flag to see Tailwind forms</p>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

const TypographyPlayground = () => (
  <div>
    <h2>Typography</h2>
    <p>Typography components will be added here.</p>
  </div>
);

const LayoutPlayground = () => (
  <div>
    <h2>Layout</h2>
    <p>Layout components will be added here.</p>
  </div>
);

export default ComponentPlayground;
