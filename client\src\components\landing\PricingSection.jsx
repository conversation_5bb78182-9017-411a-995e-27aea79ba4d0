import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, Button } from '@heroui/react';

/**
 * Pricing Section Component - Simple, Fair Pricing
 * 
 * Features:
 * - Three-tier pricing structure (Free, Professional, Enterprise)
 * - Clear feature comparison
 * - Transparent transaction fees
 * - Call-to-action buttons for each tier
 */
const PricingSection = ({ onGetStarted, onStartTrial, onContactSales }) => {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.8,
        staggerChildren: 0.2
      }
    }
  };

  const planVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  };

  // Pricing plans data
  const plans = [
    {
      id: 'free',
      name: 'FREE',
      subtitle: 'Get Started',
      price: '$0',
      period: '/month',
      description: 'Perfect for individuals and small teams getting started',
      features: [
        '5 active ventures',
        'Basic analytics',
        'Community support',
        'Standard bounties',
        'Alliance membership'
      ],
      buttonText: 'Start Free',
      buttonAction: onGetStarted,
      buttonColor: 'primary',
      popular: false,
      gradient: 'from-gray-500 to-slate-600',
      bgGradient: 'from-gray-50 to-slate-50',
      darkBgGradient: 'from-gray-900/20 to-slate-900/20'
    },
    {
      id: 'professional',
      name: 'PROFESSIONAL',
      subtitle: 'Scale & Grow',
      price: '$29',
      period: '/month',
      description: 'For growing teams and established businesses',
      features: [
        'Unlimited ventures',
        'Advanced analytics',
        'Priority support',
        'Premium bounties',
        'Advanced reporting'
      ],
      buttonText: 'Start Trial',
      buttonAction: onStartTrial,
      buttonColor: 'secondary',
      popular: true,
      gradient: 'from-purple-500 to-pink-500',
      bgGradient: 'from-purple-50 to-pink-50',
      darkBgGradient: 'from-purple-900/20 to-pink-900/20'
    },
    {
      id: 'enterprise',
      name: 'ENTERPRISE',
      subtitle: 'Custom Solutions',
      price: '$99',
      period: '/month',
      description: 'For large organizations with custom needs',
      features: [
        'Everything in Professional',
        'White-label options',
        'Custom integrations',
        'Dedicated support',
        'SLA guarantees'
      ],
      buttonText: 'Contact Sales',
      buttonAction: onContactSales,
      buttonColor: 'success',
      popular: false,
      gradient: 'from-green-500 to-emerald-500',
      bgGradient: 'from-green-50 to-emerald-50',
      darkBgGradient: 'from-green-900/20 to-emerald-900/20'
    }
  ];

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900 py-20">
      <motion.div
        className="max-w-7xl mx-auto px-6"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
      >
        {/* Section Header */}
        <motion.div variants={planVariants} className="text-center mb-16">
          <h2 className="text-5xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            💰 Simple, Fair Pricing
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Choose the plan that fits your needs. All plans include our core features 
            with transparent pricing and no hidden fees.
          </p>
        </motion.div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          {plans.map((plan, index) => (
            <motion.div
              key={plan.id}
              variants={planVariants}
              whileHover={{ scale: 1.02, y: -5 }}
              transition={{ duration: 0.3 }}
              className="relative"
            >
              {/* Popular Badge */}
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                  <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-2 rounded-full text-sm font-semibold shadow-lg">
                    Most Popular
                  </div>
                </div>
              )}

              <Card className={`h-full bg-gradient-to-br ${plan.bgGradient} dark:${plan.darkBgGradient} border-2 ${plan.popular ? 'border-purple-300 dark:border-purple-600 shadow-xl' : 'border-gray-200 dark:border-gray-700'} hover:shadow-xl transition-all duration-300`}>
                <CardBody className="p-8">
                  {/* Plan Header */}
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                      {plan.name}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 mb-4">
                      {plan.subtitle}
                    </p>
                    
                    <div className="mb-4">
                      <span className="text-5xl font-bold text-gray-900 dark:text-white">
                        {plan.price}
                      </span>
                      <span className="text-gray-600 dark:text-gray-400">
                        {plan.period}
                      </span>
                    </div>
                    
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {plan.description}
                    </p>
                  </div>

                  {/* Features List */}
                  <div className="mb-8">
                    <ul className="space-y-4">
                      {plan.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-gray-700 dark:text-gray-300">
                          <span className="text-green-500 mr-3">✅</span>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Call to Action */}
                  <Button
                    size="lg"
                    color={plan.buttonColor}
                    className={`w-full font-semibold py-4 text-lg ${plan.popular ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg' : ''} transition-all duration-300`}
                    onPress={plan.buttonAction}
                  >
                    {plan.buttonText}
                  </Button>
                </CardBody>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Transaction Fee Information */}
        <motion.div variants={planVariants} className="text-center">
          <div className="max-w-4xl mx-auto p-6 bg-white/50 dark:bg-black/20 rounded-lg backdrop-blur-sm">
            <div className="flex items-center justify-center mb-4">
              <span className="text-3xl mr-3">💡</span>
              <h4 className="text-xl font-semibold text-gray-900 dark:text-white">
                Transaction Fees
              </h4>
            </div>
            <p className="text-gray-600 dark:text-gray-300 mb-2">
              Plus 2.5% transaction fee on processed payments
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              (1.5% for Enterprise • $0.50 minimum)
            </p>
          </div>
        </motion.div>

        {/* Additional Information */}
        <motion.div variants={planVariants} className="text-center mt-16">
          <div className="max-w-4xl mx-auto">
            <h3 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
              Why Choose Royaltea?
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="p-6">
                <div className="text-4xl mb-4">🔒</div>
                <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  Secure & Reliable
                </h4>
                <p className="text-gray-600 dark:text-gray-400">
                  Enterprise-grade security with 99.9% uptime guarantee
                </p>
              </div>
              
              <div className="p-6">
                <div className="text-4xl mb-4">📈</div>
                <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  Proven Results
                </h4>
                <p className="text-gray-600 dark:text-gray-400">
                  $2.3M+ distributed fairly among 2,500+ creators
                </p>
              </div>
              
              <div className="p-6">
                <div className="text-4xl mb-4">🤝</div>
                <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  Fair & Transparent
                </h4>
                <p className="text-gray-600 dark:text-gray-400">
                  No hidden fees, clear pricing, honest revenue sharing
                </p>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default PricingSection;
