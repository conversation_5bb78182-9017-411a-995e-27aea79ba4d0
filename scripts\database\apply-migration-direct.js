// <PERSON>ript to apply the Retro Profile migration directly using Supabase SQL API
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please set SUPABASE_URL and SUPABASE_SERVICE_KEY environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);
console.log('Supabase client initialized');

// Function to execute a SQL query directly
async function executeSQLQuery(query) {
  try {
    const response = await fetch(`${supabaseUrl}/rest/v1/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabase<PERSON>ey,
        'Authorization': `Bearer ${supabaseKey}`,
        'Prefer': 'resolution=merge-duplicates'
      },
      body: JSON.stringify({
        query: query
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`SQL query failed: ${errorText}`);
    }

    return { success: true };
  } catch (error) {
    console.error('Error executing SQL query:', error);
    return { success: false, error: error.message };
  }
}

// Function to split SQL into individual statements
function splitSQLStatements(sql) {
  // Split on semicolons but ignore those inside quotes or comments
  const statements = [];
  let currentStatement = '';
  let inSingleQuote = false;
  let inDoubleQuote = false;
  let inComment = false;
  
  for (let i = 0; i < sql.length; i++) {
    const char = sql[i];
    const nextChar = sql[i + 1] || '';
    
    // Handle comments
    if (!inSingleQuote && !inDoubleQuote) {
      if (char === '-' && nextChar === '-') {
        inComment = true;
      } else if (inComment && char === '\n') {
        inComment = false;
      }
    }
    
    // Handle quotes
    if (!inComment) {
      if (char === "'" && sql[i - 1] !== '\\') {
        inSingleQuote = !inSingleQuote;
      } else if (char === '"' && sql[i - 1] !== '\\') {
        inDoubleQuote = !inDoubleQuote;
      }
    }
    
    // Add character to current statement
    currentStatement += char;
    
    // Check for statement end
    if (char === ';' && !inSingleQuote && !inDoubleQuote && !inComment) {
      statements.push(currentStatement.trim());
      currentStatement = '';
    }
  }
  
  // Add the last statement if it doesn't end with a semicolon
  if (currentStatement.trim()) {
    statements.push(currentStatement.trim());
  }
  
  return statements.filter(stmt => stmt.trim() !== '' && !stmt.trim().startsWith('--'));
}

// Main function to apply the migration
async function applyMigration() {
  try {
    console.log('Starting Retro Profile migration process...');
    
    // Read the migration SQL file
    const migrationPath = path.join(__dirname, 'supabase/migrations/20240701000003_create_retro_profile_system.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    console.log(`Read migration file: ${migrationPath}`);
    
    // Split into individual statements
    const statements = splitSQLStatements(migrationSQL);
    console.log(`Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement
    let successCount = 0;
    let failCount = 0;
    const failedStatements = [];
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`\nExecuting statement ${i + 1}/${statements.length}...`);
      console.log(statement.substring(0, 100) + (statement.length > 100 ? '...' : ''));
      
      try {
        // Check if we can execute this statement directly
        const { data, error } = await supabase.from('users').select('id').limit(1);
        
        if (error) {
          console.error('Error connecting to database:', error);
          failCount++;
          failedStatements.push({
            index: i + 1,
            statement: statement.substring(0, 100) + (statement.length > 100 ? '...' : ''),
            error: error.message
          });
          continue;
        }
        
        // Try to execute the statement
        if (statement.trim().toUpperCase().startsWith('ALTER TABLE')) {
          // Extract column name for ALTER TABLE statements
          const match = statement.match(/ADD\s+COLUMN\s+(?:IF\s+NOT\s+EXISTS\s+)?(\w+)/i);
          if (match && match[1]) {
            const columnName = match[1];
            console.log(`Adding column: ${columnName}`);
            
            // Check if column already exists
            try {
              const { data, error } = await supabase.from('users').select(columnName).limit(1);
              if (!error) {
                console.log(`Column ${columnName} already exists, skipping`);
                successCount++;
                continue;
              }
            } catch (err) {
              // Column doesn't exist, continue with adding it
            }
          }
        }
        
        // Execute the statement
        const result = await executeSQLQuery(statement);
        
        if (result.success) {
          successCount++;
          console.log('Success!');
        } else {
          failCount++;
          console.error(`Error: ${result.error}`);
          failedStatements.push({
            index: i + 1,
            statement: statement.substring(0, 100) + (statement.length > 100 ? '...' : ''),
            error: result.error
          });
        }
      } catch (error) {
        failCount++;
        console.error(`Error: ${error.message}`);
        failedStatements.push({
          index: i + 1,
          statement: statement.substring(0, 100) + (statement.length > 100 ? '...' : ''),
          error: error.message
        });
      }
    }
    
    // Print summary
    console.log('\n=== Migration Summary ===');
    console.log(`Total statements: ${statements.length}`);
    console.log(`Successfully executed: ${successCount}`);
    console.log(`Failed: ${failCount}`);
    
    if (failCount > 0) {
      console.log('\nFailed Statements:');
      failedStatements.forEach(failure => {
        console.log(`\n${failure.index}. ${failure.statement}`);
        console.log(`   Error: ${failure.error}`);
      });
    }
    
    // Check if the migration was successful
    if (failCount === 0) {
      console.log('\nMigration completed successfully!');
      
      // Verify that the columns were added
      console.log('\nVerifying migration...');
      try {
        const { data, error } = await supabase
          .from('users')
          .select('headline, location, website, cover_image_url')
          .limit(1);
        
        if (error) {
          console.error('Verification failed:', error.message);
        } else {
          console.log('Verification successful! The new columns are available.');
          console.log('Sample data:', data);
        }
      } catch (error) {
        console.error('Verification failed:', error.message);
      }
    } else {
      console.log('\nMigration completed with errors. Please check the failed statements.');
    }
  } catch (error) {
    console.error('Migration failed:', error);
  }
}

// Run the migration
applyMigration();
