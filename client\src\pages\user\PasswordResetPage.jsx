import React, { useState, useContext } from 'react';
import { Link } from 'react-router-dom';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { toast } from 'react-hot-toast';

const PasswordResetPage = () => {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const { resetPassword } = useContext(UserContext);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!email) {
      toast.error('Please enter your email address');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      await resetPassword(email);
      setIsSuccess(true);
      toast.success('Password reset email sent! Check your inbox.');
    } catch (error) {
      console.error('Error sending reset email:', error);
      toast.error(error.message || 'Failed to send reset email. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mt-5">
      <div className="row justify-content-center">
        <div className="col-md-6 col-lg-5">
          <div className="card shadow border-0 rounded-lg">
            <div className="card-body p-4">
              <h2 className="text-center mb-4">Reset Password</h2>
              
              {isSuccess ? (
                <div className="text-center">
                  <div className="alert alert-success">
                    <i className="bi bi-check-circle me-2"></i>
                    Password reset email sent!
                  </div>
                  <p>
                    We've sent instructions to reset your password to <strong>{email}</strong>.
                    Please check your email inbox and follow the instructions.
                  </p>
                  <p className="mt-4">
                    <Link to="/login" className="btn btn-primary">
                      Return to Login
                    </Link>
                  </p>
                </div>
              ) : (
                <form onSubmit={handleSubmit}>
                  <div className="mb-3">
                    <label htmlFor="email" className="form-label">Email Address</label>
                    <input
                      type="email"
                      id="email"
                      className="form-control"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="Enter your email"
                      required
                    />
                    <div className="form-text">
                      We'll send you instructions to reset your password.
                    </div>
                  </div>
                  
                  <div className="d-grid gap-2">
                    <button
                      type="submit"
                      className="btn btn-primary"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                          Sending...
                        </>
                      ) : (
                        'Send Reset Link'
                      )}
                    </button>
                    
                    <Link to="/login" className="btn btn-outline-secondary">
                      Cancel
                    </Link>
                  </div>
                </form>
              )}
            </div>
          </div>
          
          <div className="text-center mt-3">
            <Link to="/login" className="text-decoration-none">
              Remember your password? Sign in
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PasswordResetPage;
