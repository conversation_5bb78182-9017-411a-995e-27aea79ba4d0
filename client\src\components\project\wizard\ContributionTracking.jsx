import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';

const ContributionTracking = ({ projectData, setProjectData }) => {
  // Common task types with default difficulty values based on project type
  const commonTaskTypes = {
    game: [
      { name: 'Concept Art', difficulty: 4 },
      { name: 'Character Design', difficulty: 5 },
      { name: 'Environment Art', difficulty: 4 },
      { name: 'UI Design', difficulty: 3 },
      { name: 'Animation', difficulty: 5 },
      { name: 'Sound Design', difficulty: 4 },
      { name: 'Music Composition', difficulty: 4 },
      { name: 'Voice Acting', difficulty: 3 },
      { name: 'Level Design', difficulty: 4 },
      { name: 'Game Design Document', difficulty: 3 },
      { name: 'Programming - Core Systems', difficulty: 5 },
      { name: 'Programming - UI', difficulty: 3 },
      { name: 'Programming - AI', difficulty: 5 },
      { name: 'Programming - Physics', difficulty: 5 },
      { name: 'Programming - Networking', difficulty: 5 },
      { name: 'QA Testing', difficulty: 2 },
      { name: 'Bug Fixing', difficulty: 3 },
      { name: 'Project Management', difficulty: 3 },
      { name: 'Marketing Materials', difficulty: 3 },
      { name: 'Trailer Production', difficulty: 4 },
      { name: 'Attend Meeting', difficulty: 1 }
    ],
    app: [
      { name: 'UI/UX Design', difficulty: 4 },
      { name: 'Wireframing', difficulty: 3 },
      { name: 'Frontend Development', difficulty: 4 },
      { name: 'Backend Development', difficulty: 5 },
      { name: 'API Integration', difficulty: 4 },
      { name: 'Database Design', difficulty: 4 },
      { name: 'User Testing', difficulty: 2 },
      { name: 'Documentation', difficulty: 2 },
      { name: 'Bug Fixing', difficulty: 3 },
      { name: 'Performance Optimization', difficulty: 5 },
      { name: 'Security Implementation', difficulty: 5 },
      { name: 'Attend Meeting', difficulty: 1 }
    ],
    website: [
      { name: 'UI/UX Design', difficulty: 4 },
      { name: 'Wireframing', difficulty: 3 },
      { name: 'Frontend Development', difficulty: 4 },
      { name: 'Backend Development', difficulty: 5 },
      { name: 'Content Creation', difficulty: 3 },
      { name: 'SEO Optimization', difficulty: 3 },
      { name: 'Responsive Design', difficulty: 4 },
      { name: 'Cross-browser Testing', difficulty: 3 },
      { name: 'Performance Optimization', difficulty: 4 },
      { name: 'Attend Meeting', difficulty: 1 }
    ],
    other: [
      { name: 'Design', difficulty: 4 },
      { name: 'Development', difficulty: 4 },
      { name: 'Documentation', difficulty: 2 },
      { name: 'Testing', difficulty: 2 },
      { name: 'Project Management', difficulty: 3 },
      { name: 'Content Creation', difficulty: 3 },
      { name: 'Research', difficulty: 3 },
      { name: 'Analysis', difficulty: 4 },
      { name: 'Attend Meeting', difficulty: 1 }
    ]
  };

  const [newTaskType, setNewTaskType] = useState('');
  const [newTaskDifficulty, setNewTaskDifficulty] = useState(3);
  const [taskTypes, setTaskTypes] = useState([]);

  // Initialize task types based on project type
  useEffect(() => {
    if (!projectData.contribution_tracking.task_types || projectData.contribution_tracking.task_types.length === 0) {
      const projectType = projectData.project_type || 'other';
      const defaultTaskTypes = commonTaskTypes[projectType] || commonTaskTypes.other;

      setTaskTypes(defaultTaskTypes);

      setProjectData({
        ...projectData,
        contribution_tracking: {
          ...projectData.contribution_tracking,
          task_types: defaultTaskTypes
        }
      });

      toast.success(`Added default task types for ${projectType} project`);
    } else {
      setTaskTypes(projectData.contribution_tracking.task_types);
    }
  }, []);

  // Update task difficulty
  const updateTaskDifficulty = (index, newDifficulty) => {
    const updatedTaskTypes = [...taskTypes];
    updatedTaskTypes[index].difficulty = newDifficulty;

    setTaskTypes(updatedTaskTypes);

    setProjectData({
      ...projectData,
      contribution_tracking: {
        ...projectData.contribution_tracking,
        task_types: updatedTaskTypes
      }
    });
  };

  // Add new task type
  const addTaskType = () => {
    if (!newTaskType) {
      toast.error('Task type name is required');
      return;
    }

    if (taskTypes.some(task => task.name === newTaskType)) {
      toast.error('This task type already exists');
      return;
    }

    const newTask = {
      name: newTaskType,
      difficulty: newTaskDifficulty
    };

    const updatedTaskTypes = [...taskTypes, newTask];
    setTaskTypes(updatedTaskTypes);

    setProjectData({
      ...projectData,
      contribution_tracking: {
        ...projectData.contribution_tracking,
        task_types: updatedTaskTypes
      }
    });

    setNewTaskType('');
    setNewTaskDifficulty(3);
    toast.success('Task type added successfully');
  };

  // Remove task type
  const removeTaskType = (index) => {
    const updatedTaskTypes = taskTypes.filter((_, i) => i !== index);

    setTaskTypes(updatedTaskTypes);

    setProjectData({
      ...projectData,
      contribution_tracking: {
        ...projectData.contribution_tracking,
        task_types: updatedTaskTypes
      }
    });

    toast.success('Task type removed');
  };

  // Integration options
  const integrations = [
    {
      name: 'GitHub',
      icon: 'https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png',
      status: 'coming soon'
    },
    {
      name: 'Trello',
      icon: 'https://cdn.iconscout.com/icon/free/png-256/trello-226534.png',
      status: 'coming soon'
    },
    {
      name: 'Codecks',
      icon: 'https://codecks.io/favicon/favicon-32x32.png',
      status: 'coming soon'
    },
    {
      name: 'Jira',
      icon: 'https://cdn.worldvectorlogo.com/logos/jira-1.svg',
      status: 'coming soon'
    },
    {
      name: 'Discord',
      icon: 'https://assets-global.website-files.com/6257adef93867e50d84d30e2/636e0a6a49cf127bf92de1e2_icon_clyde_blurple_RGB.png',
      status: 'coming soon'
    }
  ];

  return (
    <div className="wizard-step-content">
      <h2 className="step-title">Contribution Tracking</h2>
      <p className="step-description">
        Configure common task types and their difficulty levels for your project.
      </p>

      <div className="alert alert-info mb-4">
        <div className="d-flex">
          <div className="me-3">
            <i className="bi bi-info-circle-fill fs-4"></i>
          </div>
          <div>
            <h4 className="h6">Task Difficulty Levels</h4>
            <p className="mb-0">
              We've pre-populated common task types for your project with default difficulty levels.
              You can adjust these values or add custom task types as needed.
            </p>
          </div>
        </div>
      </div>

      <div className="tracking-section">
        <h3 className="tracking-section-title">Common Task Types</h3>
        <p className="text-muted mb-3">
          These task types will be available when tracking contributions. Adjust difficulty levels as needed.
        </p>

        <div className="table-responsive mb-4">
          <table className="table table-hover">
            <thead>
              <tr>
                <th>Task Type</th>
                <th>Difficulty Level</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {taskTypes.map((task, index) => (
                <tr key={index}>
                  <td>{task.name}</td>
                  <td>
                    <div className="d-flex align-items-center">
                      <button
                        className="btn btn-sm btn-outline-secondary me-2"
                        onClick={() => updateTaskDifficulty(index, Math.max(1, task.difficulty - 1))}
                        disabled={task.difficulty <= 1}
                      >
                        <i className="bi bi-dash"></i>
                      </button>
                      <div className="difficulty-badge difficulty-{task.difficulty}">
                        {task.difficulty}
                      </div>
                      <button
                        className="btn btn-sm btn-outline-secondary ms-2"
                        onClick={() => updateTaskDifficulty(index, Math.min(5, task.difficulty + 1))}
                        disabled={task.difficulty >= 5}
                      >
                        <i className="bi bi-plus"></i>
                      </button>
                    </div>
                  </td>
                  <td>
                    <button
                      className="btn btn-sm btn-outline-danger"
                      onClick={() => removeTaskType(index)}
                    >
                      <i className="bi bi-trash"></i>
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="card mb-4">
          <div className="card-header">
            <h5 className="mb-0">Add Custom Task Type</h5>
          </div>
          <div className="card-body">
            <div className="row g-3">
              <div className="col-md-6">
                <label htmlFor="newTaskType" className="form-label">Task Name</label>
                <input
                  type="text"
                  className="form-control"
                  id="newTaskType"
                  placeholder="e.g., Character Rigging"
                  value={newTaskType}
                  onChange={(e) => setNewTaskType(e.target.value)}
                />
              </div>
              <div className="col-md-4">
                <label htmlFor="newTaskDifficulty" className="form-label">Difficulty (1-5)</label>
                <select
                  className="form-select"
                  id="newTaskDifficulty"
                  value={newTaskDifficulty}
                  onChange={(e) => setNewTaskDifficulty(parseInt(e.target.value))}
                >
                  <option value="1">1 - Very Easy</option>
                  <option value="2">2 - Easy</option>
                  <option value="3">3 - Medium</option>
                  <option value="4">4 - Hard</option>
                  <option value="5">5 - Very Hard</option>
                </select>
              </div>
              <div className="col-md-2 d-flex align-items-end">
                <button
                  type="button"
                  className="btn btn-primary w-100"
                  onClick={addTaskType}
                >
                  Add
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="difficulty-legend mb-4">
          <h5 className="h6 mb-3">Difficulty Scale Reference</h5>
          <div className="row">
            <div className="col-md-6">
              <div className="difficulty-item">
                <span className="difficulty-badge difficulty-1">1</span>
                <span className="difficulty-text">Very Easy - Minimal effort (e.g., attending meetings)</span>
              </div>
              <div className="difficulty-item">
                <span className="difficulty-badge difficulty-2">2</span>
                <span className="difficulty-text">Easy - Straightforward tasks (e.g., basic documentation)</span>
              </div>
              <div className="difficulty-item">
                <span className="difficulty-badge difficulty-3">3</span>
                <span className="difficulty-text">Medium - Average effort (e.g., standard programming tasks)</span>
              </div>
            </div>
            <div className="col-md-6">
              <div className="difficulty-item">
                <span className="difficulty-badge difficulty-4">4</span>
                <span className="difficulty-text">Hard - Significant effort (e.g., complex design work)</span>
              </div>
              <div className="difficulty-item">
                <span className="difficulty-badge difficulty-5">5</span>
                <span className="difficulty-text">Very Hard - Complex tasks (e.g., engine programming)</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="tracking-section">
        <h3 className="tracking-section-title">External Integrations</h3>
        <p className="text-muted mb-3">
          Connect with external tools to automatically track contributions (coming soon).
        </p>

        <div className="integration-list">
          {integrations.map((integration, index) => (
            <div key={index} className="integration-item">
              <img
                src={integration.icon}
                alt={integration.name}
                className="integration-icon"
              />
              <div className="integration-info">
                <div className="integration-name">{integration.name}</div>
                <div className="integration-status">
                  <span className="coming-soon-badge">
                    {integration.status}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ContributionTracking;
