# Final Migration Instructions for Users Table

This guide provides the final solution to fix the users table structure and ensure user profiles are created correctly. Follow these steps to apply the migration manually:

## Step 1: Access the SQL Editor

1. Log in to your Supabase dashboard
2. Select your project
3. Click on "SQL Editor" in the left sidebar
4. Click "New Query" to create a new SQL query

## Step 2: Check the Current Table Structure

First, let's check the current structure of the users table to understand what we're working with:

```sql
-- Check the structure of the users table
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = 'users'
ORDER BY ordinal_position;
```

## Step 3: Run the Final Migration SQL

Copy and paste the following SQL into the editor:

```sql
-- Fix users table columns to ensure consistency
-- This migration checks for all possible timestamp column names and standardizes them

DO $$
DECLARE
    has_created_at BOOLEAN := FALSE;
    has_updated_at BOOLEAN := FALSE;
    has_date_created BOOLEAN := FALSE;
BEGIN
    -- Check which columns exist
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'created_at'
    ) INTO has_created_at;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'updated_at'
    ) INTO has_updated_at;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'date_created'
    ) INTO has_date_created;
    
    -- Output the current state
    RAISE NOTICE 'Current column state: created_at=%, updated_at=%, date_created=%', 
        has_created_at, has_updated_at, has_date_created;
    
    -- Fix the columns based on what exists
    IF has_date_created AND NOT has_created_at THEN
        -- Rename date_created to created_at for consistency
        ALTER TABLE public.users RENAME COLUMN date_created TO created_at;
        RAISE NOTICE 'Renamed date_created to created_at';
        has_created_at := TRUE;
    END IF;
    
    -- Add created_at if it doesn't exist
    IF NOT has_created_at THEN
        ALTER TABLE public.users ADD COLUMN created_at TIMESTAMPTZ DEFAULT NOW();
        RAISE NOTICE 'Added created_at column';
    END IF;
    
    -- Add updated_at if it doesn't exist
    IF NOT has_updated_at THEN
        ALTER TABLE public.users ADD COLUMN updated_at TIMESTAMPTZ DEFAULT NOW();
        RAISE NOTICE 'Added updated_at column';
    END IF;
    
    -- Update the trigger function to use the standardized column names
    CREATE OR REPLACE FUNCTION public.handle_new_user()
    RETURNS TRIGGER AS $$
    DECLARE
      display_name TEXT;
      user_exists BOOLEAN;
    BEGIN
      -- Check if user already exists in the users table
      SELECT EXISTS (
        SELECT 1 FROM public.users WHERE id = NEW.id
      ) INTO user_exists;
      
      -- Only proceed if the user doesn't already exist
      IF NOT user_exists THEN
        -- Set display name from user metadata or email
        IF NEW.raw_user_meta_data->>'full_name' IS NOT NULL THEN
          display_name := NEW.raw_user_meta_data->>'full_name';
        ELSIF NEW.raw_user_meta_data->>'name' IS NOT NULL THEN
          display_name := NEW.raw_user_meta_data->>'name';
        ELSE
          -- Extract username from email (part before @)
          display_name := split_part(NEW.email, '@', 1);
        END IF;
        
        -- Insert the new user
        INSERT INTO public.users (id, email, display_name, created_at, updated_at)
        VALUES (
          NEW.id,
          NEW.email,
          display_name,
          NOW(),
          NOW()
        );
        
        RAISE NOTICE 'Created new user profile for %', NEW.email;
      ELSE
        RAISE NOTICE 'User % already exists in the users table', NEW.email;
      END IF;
      
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql SECURITY DEFINER;
    
    -- Update the user update trigger function
    CREATE OR REPLACE FUNCTION public.handle_user_update()
    RETURNS TRIGGER AS $$
    BEGIN
      -- Update the user in the users table if they exist
      UPDATE public.users
      SET 
        email = NEW.email,
        updated_at = NOW()
      WHERE id = NEW.id;
      
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql SECURITY DEFINER;
    
    -- Make sure the triggers exist
    DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
    CREATE TRIGGER on_auth_user_created
      AFTER INSERT ON auth.users
      FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
      
    DROP TRIGGER IF EXISTS on_auth_user_updated ON auth.users;
    CREATE TRIGGER on_auth_user_updated
      AFTER UPDATE ON auth.users
      FOR EACH ROW EXECUTE FUNCTION public.handle_user_update();
      
    RAISE NOTICE 'Updated trigger functions to use created_at and updated_at columns';
END $$;
```

## Step 4: Execute the Query

Click the "Run" button to execute the SQL query. You should see a success message in the results panel.

## Step 5: Verify the Changes

After running the migration, check the table structure again to confirm the columns were added:

```sql
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = 'users'
ORDER BY ordinal_position;
```

## Step 6: Test the Authentication Flow

After successfully running the migration, test the authentication to ensure that:

1. New users can sign up with Google
2. User profiles are correctly created in the `users` table
3. The `created_at` and `updated_at` columns are properly populated

## Step 7: Check for User Profiles

To check if user profiles are being created correctly, run this query:

```sql
SELECT id, email, display_name, created_at, updated_at
FROM public.users
ORDER BY created_at DESC
LIMIT 10;
```

## Troubleshooting

If you encounter any issues:

1. Check the error messages in the SQL Editor results panel
2. Make sure you have the necessary permissions to alter the `users` table
3. Verify that the `users` table exists and has the expected structure

For more help, refer to the Supabase documentation on [schema migrations](https://supabase.com/docs/guides/database/migrations).
