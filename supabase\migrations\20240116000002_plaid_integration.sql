-- Plaid Integration Database Schema
-- Migration: 20240116000002_plaid_integration.sql

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Payment method types supported by Plaid
CREATE TYPE payment_method_type AS ENUM (
  'ach_standard',
  'ach_same_day',
  'rtp', -- Real-Time Payments
  'wire_domestic',
  'wire_international'
);

-- Payment status enum
CREATE TYPE payment_status AS ENUM (
  'pending',
  'processing',
  'completed',
  'failed',
  'cancelled',
  'returned',
  'reversed'
);

-- Account types
CREATE TYPE account_type AS ENUM (
  'checking',
  'savings',
  'business_checking',
  'business_savings',
  'money_market',
  'credit_union'
);

-- Payment direction
CREATE TYPE payment_direction AS ENUM (
  'inbound',  -- Money coming in (client payments)
  'outbound', -- Money going out (royalty distributions)
  'internal'  -- Internal transfers (escrow)
);

-- Plaid linked accounts table
CREATE TABLE plaid_accounts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Plaid identifiers
  plaid_account_id TEXT NOT NULL,
  plaid_item_id TEXT NOT NULL,
  plaid_access_token TEXT NOT NULL,
  
  -- Account details
  account_name TEXT NOT NULL,
  account_type account_type NOT NULL,
  account_subtype TEXT,
  institution_name TEXT,
  institution_id TEXT,
  
  -- Account capabilities
  supports_ach BOOLEAN DEFAULT true,
  supports_same_day_ach BOOLEAN DEFAULT false,
  supports_rtp BOOLEAN DEFAULT false,
  supports_wire BOOLEAN DEFAULT false,
  
  -- Account status
  is_verified BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  verification_method TEXT,
  
  -- Balance information
  available_balance DECIMAL(15,2),
  current_balance DECIMAL(15,2),
  balance_updated_at TIMESTAMP WITH TIME ZONE,
  
  -- Metadata
  account_metadata JSONB DEFAULT '{}'::jsonb,
  plaid_metadata JSONB DEFAULT '{}'::jsonb,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id, plaid_account_id)
);

-- Payment transactions table
CREATE TABLE payment_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Transaction identifiers
  plaid_transfer_id TEXT,
  external_transaction_id TEXT,
  
  -- Parties involved
  from_user_id UUID REFERENCES auth.users(id),
  to_user_id UUID REFERENCES auth.users(id),
  from_account_id UUID REFERENCES plaid_accounts(id),
  to_account_id UUID REFERENCES plaid_accounts(id),
  
  -- Transaction details
  amount DECIMAL(15,2) NOT NULL,
  currency TEXT DEFAULT 'USD',
  payment_method payment_method_type NOT NULL,
  payment_direction payment_direction NOT NULL,
  status payment_status DEFAULT 'pending',
  
  -- Transaction metadata
  description TEXT,
  reference_id TEXT, -- Link to project, royalty, etc.
  reference_type TEXT, -- 'project_payment', 'royalty_distribution', 'escrow_release'
  
  -- Timing information
  initiated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expected_settlement_date DATE,
  actual_settlement_date DATE,
  
  -- Fee information
  plaid_fee DECIMAL(10,2) DEFAULT 0,
  platform_fee DECIMAL(10,2) DEFAULT 0,
  total_fees DECIMAL(10,2) DEFAULT 0,
  
  -- Error handling
  failure_reason TEXT,
  retry_count INTEGER DEFAULT 0,
  max_retries INTEGER DEFAULT 3,
  
  -- Compliance and audit
  compliance_data JSONB DEFAULT '{}'::jsonb,
  audit_trail JSONB DEFAULT '[]'::jsonb,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Escrow accounts table
CREATE TABLE escrow_accounts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Project/contract reference
  project_id UUID, -- References projects table
  contract_id UUID, -- References contracts table
  
  -- Escrow details
  escrow_name TEXT NOT NULL,
  total_amount DECIMAL(15,2) NOT NULL,
  current_balance DECIMAL(15,2) DEFAULT 0,
  
  -- Funding source
  funding_account_id UUID REFERENCES plaid_accounts(id),
  funding_transaction_id UUID REFERENCES payment_transactions(id),
  
  -- Release conditions
  release_conditions JSONB DEFAULT '{}'::jsonb,
  auto_release_date DATE,
  requires_manual_approval BOOLEAN DEFAULT true,
  
  -- Status
  status TEXT DEFAULT 'pending_funding', -- pending_funding, funded, releasing, released, cancelled
  
  -- Metadata
  escrow_metadata JSONB DEFAULT '{}'::jsonb,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Escrow releases table
CREATE TABLE escrow_releases (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  escrow_account_id UUID NOT NULL REFERENCES escrow_accounts(id) ON DELETE CASCADE,
  
  -- Release details
  recipient_user_id UUID NOT NULL REFERENCES auth.users(id),
  recipient_account_id UUID NOT NULL REFERENCES plaid_accounts(id),
  amount DECIMAL(15,2) NOT NULL,
  
  -- Release metadata
  release_reason TEXT,
  milestone_reference TEXT,
  approved_by UUID REFERENCES auth.users(id),
  approved_at TIMESTAMP WITH TIME ZONE,
  
  -- Payment processing
  payment_transaction_id UUID REFERENCES payment_transactions(id),
  payment_method payment_method_type DEFAULT 'ach_standard',
  
  status TEXT DEFAULT 'pending', -- pending, approved, processing, completed, failed
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Payment method preferences table
CREATE TABLE payment_preferences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Default preferences
  preferred_payment_method payment_method_type DEFAULT 'ach_standard',
  preferred_account_id UUID REFERENCES plaid_accounts(id),
  
  -- Method-specific preferences
  ach_preferences JSONB DEFAULT '{}'::jsonb,
  rtp_preferences JSONB DEFAULT '{}'::jsonb,
  wire_preferences JSONB DEFAULT '{}'::jsonb,
  
  -- Notification preferences
  notify_on_payment_received BOOLEAN DEFAULT true,
  notify_on_payment_sent BOOLEAN DEFAULT true,
  notify_on_payment_failed BOOLEAN DEFAULT true,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id)
);

-- Payment routing rules table
CREATE TABLE payment_routing_rules (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Rule conditions
  min_amount DECIMAL(15,2),
  max_amount DECIMAL(15,2),
  urgency_level TEXT, -- 'immediate', 'same_day', 'standard'
  payment_direction payment_direction,
  
  -- Preferred method
  preferred_method payment_method_type NOT NULL,
  fallback_methods JSONB DEFAULT '[]'::jsonb,
  
  -- Rule metadata
  rule_name TEXT NOT NULL,
  rule_description TEXT,
  is_active BOOLEAN DEFAULT true,
  priority INTEGER DEFAULT 1,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Payment webhooks log table
CREATE TABLE plaid_webhooks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  webhook_type TEXT NOT NULL,
  webhook_code TEXT NOT NULL,
  item_id TEXT,
  
  -- Webhook data
  webhook_data JSONB NOT NULL,
  processed BOOLEAN DEFAULT false,
  processing_error TEXT,
  
  received_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  processed_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for performance
CREATE INDEX idx_plaid_accounts_user_id ON plaid_accounts(user_id);
CREATE INDEX idx_plaid_accounts_plaid_item_id ON plaid_accounts(plaid_item_id);
CREATE INDEX idx_payment_transactions_from_user ON payment_transactions(from_user_id);
CREATE INDEX idx_payment_transactions_to_user ON payment_transactions(to_user_id);
CREATE INDEX idx_payment_transactions_status ON payment_transactions(status);
CREATE INDEX idx_payment_transactions_reference ON payment_transactions(reference_id, reference_type);
CREATE INDEX idx_escrow_accounts_project_id ON escrow_accounts(project_id);
CREATE INDEX idx_escrow_releases_escrow_id ON escrow_releases(escrow_account_id);
CREATE INDEX idx_payment_preferences_user_id ON payment_preferences(user_id);
CREATE INDEX idx_plaid_webhooks_processed ON plaid_webhooks(processed);

-- Create updated_at triggers
CREATE TRIGGER update_plaid_accounts_updated_at BEFORE UPDATE ON plaid_accounts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_payment_transactions_updated_at BEFORE UPDATE ON payment_transactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_escrow_accounts_updated_at BEFORE UPDATE ON escrow_accounts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_escrow_releases_updated_at BEFORE UPDATE ON escrow_releases FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_payment_preferences_updated_at BEFORE UPDATE ON payment_preferences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_payment_routing_rules_updated_at BEFORE UPDATE ON payment_routing_rules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE plaid_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE escrow_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE escrow_releases ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_preferences ENABLE ROW LEVEL SECURITY;

-- RLS policies
CREATE POLICY "Users can manage their own accounts" ON plaid_accounts FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view their payment transactions" ON payment_transactions FOR SELECT USING (
  auth.uid() = from_user_id OR auth.uid() = to_user_id
);
CREATE POLICY "Users can view their payment preferences" ON payment_preferences FOR ALL USING (auth.uid() = user_id);

-- Insert default payment routing rules
INSERT INTO payment_routing_rules (rule_name, rule_description, min_amount, max_amount, urgency_level, payment_direction, preferred_method, fallback_methods, priority) VALUES
('Immediate Small Payments', 'For urgent payments under $100K', 0, 100000, 'immediate', 'outbound', 'rtp', '["ach_same_day", "ach_standard"]', 1),
('Same Day Medium Payments', 'For same-day payments under $1M', 0, 1000000, 'same_day', 'outbound', 'ach_same_day', '["ach_standard"]', 2),
('Large Wire Transfers', 'For payments over $1M', 1000000, NULL, 'standard', 'outbound', 'wire_domestic', '["ach_same_day", "ach_standard"]', 3),
('Standard ACH Default', 'Default for cost-effective transfers', 0, NULL, 'standard', 'outbound', 'ach_standard', '[]', 10);

COMMENT ON TABLE plaid_accounts IS 'Stores Plaid-linked bank accounts with capabilities and verification status';
COMMENT ON TABLE payment_transactions IS 'Records all payment transactions processed through Plaid';
COMMENT ON TABLE escrow_accounts IS 'Manages escrow accounts for project funding and milestone releases';
COMMENT ON TABLE escrow_releases IS 'Tracks individual releases from escrow accounts';
COMMENT ON TABLE payment_preferences IS 'User preferences for payment methods and notifications';
COMMENT ON TABLE payment_routing_rules IS 'Rules for automatically selecting optimal payment methods';
COMMENT ON TABLE plaid_webhooks IS 'Log of Plaid webhook events for processing and debugging';
