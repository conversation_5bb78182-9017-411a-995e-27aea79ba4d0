import { supabase } from '../../../utils/supabase/supabase.utils';

/**
 * Fetch user profile data
 * @param {string} userId - The user ID to fetch profile for
 * @returns {Promise<Object>} User profile data
 */
export const getUserProfile = async (userId) => {
  const { data, error } = await supabase
    .from('users')
    .select(`
      id,
      display_name,
      bio,
      avatar_url,
      headline,
      location,
      website,
      cover_image_url,
      status_message,
      availability_status,
      profile_views,
      theme_settings,
      custom_css,
      profile_song_url,
      privacy_settings,
      social_links,
      certifications,
      awards,
      stats,
      is_premium
    `)
    .eq('id', userId)
    .single();

  if (error) {
    console.error('Error fetching user profile:', error);
    throw error;
  }

  return data;
};

/**
 * Update user profile data
 * @param {string} userId - The user ID
 * @param {Object} profileData - The profile data to update
 * @returns {Promise<Object>} Updated user profile data
 */
export const updateUserProfile = async (userId, profileData) => {
  const { data, error } = await supabase
    .from('users')
    .update(profileData)
    .eq('id', userId)
    .select();

  if (error) {
    console.error('Error updating user profile:', error);
    throw error;
  }

  return data[0];
};

/**
 * Upload profile image (avatar or cover)
 * @param {string} userId - The user ID
 * @param {File} file - The image file to upload
 * @param {string} type - The type of image ('avatar' or 'cover')
 * @returns {Promise<string>} The URL of the uploaded image
 */
export const uploadProfileImage = async (userId, file, type = 'avatar') => {
  if (!file || !userId) return null;

  try {
    // Create a unique file name
    const fileExt = file.name.split('.').pop();
    const fileName = `${type}-${userId}-${Date.now()}.${fileExt}`;
    const filePath = `${type === 'avatar' ? 'avatars' : 'covers'}/${fileName}`;

    // First, try to delete any existing image with the same pattern
    try {
      const { data: existingFiles } = await supabase.storage
        .from('user-avatars')
        .list(type === 'avatar' ? 'avatars' : 'covers', {
          search: `${type}-${userId}`
        });

      if (existingFiles && existingFiles.length > 0) {
        await supabase.storage
          .from('user-avatars')
          .remove(existingFiles.map(f => `${type === 'avatar' ? 'avatars' : 'covers'}/${f.name}`));
      }
    } catch (deleteError) {
      // Ignore delete errors, just log them
      console.log('No existing images to delete or error:', deleteError);
    }

    // Upload to Supabase Storage
    const { data, error: uploadError } = await supabase.storage
      .from('user-avatars')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true
      });

    if (uploadError) {
      console.error('Upload error details:', uploadError);
      throw uploadError;
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from('user-avatars')
      .getPublicUrl(filePath);

    // Add a timestamp to force browser to reload the image
    const cachedUrl = `${urlData.publicUrl}?t=${Date.now()}`;
    return cachedUrl;
  } catch (error) {
    console.error(`Error uploading ${type}:`, error);
    throw error;
  }
};

/**
 * Upload profile song
 * @param {string} userId - The user ID
 * @param {File} file - The audio file to upload
 * @returns {Promise<string>} The URL of the uploaded song
 */
export const uploadProfileSong = async (userId, file) => {
  if (!file || !userId) return null;

  try {
    // Create a unique file name
    const fileExt = file.name.split('.').pop();
    const fileName = `song-${userId}-${Date.now()}.${fileExt}`;
    const filePath = `songs/${fileName}`;

    // First, try to delete any existing song with the same pattern
    try {
      const { data: existingFiles } = await supabase.storage
        .from('user-avatars')
        .list('songs', {
          search: `song-${userId}`
        });

      if (existingFiles && existingFiles.length > 0) {
        await supabase.storage
          .from('user-avatars')
          .remove(existingFiles.map(f => `songs/${f.name}`));
      }
    } catch (deleteError) {
      // Ignore delete errors, just log them
      console.log('No existing songs to delete or error:', deleteError);
    }

    // Upload to Supabase Storage
    const { data, error: uploadError } = await supabase.storage
      .from('user-avatars')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true
      });

    if (uploadError) {
      console.error('Upload error details:', uploadError);
      throw uploadError;
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from('user-avatars')
      .getPublicUrl(filePath);

    return urlData.publicUrl;
  } catch (error) {
    console.error('Error uploading profile song:', error);
    throw error;
  }
};

/**
 * Record a profile view
 * @param {string} profileId - The profile ID being viewed
 * @param {string} viewerId - The viewer's ID (optional)
 * @returns {Promise<void>}
 */
export const recordProfileView = async (profileId, viewerId = null) => {
  try {
    // First, increment the profile_views counter
    await supabase.rpc('increment_profile_views', { profile_id: profileId });

    // Then, record the view details
    await supabase
      .from('profile_views')
      .insert([
        {
          profile_id: profileId,
          viewer_id: viewerId,
          viewed_at: new Date().toISOString()
        }
      ]);
  } catch (error) {
    console.error('Error recording profile view:', error);
    // Don't throw error to avoid disrupting the user experience
  }
};

/**
 * Get profile comments
 * @param {string} profileId - The profile ID
 * @param {number} limit - Maximum number of comments to return
 * @param {number} offset - Offset for pagination
 * @returns {Promise<Array>} Array of comments with author details
 */
export const getProfileComments = async (profileId, limit = 10, offset = 0) => {
  try {
    // Use the RPC function to get profile comments
    const { data, error } = await supabase
      .rpc('get_profile_comments', {
        profile_id_param: profileId,
        limit_param: limit,
        offset_param: offset
      });

    if (error) {
      console.error('Error fetching profile comments:', error);
      return [];
    }

    // Transform the data to match the expected format
    return data.map(item => ({
      id: item.id,
      content: item.content,
      created_at: item.created_at,
      is_approved: item.is_approved,
      author: {
        id: item.author_id,
        display_name: item.author_display_name,
        avatar_url: item.author_avatar_url
      }
    })) || [];
  } catch (error) {
    console.error('Error fetching profile comments:', error);
    return [];
  }
};

/**
 * Add a comment to a profile
 * @param {string} profileId - The profile ID
 * @param {string} authorId - The author's ID
 * @param {string} content - The comment content
 * @returns {Promise<Object>} The created comment
 */
export const addProfileComment = async (profileId, authorId, content) => {
  const { data, error } = await supabase
    .from('profile_comments')
    .insert([
      {
        profile_id: profileId,
        author_id: authorId,
        content,
        is_approved: false // Comments require approval by default
      }
    ])
    .select();

  if (error) {
    console.error('Error adding profile comment:', error);
    throw error;
  }

  return data[0];
};

/**
 * Get available profile themes
 * @param {boolean} includePremium - Whether to include premium themes
 * @returns {Promise<Array>} Array of theme objects
 */
export const getProfileThemes = async (includePremium = false) => {
  let query = supabase
    .from('profile_themes')
    .select('*')
    .order('name');

  if (!includePremium) {
    query = query.eq('is_premium', false);
  }

  const { data, error } = await query;

  if (error) {
    console.error('Error fetching profile themes:', error);
    throw error;
  }

  return data || [];
};

/**
 * Get top collaborators for a user
 * @param {string} userId - The user ID
 * @returns {Promise<Array>} Array of collaborator objects with user details
 */
export const getTopCollaborators = async (userId) => {
  try {
    // Use the RPC function to get top collaborators
    const { data, error } = await supabase
      .rpc('get_top_collaborators', {
        user_id_param: userId
      });

    if (error) {
      console.error('Error fetching top collaborators:', error);
      return [];
    }

    // Transform the data to match the expected format
    return data.map(item => ({
      id: item.id,
      display_order: item.display_order,
      collaborator: {
        id: item.collaborator_id,
        display_name: item.collaborator_display_name,
        avatar_url: item.collaborator_avatar_url
      }
    })) || [];
  } catch (error) {
    console.error('Error fetching top collaborators:', error);
    return [];
  }
};

/**
 * Update top collaborators for a user
 * @param {string} userId - The user ID
 * @param {Array} collaborators - Array of collaborator objects with id and display_order
 * @returns {Promise<Array>} Array of updated collaborator objects
 */
export const updateTopCollaborators = async (userId, collaborators) => {
  // First, delete existing collaborators
  await supabase
    .from('top_collaborators')
    .delete()
    .eq('user_id', userId);

  // Then, insert the new ones
  const { data, error } = await supabase
    .from('top_collaborators')
    .insert(
      collaborators.map((collab, index) => ({
        user_id: userId,
        collaborator_id: collab.id,
        display_order: collab.display_order || index
      }))
    )
    .select();

  if (error) {
    console.error('Error updating top collaborators:', error);
    throw error;
  }

  return data || [];
};
