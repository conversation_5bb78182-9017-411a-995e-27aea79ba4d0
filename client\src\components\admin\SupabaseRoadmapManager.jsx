import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

const SupabaseRoadmapManager = () => {
  const { currentUser } = useContext(UserContext);
  const [roadmapData, setRoadmapData] = useState('');
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Check if user is admin and load roadmap data
  useEffect(() => {
    const checkAdminAndLoadData = async () => {
      if (!currentUser) {
        setLoading(false);
        return;
      }

      try {
        // Check if user is admin
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', currentUser.id)
          .single();

        if (userError) {
          console.error('Error checking admin status:', userError);
          setIsAdmin(false);
        } else {
          setIsAdmin(userData?.is_admin || false);
        }

        // Load roadmap data
        const { data: roadmapEntries, error: roadmapError } = await supabase
          .from('roadmap')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(1);

        if (roadmapError) {
          console.error('Error loading roadmap data:', roadmapError);
          toast.error('Failed to load roadmap data from database');

          // Try to load from localStorage as fallback
          const localData = localStorage.getItem('royalteaRoadmapData');
          if (localData) {
            setRoadmapData(localData);
          }
        } else if (roadmapEntries && roadmapEntries.length > 0) {
          // Format the data for display
          const formattedData = JSON.stringify(roadmapEntries[0].data, null, 2);
          setRoadmapData(formattedData);

          // Also update localStorage
          localStorage.setItem('royalteaRoadmapData', JSON.stringify(roadmapEntries[0].data));

          toast.success('Loaded roadmap data from database');
        } else {
          // No data in database, try localStorage
          const localData = localStorage.getItem('royalteaRoadmapData');
          if (localData) {
            setRoadmapData(localData);
          }
        }
      } catch (error) {
        console.error('Error in initialization:', error);
      } finally {
        setLoading(false);
      }
    };

    checkAdminAndLoadData();
  }, [currentUser]);

  // Save roadmap data to Supabase
  const saveToSupabase = async () => {
    if (!isAdmin) {
      toast.error('Only admins can save to the database');
      return;
    }

    try {
      setSaving(true);

      // Parse the data to make sure it's valid JSON
      let parsedData;
      try {
        parsedData = JSON.parse(roadmapData);
      } catch (parseError) {
        toast.error('Invalid JSON data');
        setSaving(false);
        return;
      }

      // First, delete existing entries
      const { error: deleteError } = await supabase
        .from('roadmap')
        .delete()
        .not('id', 'is', null); // Delete all records

      if (deleteError) {
        console.error('Error deleting existing roadmap data:', deleteError);
        toast.error('Failed to update database');
        setSaving(false);
        return;
      }

      // Then insert the new data
      const { data, error: insertError } = await supabase
        .from('roadmap')
        .insert({
          data: parsedData,
          updated_by: currentUser.id
        })
        .select();

      if (insertError) {
        console.error('Error saving roadmap data:', insertError);
        toast.error('Failed to save to database');
      } else {
        toast.success('Saved to database successfully');

        // Update localStorage as well
        localStorage.setItem('royalteaRoadmapData', JSON.stringify(parsedData));
      }
    } catch (error) {
      console.error('Error saving data:', error);
      toast.error('An error occurred while saving');
    } finally {
      setSaving(false);
    }
  };

  // Load data from localStorage
  const loadFromLocalStorage = () => {
    const localData = localStorage.getItem('royalteaRoadmapData');
    if (localData) {
      try {
        // Format the data for display
        const formattedData = JSON.stringify(JSON.parse(localData), null, 2);
        setRoadmapData(formattedData);
        toast.success('Loaded from localStorage');
      } catch (error) {
        console.error('Error parsing localStorage data:', error);
        toast.error('Invalid data in localStorage');
      }
    } else {
      toast.error('No data found in localStorage');
    }
  };

  // Save to localStorage
  const saveToLocalStorage = () => {
    try {
      // Parse the data to make sure it's valid JSON
      const parsedData = JSON.parse(roadmapData);

      // Save to localStorage
      localStorage.setItem('royalteaRoadmapData', JSON.stringify(parsedData));
      toast.success('Saved to localStorage');
    } catch (error) {
      console.error('Error saving to localStorage:', error);
      toast.error('Invalid JSON data');
    }
  };

  // Export data to file
  const exportData = () => {
    try {
      const dataStr = 'data:text/json;charset=utf-8,' + encodeURIComponent(roadmapData);
      const downloadAnchorNode = document.createElement('a');
      downloadAnchorNode.setAttribute('href', dataStr);
      downloadAnchorNode.setAttribute('download', 'roadmap-data.json');
      document.body.appendChild(downloadAnchorNode);
      downloadAnchorNode.click();
      downloadAnchorNode.remove();
      toast.success('Data exported successfully');
    } catch (error) {
      console.error('Error exporting data:', error);
      toast.error('Failed to export data');
    }
  };

  // Import data from file
  const importData = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'application/json';

    input.onchange = (e) => {
      const file = e.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          const data = event.target.result;
          // Validate that it's valid JSON
          JSON.parse(data);

          setRoadmapData(data);
          toast.success('Data imported successfully');
        } catch (error) {
          toast.error('Error importing data. Please check the file format.');
          console.error('Import error:', error);
        }
      };
      reader.readAsText(file);
    };

    input.click();
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-6xl mx-auto">
      <h1 className="text-2xl font-bold text-blue-800 mb-4">Roadmap Data Manager</h1>

      <div className="mb-4">
        <p className="text-gray-600 mb-2">
          This tool allows you to manage the roadmap data directly in the Supabase database.
        </p>
        {!isAdmin && (
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
            <p className="text-yellow-700">
              <strong>Note:</strong> You are not an admin. You can view and edit the data, but you cannot save to the database.
            </p>
          </div>
        )}
      </div>

      <div className="flex gap-2 mb-4">
        <button
          onClick={loadFromLocalStorage}
          className="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600"
        >
          Load from localStorage
        </button>
        <button
          onClick={saveToLocalStorage}
          className="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600"
        >
          Save to localStorage
        </button>
        <button
          onClick={exportData}
          className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Export to File
        </button>
        <button
          onClick={importData}
          className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600"
        >
          Import from File
        </button>
        {isAdmin && (
          <button
            onClick={saveToSupabase}
            disabled={saving}
            className={`px-3 py-1 bg-purple-600 text-white rounded hover:bg-purple-700 ${saving ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {saving ? 'Saving...' : 'Save to Database'}
          </button>
        )}
      </div>

      <div className="mb-4">
        <label htmlFor="roadmap-data" className="block text-sm font-medium text-gray-700 mb-2">
          Roadmap Data (JSON format)
        </label>
        <textarea
          id="roadmap-data"
          value={roadmapData}
          onChange={(e) => setRoadmapData(e.target.value)}
          className="w-full h-96 p-2 border border-gray-300 rounded font-mono text-sm"
          placeholder="Paste your roadmap JSON data here"
        />
      </div>

      <div className="text-sm text-gray-500">
        <p>Instructions:</p>
        <ul className="list-disc pl-5 mt-1">
          <li>Edit the JSON data directly in the text area</li>
          <li>Click "Save to Database" to update the roadmap data in Supabase</li>
          <li>Click "Save to localStorage" to update the local copy only</li>
          <li>Use Export/Import to backup or restore the data</li>
        </ul>
      </div>
    </div>
  );
};

export default SupabaseRoadmapManager;
