// Test Collaboration Tools System
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_KEY);

// Test user IDs
const TEST_USER_1 = '2a033231-d173-4292-aa36-90f4d735bcf3';
const TEST_USER_2 = '2a033231-d173-4292-aa36-90f4d735bcf3'; // Same for testing

async function testCollaborationSystem() {
  console.log('🧪 Testing Collaboration Tools System...');
  
  let testConversationId, testMessageId, testFileId, testActivityId;
  
  try {
    // Test 1: Create Direct Conversation
    console.log('\n1️⃣ Testing direct conversation creation...');
    
    const { data: conversationId, error: conversationError } = await supabase
      .rpc('create_direct_conversation', {
        user1_id: TEST_USER_1,
        user2_id: TEST_USER_2
      });
    
    if (conversationError) {
      console.log('❌ Direct conversation creation failed:', conversationError.message);
    } else {
      testConversationId = conversationId;
      console.log('✅ Direct conversation created:', testConversationId);
    }
    
    // Test 2: Send Message
    console.log('\n2️⃣ Testing message sending...');
    
    const messageData = {
      conversation_id: testConversationId,
      sender_id: TEST_USER_1,
      content: 'Hello! This is a test message for our collaboration system.',
      message_type: 'text'
    };
    
    const { data: message, error: messageError } = await supabase
      .from('messages')
      .insert([messageData])
      .select()
      .single();
    
    if (messageError) {
      console.log('❌ Message sending failed:', messageError.message);
    } else {
      testMessageId = message.id;
      console.log('✅ Message sent:', testMessageId);
      console.log(`   Content: ${message.content}`);
      console.log(`   Type: ${message.message_type}`);
    }
    
    // Test 3: Add Message Reaction
    console.log('\n3️⃣ Testing message reactions...');
    
    const reactionData = {
      message_id: testMessageId,
      user_id: TEST_USER_1,
      reaction: '👍'
    };
    
    const { data: reaction, error: reactionError } = await supabase
      .from('message_reactions')
      .insert([reactionData])
      .select()
      .single();
    
    if (reactionError) {
      console.log('❌ Message reaction failed:', reactionError.message);
    } else {
      console.log('✅ Message reaction added:', reaction.id);
      console.log(`   Reaction: ${reaction.reaction}`);
    }
    
    // Test 4: Create Group Conversation
    console.log('\n4️⃣ Testing group conversation creation...');
    
    const groupConversationData = {
      conversation_type: 'group',
      title: 'Test Collaboration Group',
      description: 'A test group for collaboration features',
      created_by: TEST_USER_1
    };
    
    const { data: groupConversation, error: groupError } = await supabase
      .from('conversations')
      .insert([groupConversationData])
      .select()
      .single();
    
    if (groupError) {
      console.log('❌ Group conversation creation failed:', groupError.message);
    } else {
      console.log('✅ Group conversation created:', groupConversation.id);
      console.log(`   Title: ${groupConversation.title}`);
      console.log(`   Type: ${groupConversation.conversation_type}`);
      
      // Add creator as admin participant
      await supabase
        .from('conversation_participants')
        .insert([{
          conversation_id: groupConversation.id,
          user_id: TEST_USER_1,
          role: 'admin',
          can_add_members: true,
          can_remove_members: true,
          can_edit_conversation: true
        }]);
      
      console.log('✅ Group admin participant added');
    }
    
    // Test 5: File Sharing
    console.log('\n5️⃣ Testing file sharing...');
    
    const fileData = {
      file_name: 'test-collaboration-doc.pdf',
      file_size: 1024000, // 1MB
      file_type: 'application/pdf',
      file_url: 'https://example.com/files/test-doc.pdf',
      uploaded_by: TEST_USER_1,
      upload_source: 'direct',
      conversation_id: testConversationId,
      description: 'Test document for collaboration',
      tags: ['test', 'collaboration', 'document'],
      visibility: 'private'
    };
    
    const { data: file, error: fileError } = await supabase
      .from('shared_files')
      .insert([fileData])
      .select()
      .single();
    
    if (fileError) {
      console.log('❌ File sharing failed:', fileError.message);
    } else {
      testFileId = file.id;
      console.log('✅ File shared:', testFileId);
      console.log(`   Name: ${file.file_name}`);
      console.log(`   Size: ${(file.file_size / 1024).toFixed(2)} KB`);
      console.log(`   Type: ${file.file_type}`);
    }
    
    // Test 6: File Permissions
    console.log('\n6️⃣ Testing file permissions...');
    
    const permissionData = {
      file_id: testFileId,
      user_id: TEST_USER_2,
      permission_type: 'view',
      granted_by: TEST_USER_1
    };
    
    const { data: permission, error: permissionError } = await supabase
      .from('file_permissions')
      .insert([permissionData])
      .select()
      .single();
    
    if (permissionError) {
      console.log('❌ File permission creation failed:', permissionError.message);
    } else {
      console.log('✅ File permission granted:', permission.id);
      console.log(`   Permission: ${permission.permission_type}`);
    }
    
    // Test 7: Activity Feed Creation
    console.log('\n7️⃣ Testing activity feed...');
    
    const activityData = {
      activity_type: 'collaboration_started',
      activity_title: 'New collaboration started',
      activity_description: 'Started collaborating on test project with file sharing',
      actor_id: TEST_USER_1,
      target_user_id: TEST_USER_2,
      conversation_id: testConversationId,
      file_id: testFileId,
      metadata: {
        collaboration_type: 'file_sharing',
        file_name: 'test-collaboration-doc.pdf'
      },
      visibility: 'private'
    };
    
    const { data: activity, error: activityError } = await supabase
      .from('activity_feeds')
      .insert([activityData])
      .select()
      .single();
    
    if (activityError) {
      console.log('❌ Activity creation failed:', activityError.message);
    } else {
      testActivityId = activity.id;
      console.log('✅ Activity created:', testActivityId);
      console.log(`   Type: ${activity.activity_type}`);
      console.log(`   Title: ${activity.activity_title}`);
    }
    
    // Test 8: Activity Reactions
    console.log('\n8️⃣ Testing activity reactions...');
    
    const activityReactionData = {
      activity_id: testActivityId,
      user_id: TEST_USER_1,
      reaction_type: 'like'
    };
    
    const { data: activityReaction, error: activityReactionError } = await supabase
      .from('activity_reactions')
      .insert([activityReactionData])
      .select()
      .single();
    
    if (activityReactionError) {
      console.log('❌ Activity reaction failed:', activityReactionError.message);
    } else {
      console.log('✅ Activity reaction added:', activityReaction.id);
      console.log(`   Reaction: ${activityReaction.reaction_type}`);
    }
    
    // Test 9: Notification Preferences
    console.log('\n9️⃣ Testing notification preferences...');
    
    const notificationPrefsData = {
      user_id: TEST_USER_1,
      direct_messages: true,
      group_messages: true,
      message_reactions: true,
      friend_requests: true,
      project_invitations: true,
      task_assignments: true,
      skill_endorsements: true,
      alliance_updates: true,
      alliance_invitations: true,
      new_members: true,
      file_shares: true,
      file_comments: false,
      email_notifications: true,
      push_notifications: true,
      in_app_notifications: true,
      digest_frequency: 'daily'
    };
    
    const { data: notificationPrefs, error: notificationPrefsError } = await supabase
      .from('notification_preferences')
      .upsert([notificationPrefsData])
      .select()
      .single();
    
    if (notificationPrefsError) {
      console.log('❌ Notification preferences failed:', notificationPrefsError.message);
    } else {
      console.log('✅ Notification preferences set:', notificationPrefs.id);
      console.log(`   Email notifications: ${notificationPrefs.email_notifications}`);
      console.log(`   Digest frequency: ${notificationPrefs.digest_frequency}`);
    }
    
    // Test 10: Helper Functions
    console.log('\n🔟 Testing helper functions...');
    
    // Test unread message count function
    const { data: unreadCount, error: unreadError } = await supabase
      .rpc('get_unread_message_count', { user_id: TEST_USER_1 });
    
    if (unreadError) {
      console.log('❌ Unread message count failed:', unreadError.message);
    } else {
      console.log(`✅ Unread message count: ${unreadCount}`);
    }
    
    // Test complex query performance
    console.log('\n🏃 Testing query performance...');
    
    const startTime = Date.now();
    
    const { data: complexQuery, error: complexError } = await supabase
      .from('conversations')
      .select(`
        id,
        conversation_type,
        title,
        last_message_at,
        participants:conversation_participants(
          user_id,
          role,
          user:users(
            id,
            display_name,
            avatar_url
          )
        ),
        messages:messages(
          id,
          content,
          message_type,
          created_at,
          sender:users!messages_sender_id_fkey(
            id,
            display_name
          )
        )
      `)
      .eq('conversation_participants.user_id', TEST_USER_1)
      .is('conversation_participants.left_at', null)
      .limit(5);
    
    const queryTime = Date.now() - startTime;
    
    if (complexError) {
      console.log('❌ Complex query failed:', complexError.message);
    } else {
      console.log(`✅ Complex query completed in ${queryTime}ms`);
      console.log(`   Retrieved ${complexQuery?.length || 0} conversations with full details`);
    }
    
    console.log('\n🎉 Collaboration Tools System tests completed!');
    console.log('✅ Direct messaging system working');
    console.log('✅ Group conversation management working');
    console.log('✅ Message reactions functioning');
    console.log('✅ File sharing and permissions working');
    console.log('✅ Activity feed tracking working');
    console.log('✅ Activity reactions functioning');
    console.log('✅ Notification preferences working');
    console.log('✅ Helper functions operational');
    console.log('✅ Query performance acceptable');
    
  } catch (error) {
    console.error('❌ Collaboration system test failed:', error);
  } finally {
    // Cleanup test data
    console.log('\n🧹 Cleaning up test data...');
    
    if (testActivityId) {
      await supabase.from('activity_reactions').delete().eq('activity_id', testActivityId);
      await supabase.from('activity_feeds').delete().eq('id', testActivityId);
    }
    
    if (testFileId) {
      await supabase.from('file_permissions').delete().eq('file_id', testFileId);
      await supabase.from('shared_files').delete().eq('id', testFileId);
    }
    
    if (testMessageId) {
      await supabase.from('message_reactions').delete().eq('message_id', testMessageId);
      await supabase.from('messages').delete().eq('id', testMessageId);
    }
    
    if (testConversationId) {
      await supabase.from('conversation_participants').delete().eq('conversation_id', testConversationId);
      await supabase.from('conversations').delete().eq('id', testConversationId);
    }
    
    // Clean up group conversation
    await supabase.from('conversations').delete().eq('conversation_type', 'group').eq('created_by', TEST_USER_1);
    
    console.log('✅ Test data cleaned up');
  }
}

// Run tests
testCollaborationSystem();
