import { Link } from "react-router-dom";
import { useRef, useState, useEffect, useContext } from "react";
import { UserContext } from "../../../contexts/supabase-auth.context";
import { supabase } from "../../../utils/supabase/supabase.utils";

const Navbar = (props) => {
  const { currentUser } = useContext(UserContext);
  const navbarRef = useRef(null);
  const toggleRef = useRef(null);
  const { navLinks } = props;
  const [isAdmin, setIsAdmin] = useState(false);

  const [isCollapsed, setIsCollapsed] = useState(true);

  // Check if the user is an admin
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!currentUser) return;

      try {
        const { data, error } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', currentUser.id)
          .single();

        if (!error && data) {
          setIsAdmin(data.is_admin || false);
        }
      } catch (error) {
        console.error("Error checking admin status:", error);
      }
    };

    checkAdminStatus();
  }, [currentUser]);

  // Function to collapse the navbar
  const handleLinkClick = () => {
    setIsCollapsed(true);
  };

  const { logout } = useContext(UserContext);

  const handleLogout = async (e) => {
    e.preventDefault();
    handleLinkClick();
    logout();
  };

  // Close navbar when clicking outside of it
  useEffect(() => {
    // Function to detect clicks outside the navbar
    const handleClickOutside = (e) => {
      if (
        navbarRef.current &&
        !navbarRef.current.contains(e.target) &&
        toggleRef.current &&
        !toggleRef.current.contains(e.target) // Check if the click is outside navbar and toggle button
      ) {
        setIsCollapsed(true); // Collapse navbar if click is outside
      }
    };
    // Add event listener to detect clicks outside
    document.addEventListener("mousedown", handleClickOutside);

    // Cleanup the event listener when component unmounts
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  // Prevent click event propagation when clicking the toggle button
  const handleToggleClick = (e) => {
    e.stopPropagation();
    setIsCollapsed((prevState) => !prevState);
  };

  return (
    <nav className="navbar navbar-expand-lg navbar-light bg-light shadow-sm mb-4">
      <div className="container">
        <div className="d-flex align-items-center">
          <Link to="/" className="navbar-brand">
            Royaltea
          </Link>

          {isAdmin && (
            <Link
              to="/admin/roadmap"
              className="ms-2 btn btn-sm btn-outline-primary d-flex align-items-center"
              title="Development Roadmap"
            >
              <span role="img" aria-label="Construction" className="me-1">🚧</span>
              <span role="img" aria-label="Map">🗺️</span>
            </Link>
          )}
        </div>
        {currentUser && (
          <button
            ref={toggleRef}
            className="navbar-toggler"
            type="button"
            data-bs-target="#navbarNav"
            aria-controls="navbarNav"
            aria-expanded={!isCollapsed} // Dynamically update based on state
            aria-label="Toggle navigation"
            onClick={handleToggleClick}
          >
            <span className="navbar-toggler-icon"></span>
          </button>
        )}
        <div
          className={`collapse navbar-collapse ${isCollapsed ? "" : "show"}`}
          id="navbarNav"
          ref={navbarRef}
        >
          <ul className="navbar-nav ms-auto">
            {currentUser &&
              navLinks.map((link, index) => {
                return (
                  <li key={index} className="nav-item">
                    <Link
                      to={link.path}
                      className="nav-link"
                      onClick={handleLinkClick}
                    >
                      {link.title}
                    </Link>
                  </li>
                );
              })}
            {currentUser && (
              <li>
                <a href="#" className="nav-link" onClick={handleLogout}>
                  Logout
                </a>
              </li>
            )}
          </ul>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
