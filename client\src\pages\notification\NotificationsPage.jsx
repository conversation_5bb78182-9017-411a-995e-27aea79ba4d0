import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { supabase } from '../../../utils/supabase/supabase.utils';
import NotificationItem from '../../components/notification/NotificationItem';

const NotificationsPage = () => {
  const { currentUser } = useContext(UserContext);
  const [notifications, setNotifications] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const pageSize = 20;

  // Fetch notifications
  const fetchNotifications = async (tabType = activeTab, reset = false) => {
    if (!currentUser) return;

    setIsLoading(true);

    try {
      // Build query
      let query = supabase
        .from('notifications')
        .select('*')
        .eq('user_id', currentUser.id)
        .order('created_at', { ascending: false });

      // Filter by type if not 'all'
      if (tabType === 'friends') {
        query = query.or('type.eq.friend_request,type.eq.friend_request_accepted,type.eq.friend_request_rejected');
      } else if (tabType === 'projects') {
        query = query.or('type.eq.project_invitation,type.eq.project_invitation_accepted,type.eq.project_invitation_rejected');
      } else if (tabType === 'contributions') {
        query = query.or('type.eq.contribution_approved,type.eq.contribution_rejected,type.eq.contribution_changes_requested,type.eq.contribution_status_update');
      } else if (tabType === 'unread') {
        query = query.eq('is_read', false);
      }

      // Pagination
      const currentPage = reset ? 0 : page;
      query = query.range(currentPage * pageSize, (currentPage + 1) * pageSize - 1);

      const { data, error } = await query;

      if (error) throw error;

      if (reset) {
        setNotifications(data || []);
        setPage(0);
      } else {
        setNotifications(prev => [...prev, ...(data || [])]);
        setPage(currentPage + 1);
      }

      // Check if there are more notifications
      setHasMore((data || []).length === pageSize);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchNotifications('all', true);
  }, [currentUser]);

  // Handle tab change
  const handleTabChange = (tab) => {
    setActiveTab(tab);
    fetchNotifications(tab, true);
  };

  // Mark notification as read
  const markAsRead = async (notificationId) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId);

      if (error) throw error;

      // Update local state
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === notificationId
            ? { ...notification, is_read: true }
            : notification
        )
      );
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('user_id', currentUser.id)
        .eq('is_read', false);

      if (error) throw error;

      // Update local state
      setNotifications(prev =>
        prev.map(notification => ({ ...notification, is_read: true }))
      );
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  // Load more notifications
  const loadMore = () => {
    fetchNotifications(activeTab);
  };

  // Check if there are unread notifications
  const hasUnread = notifications.some(notification => !notification.is_read);

  return (
    <div className="notifications-page">
      <div className="notifications-page-header">
        <h1>Notifications</h1>
        {hasUnread && (
          <button
            className="mark-all-read-btn"
            onClick={markAllAsRead}
          >
            Mark all as read
          </button>
        )}
      </div>

      <div className="notifications-tabs">
        <div
          className={`notifications-tab ${activeTab === 'all' ? 'active' : ''}`}
          onClick={() => handleTabChange('all')}
        >
          All
        </div>
        <div
          className={`notifications-tab ${activeTab === 'unread' ? 'active' : ''}`}
          onClick={() => handleTabChange('unread')}
        >
          Unread
        </div>
        <div
          className={`notifications-tab ${activeTab === 'friends' ? 'active' : ''}`}
          onClick={() => handleTabChange('friends')}
        >
          Friends
        </div>
        <div
          className={`notifications-tab ${activeTab === 'projects' ? 'active' : ''}`}
          onClick={() => handleTabChange('projects')}
        >
          Projects
        </div>
        <div
          className={`notifications-tab ${activeTab === 'contributions' ? 'active' : ''}`}
          onClick={() => handleTabChange('contributions')}
        >
          Contributions
        </div>
      </div>

      <div className="notifications-list">
        {isLoading && page === 0 ? (
          <div className="loading-spinner">Loading...</div>
        ) : notifications.length === 0 ? (
          <div className="empty-notifications">
            <i className="bi bi-bell-slash"></i>
            <h3>No notifications</h3>
            <p>You don't have any notifications at the moment.</p>
          </div>
        ) : (
          <>
            {notifications.map(notification => (
              <NotificationItem
                key={notification.id}
                notification={notification}
                onMarkAsRead={markAsRead}
              />
            ))}

            {hasMore && (
              <button
                className="load-more-button"
                onClick={loadMore}
                disabled={isLoading}
              >
                {isLoading ? 'Loading...' : 'Load more'}
              </button>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default NotificationsPage;
