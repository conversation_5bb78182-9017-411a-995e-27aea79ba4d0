import React, { useState, useEffect } from 'react';
import {
  getActiveLoadingOperations,
  getLoadingHistory,
  clearLoadingHistory,
  resetLoadingStates
} from '../../utils/loading-monitor';
import { getActiveLoadingStates, getLoadingStateChanges, clearLoadingStateHistory } from '../../utils/loading';

/**
 * EnhancedLoadingDebugger Component
 *
 * A comprehensive debugging tool that displays all loading states and visibility information.
 * This helps identify what's triggering loading spinners when tabbing away.
 */
const EnhancedLoadingDebugger = () => {
  const [activeOperations, setActiveOperations] = useState([]);
  const [loadingHistory, setLoadingHistory] = useState([]);
  const [activeLoadingStates, setActiveLoadingStates] = useState([]);
  const [loadingStateChanges, setLoadingStateChanges] = useState([]);
  const [expanded, setExpanded] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [showLoadingStates, setShowLoadingStates] = useState(true);
  const [showVisibilityInfo, setShowVisibilityInfo] = useState(true);
  const [visibilityState, setVisibilityState] = useState(document.visibilityState);
  const [focusState, setFocusState] = useState(document.hasFocus());

  // Update active operations and history every 500ms
  useEffect(() => {
    const intervalId = setInterval(() => {
      setActiveOperations(getActiveLoadingOperations());
      setLoadingHistory(getLoadingHistory());
      setActiveLoadingStates(getActiveLoadingStates());
      setLoadingStateChanges(getLoadingStateChanges());
      setVisibilityState(document.visibilityState);
      setFocusState(document.hasFocus());
    }, 500);

    // Track visibility changes
    const handleVisibilityChange = () => {
      setVisibilityState(document.visibilityState);
      console.log('[LoadingDebugger] Visibility changed to:', document.visibilityState);
    };

    // Track focus changes
    const handleFocusChange = () => {
      setFocusState(document.hasFocus());
      console.log('[LoadingDebugger] Focus changed to:', document.hasFocus());
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocusChange);
    window.addEventListener('blur', handleFocusChange);

    return () => {
      clearInterval(intervalId);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocusChange);
      window.removeEventListener('blur', handleFocusChange);
    };
  }, []);

  // Format timestamp
  const formatTime = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false }) +
           '.' + date.getMilliseconds().toString().padStart(3, '0');
  };

  // Handle reset button
  const handleReset = () => {
    resetLoadingStates();
    clearLoadingHistory();
    clearLoadingStateHistory();
    setActiveOperations([]);
    setLoadingHistory([]);
    setActiveLoadingStates([]);
    setLoadingStateChanges([]);
  };

  // Check if there are any active loading states
  const hasActiveLoading = activeOperations.length > 0 || activeLoadingStates.length > 0;

  // Minimal view when collapsed
  if (!expanded) {
    return (
      <div
        style={{
          position: 'fixed',
          bottom: '10px',
          right: '10px',
          backgroundColor: hasActiveLoading ? '#ffdddd' : '#f0f0f0',
          border: `1px solid ${hasActiveLoading ? '#ff5555' : '#ccc'}`,
          borderRadius: '4px',
          padding: '5px 10px',
          fontSize: '12px',
          cursor: 'pointer',
          zIndex: 9999,
          boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
        }}
        onClick={() => setExpanded(true)}
      >
        🔍 Loading: {activeOperations.length + activeLoadingStates.length}
        {visibilityState !== 'visible' && ' (Hidden)'}
        {!focusState && ' (Blurred)'}
      </div>
    );
  }

  // Full expanded view
  return (
    <div
      style={{
        position: 'fixed',
        bottom: '10px',
        right: '10px',
        width: '500px',
        maxHeight: '80vh',
        backgroundColor: '#f0f0f0',
        border: '1px solid #ccc',
        borderRadius: '4px',
        padding: '10px',
        overflowY: 'auto',
        fontSize: '12px',
        zIndex: 9999,
        boxShadow: '0 2px 8px rgba(0,0,0,0.3)'
      }}
    >
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '10px',
          borderBottom: '1px solid #ccc',
          paddingBottom: '5px'
        }}
      >
        <h3 style={{ margin: 0, fontSize: '16px' }}>
          🔍 Enhanced Loading Debugger
        </h3>
        <div>
          <button
            onClick={handleReset}
            style={{
              marginRight: '5px',
              padding: '2px 5px',
              backgroundColor: '#ff5555',
              color: 'white',
              border: 'none',
              borderRadius: '3px',
              cursor: 'pointer'
            }}
          >
            Reset
          </button>
          <button
            onClick={() => setExpanded(false)}
            style={{
              padding: '2px 5px',
              backgroundColor: '#555',
              color: 'white',
              border: 'none',
              borderRadius: '3px',
              cursor: 'pointer'
            }}
          >
            Minimize
          </button>
        </div>
      </div>

      <div style={{ marginBottom: '10px' }}>
        <button
          onClick={() => setShowVisibilityInfo(!showVisibilityInfo)}
          style={{
            marginRight: '5px',
            padding: '2px 5px',
            backgroundColor: showVisibilityInfo ? '#4CAF50' : '#ddd',
            color: showVisibilityInfo ? 'white' : 'black',
            border: 'none',
            borderRadius: '3px',
            cursor: 'pointer'
          }}
        >
          {showVisibilityInfo ? 'Hide Visibility' : 'Show Visibility'}
        </button>
        <button
          onClick={() => setShowLoadingStates(!showLoadingStates)}
          style={{
            marginRight: '5px',
            padding: '2px 5px',
            backgroundColor: showLoadingStates ? '#4CAF50' : '#ddd',
            color: showLoadingStates ? 'white' : 'black',
            border: 'none',
            borderRadius: '3px',
            cursor: 'pointer'
          }}
        >
          {showLoadingStates ? 'Hide States' : 'Show States'}
        </button>
        <button
          onClick={() => setShowHistory(!showHistory)}
          style={{
            padding: '2px 5px',
            backgroundColor: showHistory ? '#4CAF50' : '#ddd',
            color: showHistory ? 'white' : 'black',
            border: 'none',
            borderRadius: '3px',
            cursor: 'pointer'
          }}
        >
          {showHistory ? 'Hide History' : 'Show History'}
        </button>
      </div>

      {showVisibilityInfo && (
        <div
          style={{
            marginBottom: '15px',
            padding: '10px',
            backgroundColor: 'white',
            borderRadius: '4px',
            border: '1px solid #ddd'
          }}
        >
          <h4 style={{ margin: '0 0 10px 0', fontSize: '14px' }}>Visibility Information</h4>
          <div>
            <p style={{ margin: '5px 0' }}>
              <strong>Current Visibility State:</strong> {visibilityState}
            </p>
            <p style={{ margin: '5px 0' }}>
              <strong>Document Hidden:</strong> {document.hidden ? 'Yes' : 'No'}
            </p>
            <p style={{ margin: '5px 0' }}>
              <strong>Window Focus:</strong> {focusState ? 'Yes' : 'No'}
            </p>
          </div>
        </div>
      )}

      {showLoadingStates && (
        <div
          style={{
            marginBottom: '15px',
            padding: '10px',
            backgroundColor: 'white',
            borderRadius: '4px',
            border: '1px solid #ddd'
          }}
        >
          <h4 style={{ margin: '0 0 10px 0', fontSize: '14px' }}>
            Active Loading States: {activeLoadingStates.length}
          </h4>
          {activeLoadingStates.length === 0 ? (
            <p style={{ color: '#666', fontStyle: 'italic' }}>No active loading states</p>
          ) : (
            <ul style={{ margin: 0, padding: '0 0 0 20px' }}>
              {activeLoadingStates.map((state, index) => (
                <li key={index} style={{ marginBottom: '10px' }}>
                  <div style={{ fontWeight: 'bold', color: '#333' }}>
                    {state.component}: {state.property} = {String(state.value)}
                  </div>
                  <div style={{ fontSize: '11px', color: '#666' }}>
                    Set at: {formatTime(state.timestamp)}
                  </div>
                  <div
                    style={{
                      fontSize: '10px',
                      backgroundColor: '#f8f8f8',
                      padding: '5px',
                      borderRadius: '3px',
                      marginTop: '5px',
                      whiteSpace: 'pre-wrap',
                      overflowX: 'auto'
                    }}
                  >
                    {state.stack}
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      )}

      <div
        style={{
          marginBottom: '15px',
          padding: '10px',
          backgroundColor: 'white',
          borderRadius: '4px',
          border: '1px solid #ddd'
        }}
      >
        <h4 style={{ margin: '0 0 10px 0', fontSize: '14px' }}>
          Active Loading Operations: {activeOperations.length}
        </h4>
        {activeOperations.length === 0 ? (
          <p style={{ color: '#666', fontStyle: 'italic' }}>No active loading operations</p>
        ) : (
          <ul style={{ margin: 0, padding: '0 0 0 20px' }}>
            {activeOperations.map((op, index) => (
              <li key={index} style={{ marginBottom: '10px' }}>
                <div style={{ fontWeight: 'bold', color: '#333' }}>
                  {op.componentName}: {op.operationName}
                </div>
                <div style={{ fontSize: '11px', color: '#666' }}>
                  Started: {formatTime(op.startTime)}
                </div>
                <div style={{ fontSize: '11px', color: '#666' }}>
                  Reason: {op.reason || 'Not specified'}
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>

      {showHistory && (
        <div
          style={{
            marginBottom: '15px',
            padding: '10px',
            backgroundColor: 'white',
            borderRadius: '4px',
            border: '1px solid #ddd'
          }}
        >
          <h4 style={{ margin: '0 0 10px 0', fontSize: '14px' }}>
            Loading State Changes: {loadingStateChanges.length}
          </h4>
          {loadingStateChanges.length === 0 ? (
            <p style={{ color: '#666', fontStyle: 'italic' }}>No loading state changes</p>
          ) : (
            <ul style={{ margin: 0, padding: '0 0 0 20px' }}>
              {loadingStateChanges.slice(0, 20).map((entry, index) => (
                <li key={index} style={{ marginBottom: '10px' }}>
                  <div style={{ fontWeight: 'bold', color: '#333' }}>
                    {entry.component}: {entry.property} = {String(entry.value)}
                  </div>
                  <div style={{ fontSize: '11px', color: '#666' }}>
                    At: {formatTime(entry.timestamp)}
                  </div>
                  <div
                    style={{
                      fontSize: '10px',
                      backgroundColor: '#f8f8f8',
                      padding: '5px',
                      borderRadius: '3px',
                      marginTop: '5px',
                      whiteSpace: 'pre-wrap',
                      overflowX: 'auto'
                    }}
                  >
                    {entry.stack}
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      )}

      {showHistory && (
        <div
          style={{
            marginBottom: '15px',
            padding: '10px',
            backgroundColor: 'white',
            borderRadius: '4px',
            border: '1px solid #ddd'
          }}
        >
          <h4 style={{ margin: '0 0 10px 0', fontSize: '14px' }}>
            Loading History: {loadingHistory.length}
          </h4>
          {loadingHistory.length === 0 ? (
            <p style={{ color: '#666', fontStyle: 'italic' }}>No loading history</p>
          ) : (
            <ul style={{ margin: 0, padding: '0 0 0 20px' }}>
              {loadingHistory.map((entry, index) => (
                <li key={index} style={{ marginBottom: '10px' }}>
                  <div style={{ fontWeight: 'bold', color: entry.result === 'error' ? '#d32f2f' : '#333' }}>
                    {entry.componentName}: {entry.operationName}
                  </div>
                  <div style={{ fontSize: '11px', color: '#666' }}>
                    At: {formatTime(entry.timestamp)}
                  </div>
                  <div style={{ fontSize: '11px', color: '#666' }}>
                    Duration: {entry.duration}ms | Result: {entry.result}
                  </div>
                  {entry.error && (
                    <div style={{ fontSize: '11px', color: '#d32f2f' }}>
                      Error: {entry.error}
                    </div>
                  )}
                </li>
              ))}
            </ul>
          )}
        </div>
      )}
    </div>
  );
};

export default EnhancedLoadingDebugger;
