// Script to update the roadmap to prioritize tech stack updates
require('dotenv').config({ path: './client/.env.local' });
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseKey) {
  console.error('Error: SUPABASE_SERVICE_KEY environment variable is not set');
  process.exit(1);
}

console.log('Using Supabase URL:', supabaseUrl);

const supabase = createClient(supabaseUrl, supabaseKey);

// Helper function to find a phase by ID
function findPhase(roadmapData, phaseId) {
  return roadmapData.find(phase => phase.id === phaseId);
}

// Helper function to find a section by ID within a phase
function findSection(roadmapData, phaseId, sectionId) {
  const phase = findPhase(roadmapData, phaseId);
  if (!phase || !phase.sections) return null;
  return phase.sections.find(section => section.id === sectionId);
}

// Function to update the roadmap
async function updateRoadmap(roadmapId, roadmapData) {
  try {
    console.log(`\n=== Updating roadmap with ID: ${roadmapId} ===`);

    const { data: updatedData, error: updateError } = await supabase
      .from('roadmap')
      .update({
        data: roadmapData,
        last_updated: new Date().toISOString()
      })
      .eq('id', roadmapId)
      .select();

    if (updateError) {
      console.error('Error updating roadmap:', updateError);
      return false;
    }

    console.log('Updated roadmap successfully');
    return true;
  } catch (error) {
    console.error('Error in updateRoadmap:', error);
    return false;
  }
}

// Function to update the latest feature in the roadmap metadata
function updateLatestFeature(roadmapData) {
  // Find the metadata phase (usually has type: 'metadata')
  const metadataPhase = roadmapData.find(phase => phase.type === 'metadata');
  
  if (!metadataPhase) {
    // If no metadata phase exists, check if the first phase has a stats property
    if (roadmapData[0] && roadmapData[0].stats) {
      if (!roadmapData[0].stats.latest_update) {
        roadmapData[0].stats.latest_update = {};
      }
      
      roadmapData[0].stats.latest_update = {
        date: new Date().toISOString(),
        title: "Tech Stack Modernization Initiative",
        author: "Development Team",
        version: "1.0.0",
        description: "Started the tech stack modernization initiative to improve performance, maintainability, and developer experience. This is now our top priority."
      };
      
      console.log('Updated latest feature in first phase stats');
      return true;
    }
    
    console.log('No metadata phase or stats found in roadmap');
    return false;
  }
  
  // Update the metadata
  if (!metadataPhase.stats) {
    metadataPhase.stats = {};
  }
  
  if (!metadataPhase.stats.latest_update) {
    metadataPhase.stats.latest_update = {};
  }
  
  metadataPhase.stats.latest_update = {
    date: new Date().toISOString(),
    title: "Tech Stack Modernization Initiative",
    author: "Development Team",
    version: "1.0.0",
    description: "Started the tech stack modernization initiative to improve performance, maintainability, and developer experience. This is now our top priority."
  };
  
  console.log('Updated latest feature in metadata phase');
  return true;
}

// Main function to prioritize tech stack updates
async function prioritizeTechStackUpdates() {
  try {
    console.log('=== Fetching current roadmap data ===');
    
    // Get the latest roadmap entry
    const { data: roadmapData, error: roadmapError } = await supabase
      .from('roadmap')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (roadmapError) {
      console.error('Error fetching roadmap from Supabase:', roadmapError);
      return;
    }
    
    if (!roadmapData || roadmapData.length === 0) {
      console.log('No roadmap data found in the database');
      return;
    }
    
    const roadmap = roadmapData[0];
    const phases = roadmap.data;
    
    console.log(`Found roadmap with ID: ${roadmap.id}`);
    
    // Track if we made any changes
    let updated = false;
    
    // Find the tech stack section
    let techPhase = findPhase(phases, 5);
    let techSection = null;
    
    if (techPhase && techPhase.sections) {
      techSection = techPhase.sections.find(s => s.title.includes("Tech Stack Evaluation"));
    }
    
    if (!techSection) {
      console.log('Tech Stack Evaluation section not found');
      return;
    }
    
    console.log(`Found Tech Stack section: ${techSection.id}`);
    
    // Create a new phase specifically for tech stack updates
    const newPhase = {
      id: 2, // Use ID 2 to make it a high priority
      title: "Tech Stack Modernization",
      timeframe: "In Progress - Priority",
      expanded: true,
      sections: [
        {
          id: "2.1",
          title: "Tech Stack Evaluation & Planning",
          tasks: [
            { id: "2.1.1", text: "Evaluate current tech stack components and identify areas for improvement", completed: true },
            { id: "2.1.2", text: "Research modern alternatives for frontend framework (Next.js, Remix, etc.)", completed: true },
            { id: "2.1.3", text: "Investigate backend service options (serverless functions, edge functions)", completed: true },
            { id: "2.1.4", text: "Plan database migration strategy for improved performance", completed: false },
            { id: "2.1.5", text: "Create proof-of-concept for updated tech stack", completed: false },
            { id: "2.1.6", text: "Develop migration plan with minimal disruption to users", completed: false }
          ]
        },
        {
          id: "2.2",
          title: "Implementation & Migration",
          tasks: [
            { id: "2.2.1", text: "Set up new development environment with updated tech stack", completed: false },
            { id: "2.2.2", text: "Implement core functionality in new tech stack", completed: false },
            { id: "2.2.3", text: "Migrate database to improved structure", completed: false },
            { id: "2.2.4", text: "Develop automated tests for new implementation", completed: false },
            { id: "2.2.5", text: "Create CI/CD pipeline for new tech stack", completed: false }
          ]
        }
      ]
    };
    
    // Shift existing phases with ID >= 2 to make room for the new phase
    // First, find the highest phase ID
    let highestId = 0;
    phases.forEach(phase => {
      if (phase.id > highestId && typeof phase.id === 'number') {
        highestId = phase.id;
      }
    });
    
    // Shift phases from highest to lowest to avoid ID conflicts
    for (let i = highestId; i >= 2; i--) {
      const phaseToShift = findPhase(phases, i);
      if (phaseToShift) {
        phaseToShift.id = i + 1;
        
        // Also update section IDs
        if (phaseToShift.sections) {
          phaseToShift.sections.forEach(section => {
            const oldSectionId = section.id;
            const newSectionId = oldSectionId.replace(/^(\d+)\./, `${i + 1}.`);
            section.id = newSectionId;
            
            // Update task IDs
            if (section.tasks) {
              section.tasks.forEach(task => {
                task.id = task.id.replace(new RegExp(`^${oldSectionId}\\.`), `${newSectionId}.`);
              });
            }
          });
        }
      }
    }
    
    // Remove the old tech stack section
    if (techPhase && techSection) {
      techPhase.sections = techPhase.sections.filter(s => s.id !== techSection.id);
      
      // If the phase has no more sections, remove it
      if (techPhase.sections.length === 0) {
        phases = phases.filter(p => p.id !== techPhase.id);
      }
    }
    
    // Insert the new phase
    // Find where to insert the new phase (after phase 1)
    const insertIndex = phases.findIndex(p => p.id === 1) + 1;
    if (insertIndex > 0) {
      phases.splice(insertIndex, 0, newPhase);
    } else {
      phases.push(newPhase);
    }
    
    // Update the latest feature metadata
    updateLatestFeature(phases);
    
    // Update the roadmap
    const success = await updateRoadmap(roadmap.id, phases);
    if (success) {
      console.log('\n=== Roadmap updated successfully with prioritized tech stack updates ===');
    } else {
      console.error('\n=== Failed to update roadmap ===');
    }
    
  } catch (error) {
    console.error('Error prioritizing tech stack updates:', error);
  }
}

// Run the function
prioritizeTechStackUpdates();
