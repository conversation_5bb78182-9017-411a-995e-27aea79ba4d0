// Script to check available tables in the database
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MjA1NTksImV4cCI6MjA1OTM5NjU1OX0.e0oaDNVbA563SZJHPm6UTFOXcLbWorKn42a_brChpKQ';
const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log('Checking available tables in the database...\n');
    
    // Try to query each potential table
    const tables = [
      'projects',
      'project',
      'contributor_agreements',
      'contributor_agreement',
      'milestones',
      'milestone',
      'project_milestones',
      'agreements',
      'agreement'
    ];
    
    for (const table of tables) {
      try {
        console.log(`Checking table: ${table}`);
        const { data, error, count } = await supabase
          .from(table)
          .select('*', { count: 'exact' })
          .limit(1);
        
        if (error) {
          console.log(`  Error: ${error.message}`);
        } else {
          console.log(`  ✓ Table exists with ${count} rows`);
          
          // Show sample data
          if (data && data.length > 0) {
            console.log(`  Sample data: ${JSON.stringify(data[0]).substring(0, 100)}...`);
          } else {
            console.log(`  No data in table`);
          }
        }
      } catch (e) {
        console.log(`  Error: ${e.message}`);
      }
      
      console.log('');
    }
    
    console.log('Database check completed.');
    
  } catch (error) {
    console.error('Error:', error);
  }
}

main();
