# Contribution Tracking Flow Wireframe
**Intuitive Work Logging and Progress Tracking**

## 📋 Flow Information
- **Flow Type**: Contribution (Work) Tracking and Logging
- **User Types**: All Alliance Members working on missions
- **Entry Points**: Mission cards, Dashboard quick actions, Mobile app
- **Target UX**: **Simple, engaging work logging with gamification**
- **Maps to**: Enhanced contribution tracking system
- **Outcome**: Accurate work tracking for fair revenue distribution

---

## 🎯 **Design Philosophy**

### **Gamified Work Tracking**
- **Progress bars** and completion percentages
- **Achievement unlocks** for milestones
- **Visual feedback** for contributions
- **Streak tracking** for consistent work
- **Contribution points** and experience system

### **Integration with Existing System**
- **Maps to**: Existing contribution tracking and time logging
- **Enhances**: Current work entry and validation
- **Bridges**: Simple logging → detailed contribution records
- **Connects**: With existing royalty calculation system

---

## 🔄 **Complete Contribution Tracking Flow**

```mermaid
flowchart TD
    A[Start Work Session] --> B[Select Mission]
    B --> C[Log Work Type]
    C --> D[Track Progress]
    D --> E[Add Details]
    E --> F[Submit Contribution]
    F --> G[Validation Process]
    G --> H[Contribution Recorded]
    
    H --> I[Update Mission Progress]
    H --> J[Calculate Contribution Points]
    H --> K[Update Revenue Share]
    
    D --> L[Pause/Resume Session]
    L --> D
    
    G --> M[Needs Review]
    M --> N[Peer Review]
    N --> O[Approved/Rejected]
    O --> H
```

---

## 📱 **Contribution Tracking Wireframes**

### **Start Work Session**
```
┌─────────────────────────────────────────────────────────────┐
│  ROYALTEA                                    👤 Sarah Chen   │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                  🚀 Ready to contribute?                    │
│                                                             │
│    Active Missions:                                         │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ ⚔️ User Authentication System                       │ │
│    │ Progress: ████████░░░░░░░░░░ 40%                    │ │
│    │ Due: 3 days • Reward: $1,200                       │ │
│    │ [Continue Work] [View Details]                      │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ 🎨 Landing Page Design                              │ │
│    │ Progress: ██████████████████░░ 90%                  │ │
│    │ Due: 1 week • Reward: $800                         │ │
│    │ [Continue Work] [View Details]                      │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ 🧪 Bug Testing Sprint                               │ │
│    │ Progress: ████░░░░░░░░░░░░░░░░ 20%                  │ │
│    │ Due: 2 weeks • Reward: $400                        │ │
│    │ [Continue Work] [View Details]                      │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    📊 Today's Progress: 2.5 hours • 🔥 3-day streak       │
│                                                             │
│    [Quick Log] [View All Missions] [Time Tracker]          │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Work Type Selection**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back                          ⚔️ User Authentication System│
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              What type of work are you doing?               │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  💻 CODING                                          │ │
│    │  Writing, debugging, implementing features         │ │
│    │  Time tracking: Automatic • Validation: Code review│ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🎨 DESIGN                                          │ │
│    │  UI/UX design, mockups, visual assets             │ │
│    │  Time tracking: Manual • Validation: Design review │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🧪 TESTING                                         │ │
│    │  QA testing, bug reporting, user testing          │ │
│    │  Time tracking: Session-based • Validation: Reports│ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  📋 PLANNING                                        │ │
│    │  Research, documentation, project planning         │ │
│    │  Time tracking: Manual • Validation: Deliverables │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │  🤝 COLLABORATION                                   │ │
│    │  Meetings, reviews, team coordination              │ │
│    │  Time tracking: Meeting logs • Validation: Notes   │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Active Work Session (Coding)**
```
┌─────────────────────────────────────────────────────────────┐
│  [Pause] [Stop]                 ⚔️ User Authentication System│
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              💻 Coding Session Active                       │
│                                                             │
│    ⏱️ Session Time: 01:23:45                               │
│    📊 Today's Total: 03:47:12                              │
│    🎯 Mission Progress: ████████░░░░░░░░░░ 40% → 45%        │
│                                                             │
│    Current Task:                                            │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ Implementing login form validation                  │ │
│    │ Status: In Progress                                 │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    🔥 Productivity Streak: 3 days                          │
│    ⚡ Focus Mode: ON (No notifications)                    │
│                                                             │
│    Quick Actions:                                           │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ [Mark Subtask Complete] [Add Note] [Take Break]    │ │
│    │ [Switch Task] [Report Issue] [Ask for Help]        │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    Recent Activity:                                         │
│    • 01:20 - Fixed email validation bug                    │
│    • 01:15 - Added password strength indicator             │
│    • 01:05 - Implemented form state management             │
│    • 00:45 - Started login form component                  │
│                                                             │
│    💡 Tip: You're in the zone! Keep going to maintain      │
│       your productivity streak.                            │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Work Session Summary**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back to Dashboard             ⚔️ User Authentication System│
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              🎉 Great work session!                         │
│                                                             │
│    Session Summary:                                         │
│    ⏱️ Duration: 2 hours 15 minutes                         │
│    💻 Work Type: Coding                                     │
│    📈 Progress: 40% → 55% (+15%)                           │
│                                                             │
│    What did you accomplish?                                 │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ ✅ Completed login form validation                  │ │
│    │ ✅ Added password strength indicator                │ │
│    │ ✅ Implemented form state management                │ │
│    │ ✅ Fixed email validation bug                       │ │
│    │ 🔄 Started signup form component (in progress)     │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    Additional Notes (optional):                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ Had some challenges with form validation library   │ │
│    │ but found a good solution. Next session will focus │ │
│    │ on completing the signup form and adding tests.    │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    Attach Evidence (optional):                             │
│    [📷 Screenshot] [📁 Files] [🔗 Links] [💬 Code Snippets]│
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │              [Submit Contribution]                  │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    This contribution will be reviewed by the mission       │
│    leader and added to your contribution history.          │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Contribution Validation**
```
┌─────────────────────────────────────────────────────────────┐
│  ROYALTEA                                    👤 Mike (Lead)  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              📋 Contribution Review                         │
│                                                             │
│    Contributor: Sarah Chen                                  │
│    Mission: ⚔️ User Authentication System                   │
│    Work Type: 💻 Coding • Duration: 2h 15m                 │
│    Date: January 16, 2025                                  │
│                                                             │
│    Progress Claimed: 40% → 55% (+15%)                      │
│                                                             │
│    Accomplishments:                                         │
│    ✅ Completed login form validation                       │
│    ✅ Added password strength indicator                     │
│    ✅ Implemented form state management                     │
│    ✅ Fixed email validation bug                            │
│    🔄 Started signup form component (in progress)          │
│                                                             │
│    Notes: "Had some challenges with form validation        │
│    library but found a good solution..."                   │
│                                                             │
│    Evidence: [View Code Changes] [Screenshots]             │
│                                                             │
│    Validation:                                              │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ Progress Assessment:                                │ │
│    │ ○ Accurate (+15%)  ○ Optimistic (+10%)             │ │
│    │ ○ Conservative (+20%)  ○ Needs clarification       │ │
│    │                                                     │ │
│    │ Quality Rating: ⭐⭐⭐⭐⭐ (5/5)                     │ │
│    │ Time Estimate: ✅ Reasonable for scope             │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│    │ [Approve]   │ │ [Request    │ │ [Reject]    │        │
│    │             │ │  Changes]   │ │             │        │
│    └─────────────┘ └─────────────┘ └─────────────┘        │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Contribution History**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back                                      📊 My Contributions│
├─────────────────────────────────────────────────────────────┤
│                                                             │
│              Your Contribution History                      │
│                                                             │
│    📈 This Month: 47.5 hours • $3,200 earned              │
│    🔥 Current Streak: 5 days • 🏆 Level: Expert           │
│                                                             │
│    Recent Contributions:                                    │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ Jan 16 • ⚔️ User Authentication • 💻 Coding         │ │
│    │ 2h 15m • +15% progress • ✅ Approved               │ │
│    │ Quality: ⭐⭐⭐⭐⭐ • Earned: $180                   │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ Jan 15 • 🎨 Landing Page Design • 🎨 Design        │ │
│    │ 3h 30m • +25% progress • ✅ Approved               │ │
│    │ Quality: ⭐⭐⭐⭐☆ • Earned: $320                   │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ Jan 14 • 🧪 Bug Testing Sprint • 🧪 Testing        │ │
│    │ 1h 45m • 8 bugs found • ✅ Approved                │ │
│    │ Quality: ⭐⭐⭐⭐⭐ • Earned: $140                   │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    📊 Analytics:                                            │
│    • Most productive time: 9-11 AM                         │
│    • Favorite work type: Coding (65%)                      │
│    • Average session: 2.3 hours                            │
│    • Quality rating: 4.7/5 stars                           │
│                                                             │
│    [Export Report] [View Analytics] [Download Timesheet]   │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

---

## 🎮 **Gamification Features**

### **Progress & Achievements**
- **Mission progress bars** with visual feedback
- **Contribution streaks** for consistent work
- **Quality ratings** and peer recognition
- **Level system** based on contribution history
- **Badges** for different types of achievements

### **Smart Tracking**
- **Automatic time tracking** for coding sessions
- **Progress estimation** based on work completed
- **Quality assessment** through peer review
- **Productivity insights** and recommendations

---

## 📱 **Mobile Responsive Design**

### **Mobile Work Session**
```
┌─────────────────────────┐
│ ⏱️ 01:23:45    [Pause] │
├─────────────────────────┤
│                         │
│ 💻 Coding Session       │
│                         │
│ ⚔️ User Auth System     │
│ Progress: 40% → 45%     │
│                         │
│ Current Task:           │
│ Login form validation   │
│                         │
│ 🔥 3-day streak         │
│                         │
│ [Complete Task]         │
│ [Add Note]              │
│ [Take Break]            │
│                         │
└─────────────────────────┘
```

---

## 🎯 **Integration with Existing System**

### **Database Mapping**
- **Work Sessions** → Enhanced contribution entries
- **Progress Tracking** → Mission/task progress updates
- **Time Logging** → Existing time tracking system
- **Validation** → Enhanced review and approval workflow
- **Revenue Calculation** → Existing royalty distribution system

### **Component Enhancement**
- **Enhances**: Existing contribution tracking forms
- **Replaces**: Complex time entry with intuitive work sessions
- **Integrates**: With existing validation and approval workflows
- **Connects**: To existing payment and revenue distribution

**This Contribution Tracking Flow transforms work logging into an engaging, gamified experience while maintaining accurate tracking for fair revenue distribution.**
