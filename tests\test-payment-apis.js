// Test Payment APIs
const fetch = require('node-fetch');

const SITE_URL = 'https://royalty.technology';

async function testPaymentAPIs() {
  const endpoints = [
    '/.netlify/functions/companies',
    '/.netlify/functions/financial-transactions',
    '/.netlify/functions/commission-payments',
    '/.netlify/functions/recurring-fees'
  ];
  
  console.log('🔍 Testing Payment Processing APIs...\n');
  
  for (const endpoint of endpoints) {
    try {
      console.log(`Testing ${endpoint}...`);
      const response = await fetch(`${SITE_URL}${endpoint}`);
      
      console.log(`Status: ${response.status}`);
      console.log(`Content-Type: ${response.headers.get('content-type')}`);
      
      const text = await response.text();
      
      // Try to parse as JSON
      try {
        const json = JSON.parse(text);
        console.log(`✅ Valid JSON response`);
        
        if (response.status === 401) {
          console.log(`🔐 Requires authentication (expected)`);
        } else if (response.status === 200) {
          console.log(`📊 Data:`, json);
        } else {
          console.log(`⚠️ Unexpected status with JSON response`);
        }
      } catch (e) {
        console.log(`❌ Not valid JSON`);
        console.log(`Response preview: ${text.substring(0, 100)}...`);
      }
      
      console.log('---\n');
      
    } catch (error) {
      console.log(`❌ Error: ${error.message}\n`);
    }
  }
}

testPaymentAPIs();
