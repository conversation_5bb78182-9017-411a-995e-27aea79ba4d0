// Escrow Dispute Resolution API
// Backend Specialist: Dispute management and resolution workflows
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Create Dispute
const createDispute = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const data = JSON.parse(event.body);
    
    // Validate required fields
    if (!data.escrow_id || !data.dispute_reason || !data.dispute_type) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          error: 'escrow_id, dispute_reason, and dispute_type are required' 
        })
      };
    }

    // Verify escrow exists and user has access
    const { data: escrow, error: escrowError } = await supabase
      .from('financial_transactions')
      .select(`
        *,
        projects(
          id,
          name,
          created_by,
          teams(
            team_members(user_id, role)
          )
        )
      `)
      .eq('id', data.escrow_id)
      .eq('transaction_type', 'escrow_creation')
      .single();

    if (escrowError) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Escrow account not found' })
      };
    }

    // Check if user can create dispute
    const canDispute = escrow.created_by === userId ||
      escrow.payee_user_id === userId ||
      (escrow.projects?.teams?.team_members?.some(m => m.user_id === userId));

    if (!canDispute) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Not authorized to create dispute for this escrow' })
      };
    }

    // Create dispute record using financial_transactions table
    const disputeData = {
      company_id: escrow.company_id,
      project_id: escrow.project_id,
      transaction_type: 'escrow_dispute',
      transaction_category: 'dispute_resolution',
      gross_amount: data.disputed_amount || escrow.gross_amount,
      currency: escrow.currency,
      payee_user_id: userId,
      description: `Dispute: ${data.dispute_type} - ${data.dispute_reason}`,
      reference_number: `DISPUTE-${data.escrow_id}-${Date.now()}`,
      created_by: userId,
      status: 'open'
    };

    const { data: dispute, error: disputeError } = await supabase
      .from('financial_transactions')
      .insert([disputeData])
      .select()
      .single();

    if (disputeError) {
      throw new Error(`Failed to create dispute: ${disputeError.message}`);
    }

    // Store dispute metadata
    const disputeMetadata = {
      revenue_id: dispute.id,
      project_id: escrow.project_id,
      amount: data.disputed_amount || escrow.gross_amount,
      currency: escrow.currency,
      escrow_date: new Date().toISOString().split('T')[0],
      status: 'open',
      reason: `Dispute: ${data.dispute_type}`,
      release_condition: JSON.stringify({
        dispute_type: data.dispute_type,
        dispute_reason: data.dispute_reason,
        evidence_urls: data.evidence_urls || [],
        escrow_id: data.escrow_id,
        priority: data.priority || 'medium',
        requested_resolution: data.requested_resolution
      }),
      created_by: userId
    };

    const { data: metadata, error: metadataError } = await supabase
      .from('revenue_escrow')
      .insert([disputeMetadata])
      .select()
      .single();

    if (metadataError) {
      // Rollback dispute creation
      await supabase.from('financial_transactions').delete().eq('id', dispute.id);
      throw new Error(`Failed to create dispute metadata: ${metadataError.message}`);
    }

    // Update escrow status to disputed
    await supabase
      .from('financial_transactions')
      .update({ status: 'disputed' })
      .eq('id', data.escrow_id);

    // Notify relevant parties
    await notifyDisputeCreated(escrow, dispute, data);

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        dispute: {
          id: dispute.id,
          escrow_id: data.escrow_id,
          dispute_type: data.dispute_type,
          dispute_reason: data.dispute_reason,
          disputed_amount: data.disputed_amount || escrow.gross_amount,
          priority: data.priority || 'medium',
          status: 'open',
          created_by: userId,
          created_at: dispute.created_at,
          metadata_id: metadata.id
        }
      })
    };

  } catch (error) {
    console.error('Create dispute error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to create dispute' })
    };
  }
};

// Get Dispute Details
const getDispute = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const disputeId = event.path.split('/').pop();

    const { data: dispute, error: disputeError } = await supabase
      .from('financial_transactions')
      .select(`
        *,
        projects(
          id,
          name,
          title,
          created_by,
          teams(
            id,
            name,
            team_members(user_id, role)
          )
        ),
        creator:users!financial_transactions_created_by_fkey(
          id,
          display_name,
          email
        )
      `)
      .eq('id', disputeId)
      .eq('transaction_type', 'escrow_dispute')
      .single();

    if (disputeError) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Dispute not found' })
      };
    }

    // Check access permissions
    const hasAccess = dispute.created_by === userId ||
      dispute.projects.created_by === userId ||
      (dispute.projects.teams?.team_members?.some(m => m.user_id === userId));

    if (!hasAccess) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Access denied' })
      };
    }

    // Get dispute metadata
    const { data: metadata } = await supabase
      .from('revenue_escrow')
      .select('*')
      .eq('revenue_id', disputeId)
      .single();

    const disputeDetails = metadata ? JSON.parse(metadata.release_condition || '{}') : {};

    // Get dispute messages/updates
    const { data: updates } = await supabase
      .from('financial_transactions')
      .select(`
        *,
        creator:users!financial_transactions_created_by_fkey(
          id,
          display_name
        )
      `)
      .like('reference_number', `DISPUTE-UPDATE-${disputeId}%`)
      .order('created_at', { ascending: true });

    const response = {
      id: dispute.id,
      escrow_id: disputeDetails.escrow_id,
      dispute_type: disputeDetails.dispute_type,
      dispute_reason: disputeDetails.dispute_reason,
      disputed_amount: dispute.gross_amount,
      priority: disputeDetails.priority || 'medium',
      requested_resolution: disputeDetails.requested_resolution,
      evidence_urls: disputeDetails.evidence_urls || [],
      status: dispute.status,
      project: dispute.projects ? {
        id: dispute.projects.id,
        name: dispute.projects.name || dispute.projects.title,
        alliance: dispute.projects.teams ? {
          id: dispute.projects.teams.id,
          name: dispute.projects.teams.name
        } : null
      } : null,
      created_by: dispute.creator,
      created_at: dispute.created_at,
      updates: updates || [],
      resolution: null // TODO: Add resolution tracking
    };

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(response)
    };

  } catch (error) {
    console.error('Get dispute error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to get dispute' })
    };
  }
};

// Add Dispute Update/Message
const addDisputeUpdate = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const disputeId = event.path.split('/')[0];
    const data = JSON.parse(event.body);

    if (!data.message) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Message is required' })
      };
    }

    // Verify dispute exists and user has access
    const { data: dispute, error: disputeError } = await supabase
      .from('financial_transactions')
      .select(`
        *,
        projects(
          created_by,
          teams(
            team_members(user_id, role)
          )
        )
      `)
      .eq('id', disputeId)
      .eq('transaction_type', 'escrow_dispute')
      .single();

    if (disputeError) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Dispute not found' })
      };
    }

    const hasAccess = dispute.created_by === userId ||
      dispute.projects.created_by === userId ||
      (dispute.projects.teams?.team_members?.some(m => m.user_id === userId));

    if (!hasAccess) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Access denied' })
      };
    }

    // Create update record
    const updateData = {
      company_id: dispute.company_id,
      project_id: dispute.project_id,
      transaction_type: 'dispute_update',
      transaction_category: 'dispute_communication',
      gross_amount: 0,
      currency: dispute.currency,
      payee_user_id: userId,
      description: data.message,
      reference_number: `DISPUTE-UPDATE-${disputeId}-${Date.now()}`,
      created_by: userId,
      status: 'active'
    };

    const { data: update, error: updateError } = await supabase
      .from('financial_transactions')
      .insert([updateData])
      .select()
      .single();

    if (updateError) {
      throw new Error(`Failed to add dispute update: ${updateError.message}`);
    }

    // Notify other parties
    await notifyDisputeUpdate(dispute, update, data.message);

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        update: {
          id: update.id,
          dispute_id: disputeId,
          message: data.message,
          created_by: userId,
          created_at: update.created_at
        }
      })
    };

  } catch (error) {
    console.error('Add dispute update error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to add dispute update' })
    };
  }
};

// Resolve Dispute
const resolveDispute = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const disputeId = event.path.split('/').pop();
    const data = JSON.parse(event.body);

    if (!data.resolution_type || !data.resolution_details) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          error: 'resolution_type and resolution_details are required' 
        })
      };
    }

    // Get dispute and verify permissions
    const { data: dispute, error: disputeError } = await supabase
      .from('financial_transactions')
      .select(`
        *,
        projects(
          created_by,
          teams(
            team_members(user_id, role)
          )
        )
      `)
      .eq('id', disputeId)
      .eq('transaction_type', 'escrow_dispute')
      .single();

    if (disputeError) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Dispute not found' })
      };
    }

    // Check if user can resolve (project owner or alliance admin)
    const canResolve = dispute.projects.created_by === userId ||
      (dispute.projects.teams?.team_members?.some(m => 
        m.user_id === userId && ['founder', 'owner', 'admin'].includes(m.role)
      ));

    if (!canResolve) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Not authorized to resolve this dispute' })
      };
    }

    // Update dispute status
    const { data: resolvedDispute, error: resolveError } = await supabase
      .from('financial_transactions')
      .update({ 
        status: 'resolved',
        updated_at: new Date().toISOString()
      })
      .eq('id', disputeId)
      .select()
      .single();

    if (resolveError) {
      throw new Error(`Failed to resolve dispute: ${resolveError.message}`);
    }

    // Update metadata with resolution
    await supabase
      .from('revenue_escrow')
      .update({ 
        status: 'resolved',
        released_by: userId,
        release_date: new Date().toISOString().split('T')[0]
      })
      .eq('revenue_id', disputeId);

    // Get escrow ID from metadata
    const { data: metadata } = await supabase
      .from('revenue_escrow')
      .select('release_condition')
      .eq('revenue_id', disputeId)
      .single();

    const disputeDetails = metadata ? JSON.parse(metadata.release_condition || '{}') : {};

    // Update original escrow status
    if (disputeDetails.escrow_id) {
      const newEscrowStatus = data.resolution_type === 'release_funds' ? 'releasing' : 'active';
      await supabase
        .from('financial_transactions')
        .update({ status: newEscrowStatus })
        .eq('id', disputeDetails.escrow_id);
    }

    // Notify parties of resolution
    await notifyDisputeResolution(dispute, data);

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        dispute: {
          id: disputeId,
          status: 'resolved',
          resolution_type: data.resolution_type,
          resolution_details: data.resolution_details,
          resolved_by: userId,
          resolved_at: new Date().toISOString()
        }
      })
    };

  } catch (error) {
    console.error('Resolve dispute error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to resolve dispute' })
    };
  }
};

// Helper notification functions
const notifyDisputeCreated = async (escrow, dispute, data) => {
  console.log('Dispute created notification:', { escrow_id: escrow.id, dispute_id: dispute.id });
  // TODO: Implement notifications
};

const notifyDisputeUpdate = async (dispute, update, message) => {
  console.log('Dispute update notification:', { dispute_id: dispute.id, update_id: update.id });
  // TODO: Implement notifications
};

const notifyDisputeResolution = async (dispute, resolution) => {
  console.log('Dispute resolution notification:', { dispute_id: dispute.id, resolution: resolution.resolution_type });
  // TODO: Implement notifications
};

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/escrow-disputes', '');

  try {
    let response;

    if (event.httpMethod === 'POST') {
      if (path === '' || path === '/') {
        response = await createDispute(event);
      } else if (path.includes('/update')) {
        response = await addDisputeUpdate(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'GET') {
      response = await getDispute(event);
    } else if (event.httpMethod === 'PUT') {
      if (path.includes('/resolve')) {
        response = await resolveDispute(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('Escrow Disputes API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
