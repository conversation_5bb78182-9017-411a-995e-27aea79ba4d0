# Bento Grid System
**Royaltea Platform Layout & Wireframe Standards**

## 🎯 **Layout Philosophy**

The Royaltea platform uses a **three-column layout system** with a central bento grid and contextual helper sidebars. This creates an intuitive, consistent user experience across all platform areas.

---

## 📐 **Standard Layout Pattern**

### **Core Layout Structure**
```
┌─────┐ ┌─────────────────────────────────────────────────────────┐ ┌─────┐
│ 🔔  │ │                    PAGE TITLE                           │ │     │
│     │ │                                                         │ │     │
│ 📧  │ │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │ │ C   │
│     │ │  │   WIDGET    │ │   WIDGET    │ │   WIDGET    │       │ │ O   │
│ 📋  │ │  │             │ │             │ │             │       │ │ N   │
│     │ │  └─────────────┘ └─────────────┘ └─────────────┘       │ │ T   │
│ 👥  │ │                                                         │ │ E   │
│     │ │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │ │ X   │
│ ⚙️  │ │  │   WIDGET    │ │   WIDGET    │ │   WIDGET    │       │ │ T   │
│     │ │  │             │ │             │ │             │       │ │     │
│     │ │  └─────────────┘ └─────────────┘ └─────────────┘       │ │ A   │
│     │ │                                                         │ │ C   │
└─────┘ └─────────────────────────────────────────────────────────┘ └─────┘
```

---

## 🔧 **Component Specifications**

### **Left Sidebar (Persistent Helpers)**
**Width**: 60px
**Purpose**: Always-available global actions
**Contents**:
- 🔔 **Notifications** - Global notification bell
- 📧 **Messages** - Direct messages and communications
- 📋 **Tasks** - Quick task overview
- 👥 **Social** - Ally network and connections
- ⚙️ **Settings** - User preferences

### **Center Area (Bento Grid)**
**Width**: Flexible (remaining space)
**Purpose**: Main content and widgets
**Grid System**:
- **1x1**: Small widgets (status, quick stats)
- **2x1**: Medium widgets (lists, forms)
- **2x2**: Large widgets (dashboards, detailed views)
- **3x1**: Wide widgets (timelines, progress bars)
- **4x1**: Full-width widgets (charts, tables)

### **Right Sidebar (Contextual Actions)**
**Width**: 60px
**Purpose**: Context-specific quick actions
**Contents vary by page**:

**Projects Context**:
- ➕ Add Project
- ⬇️ Import/Export
- 🗑️ Archive
- 📋 Reports

**Tasks Context**:
- ➕ Quick Add
- ✓ Bulk Actions
- 📊 Analytics
- 🔍 Search

**Revenue Context**:
- � Add Payment Method
- 💸 Withdraw Funds
- �📄 Generate Report
- � Export Data
- ⚙️ Settings

**Gamification Context**:
- 💰 Cash Out ORBs
- 🛒 ORB Store
- 📊 View Stats
- ⚙️ Preferences

**Alliance Context**:
- ➕ New Venture
- 👥 Invite Members
- 📊 Reports
- ⚙️ Settings

**Social Context**:
- ➕ Add Ally
- � Find People
- 📊 Network Stats
- ⚙️ Preferences

**Navigation Context**:
- 🔍 Find/Search
- 🎨 Customize Layout
- 🌍 World View Toggle
- ⚙️ Preferences

---

## 🎨 **Widget Design Standards**

### **Widget Header Pattern**
```
┌─────────────────────────────────────┐
│ 📊 Widget Title           [Action]  │
├─────────────────────────────────────┤
│                                     │
│         Widget Content              │
│                                     │
└─────────────────────────────────────┘
```

### **Widget Sizing Rules**
- **Minimum**: 200px width, 150px height
- **Padding**: 16px internal padding
- **Spacing**: 12px between widgets
- **Border Radius**: 8px for modern appearance
- **Responsive**: Stack vertically on mobile

---

## 📱 **Responsive Behavior**

### **Desktop (> 1024px)**
- Full three-column layout
- All widgets visible
- Hover states and tooltips

### **Tablet (768px - 1024px)**
- Collapsed sidebars (icons only)
- 2-column widget grid
- Touch-optimized interactions

### **Mobile (< 768px)**
- Single column layout
- Bottom navigation bar
- Swipeable widget cards
- Simplified contextual actions

---

## 🎯 **Implementation Guidelines**

### **For Management/Dashboard Wireframes**
1. **Always use the three-column pattern**
2. **Left sidebar**: Persistent global actions
3. **Center**: Bento grid with appropriate widgets
4. **Right sidebar**: Context-specific actions
5. **Consistent spacing and sizing**

### **For Onboarding/Creation Wireframes**
1. **Full-screen immersive experience**
2. **Minimal UI elements** - only exit button and progress indicators
3. **One question/step at a time** for focus
4. **Large, simple controls** that feel effortless
5. **Template shortcuts** for power users
6. **Guided progression** with clear next steps

---

## 🔄 **Pattern Selection Guide**

### **Use Bento Grid Pattern For:**
- Dashboard views
- Project management interfaces
- System administration
- Data visualization
- Multi-task workflows
- Power user interfaces

### **Use Immersive Flow Pattern For:**
- Initial user onboarding
- Account setup/signup
- Venture creation wizard
- Alliance creation wizard
- First-time tutorials
- Complex form workflows
- Any single-focus task requiring concentration

### **Immersive Flow Success Criteria:**
- **<5 minutes to first meaningful action** (PRD requirement)
- **Template shortcuts** available for experienced users
- **Progressive disclosure** - reveal complexity gradually
- **Context preservation** - maintain user choices across steps
- **Graceful exit** - save progress and allow return

### **Widget Content Guidelines**
- **Clear hierarchy**: Title, content, actions
- **Scannable information**: Key metrics prominent
- **Actionable elements**: Clear buttons and links
- **Status indicators**: Visual feedback for states

---

## 🎨 **Immersive Flow Design Pattern**

### **Full-Screen Wizard Layout**
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ✕                                                                           │
│                                                                             │
│                                                                             │
│                        MAIN QUESTION/CONTENT                               │
│                                                                             │
│                     [SIMPLE CONTROL/INPUT]                                 │
│                                                                             │
│                                                                             │
│                     [Continue] [Skip] [Template]                           │
│                                                                             │
│                           ● ○ ○ ○ ○                                        │
└─────────────────────────────────────────────────────────────────────────────┘
```

### **Immersive Flow Principles**
- **Minimal chrome**: Only exit button (✕) visible
- **Centered content**: Focus on single task/question
- **Large touch targets**: Easy interaction
- **Progress indicators**: Dots showing step progress
- **Template shortcuts**: Quick completion options
- **Smooth transitions**: Between steps for flow

---

## ✅ **Wireframe Checklist**

### **For Management/Dashboard Wireframes:**
- [ ] Uses three-column layout pattern
- [ ] Left sidebar has persistent helpers
- [ ] Right sidebar has contextual actions
- [ ] Widgets follow sizing standards
- [ ] Mobile responsive behavior specified
- [ ] All interactive elements clearly marked
- [ ] Consistent with existing wireframes

### **For Onboarding/Creation Wireframes:**
- [ ] Uses full-screen immersive pattern
- [ ] Minimal UI elements (only exit button)
- [ ] One clear action per step
- [ ] Large, simple controls
- [ ] Progress indicators present
- [ ] Template shortcuts available
- [ ] Smooth step transitions specified

---

**This bento grid system ensures consistent, intuitive navigation and layout across the entire Royaltea platform while maintaining flexibility for different content types and user workflows.**
