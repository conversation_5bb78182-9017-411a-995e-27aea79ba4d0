# Deployment & Database Management Guide
**Comprehensive Operations Manual**

## 📋 Document Information
- **Document Type**: Operations Guide
- **Version**: 1.0
- **Last Updated**: January 16, 2025
- **Owner**: Royaltea DevOps Team
- **Status**: Active
- **Classification**: Internal Use

---

## 🚀 Deployment Procedures

### **Primary Deployment Method**
```powershell
# Build and deploy to production (recommended)
./deploy-netlify-cli.ps1 -RunBuild -NonInteractive
```

### **Alternative Deployment Methods**
```powershell
# Build only (for manual upload)
./build-netlify-deploy.ps1

# Legacy deployment method
./build-and-deploy.ps1
```

### **Environment Configuration**

#### **Required Environment Variables**
```bash
# Supabase Configuration
SUPABASE_URL=https://hqqlrrqvjcetoxbdjgzx.supabase.co
SUPABASE_SERVICE_KEY=[service-key]
VITE_SUPABASE_URL=https://hqqlrrqvjcetoxbdjgzx.supabase.co
VITE_SUPABASE_ANON_KEY=[anon-key]

# Site Configuration
SITE_URL=https://royalty.technology

# Analytics (optional)
VITE_GA_TRACKING_ID=G-S7SFML469V
```

#### **Environment Files**
- **Production**: Netlify environment variables
- **Development**: `client/.env.local`
- **Template**: `.env.example` (to be created)

### **Deployment Environments**

#### **Production**
- **URL**: https://royalty.technology
- **Hosting**: Netlify
- **Domain**: GoDaddy → Netlify
- **Build Command**: `npm run build`
- **Publish Directory**: `client/dist`

#### **Development**
- **URL**: http://localhost:5173
- **Command**: `npm run dev`
- **Hot Reload**: Enabled
- **Source Maps**: Enabled

### **Deployment Checklist**
- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] Build process successful
- [ ] Tests passing
- [ ] Performance check completed
- [ ] Rollback plan prepared

### **Troubleshooting Deployment Issues**

#### **Build Failures**
```bash
# Check dependencies
npm install
npm audit fix

# Clear cache and rebuild
rm -rf node_modules package-lock.json
npm install
npm run build
```

#### **Environment Issues**
```bash
# Verify environment variables
echo $VITE_SUPABASE_URL
echo $VITE_SUPABASE_ANON_KEY

# Check .env.local file
cat client/.env.local
```

#### **Netlify CLI Issues**
```bash
# Re-authenticate
netlify logout
netlify login

# Check site configuration
netlify status
netlify env:list
```

---

## 🗄️ Database Management

### **Database Information**
- **Provider**: Supabase (PostgreSQL)
- **URL**: https://hqqlrrqvjcetoxbdjgzx.supabase.co
- **Dashboard**: https://supabase.com/dashboard/project/hqqlrrqvjcetoxbdjgzx
- **Environment**: Production
- **Backup**: Automated daily backups

### **Database Access Methods**

#### **1. Supabase Dashboard**
- **URL**: https://supabase.com/dashboard
- **Features**: Table editor, SQL editor, API docs
- **Use Case**: Quick queries, data inspection

#### **2. Supabase CLI**
```bash
# Install CLI
npm install -g supabase

# Login and link project
supabase login
supabase link --project-ref hqqlrrqvjcetoxbdjgzx

# Run migrations
supabase db push
```

#### **3. Direct PostgreSQL Connection**
```bash
# Connection string format
postgresql://postgres:[password]@db.hqqlrrqvjcetoxbdjgzx.supabase.co:5432/postgres
```

### **Migration Management**

#### **Migration Locations**
```
supabase/migrations/          # Official Supabase migrations
database/migrations/          # Custom migration scripts (planned)
scripts/database/migrations/  # Migration application scripts
```

#### **Apply Migrations**
```bash
# Using Supabase CLI (recommended)
supabase db push

# Using custom scripts
node scripts/database/apply-migration-direct.js [migration-file]
node scripts/database/apply-migration-sql.js [sql-file]
node scripts/database/apply-migration-via-api.js [migration]
```

#### **Check Migration Status**
```bash
# Supabase migration status
supabase migration list

# Custom migration check
node scripts/database/check-migration.js
```

### **Database Validation Scripts**

#### **Table Validation**
```bash
# Check all tables
node check-supabase-tables.js

# Check specific features
node check-agreements-status.js
node check-projects.js
node check-contributions-schema.js
node check-project-activities-table.js
```

#### **Data Integrity Checks**
```bash
# Validate relationships
node check-database.js

# Check production data
node check-production.js

# Verify roadmap data
node check-roadmap-data.js
```

### **Data Management**

#### **Development Data**
```bash
# Create test users
node create-fresh-test-users.js

# Create test data
node create-final-test.js
node create-test-invitation.js

# Clean test data
node cleanup-all-test-data.js
```

#### **Data Backup & Recovery**
```bash
# Manual backup (to be implemented)
node scripts/database/backup-production-data.js

# Data export
supabase db dump --data-only > backup.sql

# Data restore
psql -h db.hqqlrrqvjcetoxbdjgzx.supabase.co -U postgres -d postgres < backup.sql
```

### **Database Schema Management**

#### **Key Tables**
- **auth.users**: User authentication (Supabase managed)
- **public.users**: User profiles and metadata
- **public.projects**: Project management (to become ventures)
- **public.teams**: Team management (to become alliances)
- **public.contributions**: Work tracking and validation
- **public.agreements**: Contract and agreement management

#### **Planned Schema Changes**
- **Teams → Alliances**: Rename and enhance team functionality
- **Projects → Ventures**: Add revenue model configuration
- **Missions Table**: New task management system
- **User Allies Table**: Friend relationship system

### **Emergency Procedures**

#### **Database Rollback**
```bash
# Identify last good migration
supabase migration list

# Rollback to specific migration
supabase db reset --to [migration-timestamp]

# Verify rollback success
node check-database.js
```

#### **Data Recovery**
```bash
# Restore from backup
supabase db reset
psql -h db.hqqlrrqvjcetoxbdjgzx.supabase.co -U postgres -d postgres < backup.sql

# Verify data integrity
node check-supabase-tables.js
```

#### **Performance Issues**
```bash
# Check slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

# Analyze table performance
ANALYZE;
VACUUM;
```

---

## 📊 Monitoring & Maintenance

### **Performance Monitoring**
- **Supabase Dashboard**: Real-time metrics
- **Netlify Analytics**: Deployment and traffic metrics
- **Custom Monitoring**: Application-specific metrics (to be implemented)

### **Regular Maintenance Tasks**
- **Daily**: Check deployment status and error logs
- **Weekly**: Review database performance and usage
- **Monthly**: Update dependencies and security patches
- **Quarterly**: Comprehensive backup and disaster recovery testing

### **Alert Procedures**
- **Deployment Failures**: Check build logs and environment variables
- **Database Issues**: Check Supabase status and connection health
- **Performance Degradation**: Review metrics and optimize queries

---

**This guide provides comprehensive procedures for deployment and database management. Keep this document updated as procedures evolve and new tools are implemented.**
