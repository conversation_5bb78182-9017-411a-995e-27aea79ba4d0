-- Combined Skills System Migration
-- Copy and paste this entire file into the Supabase SQL Editor

-- Part 1: Create tables for the skill vetting system
-- This migration adds support for skills, user skills, and skill verifications

-- Create the skills table to store the skill taxonomy
CREATE TABLE IF NOT EXISTS public.skills (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category TEXT NOT NULL, -- Level 1: Skill Category (e.g., "Programming & Development")
    area TEXT NOT NULL, -- Level 2: Skill Area (e.g., "Game Engines")
    name TEXT NOT NULL, -- Level 3: Specific Skill (e.g., "Unreal Engine")
    micro_skill TEXT, -- Level 4: Micro-Skill (e.g., "Blueprint Visual Scripting")
    mastery_component TEXT, -- Level 5: Mastery Component (e.g., "Blueprint Component System")
    description TEXT,
    icon TEXT, -- Icon identifier for UI display
    parent_id UUID REFERENCES public.skills(id), -- For hierarchical relationship
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(category, area, name, micro_skill, mastery_component)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_skills_hierarchy ON public.skills(category, area, name, micro_skill);

-- Create the user_skills table to track user skills and verification levels
CREATE TABLE IF NOT EXISTS public.user_skills (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    skill_id UUID NOT NULL REFERENCES public.skills(id) ON DELETE CASCADE,
    proficiency_score INTEGER DEFAULT 0, -- 0-100 score
    currency_score INTEGER DEFAULT 0, -- Recency score
    depth_score INTEGER DEFAULT 0, -- Mastery depth score
    breadth_score INTEGER DEFAULT 0, -- Range of applications score
    relevance_score INTEGER DEFAULT 0, -- Match to industry standards score
    verification_level INTEGER DEFAULT 0, -- 0=Unverified, 1=Learning, 2=Peer, 3=Project, 4=Expert, 5=Industry
    is_public BOOLEAN DEFAULT true, -- Whether this skill is visible on public profile
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(user_id, skill_id)
);

-- Create index for faster user skill lookups
CREATE INDEX IF NOT EXISTS idx_user_skills_user ON public.user_skills(user_id);
CREATE INDEX IF NOT EXISTS idx_user_skills_verification ON public.user_skills(verification_level);

-- Create the skill_verifications table to track verification evidence
CREATE TABLE IF NOT EXISTS public.skill_verifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_skill_id UUID NOT NULL REFERENCES public.user_skills(id) ON DELETE CASCADE,
    verification_type TEXT NOT NULL, -- 'self_assessment', 'learning_platform', 'project_evidence', 'peer_endorsement', 'challenge', 'expert', 'activity'
    verification_source TEXT, -- Source of verification (e.g., 'LinkedIn Learning', 'GitHub', etc.)
    verification_data JSONB, -- Detailed verification data
    verified_by UUID REFERENCES auth.users(id) ON DELETE SET NULL, -- For peer/expert verifications
    verified_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    expires_at TIMESTAMP WITH TIME ZONE, -- For time-limited verifications
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create index for faster verification lookups
CREATE INDEX IF NOT EXISTS idx_skill_verifications_user_skill ON public.skill_verifications(user_skill_id);
CREATE INDEX IF NOT EXISTS idx_skill_verifications_type ON public.skill_verifications(verification_type);

-- Add RLS policies for the skills table
ALTER TABLE public.skills ENABLE ROW LEVEL SECURITY;

-- Anyone can view skills
CREATE POLICY "Anyone can view skills"
ON public.skills
FOR SELECT
TO authenticated
USING (true);

-- Only admins can modify skills
CREATE POLICY "Only admins can insert skills"
ON public.skills
FOR INSERT
TO authenticated
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid() AND is_admin = true
    )
);

CREATE POLICY "Only admins can update skills"
ON public.skills
FOR UPDATE
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid() AND is_admin = true
    )
);

CREATE POLICY "Only admins can delete skills"
ON public.skills
FOR DELETE
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid() AND is_admin = true
    )
);

-- Add RLS policies for the user_skills table
ALTER TABLE public.user_skills ENABLE ROW LEVEL SECURITY;

-- Users can view their own skills and public skills of others
CREATE POLICY "Users can view their own skills"
ON public.user_skills
FOR SELECT
TO authenticated
USING (
    auth.uid() = user_id OR is_public = true
);

-- Users can insert their own skills
CREATE POLICY "Users can insert their own skills"
ON public.user_skills
FOR INSERT
TO authenticated
WITH CHECK (
    auth.uid() = user_id
);

-- Users can update their own skills
CREATE POLICY "Users can update their own skills"
ON public.user_skills
FOR UPDATE
TO authenticated
USING (
    auth.uid() = user_id
);

-- Users can delete their own skills
CREATE POLICY "Users can delete their own skills"
ON public.user_skills
FOR DELETE
TO authenticated
USING (
    auth.uid() = user_id
);

-- Add RLS policies for the skill_verifications table
ALTER TABLE public.skill_verifications ENABLE ROW LEVEL SECURITY;

-- Users can view verifications for their own skills and public skills
CREATE POLICY "Users can view skill verifications"
ON public.skill_verifications
FOR SELECT
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM public.user_skills
        WHERE id = user_skill_id AND (user_id = auth.uid() OR is_public = true)
    )
);

-- Users can add verifications to their own skills
CREATE POLICY "Users can insert skill verifications"
ON public.skill_verifications
FOR INSERT
TO authenticated
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.user_skills
        WHERE id = user_skill_id AND user_id = auth.uid()
    )
);

-- Users can update verifications they created
CREATE POLICY "Users can update skill verifications"
ON public.skill_verifications
FOR UPDATE
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM public.user_skills
        WHERE id = user_skill_id AND user_id = auth.uid()
    )
);

-- Users can delete verifications they created
CREATE POLICY "Users can delete skill verifications"
ON public.skill_verifications
FOR DELETE
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM public.user_skills
        WHERE id = user_skill_id AND user_id = auth.uid()
    )
);

-- Update the users table to include a skills field for quick access
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS skills JSONB DEFAULT '[]'::jsonb;

-- Part 2: Seed initial skill taxonomy data
-- This migration adds the initial set of skills for the Game Development domain

-- Function to insert skills with proper hierarchy
CREATE OR REPLACE FUNCTION insert_skill_hierarchy(
    p_category TEXT,
    p_area TEXT,
    p_name TEXT,
    p_micro_skill TEXT DEFAULT NULL,
    p_mastery_component TEXT DEFAULT NULL,
    p_description TEXT DEFAULT NULL,
    p_icon TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    parent_id UUID;
    current_id UUID;
BEGIN
    -- Insert or get the parent skill (if applicable)
    IF p_micro_skill IS NOT NULL AND p_mastery_component IS NOT NULL THEN
        -- We're inserting a mastery component, so get the micro-skill parent
        SELECT id INTO parent_id FROM public.skills
        WHERE category = p_category AND area = p_area AND name = p_name
        AND micro_skill = p_micro_skill AND mastery_component IS NULL;
    ELSIF p_micro_skill IS NOT NULL THEN
        -- We're inserting a micro-skill, so get the specific skill parent
        SELECT id INTO parent_id FROM public.skills
        WHERE category = p_category AND area = p_area AND name = p_name
        AND micro_skill IS NULL AND mastery_component IS NULL;
    ELSIF p_name IS NOT NULL THEN
        -- We're inserting a specific skill, so get the skill area parent
        SELECT id INTO parent_id FROM public.skills
        WHERE category = p_category AND area = p_area AND name IS NULL
        AND micro_skill IS NULL AND mastery_component IS NULL;
    ELSIF p_area IS NOT NULL THEN
        -- We're inserting a skill area, so get the category parent
        SELECT id INTO parent_id FROM public.skills
        WHERE category = p_category AND area IS NULL AND name IS NULL
        AND micro_skill IS NULL AND mastery_component IS NULL;
    END IF;

    -- Insert the skill
    INSERT INTO public.skills (
        category,
        area,
        name,
        micro_skill,
        mastery_component,
        description,
        icon,
        parent_id
    ) VALUES (
        p_category,
        p_area,
        p_name,
        p_micro_skill,
        p_mastery_component,
        p_description,
        p_icon,
        parent_id
    )
    ON CONFLICT (category, area, name, micro_skill, mastery_component)
    DO UPDATE SET
        description = EXCLUDED.description,
        icon = EXCLUDED.icon,
        parent_id = EXCLUDED.parent_id,
        updated_at = now()
    RETURNING id INTO current_id;

    RETURN current_id;
END;
$$ LANGUAGE plpgsql;
