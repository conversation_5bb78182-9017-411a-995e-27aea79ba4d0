import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON> } from 'react-minimal-pie-chart';
import { toast } from 'react-hot-toast';

const RoyaltyModel = ({ projectData, setProjectData }) => {
  const [modelType, setModelType] = useState(projectData.royalty_model.model_type || 'custom');
  const [modelSchema, setModelSchema] = useState(projectData.royalty_model.model_schema || 'cog');
  const [tasksWeight, setTasksWeight] = useState(
    projectData.royalty_model.configuration.tasks_weight || 33.33
  );
  const [hoursWeight, setHoursWeight] = useState(
    projectData.royalty_model.configuration.hours_weight || 33.33
  );
  const [difficultyWeight, setDifficultyWeight] = useState(
    projectData.royalty_model.configuration.difficulty_weight || 33.34
  );
  // Note: We don't need platform fee settings as they're handled by the platforms directly
  const [isPreExpense, setIsPreExpense] = useState(
    projectData.royalty_model.is_pre_expense !== undefined
      ? projectData.royalty_model.is_pre_expense
      : true
  );

  // Financial parameters
  const [minPayout, setMinPayout] = useState(
    projectData.royalty_model.min_payout !== undefined
      ? projectData.royalty_model.min_payout
      : 10000
  );

  const [maxPayout, setMaxPayout] = useState(
    projectData.royalty_model.max_payout !== undefined
      ? projectData.royalty_model.max_payout
      : 1000000
  );

  const [revenueShare, setRevenueShare] = useState(
    projectData.royalty_model.revenue_share !== undefined
      ? projectData.royalty_model.revenue_share
      : 50
  );

  // Sample data for royalty distribution preview
  const [previewData, setPreviewData] = useState({
    contributors: [
      { name: 'Alice', tasks: 10, hours: 40, difficulty: 4 },
      { name: 'Bob', tasks: 5, hours: 30, difficulty: 5 },
      { name: 'Charlie', tasks: 8, hours: 20, difficulty: 3 }
    ]
  });

  // State for editing preview data
  const [isEditingPreview, setIsEditingPreview] = useState(false);
  const [editedPreviewData, setEditedPreviewData] = useState(null);

  // Handle preview data editing
  const handleEditPreviewData = () => {
    setEditedPreviewData(JSON.parse(JSON.stringify(previewData)));
    setIsEditingPreview(true);
  };

  // Handle contributor data change
  const handleContributorChange = (index, field, value) => {
    const newData = { ...editedPreviewData };
    newData.contributors[index][field] = field === 'name' ? value : parseInt(value, 10) || 0;
    setEditedPreviewData(newData);
  };

  // Save edited preview data
  const savePreviewData = () => {
    setPreviewData(editedPreviewData);
    setIsEditingPreview(false);
    toast.success('Preview data updated');
  };

  // Cancel preview data editing
  const cancelPreviewDataEdit = () => {
    setIsEditingPreview(false);
    setEditedPreviewData(null);
  };

  // Update project data when model type changes
  useEffect(() => {
    setProjectData({
      ...projectData,
      royalty_model: {
        model_type: modelType,
        model_schema: modelSchema,
        configuration: {
          tasks_weight: tasksWeight,
          hours_weight: hoursWeight,
          difficulty_weight: difficultyWeight
        },
        is_pre_expense: isPreExpense,
        min_payout: minPayout,
        max_payout: maxPayout,
        revenue_share: revenueShare
        // Platform fee percentage removed as it's handled by platforms directly
      }
    });
  }, [modelType, modelSchema, tasksWeight, hoursWeight, difficultyWeight, isPreExpense, minPayout, maxPayout, revenueShare]);

  // Handle weight change
  const handleWeightChange = (type, value) => {
    const numValue = parseFloat(value);

    if (type === 'tasks') {
      setTasksWeight(numValue);

      // Adjust other weights to ensure total is 100%
      const remainingWeight = 100 - numValue;
      const ratio = hoursWeight / (hoursWeight + difficultyWeight);

      setHoursWeight(parseFloat((remainingWeight * ratio).toFixed(2)));
      setDifficultyWeight(parseFloat((remainingWeight * (1 - ratio)).toFixed(2)));
    } else if (type === 'hours') {
      setHoursWeight(numValue);

      // Adjust other weights to ensure total is 100%
      const remainingWeight = 100 - numValue;
      const ratio = tasksWeight / (tasksWeight + difficultyWeight);

      setTasksWeight(parseFloat((remainingWeight * ratio).toFixed(2)));
      setDifficultyWeight(parseFloat((remainingWeight * (1 - ratio)).toFixed(2)));
    } else if (type === 'difficulty') {
      setDifficultyWeight(numValue);

      // Adjust other weights to ensure total is 100%
      const remainingWeight = 100 - numValue;
      const ratio = tasksWeight / (tasksWeight + hoursWeight);

      setTasksWeight(parseFloat((remainingWeight * ratio).toFixed(2)));
      setHoursWeight(parseFloat((remainingWeight * (1 - ratio)).toFixed(2)));
    }
  };

  // Reset weights to equal distribution
  const resetWeights = () => {
    setTasksWeight(33.33);
    setHoursWeight(33.33);
    setDifficultyWeight(33.34);
    toast.success('Weights reset to equal distribution');
  };

  // Calculate royalty distribution based on the selected model
  const calculateRoyaltyDistribution = () => {
    const { contributors } = previewData;
    let distribution = [];

    if (modelType === 'equal') {
      // Equal split model
      const equalShare = 100 / contributors.length;
      distribution = contributors.map(contributor => ({
        name: contributor.name,
        percentage: parseFloat(equalShare.toFixed(2))
      }));
    } else if (modelType === 'task') {
      // Task-based model
      const totalTasks = contributors.reduce((sum, c) => sum + c.tasks, 0);
      distribution = contributors.map(contributor => ({
        name: contributor.name,
        percentage: parseFloat(((contributor.tasks / totalTasks) * 100).toFixed(2))
      }));
    } else if (modelType === 'time') {
      // Time-based model
      const totalHours = contributors.reduce((sum, c) => sum + c.hours, 0);
      distribution = contributors.map(contributor => ({
        name: contributor.name,
        percentage: parseFloat(((contributor.hours / totalHours) * 100).toFixed(2))
      }));
    } else if (modelType === 'role') {
      // Role-based model (simplified example)
      distribution = [
        { name: 'Alice (Developer)', percentage: 40 },
        { name: 'Bob (Designer)', percentage: 30 },
        { name: 'Charlie (Manager)', percentage: 30 }
      ];
    } else if (modelType === 'custom') {
      // Custom model (CoG) - Improved to prevent task count from heavily skewing results
      // Calculate scores for each contributor
      const totalTasks = contributors.reduce((sum, c) => sum + c.tasks, 0);
      const totalHours = contributors.reduce((sum, c) => sum + c.hours, 0);

      // Calculate weighted difficulty - using average difficulty instead of multiplying by tasks
      const avgDifficulties = contributors.map(c => ({
        name: c.name,
        avgDifficulty: c.difficulty // Already an average
      }));
      const totalAvgDifficulty = avgDifficulties.reduce((sum, c) => sum + c.avgDifficulty, 0);

      const scores = contributors.map(contributor => {
        // Task score - normalized to reduce impact of high task counts
        const taskScore = totalTasks > 0 ?
          (Math.sqrt(contributor.tasks) / contributors.reduce((sum, c) => sum + Math.sqrt(c.tasks), 0)) *
          (tasksWeight / 100) : 0;

        // Hour score - standard calculation
        const hourScore = totalHours > 0 ?
          (contributor.hours / totalHours) * (hoursWeight / 100) : 0;

        // Difficulty score - using average difficulty instead of multiplying by tasks
        const difficultyScore = totalAvgDifficulty > 0 ?
          (contributor.difficulty / totalAvgDifficulty) * (difficultyWeight / 100) : 0;

        return {
          name: contributor.name,
          score: taskScore + hourScore + difficultyScore,
          components: { taskScore, hourScore, difficultyScore } // Store components for debugging
        };
      });

      const totalScore = scores.reduce((sum, s) => sum + s.score, 0);

      distribution = scores.map(score => ({
        name: score.name,
        percentage: parseFloat(((score.score / totalScore) * 100).toFixed(2))
      }));
    }

    return distribution;
  };

  return (
    <div className="wizard-step-content">
      <h2 className="step-title">Royalty Model Configuration</h2>
      <p className="step-description">
        Choose how royalties will be distributed among contributors.
      </p>

      <div className="model-selector">
        <div
          className={`model-option ${modelType === 'custom' ? 'selected' : ''}`}
          onClick={() => setModelType('custom')}
        >
          <div className="model-option-header">
            <div className="model-option-icon">
              <i className="bi bi-sliders"></i>
            </div>
            <div className="model-option-title">Custom Model</div>
          </div>
          <div className="model-option-description">
            Create a custom model that combines multiple factors with weighted
            percentages.
          </div>
        </div>

        <div
          className={`model-option ${modelType === 'equal' ? 'selected' : ''}`}
          onClick={() => setModelType('equal')}
        >
          <div className="model-option-header">
            <div className="model-option-icon">
              <i className="bi bi-distribute-horizontal"></i>
            </div>
            <div className="model-option-title">Equal Split</div>
          </div>
          <div className="model-option-description">
            All contributors receive an equal share of royalties regardless of their
            contribution amount.
          </div>
        </div>

        <div
          className={`model-option ${modelType === 'task' ? 'selected' : ''}`}
          onClick={() => setModelType('task')}
        >
          <div className="model-option-header">
            <div className="model-option-icon">
              <i className="bi bi-check2-square"></i>
            </div>
            <div className="model-option-title">Task-based</div>
          </div>
          <div className="model-option-description">
            Royalties are distributed based on the number of tasks completed by each
            contributor.
          </div>
        </div>

        <div
          className={`model-option ${modelType === 'time' ? 'selected' : ''}`}
          onClick={() => setModelType('time')}
        >
          <div className="model-option-header">
            <div className="model-option-icon">
              <i className="bi bi-clock"></i>
            </div>
            <div className="model-option-title">Time-based</div>
          </div>
          <div className="model-option-description">
            Royalties are distributed based on the hours tracked by each contributor.
          </div>
        </div>

        <div
          className={`model-option ${modelType === 'role' ? 'selected' : ''}`}
          onClick={() => setModelType('role')}
        >
          <div className="model-option-header">
            <div className="model-option-icon">
              <i className="bi bi-person-badge"></i>
            </div>
            <div className="model-option-title">Role-based</div>
          </div>
          <div className="model-option-description">
            Royalties are distributed based on predefined percentages for each role.
          </div>
        </div>
      </div>

      {modelType === 'custom' && (
        <div className="custom-model-builder">
          <div className="d-flex justify-content-between align-items-center mb-4">
            <h3 className="h5 mb-0">Custom Model Builder</h3>
            <div>
              <select
                className="form-select form-select-sm me-2 d-inline-block"
                style={{ width: 'auto' }}
                value={modelSchema}
                onChange={(e) => setModelSchema(e.target.value)}
              >
                <option value="cog">CoG Model (Tasks-Hours-Difficulty)</option>
                <option value="studio-a">Studio A Algorithm (Coming Soon)</option>
                <option value="studio-b">Studio B Algorithm (Coming Soon)</option>
              </select>
              <button
                type="button"
                className="btn btn-sm btn-outline-secondary"
                onClick={resetWeights}
              >
                Reset to Equal
              </button>
            </div>
          </div>

          <div className="row">
            <div className="col-md-7">
              <div className="weight-slider-container">
                <div className="weight-slider-header">
                  <div className="weight-slider-label">
                    Tasks Weight
                    <i
                      className="bi bi-info-circle ms-1"
                      data-bs-toggle="tooltip"
                      title="Weight given to the number of tasks completed"
                    ></i>
                  </div>
                  <div className="weight-slider-value">{tasksWeight}%</div>
                </div>
                <input
                  type="range"
                  className="form-range weight-slider"
                  min="0"
                  max="100"
                  step="1"
                  value={tasksWeight}
                  onChange={(e) => handleWeightChange('tasks', e.target.value)}
                />
              </div>

              <div className="weight-slider-container">
                <div className="weight-slider-header">
                  <div className="weight-slider-label">
                    Hours Weight
                    <i
                      className="bi bi-info-circle ms-1"
                      data-bs-toggle="tooltip"
                      title="Weight given to the hours tracked"
                    ></i>
                  </div>
                  <div className="weight-slider-value">{hoursWeight}%</div>
                </div>
                <input
                  type="range"
                  className="form-range weight-slider"
                  min="0"
                  max="100"
                  step="1"
                  value={hoursWeight}
                  onChange={(e) => handleWeightChange('hours', e.target.value)}
                />
              </div>

              <div className="weight-slider-container">
                <div className="weight-slider-header">
                  <div className="weight-slider-label">
                    Difficulty Weight
                    <i
                      className="bi bi-info-circle ms-1"
                      data-bs-toggle="tooltip"
                      title="Weight given to the difficulty of tasks"
                    ></i>
                  </div>
                  <div className="weight-slider-value">{difficultyWeight}%</div>
                </div>
                <input
                  type="range"
                  className="form-range weight-slider"
                  min="0"
                  max="100"
                  step="1"
                  value={difficultyWeight}
                  onChange={(e) => handleWeightChange('difficulty', e.target.value)}
                />
              </div>

              {/* Financial Parameters Section */}
              <div className="financial-parameters-container mt-4">
                <h4 className="h6 mb-3">Financial Parameters</h4>

                <div className="mb-3">
                  <label htmlFor="revenueShare" className="form-label">
                    Revenue Share Percentage
                    <i
                      className="bi bi-info-circle ms-1"
                      data-bs-toggle="tooltip"
                      title="Percentage of revenue shared with contributors"
                    ></i>
                  </label>
                  <div className="input-group">
                    <input
                      type="number"
                      className="form-control"
                      id="revenueShare"
                      min="1"
                      max="100"
                      value={revenueShare}
                      onChange={(e) => setRevenueShare(parseInt(e.target.value) || 50)}
                    />
                    <span className="input-group-text">%</span>
                  </div>
                  <div className="form-text">
                    Percentage of revenue that will be shared with contributors.
                  </div>
                </div>

                <div className="mb-3">
                  <label htmlFor="minPayout" className="form-label">
                    Minimum Payout Threshold
                    <i
                      className="bi bi-info-circle ms-1"
                      data-bs-toggle="tooltip"
                      title="Minimum amount before payouts are processed"
                    ></i>
                  </label>
                  <div className="input-group">
                    <span className="input-group-text">$</span>
                    <input
                      type="number"
                      className="form-control"
                      id="minPayout"
                      min="0"
                      step="10"
                      value={minPayout / 100}
                      onChange={(e) => setMinPayout(Math.round(parseFloat(e.target.value) * 100) || 0)}
                    />
                  </div>
                  <div className="form-text">
                    Minimum amount that must be accumulated before a payout is processed.
                  </div>
                </div>

                <div className="mb-3">
                  <label htmlFor="maxPayout" className="form-label">
                    Maximum Payout
                    <i
                      className="bi bi-info-circle ms-1"
                      data-bs-toggle="tooltip"
                      title="Maximum amount that can be paid out"
                    ></i>
                  </label>
                  <div className="input-group">
                    <span className="input-group-text">$</span>
                    <input
                      type="number"
                      className="form-control"
                      id="maxPayout"
                      min="0"
                      step="1000"
                      value={maxPayout / 100}
                      onChange={(e) => setMaxPayout(Math.round(parseFloat(e.target.value) * 100) || 0)}
                    />
                  </div>
                  <div className="form-text">
                    Maximum amount that can be paid out in total (0 for no limit).
                  </div>
                </div>
              </div>

              {/* Platform fee section removed - platform fees are handled by the platforms directly */}
              <div className="info-container mt-4 p-3 border rounded bg-light">
                <h4 className="h6 mb-3">Platform Fee Information</h4>
                <p className="text-muted small mb-0">
                  Platform fees are specific to each platform and are automatically calculated before the money reaches Royaltea's escrow.
                  You don't need to configure platform fees in the royalty model.
                </p>
              </div>
            </div>

            <div className="col-md-5">
              <div className="pie-chart-container">
                <h4 className="h6 mb-3">Weight Distribution</h4>
                <PieChart
                  data={[
                    { title: 'Tasks', value: tasksWeight, color: '#3b82f6' },
                    { title: 'Hours', value: hoursWeight, color: '#10b981' },
                    { title: 'Difficulty', value: difficultyWeight, color: '#f59e0b' }
                  ]}
                  lineWidth={60}
                  paddingAngle={2}
                  rounded
                  label={({ dataEntry }) => `${dataEntry.title}`}
                  labelStyle={{
                    fontSize: '5px',
                    fontFamily: 'sans-serif',
                    fill: '#fff'
                  }}
                  labelPosition={70}
                  style={{ height: '200px' }}
                />
                <div className="d-flex justify-content-center mt-3">
                  <div className="d-flex align-items-center me-3">
                    <div
                      style={{
                        width: '12px',
                        height: '12px',
                        backgroundColor: '#3b82f6',
                        borderRadius: '2px',
                        marginRight: '4px'
                      }}
                    ></div>
                    <span className="small">Tasks</span>
                  </div>
                  <div className="d-flex align-items-center me-3">
                    <div
                      style={{
                        width: '12px',
                        height: '12px',
                        backgroundColor: '#10b981',
                        borderRadius: '2px',
                        marginRight: '4px'
                      }}
                    ></div>
                    <span className="small">Hours</span>
                  </div>
                  <div className="d-flex align-items-center">
                    <div
                      style={{
                        width: '12px',
                        height: '12px',
                        backgroundColor: '#f59e0b',
                        borderRadius: '2px',
                        marginRight: '4px'
                      }}
                    ></div>
                    <span className="small">Difficulty</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {modelType === 'custom' && (
        <div className="algorithm-info-container mt-4 p-3 border rounded bg-light">
          <h4 className="h6 mb-3">Algorithm Information</h4>

          {modelSchema === 'cog' && (
            <>
              <p className="mb-2">
                <strong>CoG Model (Tasks-Hours-Difficulty)</strong> - Our recommended algorithm that balances:
              </p>
              <ul className="mb-3">
                <li><strong>Tasks:</strong> Number of tasks completed (using square root normalization to prevent skewing)</li>
                <li><strong>Hours:</strong> Time spent on contributions</li>
                <li><strong>Difficulty:</strong> Average complexity of completed tasks</li>
              </ul>
              <p className="text-muted small mb-3">
                This balanced approach ensures fair compensation based on both quantity and quality of work.
              </p>
              <div className="algorithm-details p-2 border rounded bg-custom-white small">
                <p className="mb-1"><strong>Technical Details:</strong></p>
                <ul className="mb-0">
                  <li>Task score uses square root normalization to prevent contributors with many small tasks from dominating</li>
                  <li>Difficulty is calculated using the average difficulty rating rather than multiplying by task count</li>
                  <li>This creates a more balanced distribution that rewards both quantity and quality</li>
                </ul>
              </div>
            </>
          )}

          {modelSchema === 'studio-a' && (
            <>
              <p className="mb-2">
                <strong>Studio A Algorithm</strong> - Coming soon
              </p>
              <p className="text-muted small mb-3">
                This algorithm will be available in a future update. It uses a different weighting system focused on specific studio needs.
              </p>
            </>
          )}

          {modelSchema === 'studio-b' && (
            <>
              <p className="mb-2">
                <strong>Studio B Algorithm</strong> - Coming soon
              </p>
              <p className="text-muted small mb-3">
                This algorithm will be available in a future update. It uses a different approach to contribution valuation.
              </p>
            </>
          )}

          {/* Custom algorithm option removed */}
        </div>
      )}

      {modelType === 'custom' && modelSchema === 'cog' && (
        <div className="default-difficulty-container mt-4 p-3 border rounded bg-light">
          <h4 className="h6 mb-3">Default Task Types & Difficulty Scores</h4>
          <p className="text-muted small mb-3">
            These default difficulty scores will be used when tracking contributions. You can customize these later.
          </p>

          <div className="table-responsive">
            <table className="table table-sm">
              <thead>
                <tr>
                  <th>Task Type</th>
                  <th>Difficulty Level</th>
                  <th>Points</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>Programming</td>
                  <td>Medium</td>
                  <td>5</td>
                </tr>
                <tr>
                  <td>Art</td>
                  <td>Hard</td>
                  <td>10</td>
                </tr>
                <tr>
                  <td>QA</td>
                  <td>Easy</td>
                  <td>2</td>
                </tr>
                <tr>
                  <td>Writing</td>
                  <td>Medium</td>
                  <td>5</td>
                </tr>
                <tr>
                  <td>Engineering</td>
                  <td>Hard</td>
                  <td>10</td>
                </tr>
                <tr>
                  <td>Admin</td>
                  <td>Medium</td>
                  <td>5</td>
                </tr>
                <tr>
                  <td>Design</td>
                  <td>Medium</td>
                  <td>5</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Royalty Distribution Preview */}
      <div className="royalty-preview-container mt-4 p-3 border rounded">
        <div className="d-flex justify-content-between align-items-center mb-3">
          <h4 className="h5 mb-0">Royalty Distribution Preview</h4>
          {!isEditingPreview ? (
            <button
              type="button"
              className="btn btn-sm btn-outline-primary"
              onClick={handleEditPreviewData}
            >
              <i className="bi bi-pencil me-1"></i> Edit Sample Data
            </button>
          ) : (
            <div>
              <button
                type="button"
                className="btn btn-sm btn-outline-secondary me-2"
                onClick={cancelPreviewDataEdit}
              >
                Cancel
              </button>
              <button
                type="button"
                className="btn btn-sm btn-primary"
                onClick={savePreviewData}
              >
                Save Changes
              </button>
            </div>
          )}
        </div>
        <p className="text-muted small mb-3">
          This is a preview of how royalties would be distributed based on the selected model and sample data.
        </p>

        <div className="row">
          <div className="col-md-6">
            <h5 className="h6 mb-3">Sample Contributor Data</h5>
            <div className="table-responsive">
              <table className="table table-sm table-bordered">
                <thead className="table-light">
                  <tr>
                    <th>Contributor</th>
                    <th>Tasks</th>
                    <th>Hours</th>
                    <th>Avg. Difficulty</th>
                  </tr>
                </thead>
                <tbody>
                  {!isEditingPreview ? (
                    // Display mode
                    previewData.contributors.map((contributor, index) => (
                      <tr key={index}>
                        <td>{contributor.name}</td>
                        <td>{contributor.tasks}</td>
                        <td>{contributor.hours}</td>
                        <td>{contributor.difficulty}</td>
                      </tr>
                    ))
                  ) : (
                    // Edit mode
                    editedPreviewData.contributors.map((contributor, index) => (
                      <tr key={index}>
                        <td>
                          <input
                            type="text"
                            className="form-control form-control-sm"
                            value={contributor.name}
                            onChange={(e) => handleContributorChange(index, 'name', e.target.value)}
                          />
                        </td>
                        <td>
                          <input
                            type="number"
                            className="form-control form-control-sm"
                            value={contributor.tasks}
                            min="0"
                            onChange={(e) => handleContributorChange(index, 'tasks', e.target.value)}
                          />
                        </td>
                        <td>
                          <input
                            type="number"
                            className="form-control form-control-sm"
                            value={contributor.hours}
                            min="0"
                            onChange={(e) => handleContributorChange(index, 'hours', e.target.value)}
                          />
                        </td>
                        <td>
                          <input
                            type="number"
                            className="form-control form-control-sm"
                            value={contributor.difficulty}
                            min="1"
                            max="10"
                            onChange={(e) => handleContributorChange(index, 'difficulty', e.target.value)}
                          />
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>

          <div className="col-md-6">
            <h5 className="h6 mb-3">Calculated Distribution</h5>
            <div className="table-responsive">
              <table className="table table-sm table-bordered">
                <thead className="table-light">
                  <tr>
                    <th>Contributor</th>
                    <th>Percentage</th>
                    <th>Visual</th>
                  </tr>
                </thead>
                <tbody>
                  {calculateRoyaltyDistribution().map((distribution, index) => (
                    <tr key={index}>
                      <td>{distribution.name}</td>
                      <td>{distribution.percentage}%</td>
                      <td>
                        <div className="progress" style={{ height: '15px' }}>
                          <div
                            className="progress-bar"
                            role="progressbar"
                            style={{
                              width: `${distribution.percentage}%`,
                              backgroundColor: index === 0 ? '#3b82f6' : index === 1 ? '#10b981' : '#f59e0b'
                            }}
                            aria-valuenow={distribution.percentage}
                            aria-valuemin="0"
                            aria-valuemax="100"
                          ></div>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <div className="alert alert-info mt-4">
        <div className="d-flex">
          <div className="me-3">
            <i className="bi bi-info-circle-fill fs-4"></i>
          </div>
          <div>
            <h4 className="h6">How Royalty Models Work</h4>
            <p className="mb-0">
              {modelType === 'equal' && (
                "With the Equal Split model, all contributors receive an equal share of royalties. For example, if there are 5 contributors, each will receive 20% of the royalties."
              )}
              {modelType === 'task' && (
                "With the Task-based model, royalties are distributed based on the number of tasks completed by each contributor. Contributors who complete more tasks receive a larger share of royalties."
              )}
              {modelType === 'time' && (
                "With the Time-based model, royalties are distributed based on the hours tracked by each contributor. Contributors who track more hours receive a larger share of royalties."
              )}
              {modelType === 'role' && (
                "With the Role-based model, royalties are distributed based on predefined percentages for each role. For example, Developers might receive 40%, Designers 30%, and Project Managers 30%."
              )}
              {modelType === 'custom' && modelSchema === 'cog' && (
                "With the CoG model (Tasks-Hours-Difficulty), royalties are distributed based on a weighted combination of tasks completed, hours tracked, and task difficulty. This balanced approach uses square root normalization for tasks and average difficulty ratings to prevent any single factor from dominating the calculation."
              )}
              {modelType === 'custom' && modelSchema === 'studio-a' && (
                "The Studio A Algorithm (coming soon) will offer an alternative approach to royalty distribution based on studio feedback. This algorithm will have its own unique parameters and weighting system."
              )}
              {modelType === 'custom' && modelSchema === 'studio-b' && (
                "The Studio B Algorithm (coming soon) will provide another option for royalty distribution with different parameters tailored to specific project types and team structures."
              )}
              {/* Custom algorithm explanation removed */}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoyaltyModel;
