import { supabase } from '../lib/supabase';

class PlaidService {
  constructor() {
    this.plaidEnv = process.env.REACT_APP_PLAID_ENV || 'sandbox';
    this.plaidClientId = process.env.REACT_APP_PLAID_CLIENT_ID;
    this.plaidPublicKey = process.env.REACT_APP_PLAID_PUBLIC_KEY;
    this.baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
  }

  /**
   * Initialize Plaid Link for account connection
   */
  async initializePlaidLink(userId, products = ['auth', 'transactions', 'transfer']) {
    try {
      const response = await fetch(`${this.baseUrl}/api/plaid/link-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await this.getAuthToken()}`
        },
        body: JSON.stringify({
          user_id: userId,
          products,
          country_codes: ['US'],
          language: 'en'
        })
      });

      if (!response.ok) {
        throw new Error('Failed to create link token');
      }

      const data = await response.json();
      return data.link_token;
    } catch (error) {
      console.error('Error initializing Plaid Link:', error);
      throw error;
    }
  }

  /**
   * Exchange public token for access token and store account info
   */
  async exchangePublicToken(publicToken, userId) {
    try {
      const response = await fetch(`${this.baseUrl}/api/plaid/exchange-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await this.getAuthToken()}`
        },
        body: JSON.stringify({
          public_token: publicToken,
          user_id: userId
        })
      });

      if (!response.ok) {
        throw new Error('Failed to exchange public token');
      }

      const data = await response.json();

      // Store account information in Supabase
      await this.storeAccountInfo(data.accounts, data.access_token, data.item_id, userId);

      return data;
    } catch (error) {
      console.error('Error exchanging public token:', error);
      throw error;
    }
  }

  /**
   * Store account information in Supabase
   */
  async storeAccountInfo(accounts, accessToken, itemId, userId) {
    try {
      const accountsToInsert = accounts.map(account => ({
        user_id: userId,
        plaid_account_id: account.account_id,
        plaid_item_id: itemId,
        plaid_access_token: accessToken,
        account_name: account.name,
        account_type: this.mapAccountType(account.type),
        account_subtype: account.subtype,
        institution_name: account.institution?.name,
        institution_id: account.institution?.institution_id,
        supports_ach: true, // Most accounts support ACH
        supports_same_day_ach: account.subtype !== 'savings', // Savings typically don't support same-day
        supports_rtp: false, // Will be updated based on institution capabilities
        supports_wire: account.type === 'depository',
        available_balance: account.balances?.available,
        current_balance: account.balances?.current,
        balance_updated_at: new Date().toISOString(),
        account_metadata: {
          mask: account.mask,
          official_name: account.official_name,
          balances: account.balances
        },
        plaid_metadata: account
      }));

      const { data, error } = await supabase
        .from('plaid_accounts')
        .upsert(accountsToInsert, {
          onConflict: 'user_id,plaid_account_id',
          ignoreDuplicates: false
        })
        .select();

      if (error) throw error;

      // Update payment capabilities for each account
      await this.updatePaymentCapabilities(data);

      return data;
    } catch (error) {
      console.error('Error storing account info:', error);
      throw error;
    }
  }

  /**
   * Update payment capabilities based on institution and account type
   */
  async updatePaymentCapabilities(accounts) {
    try {
      for (const account of accounts) {
        const capabilities = await this.checkPaymentCapabilities(account.plaid_account_id);

        const { error } = await supabase
          .from('plaid_accounts')
          .update({
            supports_ach: capabilities.ach,
            supports_same_day_ach: capabilities.same_day_ach,
            supports_rtp: capabilities.rtp,
            supports_wire: capabilities.wire
          })
          .eq('id', account.id);

        if (error) throw error;
      }
    } catch (error) {
      console.error('Error updating payment capabilities:', error);
    }
  }

  /**
   * Check payment capabilities for an account
   */
  async checkPaymentCapabilities(accountId) {
    try {
      const response = await fetch(`${this.baseUrl}/api/plaid/payment-capabilities`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await this.getAuthToken()}`
        },
        body: JSON.stringify({ account_id: accountId })
      });

      if (!response.ok) {
        // Return default capabilities if check fails
        return {
          ach: true,
          same_day_ach: true,
          rtp: false,
          wire: true
        };
      }

      return await response.json();
    } catch (error) {
      console.error('Error checking payment capabilities:', error);
      return {
        ach: true,
        same_day_ach: true,
        rtp: false,
        wire: true
      };
    }
  }

  /**
   * Initiate a payment transfer
   */
  async initiateTransfer(transferData) {
    try {
      const {
        fromAccountId,
        toAccountId,
        amount,
        paymentMethod = 'ach_standard',
        description,
        referenceId,
        referenceType
      } = transferData;

      // Validate payment method is supported
      await this.validatePaymentMethod(fromAccountId, toAccountId, paymentMethod);

      const response = await fetch(`${this.baseUrl}/api/plaid/transfer`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await this.getAuthToken()}`
        },
        body: JSON.stringify({
          from_account_id: fromAccountId,
          to_account_id: toAccountId,
          amount: amount,
          payment_method: paymentMethod,
          description: description,
          reference_id: referenceId,
          reference_type: referenceType
        })
      });

      if (!response.ok) {
        throw new Error('Failed to initiate transfer');
      }

      const data = await response.json();

      // Store transaction record
      await this.storeTransactionRecord(data, transferData);

      return data;
    } catch (error) {
      console.error('Error initiating transfer:', error);
      throw error;
    }
  }

  /**
   * Store transaction record in database
   */
  async storeTransactionRecord(plaidResponse, originalData) {
    try {
      const { data, error } = await supabase
        .from('payment_transactions')
        .insert({
          plaid_transfer_id: plaidResponse.transfer_id,
          from_account_id: originalData.fromAccountId,
          to_account_id: originalData.toAccountId,
          amount: originalData.amount,
          payment_method: originalData.paymentMethod,
          payment_direction: 'outbound', // Assuming outbound for now
          status: 'pending',
          description: originalData.description,
          reference_id: originalData.referenceId,
          reference_type: originalData.referenceType,
          expected_settlement_date: plaidResponse.expected_settlement_date,
          plaid_fee: plaidResponse.fee || 0,
          compliance_data: plaidResponse.compliance_data || {}
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error storing transaction record:', error);
      throw error;
    }
  }

  /**
   * Validate that payment method is supported for the accounts
   */
  async validatePaymentMethod(fromAccountId, toAccountId, paymentMethod) {
    try {
      const { data: accounts, error } = await supabase
        .from('plaid_accounts')
        .select('*')
        .in('id', [fromAccountId, toAccountId]);

      if (error) throw error;

      const fromAccount = accounts.find(a => a.id === fromAccountId);
      const toAccount = accounts.find(a => a.id === toAccountId);

      if (!fromAccount || !toAccount) {
        throw new Error('One or more accounts not found');
      }

      // Check if payment method is supported
      const methodSupported = this.isPaymentMethodSupported(fromAccount, toAccount, paymentMethod);

      if (!methodSupported) {
        throw new Error(`Payment method ${paymentMethod} not supported for these accounts`);
      }

      return true;
    } catch (error) {
      console.error('Error validating payment method:', error);
      throw error;
    }
  }

  /**
   * Check if payment method is supported for both accounts
   */
  isPaymentMethodSupported(fromAccount, toAccount, paymentMethod) {
    const methodMap = {
      'ach_standard': 'supports_ach',
      'ach_same_day': 'supports_same_day_ach',
      'rtp': 'supports_rtp',
      'wire_domestic': 'supports_wire',
      'wire_international': 'supports_wire'
    };

    const supportField = methodMap[paymentMethod];
    if (!supportField) return false;

    return fromAccount[supportField] && toAccount[supportField];
  }

  /**
   * Get optimal payment method for a transfer
   */
  async getOptimalPaymentMethod(fromAccountId, toAccountId, amount, urgency = 'standard') {
    try {
      const { data: accounts, error } = await supabase
        .from('plaid_accounts')
        .select('*')
        .in('id', [fromAccountId, toAccountId]);

      if (error) throw error;

      const fromAccount = accounts.find(a => a.id === fromAccountId);
      const toAccount = accounts.find(a => a.id === toAccountId);

      // Get routing rules
      const { data: rules, error: rulesError } = await supabase
        .from('payment_routing_rules')
        .select('*')
        .eq('is_active', true)
        .order('priority', { ascending: true });

      if (rulesError) throw rulesError;

      // Find matching rule
      for (const rule of rules) {
        if (this.ruleMatches(rule, amount, urgency) &&
            this.isPaymentMethodSupported(fromAccount, toAccount, rule.preferred_method)) {
          return rule.preferred_method;
        }
      }

      // Default fallback
      if (this.isPaymentMethodSupported(fromAccount, toAccount, 'ach_standard')) {
        return 'ach_standard';
      }

      throw new Error('No supported payment method found');
    } catch (error) {
      console.error('Error getting optimal payment method:', error);
      return 'ach_standard'; // Safe fallback
    }
  }

  /**
   * Check if routing rule matches the transfer criteria
   */
  ruleMatches(rule, amount, urgency) {
    if (rule.min_amount && amount < rule.min_amount) return false;
    if (rule.max_amount && amount > rule.max_amount) return false;
    if (rule.urgency_level && rule.urgency_level !== urgency) return false;
    return true;
  }

  /**
   * Map Plaid account type to our enum
   */
  mapAccountType(plaidType) {
    const typeMap = {
      'depository': 'checking', // Default for depository
      'credit': 'checking',
      'loan': 'checking',
      'investment': 'checking'
    };
    return typeMap[plaidType] || 'checking';
  }

  /**
   * Get authentication token for API calls
   */
  async getAuthToken() {
    const { data: { session } } = await supabase.auth.getSession();
    return session?.access_token;
  }

  /**
   * Get user's linked accounts
   */
  async getUserAccounts(userId) {
    try {
      const { data, error } = await supabase
        .from('plaid_accounts')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching user accounts:', error);
      throw error;
    }
  }

  /**
   * Update account balances
   */
  async updateAccountBalances(userId) {
    try {
      const accounts = await this.getUserAccounts(userId);

      for (const account of accounts) {
        const response = await fetch(`${this.baseUrl}/api/plaid/balances`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${await this.getAuthToken()}`
          },
          body: JSON.stringify({
            access_token: account.plaid_access_token,
            account_id: account.plaid_account_id
          })
        });

        if (response.ok) {
          const balanceData = await response.json();

          await supabase
            .from('plaid_accounts')
            .update({
              available_balance: balanceData.available,
              current_balance: balanceData.current,
              balance_updated_at: new Date().toISOString()
            })
            .eq('id', account.id);
        }
      }
    } catch (error) {
      console.error('Error updating account balances:', error);
    }
  }
}

export const plaidService = new PlaidService();
export default plaidService;
