import React, { useState, useEffect } from 'react';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

const ContributionSummary = ({ projectId, userId = null }) => {
  const [summary, setSummary] = useState({
    totalContributions: 0,
    totalHours: 0,
    byCategory: {},
    byTaskType: {},
    byUser: {},
    recentActivity: []
  });
  const [loading, setLoading] = useState(true);
  const [users, setUsers] = useState({});

  // Fetch contribution summary
  useEffect(() => {
    const fetchSummary = async () => {
      try {
        setLoading(true);

        // Base query for contributions
        let query = supabase
          .from('contributions')
          .select('*')
          .eq('validation_status', 'approved'); // Only count approved contributions

        // Filter by project if provided
        if (projectId) {
          query = query.eq('project_id', projectId);
        }

        // Filter by user if provided
        if (userId) {
          query = query.eq('user_id', userId);
        }

        const { data, error } = await query;

        if (error) throw error;

        if (!data || data.length === 0) {
          setLoading(false);
          return;
        }

        // Calculate summary statistics
        const summaryData = calculateSummary(data);
        setSummary(summaryData);

        // Fetch user data for display
        await fetchUserData(summaryData.byUser);

      } catch (error) {
        console.error('Error fetching contribution summary:', error);
        toast.error('Failed to load contribution summary');
      } finally {
        setLoading(false);
      }
    };

    fetchSummary();
  }, [projectId, userId]);

  // Calculate summary statistics from contribution data
  const calculateSummary = (contributions) => {
    const totalContributions = contributions.length;
    const totalHours = contributions.reduce((sum, c) => sum + parseFloat(c.hours_spent), 0);

    // Group by category
    const byCategory = {};
    contributions.forEach(c => {
      if (!byCategory[c.category]) {
        byCategory[c.category] = {
          count: 0,
          hours: 0
        };
      }
      byCategory[c.category].count += 1;
      byCategory[c.category].hours += parseFloat(c.hours_spent);
    });

    // Group by task type
    const byTaskType = {};
    contributions.forEach(c => {
      if (!byTaskType[c.task_type]) {
        byTaskType[c.task_type] = {
          count: 0,
          hours: 0
        };
      }
      byTaskType[c.task_type].count += 1;
      byTaskType[c.task_type].hours += parseFloat(c.hours_spent);
    });

    // Group by user
    const byUser = {};
    contributions.forEach(c => {
      if (!byUser[c.user_id]) {
        byUser[c.user_id] = {
          count: 0,
          hours: 0
        };
      }
      byUser[c.user_id].count += 1;
      byUser[c.user_id].hours += parseFloat(c.hours_spent);
    });

    // Get recent activity (last 5 contributions)
    const recentActivity = [...contributions]
      .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
      .slice(0, 5);

    return {
      totalContributions,
      totalHours,
      byCategory,
      byTaskType,
      byUser,
      recentActivity
    };
  };

  // Fetch user data for display
  const fetchUserData = async (userSummary) => {
    try {
      const userIds = Object.keys(userSummary);

      if (userIds.length === 0) return;

      const { data, error } = await supabase
        .from('users')
        .select('id, email, display_name')
        .in('id', userIds);

      if (error) throw error;

      // Create a map of user data
      const userMap = {};
      data.forEach(user => {
        userMap[user.id] = user;
      });

      setUsers(userMap);
    } catch (error) {
      console.error('Error fetching user data:', error);
    }
  };

  // Get user display name
  const getUserName = (userId) => {
    const user = users[userId];
    if (!user) return 'Unknown User';
    return user.display_name || user.email;
  };

  if (loading) {
    return <div className="contribution-summary-loading">Loading summary...</div>;
  }

  if (summary.totalContributions === 0) {
    return <div className="contribution-summary-empty">No contributions found.</div>;
  }

  return (
    <div className="contribution-summary">
      <div className="summary-header">
        <div className="summary-stat">
          <div className="stat-value">{summary.totalContributions}</div>
          <div className="stat-label">Contributions</div>
        </div>
        <div className="summary-stat">
          <div className="stat-value">{summary.totalHours.toFixed(1)}</div>
          <div className="stat-label">Hours</div>
        </div>
      </div>
      <div className="summary-note">
        <i className="bi bi-info-circle"></i> Only approved contributions are counted in metrics
      </div>

      <div className="summary-sections">
        {/* By Category */}
        <div className="summary-section">
          <h4 className="section-title">By Category</h4>
          <div className="summary-list">
            {Object.entries(summary.byCategory).map(([category, data]) => (
              <div key={category} className="summary-item">
                <div className="item-name">{category}</div>
                <div className="item-stats">
                  <span className="item-count">{data.count}</span>
                  <span className="item-hours">{data.hours.toFixed(1)}h</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* By Task Type */}
        <div className="summary-section">
          <h4 className="section-title">By Task Type</h4>
          <div className="summary-list">
            {Object.entries(summary.byTaskType).map(([taskType, data]) => (
              <div key={taskType} className="summary-item">
                <div className="item-name">{taskType}</div>
                <div className="item-stats">
                  <span className="item-count">{data.count}</span>
                  <span className="item-hours">{data.hours.toFixed(1)}h</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* By User (only show if not filtered by user) */}
        {!userId && (
          <div className="summary-section">
            <h4 className="section-title">By Contributor</h4>
            <div className="summary-list">
              {Object.entries(summary.byUser).map(([userId, data]) => (
                <div key={userId} className="summary-item">
                  <div className="item-name">{getUserName(userId)}</div>
                  <div className="item-stats">
                    <span className="item-count">{data.count}</span>
                    <span className="item-hours">{data.hours.toFixed(1)}h</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ContributionSummary;
