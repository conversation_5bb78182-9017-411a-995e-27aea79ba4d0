import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../../../contexts/supabase-auth.context';
import { supabase } from '../../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

const TeamContributors = ({ projectData, setProjectData, projectId }) => {
  const { currentUser } = useContext(UserContext);
  const [newContributor, setNewContributor] = useState({
    email: '',
    display_name: '',
    role: '',
    permission_level: 'Contributor',
    is_admin: false,
    address: '',
    state: '',
    county: '',
    title: '',
    is_company: false,
    company_name: '',
    signer_name: '',
    signer_title: ''
  });
  const [batchEmails, setBatchEmails] = useState('');
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [showSearchResults, setShowSearchResults] = useState(false);

  // Permission levels
  const permissionLevels = [
    { value: 'Owner', label: 'Owner' },
    { value: 'Admin', label: 'Admin' },
    { value: 'Contributor', label: 'Contributor' },
    { value: 'Viewer', label: 'Viewer' }
  ];

  // Fetch contributors if projectId exists
  useEffect(() => {
    const fetchContributors = async () => {
      if (!projectId) return;

      try {
        setLoading(true);

        const { data, error } = await supabase
          .from('project_contributors')
          .select('*')
          .eq('project_id', projectId);

        if (error) throw error;

        setProjectData({
          ...projectData,
          contributors: data || []
        });
      } catch (error) {
        console.error('Error fetching contributors:', error);
        toast.error('Failed to load contributors');
      } finally {
        setLoading(false);
      }
    };

    fetchContributors();
  }, [projectId]);

  // Search users
  const searchUsers = async (query) => {
    if (!query || query.trim().length < 2) {
      setSearchResults([]);
      setShowSearchResults(false);
      return;
    }

    try {
      // Search by display name (partial match)
      const { data, error } = await supabase
        .from('users')
        .select('id, email, display_name, avatar_url')
        .ilike('display_name', `%${query}%`)
        .limit(5);

      if (error) throw error;

      setSearchResults(data || []);
      setShowSearchResults(data.length > 0);
    } catch (error) {
      console.error('Error searching users:', error);
      toast.error('Failed to search users');
    }
  };

  // Handle search input change
  const handleSearchChange = (e) => {
    const query = e.target.value;
    setSearchQuery(query);
    searchUsers(query);
  };

  // Handle search result click
  const handleSearchResultClick = (user) => {
    setNewContributor({
      ...newContributor,
      email: user.email,
      display_name: user.display_name,
      user_id: user.id
    });
    setSearchQuery('');
    setShowSearchResults(false);
  };

  // Add contributor
  const addContributor = () => {
    // Validate email
    if (!newContributor.email) {
      toast.error('Email is required');
      return;
    }

    // Check if email already exists
    const emailExists = projectData.contributors.some(
      (contributor) => contributor.email === newContributor.email
    );

    if (emailExists) {
      toast.error('This email is already added as a contributor');
      return;
    }

    // Add new contributor
    const contributor = {
      ...newContributor,
      id: null, // Will be set by the database
      project_id: projectId,
      status: 'pending',
      invitation_sent_at: new Date().toISOString()
    };

    setProjectData({
      ...projectData,
      contributors: [...projectData.contributors, contributor]
    });

    // Reset form
    setNewContributor({
      email: '',
      display_name: '',
      role: '',
      permission_level: 'Contributor',
      is_admin: false,
      address: '',
      state: '',
      county: '',
      title: '',
      is_company: false,
      company_name: '',
      signer_name: '',
      signer_title: ''
    });

    toast.success('Contributor added successfully');
  };

  // Remove contributor
  const removeContributor = (index) => {
    const updatedContributors = [...projectData.contributors];
    updatedContributors.splice(index, 1);

    setProjectData({
      ...projectData,
      contributors: updatedContributors
    });

    toast.success('Contributor removed');
  };

  // Update contributor
  const updateContributor = (index, field, value) => {
    const updatedContributors = [...projectData.contributors];
    updatedContributors[index] = {
      ...updatedContributors[index],
      [field]: value
    };

    setProjectData({
      ...projectData,
      contributors: updatedContributors
    });
  };

  // Batch invite
  const handleBatchInvite = () => {
    if (!batchEmails) {
      toast.error('Please enter at least one email');
      return;
    }

    // Split emails by comma, newline, or space
    const emails = batchEmails
      .split(/[,\\n\\s]+/)
      .map((email) => email.trim())
      .filter((email) => email);

    if (emails.length === 0) {
      toast.error('Please enter valid emails');
      return;
    }

    // Add each email as a contributor
    const newContributors = [];
    const existingEmails = [];

    emails.forEach((email) => {
      // Skip if email is invalid
      if (!email.includes('@')) {
        toast.error(`Invalid email: ${email}`);
        return;
      }

      // Skip if email already exists
      if (
        projectData.contributors.some((contributor) => contributor.email === email) ||
        existingEmails.includes(email)
      ) {
        existingEmails.push(email);
        return;
      }

      // Add new contributor
      newContributors.push({
        email,
        display_name: '',
        role: '',
        permission_level: 'Contributor',
        is_admin: false,
        address: '',
        state: '',
        county: '',
        title: '',
        is_company: false,
        company_name: '',
        signer_name: '',
        signer_title: '',
        id: null,
        project_id: projectId,
        status: 'pending',
        invitation_sent_at: new Date().toISOString()
      });
    });

    if (newContributors.length === 0) {
      toast.error('All emails are already added as contributors');
      return;
    }

    setProjectData({
      ...projectData,
      contributors: [...projectData.contributors, ...newContributors]
    });

    // Show success message
    toast.success(`Added ${newContributors.length} contributors`);

    // Show warning for existing emails
    if (existingEmails.length > 0) {
      toast.error(`${existingEmails.length} emails were already added`);
    }

    // Reset batch emails
    setBatchEmails('');
  };

  return (
    <div className="wizard-step-content">
      <h2 className="step-title">Team & Contributors</h2>
      <p className="step-description">
        Add team members and contributors to your project.
      </p>

      <div className="row mt-4">
        <div className="col-md-6">
          <h3 className="h5 mb-3">Add Contributor</h3>

          <div className="mb-3 position-relative">
            <label htmlFor="searchUser" className="form-label">
              Search User
            </label>
            <input
              type="text"
              className="form-control"
              id="searchUser"
              value={searchQuery}
              onChange={handleSearchChange}
              placeholder="Search by name..."
            />

            {showSearchResults && (
              <div className="search-results">
                <ul className="list-group">
                  {searchResults.map((user) => (
                    <li
                      key={user.id}
                      className="list-group-item search-result-item"
                      onClick={() => handleSearchResultClick(user)}
                    >
                      <div className="d-flex align-items-center">
                        <div className="search-result-avatar me-2">
                          <img
                            src={user.avatar_url || '/default-avatar-specs.png'}
                            alt={`${user.display_name}'s avatar`}
                          />
                        </div>
                        <div>
                          <div className="search-result-name">{user.display_name}</div>
                          <div className="search-result-email">{user.email}</div>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          <div className="mb-3">
            <label htmlFor="contributorEmail" className="form-label">
              Email <span className="text-danger">*</span>
            </label>
            <input
              type="email"
              className="form-control"
              id="contributorEmail"
              value={newContributor.email}
              onChange={(e) =>
                setNewContributor({ ...newContributor, email: e.target.value })
              }
              placeholder="Enter email"
              required
            />
          </div>

          <div className="mb-3">
            <label htmlFor="contributorName" className="form-label">
              Display Name
            </label>
            <input
              type="text"
              className="form-control"
              id="contributorName"
              value={newContributor.display_name}
              onChange={(e) =>
                setNewContributor({ ...newContributor, display_name: e.target.value })
              }
              placeholder="Enter display name"
            />
          </div>

          <div className="mb-3">
            <div className="form-check form-switch">
              <input
                className="form-check-input"
                type="checkbox"
                id="isCompany"
                checked={newContributor.is_company}
                onChange={(e) =>
                  setNewContributor({ ...newContributor, is_company: e.target.checked })
                }
              />
              <label className="form-check-label" htmlFor="isCompany">
                This contributor is a company
              </label>
            </div>
          </div>

          {newContributor.is_company ? (
            <>
              <div className="mb-3">
                <label htmlFor="companyName" className="form-label">
                  Company Name
                </label>
                <input
                  type="text"
                  className="form-control"
                  id="companyName"
                  value={newContributor.company_name}
                  onChange={(e) =>
                    setNewContributor({ ...newContributor, company_name: e.target.value })
                  }
                  placeholder="Enter company name"
                />
              </div>

              <div className="row">
                <div className="col-md-6">
                  <div className="mb-3">
                    <label htmlFor="signerName" className="form-label">
                      Signer Name
                    </label>
                    <input
                      type="text"
                      className="form-control"
                      id="signerName"
                      value={newContributor.signer_name}
                      onChange={(e) =>
                        setNewContributor({ ...newContributor, signer_name: e.target.value })
                      }
                      placeholder="Person signing the agreement"
                    />
                  </div>
                </div>

                <div className="col-md-6">
                  <div className="mb-3">
                    <label htmlFor="signerTitle" className="form-label">
                      Signer Title
                    </label>
                    <input
                      type="text"
                      className="form-control"
                      id="signerTitle"
                      value={newContributor.signer_title}
                      onChange={(e) =>
                        setNewContributor({ ...newContributor, signer_title: e.target.value })
                      }
                      placeholder="e.g. CEO, CTO"
                    />
                  </div>
                </div>
              </div>
            </>
          ) : null}

          <div className="mb-3">
            <label htmlFor="contributorAddress" className="form-label">
              Address
            </label>
            <input
              type="text"
              className="form-control"
              id="contributorAddress"
              value={newContributor.address}
              onChange={(e) =>
                setNewContributor({ ...newContributor, address: e.target.value })
              }
              placeholder="Enter address"
            />
            <div className="form-text">
              Required for agreement generation.
            </div>
          </div>

          <div className="row">
            <div className="col-md-6">
              <div className="mb-3">
                <label htmlFor="contributorState" className="form-label">
                  State/Province
                </label>
                <input
                  type="text"
                  className="form-control"
                  id="contributorState"
                  value={newContributor.state}
                  onChange={(e) =>
                    setNewContributor({ ...newContributor, state: e.target.value })
                  }
                  placeholder="Enter state/province"
                />
              </div>
            </div>

            <div className="col-md-6">
              <div className="mb-3">
                <label htmlFor="contributorCounty" className="form-label">
                  County/Region
                </label>
                <input
                  type="text"
                  className="form-control"
                  id="contributorCounty"
                  value={newContributor.county}
                  onChange={(e) =>
                    setNewContributor({ ...newContributor, county: e.target.value })
                  }
                  placeholder="Enter county/region"
                />
              </div>
            </div>
          </div>

          <div className="row">
            <div className="col-md-6">
              <div className="mb-3">
                <label htmlFor="contributorRole" className="form-label">
                  Role
                </label>
                <input
                  type="text"
                  className="form-control"
                  id="contributorRole"
                  value={newContributor.role}
                  onChange={(e) =>
                    setNewContributor({ ...newContributor, role: e.target.value })
                  }
                  placeholder="e.g. Developer, Designer"
                />
              </div>
            </div>

            <div className="col-md-6">
              <div className="mb-3">
                <label htmlFor="contributorTitle" className="form-label">
                  Title
                </label>
                <input
                  type="text"
                  className="form-control"
                  id="contributorTitle"
                  value={newContributor.title}
                  onChange={(e) =>
                    setNewContributor({ ...newContributor, title: e.target.value })
                  }
                  placeholder="e.g. Lead Developer"
                />
              </div>
            </div>
          </div>

          <div className="mb-3">
            <label htmlFor="contributorPermission" className="form-label">
              Permission Level
            </label>
            <select
              className="form-select"
              id="contributorPermission"
              value={newContributor.permission_level}
              onChange={(e) =>
                setNewContributor({
                  ...newContributor,
                  permission_level: e.target.value
                })
              }
            >
              {permissionLevels.map((level) => (
                <option key={level.value} value={level.value}>
                  {level.label}
                </option>
              ))}
            </select>
          </div>

          <button
            type="button"
            className="btn btn-primary"
            onClick={addContributor}
          >
            Add Contributor
          </button>

          <div className="batch-invite-container mt-4">
            <h4 className="h6 mb-3">Batch Invite</h4>
            <div className="mb-3">
              <label htmlFor="batchEmails" className="form-label">
                Enter multiple emails
              </label>
              <textarea
                className="form-control"
                id="batchEmails"
                rows="3"
                value={batchEmails}
                onChange={(e) => setBatchEmails(e.target.value)}
                placeholder="Enter emails separated by commas, spaces, or new lines"
              ></textarea>
              <div className="form-text">
                All users will be added with Contributor permission level.
              </div>
            </div>

            <button
              type="button"
              className="btn btn-outline-primary"
              onClick={handleBatchInvite}
            >
              Invite All
            </button>
          </div>
        </div>

        <div className="col-md-6">
          <h3 className="h5 mb-3">Contributors ({projectData.contributors.length})</h3>

          {projectData.contributors.length === 0 ? (
            <div className="alert alert-info">
              No contributors added yet. Add team members to collaborate on this project.
            </div>
          ) : (
            <div className="contributor-list">
              {projectData.contributors.map((contributor, index) => (
                <div key={index} className="contributor-item">
                  <div className="contributor-header">
                    <div className="d-flex align-items-center">
                      <div className="contributor-avatar">
                        <img
                          src={contributor.avatar_url || '/default-avatar-specs.png'}
                          alt={`${contributor.display_name || contributor.email}'s avatar`}
                        />
                      </div>
                      <div className="contributor-info">
                        <div className="contributor-name">
                          {contributor.display_name || 'Unnamed Contributor'}
                        </div>
                        <div className="contributor-email">{contributor.email}</div>
                      </div>
                    </div>

                    <div className="d-flex align-items-center">
                      <span
                        className={`contributor-status me-2 status-${contributor.status}`}
                      >
                        {contributor.status}
                      </span>

                      <div className="contributor-actions">
                        <button
                          type="button"
                          className="btn btn-sm btn-outline-danger"
                          onClick={() => removeContributor(index)}
                          disabled={contributor.email === currentUser?.email}
                        >
                          <i className="bi bi-trash"></i>
                        </button>
                      </div>
                    </div>
                  </div>

                  <div className="row mt-3">
                    <div className="col-md-6">
                      <div className="mb-2">
                        <label className="form-label small">Role</label>
                        <input
                          type="text"
                          className="form-control form-control-sm"
                          value={contributor.role || ''}
                          onChange={(e) =>
                            updateContributor(index, 'role', e.target.value)
                          }
                          placeholder="e.g. Developer, Designer"
                          disabled={contributor.email === currentUser?.email}
                        />
                      </div>
                    </div>

                    <div className="col-md-6">
                      <div className="mb-2">
                        <label className="form-label small">Permission Level</label>
                        <select
                          className="form-select form-select-sm"
                          value={contributor.permission_level}
                          onChange={(e) =>
                            updateContributor(index, 'permission_level', e.target.value)
                          }
                          disabled={contributor.email === currentUser?.email}
                        >
                          {permissionLevels.map((level) => (
                            <option key={level.value} value={level.value}>
                              {level.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Admin checkbox removed - permission level dropdown handles this */}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TeamContributors;
