// Script to check the status of all agreements
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client with the production URL and service role key
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzgyMDU1OSwiZXhwIjoyMDU5Mzk2NTU5fQ.k5xvFBzBxNMCtPd0d8Ko9dAcT5Y3op3ZgFvlAf85LLs';
const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log('Checking the status of all agreements...\n');
    
    // Get all projects
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('*');
    
    if (projectsError) {
      console.error('Error fetching projects:', projectsError);
      return;
    }
    
    if (!projects || projects.length === 0) {
      console.log('No projects found');
      return;
    }
    
    console.log(`Found ${projects.length} projects`);
    
    // Process each project
    for (const project of projects) {
      console.log(`\nChecking project: ${project.name} (${project.id})`);
      console.log(`Project type: ${project.project_type || 'Unknown'}`);
      console.log(`Description: ${project.description || 'No description'}`);
      
      // Get agreements for this project
      const { data: agreements, error: agreementsError } = await supabase
        .from('contributor_agreements')
        .select('*')
        .eq('project_id', project.id);
      
      if (agreementsError) {
        console.error(`Error fetching agreements for project ${project.name}:`, agreementsError);
        continue;
      }
      
      if (!agreements || agreements.length === 0) {
        console.log(`No agreements found for project ${project.name}`);
        continue;
      }
      
      console.log(`Found ${agreements.length} agreements for project ${project.name}`);
      
      // Process each agreement
      for (const agreement of agreements) {
        console.log(`\n  Agreement ID: ${agreement.id}`);
        console.log(`  Version: ${agreement.version || 1}`);
        console.log(`  Status: ${agreement.status || 'Unknown'}`);
        
        // Check for Village references
        const villageReferences = checkForVillageReferences(agreement.agreement_text);
        
        if (villageReferences.length > 0) {
          console.log(`  Contains ${villageReferences.length} Village references`);
          
          // If this is VotA, it's expected to have Village references
          if (project.name === 'VotA') {
            console.log(`  ✓ Village references are expected for VotA project`);
          } else {
            console.log(`  ⚠️ Unexpected Village references in ${project.name} project:`);
            villageReferences.forEach((ref, index) => {
              if (index < 3) { // Only show the first 3 references
                console.log(`\n  ${index + 1}. Line ${ref.lineNumber}: ${ref.line}`);
                console.log(`     Context: ${ref.context}`);
              }
            });
            
            if (villageReferences.length > 3) {
              console.log(`\n  ... and ${villageReferences.length - 3} more references`);
            }
          }
        } else {
          // If this is VotA, it should have Village references
          if (project.name === 'VotA') {
            console.log(`  ⚠️ VotA agreement is missing expected Village references`);
          } else {
            console.log(`  ✓ No Village references found in agreement ${agreement.id}`);
          }
        }
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

// Helper function to check for Village references
function checkForVillageReferences(text) {
  if (!text) return [];
  
  const lines = text.split('\n');
  const references = [];
  
  const villagePatterns = [
    /village/i,
    /Village of The Ages/i,
    /Village of the Ages/i
  ];
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    for (const pattern of villagePatterns) {
      if (pattern.test(line)) {
        // Get context (1 line before and after)
        const start = Math.max(0, i - 1);
        const end = Math.min(lines.length - 1, i + 1);
        const context = lines.slice(start, end + 1).join('\n');
        
        references.push({
          lineNumber: i + 1,
          line: line,
          context: context
        });
        
        // Only add each line once, even if it matches multiple patterns
        break;
      }
    }
  }
  
  return references;
}

main();
