import React, { useState } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Tabs, Tab } from '@heroui/react';
import { motion } from 'framer-motion';

/**
 * Growth Trends Widget - 2x2 Bento Grid Component
 * 
 * Features:
 * - Interactive trend charts for revenue, users, missions
 * - Multiple time period views
 * - Growth projections and forecasting
 * - Export and analysis capabilities
 */
const GrowthTrends = ({ data, period, className = "" }) => {
  const [selectedMetric, setSelectedMetric] = useState('revenue');
  
  const {
    revenueGrowth = [],
    userGrowth = [],
    missionGrowth = []
  } = data || {};

  // Chart data mapping
  const chartData = {
    revenue: {
      data: revenueGrowth,
      label: 'Revenue Growth',
      color: 'text-green-600',
      bgColor: 'bg-green-500',
      icon: '💰',
      format: (value) => `$${(value / 1000).toFixed(0)}K`
    },
    users: {
      data: userGrowth,
      label: 'User Growth',
      color: 'text-blue-600',
      bgColor: 'bg-blue-500',
      icon: '👥',
      format: (value) => value.toString()
    },
    missions: {
      data: missionGrowth,
      label: 'Mission Growth',
      color: 'text-purple-600',
      bgColor: 'bg-purple-500',
      icon: '🎯',
      format: (value) => value.toString()
    }
  };

  const currentChart = chartData[selectedMetric];
  const maxValue = Math.max(...currentChart.data);
  const minValue = Math.min(...currentChart.data);
  const growth = currentChart.data.length > 1 
    ? ((currentChart.data[currentChart.data.length - 1] - currentChart.data[0]) / currentChart.data[0] * 100).toFixed(1)
    : 0;

  // Generate chart points for SVG
  const generateChartPoints = (data) => {
    if (!data || data.length === 0) return '';
    
    const width = 300;
    const height = 120;
    const padding = 20;
    
    const xStep = (width - 2 * padding) / (data.length - 1);
    const yRange = maxValue - minValue;
    
    return data.map((value, index) => {
      const x = padding + index * xStep;
      const y = height - padding - ((value - minValue) / yRange) * (height - 2 * padding);
      return `${x},${y}`;
    }).join(' ');
  };

  const chartPoints = generateChartPoints(currentChart.data);

  return (
    <div className={`growth-trends ${className}`}>
      <Card className="bg-gradient-to-br from-indigo-50 to-purple-100 dark:from-indigo-900/20 dark:to-purple-800/20 border-2 border-indigo-200 dark:border-indigo-700 h-full">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <span className="text-2xl">📈</span>
              <h3 className="text-lg font-semibold">Growth Trends</h3>
            </div>
            <div className="flex gap-2">
              <Button size="sm" variant="flat">
                Projections
              </Button>
              <Button size="sm" variant="flat">
                Export
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardBody className="pt-0">
          {/* Metric Selector */}
          <Tabs
            selectedKey={selectedMetric}
            onSelectionChange={setSelectedMetric}
            variant="bordered"
            className="mb-4"
          >
            <Tab
              key="revenue"
              title={
                <div className="flex items-center gap-2">
                  <span>💰</span>
                  <span className="hidden sm:inline">Revenue</span>
                </div>
              }
            />
            <Tab
              key="users"
              title={
                <div className="flex items-center gap-2">
                  <span>👥</span>
                  <span className="hidden sm:inline">Users</span>
                </div>
              }
            />
            <Tab
              key="missions"
              title={
                <div className="flex items-center gap-2">
                  <span>🎯</span>
                  <span className="hidden sm:inline">Missions</span>
                </div>
              }
            />
          </Tabs>

          {/* Current Metric Display */}
          <div className="flex items-center justify-between mb-4">
            <div>
              <div className={`text-2xl font-bold ${currentChart.color}`}>
                {currentChart.format(currentChart.data[currentChart.data.length - 1] || 0)}
              </div>
              <div className="text-sm text-default-600">{currentChart.label}</div>
            </div>
            <Chip 
              color={growth >= 0 ? 'success' : 'danger'} 
              variant="flat"
              startContent={<span>{growth >= 0 ? '↗️' : '↘️'}</span>}
            >
              {growth >= 0 ? '+' : ''}{growth}%
            </Chip>
          </div>

          {/* Chart Visualization */}
          <div className="mb-4 p-4 bg-white/50 dark:bg-slate-800/50 rounded-lg">
            <svg width="100%" height="120" viewBox="0 0 300 120" className="overflow-visible">
              {/* Grid lines */}
              <defs>
                <pattern id="grid" width="60" height="24" patternUnits="userSpaceOnUse">
                  <path d="M 60 0 L 0 0 0 24" fill="none" stroke="currentColor" strokeWidth="0.5" opacity="0.2"/>
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#grid)" />
              
              {/* Chart line */}
              {chartPoints && (
                <motion.polyline
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="3"
                  points={chartPoints}
                  className={currentChart.color}
                  initial={{ pathLength: 0 }}
                  animate={{ pathLength: 1 }}
                  transition={{ duration: 1.5, ease: "easeInOut" }}
                />
              )}
              
              {/* Data points */}
              {currentChart.data.map((value, index) => {
                const x = 20 + index * ((300 - 40) / (currentChart.data.length - 1));
                const y = 100 - ((value - minValue) / (maxValue - minValue)) * 80;
                return (
                  <motion.circle
                    key={index}
                    cx={x}
                    cy={y}
                    r="4"
                    className={`${currentChart.bgColor} fill-current`}
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  />
                );
              })}
            </svg>
          </div>

          {/* Period Labels */}
          <div className="flex justify-between text-xs text-default-500 mb-4">
            {period === '7d' && ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => (
              <span key={index}>{day}</span>
            ))}
            {period === '30d' && ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6'].map((week, index) => (
              <span key={index}>{week}</span>
            ))}
            {period === '6m' && ['Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan'].map((month, index) => (
              <span key={index}>{month}</span>
            ))}
          </div>

          {/* Key Insights */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-default-600">Peak Value</span>
              <span className="font-medium">{currentChart.format(maxValue)}</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-default-600">Growth Rate</span>
              <span className={`font-medium ${growth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {growth >= 0 ? '+' : ''}{growth}%
              </span>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default GrowthTrends;
