import React, { createContext, useContext, useState, useCallback } from 'react';

/**
 * Data Synchronization Context
 * 
 * Provides real-time data synchronization between different parts of the application,
 * particularly between Track and Earn journeys. When contributions are added/updated,
 * all relevant components are notified to refresh their data.
 */

const DataSyncContext = createContext();

export const useDataSync = () => {
  const context = useContext(DataSyncContext);
  if (!context) {
    throw new Error('useDataSync must be used within a DataSyncProvider');
  }
  return context;
};

export const DataSyncProvider = ({ children }) => {
  const [syncTriggers, setSyncTriggers] = useState({
    contributions: 0,
    earnings: 0,
    projects: 0,
    payments: 0
  });

  // Trigger refresh for specific data types
  const triggerSync = useCallback((dataType) => {
    setSyncTriggers(prev => ({
      ...prev,
      [dataType]: prev[dataType] + 1
    }));
  }, []);

  // Trigger multiple data types at once
  const triggerMultipleSync = useCallback((dataTypes) => {
    setSyncTriggers(prev => {
      const newTriggers = { ...prev };
      dataTypes.forEach(type => {
        newTriggers[type] = prev[type] + 1;
      });
      return newTriggers;
    });
  }, []);

  // Specific trigger functions for common operations
  const triggerContributionSync = useCallback(() => {
    // When contributions change, earnings should also update
    triggerMultipleSync(['contributions', 'earnings']);
  }, [triggerMultipleSync]);

  const triggerEarningsSync = useCallback(() => {
    triggerSync('earnings');
  }, [triggerSync]);

  const triggerProjectSync = useCallback(() => {
    // When projects change, contributions and earnings might also need updates
    triggerMultipleSync(['projects', 'contributions', 'earnings']);
  }, [triggerMultipleSync]);

  const triggerPaymentSync = useCallback(() => {
    // When payments change, earnings should also update
    triggerMultipleSync(['payments', 'earnings']);
  }, [triggerMultipleSync]);

  const value = {
    syncTriggers,
    triggerSync,
    triggerMultipleSync,
    triggerContributionSync,
    triggerEarningsSync,
    triggerProjectSync,
    triggerPaymentSync
  };

  return (
    <DataSyncContext.Provider value={value}>
      {children}
    </DataSyncContext.Provider>
  );
};

export default DataSyncContext;
