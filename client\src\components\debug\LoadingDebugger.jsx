import React, { useState, useEffect } from 'react';
import { 
  getActiveLoadingOperations, 
  getLoadingHistory, 
  clearLoadingHistory, 
  resetLoadingStates 
} from '../../utils/loading-monitor';

/**
 * LoadingDebugger Component
 * 
 * A debugging tool that displays all active loading operations and loading history.
 * This can help identify unexpected loading triggers and debug loading issues.
 */
const LoadingDebugger = () => {
  const [activeOperations, setActiveOperations] = useState([]);
  const [loadingHistory, setLoadingHistory] = useState([]);
  const [expanded, setExpanded] = useState(false);
  const [showHistory, setShowHistory] = useState(false);

  // Update active operations and history every second
  useEffect(() => {
    const intervalId = setInterval(() => {
      setActiveOperations(getActiveLoadingOperations());
      setLoadingHistory(getLoadingHistory());
    }, 1000);

    return () => clearInterval(intervalId);
  }, []);

  // Handle clear history button
  const handleClearHistory = () => {
    clearLoadingHistory();
    setLoadingHistory([]);
  };

  // Handle reset loading states button
  const handleResetLoadingStates = () => {
    if (window.confirm('Are you sure you want to reset all loading states? This should only be used as a last resort.')) {
      resetLoadingStates();
      setActiveOperations([]);
    }
  };

  // Format timestamp
  const formatTime = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false });
  };

  // If there are no active operations and the debugger is not expanded, show a minimal view
  if (activeOperations.length === 0 && !expanded) {
    return (
      <div 
        className="loading-debugger-minimal"
        style={{
          position: 'fixed',
          bottom: '10px',
          right: '10px',
          backgroundColor: '#f0f0f0',
          border: '1px solid #ccc',
          borderRadius: '4px',
          padding: '5px 10px',
          fontSize: '12px',
          cursor: 'pointer',
          zIndex: 9999
        }}
        onClick={() => setExpanded(true)}
      >
        🔍 Loading: None
      </div>
    );
  }

  return (
    <div 
      className="loading-debugger"
      style={{
        position: 'fixed',
        bottom: '10px',
        right: '10px',
        width: expanded ? '400px' : '200px',
        maxHeight: expanded ? '80vh' : '30px',
        backgroundColor: '#f0f0f0',
        border: '1px solid #ccc',
        borderRadius: '4px',
        padding: '10px',
        overflowY: expanded ? 'auto' : 'hidden',
        fontSize: '12px',
        zIndex: 9999,
        transition: 'all 0.3s ease'
      }}
    >
      <div 
        className="loading-debugger-header"
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: expanded ? '10px' : '0',
          cursor: 'pointer'
        }}
        onClick={() => setExpanded(!expanded)}
      >
        <h3 style={{ margin: 0, fontSize: '14px' }}>
          🔍 Loading Monitor {activeOperations.length > 0 ? `(${activeOperations.length} active)` : ''}
        </h3>
        <span>{expanded ? '▼' : '▲'}</span>
      </div>

      {expanded && (
        <>
          <div className="loading-debugger-controls" style={{ marginBottom: '10px' }}>
            <button 
              onClick={() => setShowHistory(!showHistory)}
              style={{
                marginRight: '10px',
                padding: '3px 8px',
                fontSize: '12px'
              }}
            >
              {showHistory ? 'Show Active' : 'Show History'}
            </button>
            {showHistory && (
              <button 
                onClick={handleClearHistory}
                style={{
                  marginRight: '10px',
                  padding: '3px 8px',
                  fontSize: '12px'
                }}
              >
                Clear History
              </button>
            )}
            <button 
              onClick={handleResetLoadingStates}
              style={{
                padding: '3px 8px',
                fontSize: '12px',
                backgroundColor: '#ff6b6b',
                color: 'white',
                border: 'none',
                borderRadius: '3px'
              }}
            >
              Reset All
            </button>
          </div>

          {!showHistory && (
            <div className="active-operations">
              <h4 style={{ margin: '0 0 5px 0', fontSize: '13px' }}>Active Loading Operations</h4>
              {activeOperations.length === 0 ? (
                <p style={{ margin: '0 0 10px 0', fontStyle: 'italic' }}>No active loading operations</p>
              ) : (
                <ul style={{ margin: 0, padding: '0 0 0 20px' }}>
                  {activeOperations.map((op) => (
                    <li key={op.id} style={{ marginBottom: '5px' }}>
                      <div><strong>{op.componentName}</strong> - {op.operationName}</div>
                      {op.reason && <div>Reason: {op.reason}</div>}
                      <div>Started: {formatTime(op.startTime)} ({Math.round(op.duration / 1000)}s ago)</div>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          )}

          {showHistory && (
            <div className="loading-history">
              <h4 style={{ margin: '0 0 5px 0', fontSize: '13px' }}>Loading History</h4>
              {loadingHistory.length === 0 ? (
                <p style={{ margin: '0 0 10px 0', fontStyle: 'italic' }}>No loading history</p>
              ) : (
                <ul style={{ margin: 0, padding: '0 0 0 20px' }}>
                  {loadingHistory.map((entry, index) => (
                    <li key={index} style={{ marginBottom: '5px' }}>
                      <div>
                        <span style={{ 
                          color: entry.type === 'start' ? 'green' : entry.type === 'end' ? 'blue' : 'red',
                          fontWeight: 'bold'
                        }}>
                          {entry.type.toUpperCase()}
                        </span>
                        {' '}
                        <span>{formatTime(entry.timestamp)}</span>
                        {' '}
                        <span>{entry.componentName} - {entry.operationName}</span>
                      </div>
                      {entry.duration && <div>Duration: {entry.duration}ms</div>}
                      {entry.result && <div>Result: {entry.result}</div>}
                    </li>
                  ))}
                </ul>
              )}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default LoadingDebugger;
