// Check for existing alliance/venture specific columns
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_KEY);

async function checkAllianceColumns() {
  console.log('🔍 Checking for Alliance/Venture specific columns...');
  
  // Check if teams table has alliance-specific columns
  try {
    const { data, error } = await supabase
      .from('teams')
      .select('*')
      .limit(1);
    
    if (!error && data.length > 0) {
      console.log('\n📋 Teams table columns:', Object.keys(data[0]));
      console.log('Sample team data:', data[0]);
    } else if (!error) {
      console.log('\n📋 Teams table exists but is empty');
      // Try to get table structure by inserting and rolling back
      console.log('Attempting to get table structure...');
    } else {
      console.log('❌ Teams table error:', error.message);
    }
  } catch (err) {
    console.log('❌ Teams table check failed:', err.message);
  }
  
  // Check companies table
  try {
    const { data, error } = await supabase
      .from('companies')
      .select('*')
      .limit(1);
    
    if (!error) {
      console.log('\n✅ Companies table exists');
      if (data.length > 0) {
        console.log('Companies columns:', Object.keys(data[0]));
        console.log('Sample company:', data[0]);
      } else {
        console.log('Companies table is empty');
      }
    }
  } catch (err) {
    console.log('\n❌ Companies table not found:', err.message);
  }

  // Check team_members table structure
  try {
    const { data, error } = await supabase
      .from('team_members')
      .select('*')
      .limit(1);
    
    if (!error && data.length > 0) {
      console.log('\n📋 Team_members table columns:', Object.keys(data[0]));
    } else if (!error) {
      console.log('\n📋 Team_members table exists but is empty');
    }
  } catch (err) {
    console.log('\n❌ Team_members table check failed:', err.message);
  }

  // Check for revenue_models or similar tables
  try {
    const { data, error } = await supabase
      .from('revenue_models')
      .select('*')
      .limit(1);
    
    if (!error) {
      console.log('\n✅ Revenue_models table exists');
      if (data.length > 0) {
        console.log('Revenue_models columns:', Object.keys(data[0]));
      }
    }
  } catch (err) {
    console.log('\n❌ Revenue_models table not found');
  }
}

checkAllianceColumns();
