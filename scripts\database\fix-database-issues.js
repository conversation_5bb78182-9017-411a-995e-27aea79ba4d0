// Fix database issues and create compliance tables
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  'https://hqqlrrqvjcetoxbdjgzx.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzgyMDU1OSwiZXhwIjoyMDU5Mzk2NTU5fQ.k5xvFBzBxNMCtPd0d8Ko9dAcT5Y3op3ZgFvlAf85LLs'
);

async function fixDatabaseIssues() {
  console.log('🔧 Fixing database issues...\n');
  
  try {
    // Step 1: Create companies table using direct SQL
    console.log('1. Creating companies table...');
    
    const createCompaniesSQL = `
      CREATE TABLE IF NOT EXISTS public.companies (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        legal_name TEXT NOT NULL,
        tax_id TEXT NOT NULL UNIQUE,
        company_type TEXT NOT NULL CHECK (company_type IN ('corporation', 'llc', 'partnership', 'sole_proprietorship')),
        incorporation_state TEXT,
        incorporation_country TEXT DEFAULT 'US',
        doing_business_as TEXT,
        business_description TEXT,
        website_url TEXT,
        primary_address JSONB NOT NULL,
        primary_email TEXT NOT NULL,
        primary_phone TEXT,
        is_active BOOLEAN DEFAULT true,
        created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
      );
      
      ALTER TABLE public.companies ENABLE ROW LEVEL SECURITY;
      
      CREATE POLICY "companies_policy" ON public.companies
        FOR ALL USING (true);
    `;
    
    const { data: companiesResult, error: companiesError } = await supabase
      .from('_sql')
      .select('*')
      .eq('query', createCompaniesSQL);
      
    if (companiesError) {
      console.log(`❌ Companies table creation failed: ${companiesError.message}`);
    } else {
      console.log('✅ Companies table created');
    }
    
    // Step 2: Add company fields to teams table
    console.log('\n2. Adding company fields to teams table...');
    
    const alterTeamsSQL = `
      ALTER TABLE public.teams 
      ADD COLUMN IF NOT EXISTS company_id UUID REFERENCES public.companies(id),
      ADD COLUMN IF NOT EXISTS is_business_entity BOOLEAN DEFAULT false,
      ADD COLUMN IF NOT EXISTS alliance_type TEXT DEFAULT 'emerging';
    `;
    
    // Use a different approach - try to add columns one by one
    try {
      await supabase.from('teams').select('company_id').limit(1);
      console.log('✅ company_id column already exists');
    } catch (error) {
      if (error.message.includes('column "company_id" does not exist')) {
        console.log('Adding company_id column...');
        // We'll need to use the Supabase dashboard for this
      }
    }
    
    // Step 3: Create financial_transactions table
    console.log('\n3. Creating financial_transactions table...');
    
    const createFinancialSQL = `
      CREATE TABLE IF NOT EXISTS public.financial_transactions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        company_id UUID NOT NULL,
        transaction_type TEXT NOT NULL,
        gross_amount DECIMAL(12,2) NOT NULL CHECK (gross_amount >= 0),
        net_amount DECIMAL(12,2) NOT NULL CHECK (net_amount >= 0),
        currency TEXT DEFAULT 'USD',
        payee_user_id UUID REFERENCES auth.users(id),
        status TEXT DEFAULT 'pending',
        description TEXT NOT NULL,
        created_by UUID NOT NULL REFERENCES auth.users(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
      );
      
      ALTER TABLE public.financial_transactions ENABLE ROW LEVEL SECURITY;
      
      CREATE POLICY "financial_transactions_policy" ON public.financial_transactions
        FOR ALL USING (true);
    `;
    
    // Step 4: Insert VRC sample data
    console.log('\n4. Inserting VRC sample data...');
    
    const vrcData = {
      legal_name: 'VRC Entertainment LLC',
      tax_id: '12-3456789',
      company_type: 'llc',
      incorporation_state: 'CA',
      incorporation_country: 'US',
      doing_business_as: 'VRC Films',
      business_description: 'Independent film production and talent management company',
      website_url: 'https://vrcfilms.com',
      primary_address: {
        street: '123 Hollywood Blvd',
        city: 'Los Angeles',
        state: 'CA',
        zip: '90028',
        country: 'US'
      },
      primary_email: '<EMAIL>',
      primary_phone: '******-123-4567',
      is_active: true
    };
    
    const { data: insertResult, error: insertError } = await supabase
      .from('companies')
      .upsert(vrcData, { onConflict: 'tax_id' })
      .select();
      
    if (insertError) {
      console.log(`❌ VRC data insertion failed: ${insertError.message}`);
    } else {
      console.log('✅ VRC sample data inserted');
      console.log(`   Company ID: ${insertResult[0]?.id}`);
    }
    
    // Step 5: Test the companies table
    console.log('\n5. Testing companies table...');
    
    const { data: testData, error: testError } = await supabase
      .from('companies')
      .select('*')
      .limit(5);
      
    if (testError) {
      console.log(`❌ Companies table test failed: ${testError.message}`);
    } else {
      console.log(`✅ Companies table working (${testData?.length || 0} records)`);
      if (testData && testData.length > 0) {
        console.log(`   Sample: ${testData[0].legal_name}`);
      }
    }
    
    console.log('\n🎉 Database fixes completed!');
    console.log('\n📝 Manual steps needed:');
    console.log('1. Add company_id, is_business_entity, alliance_type columns to teams table via Supabase dashboard');
    console.log('2. Create financial_transactions, commission_payments, recurring_fees tables via dashboard');
    console.log('3. Fix RLS policies for team_members table');
    console.log('4. Confirm user emails for testing');
    
  } catch (error) {
    console.log(`❌ Database fix failed: ${error.message}`);
  }
}

fixDatabaseIssues();
