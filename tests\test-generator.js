// Simple test script for the agreement generator
const fs = require('fs');
const path = require('path');

// Import the NewAgreementGenerator class
const { NewAgreementGenerator } = require('./client/src/utils/agreement/newAgreementGenerator');

// Create an instance of the generator
const generator = new NewAgreementGenerator();

// Load the agreement template
const templatePath = path.join(__dirname, 'client/public/example-cog-contributor-agreement.md');
const templateText = fs.readFileSync(templatePath, 'utf8');

// Create a test project
const testProject = {
  name: 'Test Game Project',
  description: 'This is a test game project with medium duration',
  projectType: 'game',
  projectLength: 'medium',
  estimatedDuration: 6,
  company_name: 'Test Company',
  contributors: [
    {
      permission_level: 'Owner',
      display_name: 'Test Owner',
      users: {
        display_name: 'Test Owner',
        email: '<EMAIL>',
        address: '123 Test St, Test City, Test State 12345',
        state: 'Test State',
        county: 'Test County',
        title: 'CEO'
      }
    }
  ],
  milestones: [
    {
      title: 'First Milestone',
      description: 'Complete initial setup',
      deadline: '2024-12-31'
    },
    {
      title: 'Second Milestone',
      description: 'Implement core features',
      deadline: '2025-03-31'
    }
  ]
};

// Create test options
const testOptions = {
  contributors: testProject.contributors,
  currentUser: {
    id: 'test-user-id',
    email: '<EMAIL>',
    user_metadata: {
      full_name: 'Test Contributor'
    }
  },
  royaltyModel: {
    model_type: 'custom',
    contributor_percentage: 33,
    min_payout: 1000,
    max_payout: 100000,
    configuration: {
      tasks_weight: 30,
      hours_weight: 30,
      difficulty_weight: 40
    }
  },
  milestones: testProject.milestones,
  fullName: 'Test Contributor'
};

// Generate the agreement
try {
  const agreement = generator.generateAgreement(templateText, testProject, testOptions);
  
  // Save the generated agreement to a file
  const outputPath = path.join(__dirname, 'test-output-simple.md');
  fs.writeFileSync(outputPath, agreement);
  
  console.log(`Generated agreement saved to ${outputPath}`);
  
  // Check for key phrases
  const phrasesToCheck = [
    'Test Game Project',
    'Test Company',
    'Test Contributor',
    'EXHIBIT I',
    'EXHIBIT II',
    'Core Gameplay Mechanics',
    'First Milestone',
    'Second Milestone'
  ];
  
  phrasesToCheck.forEach(phrase => {
    if (agreement.includes(phrase)) {
      console.log(`✓ Found phrase: "${phrase}"`);
    } else {
      console.log(`✗ Missing phrase: "${phrase}"`);
    }
  });
  
  // Check for phrases that should be replaced
  const phrasesToReplace = [
    'City of Gamers Inc.',
    'Village of The Ages',
    'a village simulation game where players guide communities through historical progressions',
    'Gynell Journigan'
  ];
  
  phrasesToReplace.forEach(phrase => {
    if (agreement.includes(phrase)) {
      console.log(`✗ Found unreplaced phrase: "${phrase}"`);
    } else {
      console.log(`✓ Phrase properly replaced: "${phrase}"`);
    }
  });
  
} catch (error) {
  console.error('Error generating agreement:', error);
}
