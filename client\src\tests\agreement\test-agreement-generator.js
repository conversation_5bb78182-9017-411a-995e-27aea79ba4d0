/**
 * Test utility for agreement generation
 * 
 * This utility generates test agreements using the agreement generator
 * and saves them to files for inspection.
 */

import fs from 'fs';
import path from 'path';
import { NewAgreementGenerator } from '../../utils/agreement/newAgreementGenerator';
import { gameProjectTest, musicProjectTest, softwareProjectTest } from './test-cases';

// Create output directory if it doesn't exist
const outputDir = path.join(__dirname, 'output');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

/**
 * Load the agreement template
 * @returns {Promise<string>} The agreement template text
 */
async function loadAgreementTemplate() {
  try {
    const templatePath = path.join(__dirname, '../../../public/example-cog-contributor-agreement.md');
    return fs.readFileSync(templatePath, 'utf8');
  } catch (error) {
    console.error('Error loading agreement template:', error);
    throw error;
  }
}

/**
 * Generate and save a test agreement
 * @param {Object} project - The project data
 * @param {string} filename - The output filename
 * @param {Object} options - Additional options for the agreement generator
 * @returns {Promise<void>}
 */
async function generateAndSaveAgreement(project, filename, options = {}) {
  try {
    // Load the agreement template
    const templateText = await loadAgreementTemplate();
    
    // Create a new agreement generator
    const generator = new NewAgreementGenerator();
    
    // Generate the agreement
    const agreement = generator.generateAgreement(templateText, project, options);
    
    // Save the agreement to a file
    const outputPath = path.join(outputDir, filename);
    fs.writeFileSync(outputPath, agreement, 'utf8');
    
    console.log(`Agreement saved to ${outputPath}`);
  } catch (error) {
    console.error(`Error generating agreement for ${filename}:`, error);
  }
}

/**
 * Run all test cases
 * @returns {Promise<void>}
 */
async function runTests() {
  console.log('Running agreement generation tests...');
  
  // Test Case 1: Game Project with Custom Royalty Model
  await generateAndSaveAgreement(
    gameProjectTest,
    'game-project-agreement.md',
    {
      contributors: gameProjectTest.contributors,
      currentUser: {
        email: '<EMAIL>',
        user_metadata: { full_name: 'John Doe' }
      },
      royaltyModel: gameProjectTest.royalty_model,
      milestones: gameProjectTest.milestones,
      fullName: 'John Doe'
    }
  );
  
  // Test Case 2: Music Project with Equal Split Royalty Model
  await generateAndSaveAgreement(
    musicProjectTest,
    'music-project-agreement.md',
    {
      contributors: musicProjectTest.contributors,
      currentUser: {
        email: '<EMAIL>',
        user_metadata: { full_name: 'Sarah Johnson' }
      },
      royaltyModel: musicProjectTest.royalty_model,
      milestones: musicProjectTest.milestones,
      fullName: 'Sarah Johnson'
    }
  );
  
  // Test Case 3: Software Project with Task-Based Royalty Model
  await generateAndSaveAgreement(
    softwareProjectTest,
    'software-project-agreement.md',
    {
      contributors: softwareProjectTest.contributors,
      currentUser: {
        email: '<EMAIL>',
        user_metadata: { full_name: 'Michael Chen' }
      },
      royaltyModel: softwareProjectTest.royalty_model,
      milestones: softwareProjectTest.milestones,
      fullName: 'Michael Chen'
    }
  );
  
  console.log('All tests completed.');
}

// Run the tests
runTests().catch(error => {
  console.error('Error running tests:', error);
});

export { generateAndSaveAgreement, loadAgreementTemplate };
