import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "../../../utils/supabase/supabase.utils";
import LoadingAnimation from "../../components/layout/LoadingAnimation";
import { toast } from "react-hot-toast";

const AuthCallback = () => {
  const navigate = useNavigate();
  const [status, setStatus] = useState("Processing authentication...");

  useEffect(() => {
    // Log the current URL for debugging
    console.log('Auth callback URL:', window.location.href);
    console.log('URL hash:', window.location.hash);
    console.log('URL search params:', window.location.search);

    // Handle the OAuth callback
    const handleAuthCallback = async () => {
      try {
        setStatus("Checking session...");

        // Check if there's an error in the URL
        const urlParams = new URLSearchParams(window.location.search);
        const error = urlParams.get('error');
        const errorDescription = urlParams.get('error_description');

        if (error) {
          console.error('Error in URL:', error, errorDescription);
          setStatus(`Authentication error: ${errorDescription || error}`);

          // Check for specific errors
          if (errorDescription && (
            errorDescription.includes("column \"updated_at\" of relation \"users\" does not exist") ||
            errorDescription.includes("column \"created_at\" of relation \"users\" does not exist")
          )) {
            setStatus("Database schema error: Timestamp columns are missing from the users table.\n\nPlease run the comprehensive migration script to add both 'created_at' and 'updated_at' columns to the users table.");
            toast.error("Database schema error. Please contact the administrator.");
            setTimeout(() => navigate("/login"), 5000);
            return;
          }

          toast.error("Authentication failed: " + (errorDescription || error));
          setTimeout(() => navigate("/login"), 3000);
          return;
        }

        // Check if we have a code in the URL (for PKCE flow)
        const code = urlParams.get('code');

        if (code) {
          console.log('Found authorization code in URL');
          setStatus("Found authorization code. Processing...");
          // The code will be automatically processed by Supabase
          // when we call getSession()
        } else {
          console.log('No authorization code found in URL');
        }

        // Check if we have a token in the URL hash
        let accessToken = null;
        let refreshToken = null;

        if (window.location.hash) {
          const hashParams = new URLSearchParams(window.location.hash.substring(1));
          accessToken = hashParams.get("access_token");
          refreshToken = hashParams.get("refresh_token");

          console.log('Hash params:', hashParams.toString());
          console.log('Access token exists:', !!accessToken);
          console.log('Refresh token exists:', !!refreshToken);

          if (accessToken && refreshToken) {
            setStatus("Found tokens in URL hash. Setting session...");
            // Set the session manually
            const { error } = await supabase.auth.setSession({
              access_token: accessToken,
              refresh_token: refreshToken,
            });

            if (error) {
              console.error("Error setting session from hash:", error);
              // Continue and try to get the session anyway
            } else {
              console.log("Successfully set session from hash");
            }
          }
        }

        // Try to get the current session
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

        console.log('Session data:', sessionData ? 'exists' : 'null');
        if (sessionError) {
          console.error("Error getting session:", sessionError);
          setStatus("Session error. Trying URL parameters...");

          // If getting session fails and we haven't already tried with the hash params
          if (!accessToken && !refreshToken) {
            // Try to extract from hash again (just to be sure)
            const hashParams = new URLSearchParams(window.location.hash.substring(1));
            accessToken = hashParams.get("access_token");
            refreshToken = hashParams.get("refresh_token");

            if (accessToken && refreshToken) {
              setStatus("Setting session from URL parameters...");
              // Set the session manually
              const { error } = await supabase.auth.setSession({
                access_token: accessToken,
                refresh_token: refreshToken,
              });

              if (error) {
                console.error("Error setting session:", error);
                toast.error("Authentication failed. Please try again.");
                navigate("/login");
                return;
              }
            } else {
              console.error("No tokens found in URL");
              toast.error("Authentication failed. No tokens found.");
              navigate("/login");
              return;
            }
          } else {
            toast.error("Authentication failed. Could not establish session.");
            navigate("/login");
            return;
          }
        }

        // Get the user from the session
        let user = sessionData?.session?.user || null;

        if (!user) {
          // Try to get the session again after setting it from URL parameters
          const { data: refreshedSession, error: refreshError } = await supabase.auth.getSession();

          if (refreshError || !refreshedSession?.session?.user) {
            console.error("No user found after authentication");
            toast.error("Authentication failed. No user found.");
            navigate("/login");
            return;
          }

          // Use the user from the refreshed session
          user = refreshedSession.session.user;
        }

        console.log("User authenticated:", user.id, user.email);
        setStatus("Authentication successful. Checking user profile...");

        // Check if user profile exists in the database
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('id, display_name, email')
          .eq('id', user.id)
          .single();

        if (userError) {
          console.log("User profile check result:", userError.code, userError.message);

          if (userError.code === 'PGRST116') { // PGRST116 is "not found"
            console.log("User profile not found, will create one");
          } else {
            console.error("Error checking user profile:", userError);
          }
        } else {
          console.log("Found existing user profile:", userData);
        }

        // If user profile doesn't exist, create it
        if (!userData) {
          setStatus("Creating user profile...");

          // Get display name from user metadata or email
          const displayName =
            user.user_metadata?.full_name ||
            user.user_metadata?.name ||
            user.email.split('@')[0];

          console.log("Creating user profile with display name:", displayName);

          try {
            const { data: insertData, error: insertError } = await supabase
              .from('users')
              .insert([
                {
                  id: user.id,
                  email: user.email,
                  display_name: displayName,
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString()
                }
              ])
              .select();

            if (insertError) {
              console.error("Error creating user profile:", insertError);
              // Try a simpler approach with fewer fields
              const { error: simpleInsertError } = await supabase
                .from('users')
                .insert([
                  {
                    id: user.id,
                    email: user.email,
                    display_name: displayName
                  }
                ]);

              if (simpleInsertError) {
                console.error("Error with simple user profile creation:", simpleInsertError);
                toast.warning("Signed in, but couldn't create your profile. Some features may be limited.");
              } else {
                console.log("Created user profile with simple approach");
              }
            } else {
              console.log("Successfully created user profile:", insertData);
            }
          } catch (profileError) {
            console.error("Exception creating user profile:", profileError);
            toast.warning("Signed in, but couldn't create your profile. Some features may be limited.");
          }
        }

        // Redirect to the dashboard with success message
        toast.success("Successfully signed in!");
        navigate("/");
      } catch (error) {
        console.error("Error in auth callback:", error);
        toast.error("Authentication failed. Please try again.");
        navigate("/login");
      }
    };

    handleAuthCallback();
  }, [navigate]);

  // Determine status message class based on content
  const getStatusClass = () => {
    if (status.includes('error') || status.includes('failed')) {
      return 'auth-status-message auth-error';
    } else if (status.includes('warning')) {
      return 'auth-status-message auth-warning';
    } else if (status.includes('success') || status.includes('Successfully')) {
      return 'auth-status-message auth-success';
    }
    return 'auth-status-message';
  };

  return (
    <div className="auth-callback-container">
      <LoadingAnimation />
      <p className={getStatusClass()}>{status}</p>
    </div>
  );
};

export default AuthCallback;
