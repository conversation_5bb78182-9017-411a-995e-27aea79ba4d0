import { useContext, useState, useEffect, useRef } from "react";
import { UserContext } from "../../../contexts/supabase-auth.context";
import { getProjects, subscribeToTable, supabase } from "../../../utils/supabase/supabase.utils";
import usePageVisibility from "../../hooks/usePageVisibility";
import { useLoadingMonitor } from "../../utils/loading";
import { isDevToolsMode, resetDevToolsDetection } from "../../utils/visibility-handler";
import BugReportModal from "../../components/bugs/BugReportModal";
import { Card, CardBody, CardHeader, Button } from "../../components/ui/heroui";

const SimpleDashboard = () => {
  const { currentUser, logout } = useContext(UserContext);
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [userData, setUserData] = useState(null);
  const [userLoading, setUserLoading] = useState(true);
  const [showBugReportModal, setShowBugReportModal] = useState(false);
  const isPageVisible = usePageVisibility();
  const subscriptionRef = useRef(null);

  // Initialize loading monitor
  const loadingMonitor = useLoadingMonitor('SimpleDashboard');

  // Track if a fetch is in progress to prevent duplicate fetches
  const userFetchInProgress = useRef(false);
  const projectFetchInProgress = useRef(false);

  // Process tokens in URL hash if present
  useEffect(() => {
    // Check for tokens in URL hash
    const processUrlTokens = async () => {
      if (window.location.hash) {
        console.log('Found hash in URL, checking for tokens...');
        const hashParams = new URLSearchParams(window.location.hash.substring(1));
        const accessToken = hashParams.get("access_token");
        const refreshToken = hashParams.get("refresh_token");

        if (accessToken && refreshToken) {
          console.log('Found tokens in URL hash, setting session...');
          try {
            // Set the session manually
            const { error } = await supabase.auth.setSession({
              access_token: accessToken,
              refresh_token: refreshToken,
            });

            if (error) {
              console.error("Error setting session from hash:", error);
            } else {
              console.log("Successfully set session from hash");

              // Clear the hash from the URL
              window.history.replaceState(null, null, 'https://royalty.technology');

              // Check if user profile exists and create if needed
              const { data: sessionData } = await supabase.auth.getSession();
              const user = sessionData?.session?.user;

              if (user) {
                // Check if user profile exists
                const { data: userData, error: userError } = await supabase
                  .from('users')
                  .select('id')
                  .eq('id', user.id)
                  .single();

                if (userError && userError.code === 'PGRST116') { // Not found
                  console.log('User profile not found, creating one...');

                  // Create user profile
                  const displayName =
                    user.user_metadata?.full_name ||
                    user.user_metadata?.name ||
                    user.email.split('@')[0];

                  const { error: insertError } = await supabase
                    .from('users')
                    .insert([
                      {
                        id: user.id,
                        email: user.email,
                        display_name: displayName
                      }
                    ]);

                  if (insertError) {
                    console.error('Error creating user profile:', insertError);
                  } else {
                    console.log('Successfully created user profile');
                  }
                }
              }
            }
          } catch (error) {
            console.error('Error processing tokens:', error);
          }
        }
      }
    };

    processUrlTokens();
  }, []);

  // Fetch user data - only depends on currentUser, not on visibility
  useEffect(() => {
    // Skip if no user or if a fetch is already in progress
    if (!currentUser || userFetchInProgress.current) {
      return;
    }

    const fetchUserData = async () => {
      // Set flag to prevent duplicate fetches
      userFetchInProgress.current = true;

      // Start loading operation
      const loadingId = loadingMonitor.startLoading('fetchUserData', 'Fetching user profile data');

      try {
        setUserLoading(true);

        // Get user data from Supabase
        const { data, error } = await supabase
          .from('users')
          .select('display_name, avatar_url, is_premium')
          .eq('id', currentUser.id)
          .single();

        if (error) {
          // Handle database permission/table issues gracefully
          if (error.message?.includes('401') || error.message?.includes('permission') || error.message?.includes('relation') || error.message?.includes('does not exist')) {
            console.warn('Database table access issue (this is normal if database is not set up yet):', error.message);
            // Use fallback user data from auth
            setUserData({
              display_name: currentUser.user_metadata?.full_name || currentUser.user_metadata?.name || currentUser.email?.split('@')[0] || 'User',
              avatar_url: currentUser.user_metadata?.avatar_url || '/default-avatar.png',
              is_premium: false
            });
            loadingMonitor.endLoading(loadingId, 'success');
            return;
          }
          loadingMonitor.endLoading(loadingId, 'error');
          throw error;
        }

        setUserData(data);
        loadingMonitor.endLoading(loadingId, 'success');
      } catch (error) {
        console.error('Error fetching user data:', error);
        loadingMonitor.endLoading(loadingId, `error: ${error.message}`);
      } finally {
        setUserLoading(false);
        // Clear the flag when done
        userFetchInProgress.current = false;
      }
    };

    fetchUserData();
  }, [currentUser]); // Only depend on currentUser, not visibility

  // Define fetchProjects outside of useEffect so it can be reused
  const fetchProjects = async () => {
    // Skip if a fetch is already in progress
    if (projectFetchInProgress.current) {
      console.log('Project fetch already in progress, skipping');
      return;
    }

    // Set flag to prevent duplicate fetches
    projectFetchInProgress.current = true;

    // Start loading operation
    const loadingId = loadingMonitor.startLoading('fetchProjects', 'Fetching project list');

    try {
      const projectsData = await getProjects();
      setProjects(projectsData || []);
      loadingMonitor.endLoading(loadingId, 'success');
    } catch (error) {
      console.error("Error fetching projects:", error);
      loadingMonitor.endLoading(loadingId, `error: ${error.message}`);
    } finally {
      setLoading(false);
      // Clear the flag when done
      projectFetchInProgress.current = false;
    }
  };

  // Fetch projects - only depends on currentUser, not on visibility
  useEffect(() => {
    // Skip if no user
    if (!currentUser) {
      return;
    }

    // Initial fetch
    fetchProjects();

    // Subscribe to realtime updates
    let subscription;
    try {
      subscription = subscribeToTable('projects', (payload) => {
        // Refetch when we get a realtime update
        fetchProjects();
      });

      // Store the subscription for cleanup
      subscriptionRef.current = subscription;
    } catch (error) {
      console.error("Error subscribing to projects table:", error);
    }

    // Clean up subscription when component unmounts
    return () => {
      if (subscriptionRef.current) {
        if (typeof subscriptionRef.current.unsubscribe === 'function') {
          subscriptionRef.current.unsubscribe();
        }
        subscriptionRef.current = null;
      }
    };
  }, [currentUser]); // Only depend on currentUser, not visibility

  // Track the last time we refreshed data
  const lastRefreshTime = useRef(Date.now());
  const MIN_REFRESH_INTERVAL = 5000; // Minimum 5 seconds between refreshes

  // Add a separate effect to handle visibility changes
  useEffect(() => {
    // When the page becomes visible again, refresh data
    if (isPageVisible && currentUser) {
      const now = Date.now();
      console.log('Page became visible, considering refresh');

      // Skip refresh if we're in dev tools mode
      if (isDevToolsMode()) {
        console.log('In dev tools mode, skipping refresh to prevent UI freeze');
        return;
      }

      // Skip refresh if we refreshed recently
      if (now - lastRefreshTime.current < MIN_REFRESH_INTERVAL) {
        console.log(`Skipping refresh, last refresh was ${now - lastRefreshTime.current}ms ago`);
        return;
      }

      console.log('Refreshing data after visibility change');
      lastRefreshTime.current = now;

      // Use setTimeout to avoid multiple simultaneous fetches
      const timeoutId = setTimeout(() => {
        // Check if component is still mounted
        if (!currentUser) {
          console.log('Component unmounted, skipping refresh');
          return;
        }

        // Only fetch if not already fetching
        if (!userFetchInProgress.current) {
          const loadingId = loadingMonitor.startLoading('fetchUserData', 'Refreshing user data after visibility change');
          userFetchInProgress.current = true;

          // Fetch user data
          supabase
            .from('users')
            .select('display_name, avatar_url, is_premium')
            .eq('id', currentUser.id)
            .single()
            .then(({ data, error }) => {
              if (error) {
                // Handle database permission/table issues gracefully
                if (error.message?.includes('401') || error.message?.includes('permission') || error.message?.includes('relation') || error.message?.includes('does not exist')) {
                  console.warn('Database table access issue during refresh (this is normal if database is not set up yet):', error.message);
                  // Use fallback user data from auth
                  setUserData({
                    display_name: currentUser.user_metadata?.full_name || currentUser.user_metadata?.name || currentUser.email?.split('@')[0] || 'User',
                    avatar_url: currentUser.user_metadata?.avatar_url || '/default-avatar.png',
                    is_premium: false
                  });
                  loadingMonitor.endLoading(loadingId, 'success');
                } else {
                  loadingMonitor.endLoading(loadingId, 'error');
                  console.error('Error refreshing user data:', error);
                }
              } else {
                setUserData(data);
                loadingMonitor.endLoading(loadingId, 'success');
              }
              userFetchInProgress.current = false;
            })
            .catch(error => {
              console.error('Unexpected error in user data refresh:', error);
              loadingMonitor.endLoading(loadingId, `error: ${error.message}`);
              userFetchInProgress.current = false;
            });
        }

        // Fetch projects if not already fetching
        if (!projectFetchInProgress.current) {
          fetchProjects();
        }
      }, 1500); // Longer delay to avoid race conditions

      // Clean up timeout if component unmounts
      return () => clearTimeout(timeoutId);
    }
  }, [isPageVisible, currentUser]);

  // Reset dev tools detection when component unmounts
  useEffect(() => {
    return () => {
      resetDevToolsDetection();
    };
  }, []);

  return (
    <div className="tw-min-h-screen tw-bg-white tw-container tw-mx-auto tw-mt-8 tw-px-4">
      <div className="tw-flex tw-justify-center">
        <div className="tw-w-full tw-max-w-2xl">
          <Card className="tw-shadow-lg tw-bg-white tw-border tw-border-gray-200">
            <CardContent className="tw-p-8 tw-bg-white">
              <h2 className="tw-text-center tw-text-2xl tw-font-bold tw-mb-6 tw-text-gray-900">Welcome to Royaltea</h2>

              <div className="tw-text-center tw-mb-6">
                {userLoading ? (
                  <div className="tw-flex tw-justify-center tw-items-center">
                    <div className="tw-animate-spin tw-rounded-full tw-h-4 tw-w-4 tw-border-b-2 tw-border-blue-600 tw-mr-2"></div>
                    <span>Loading user data...</span>
                  </div>
                ) : (
                  <>
                    <h4 className="tw-text-xl tw-font-semibold tw-mb-3 tw-text-gray-900">
                      Hello, {userData?.display_name || currentUser?.user_metadata?.full_name || currentUser?.email}
                    </h4>
                    <div className="tw-flex tw-justify-center tw-mb-4">
                      <img
                        src={userData?.avatar_url || (userData?.is_premium ? '/default-avatar-crown.png' : '/default-avatar-specs.png')}
                        alt="Profile"
                        className="tw-rounded-full tw-w-20 tw-h-20 tw-object-cover"
                      />
                    </div>
                  </>
                )}
              </div>

              <div className="tw-mt-6 tw-mb-6">
                <div className="tw-flex tw-justify-between tw-items-center tw-mb-4">
                  <h5 className="tw-text-lg tw-font-semibold tw-text-gray-900">
                    Projects {loading ? "(Loading...)" : `(${projects.length})`}
                  </h5>
                </div>
                {!loading && projects.length > 0 ? (
                  <div className="tw-space-y-3">
                    {projects.map(project => (
                      <div key={project.id} className="tw-flex tw-items-center tw-p-4 tw-border tw-border-gray-200 tw-rounded-lg tw-bg-white tw-hover:bg-gray-50 tw-transition-colors">
                        {project.thumbnail_url ? (
                          <div className="tw-mr-4 tw-w-12 tw-h-12 tw-flex-shrink-0">
                            <img
                              src={project.thumbnail_url}
                              alt={project.title || 'Project'}
                              className="tw-w-full tw-h-full tw-object-cover tw-rounded"
                            />
                          </div>
                        ) : (
                          <div className="tw-mr-4 tw-w-12 tw-h-12 tw-flex tw-items-center tw-justify-center tw-bg-slate-100 tw-rounded tw-flex-shrink-0">
                            <i className="bi bi-briefcase tw-text-xl tw-opacity-60"></i>
                          </div>
                        )}
                        <div className="tw-flex-grow">
                          <a href={`/project/${project.id}`} className="tw-text-blue-600 tw-hover:text-blue-800 tw-no-underline tw-font-medium">
                            {project.title || project.name || 'Untitled Project'}
                          </a>
                          {project.description && (
                            <div className="tw-text-slate-500 tw-text-sm tw-mt-1">
                              {project.description.substring(0, 60)}{project.description.length > 60 ? '...' : ''}
                            </div>
                          )}
                        </div>
                        <div>
                          <span className="tw-inline-flex tw-items-center tw-px-2.5 tw-py-0.5 tw-rounded-full tw-text-xs tw-font-medium tw-bg-green-100 tw-text-green-800">
                            Active
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : !loading ? (
                  <p className="tw-text-gray-500">No projects found. Projects will appear here in real-time as they are created.</p>
                ) : null}
              </div>

              <div className="tw-flex tw-justify-center tw-mt-6">
                <Button
                  variant="destructive"
                  onClick={logout}
                >
                  Log Out
                </Button>
              </div>

              <div className="tw-mt-8">
                <div className="tw-flex tw-justify-center">
                  <Button
                    variant="outline"
                    onClick={() => setShowBugReportModal(true)}
                    className="tw-flex tw-items-center"
                  >
                    <i className="bi bi-bug tw-mr-2"></i>
                    Report a Bug
                  </Button>
                </div>
              </div>

              {/* Bug Report Modal */}
              <BugReportModal
                isOpen={showBugReportModal}
                onClose={() => setShowBugReportModal(false)}
                onSuccess={() => setShowBugReportModal(false)}
              />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default SimpleDashboard;
