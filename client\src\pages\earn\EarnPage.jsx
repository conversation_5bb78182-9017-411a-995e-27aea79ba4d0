import React, { useContext } from 'react';
import { UserContext } from '../../../contexts/supabase-auth.context.jsx';
import EarnCanvas from '../../components/canvas/EarnCanvas';

/**
 * Earn Page Component
 *
 * Main page for the Earn journey in the experimental navigation system.
 * Displays earnings, royalty calculations, and payment information.
 */

const EarnPage = () => {
  const { currentUser } = useContext(UserContext);

  return (
    <div className="min-h-screen">
      <EarnCanvas currentUser={currentUser} />
    </div>
  );
};

export default EarnPage;
