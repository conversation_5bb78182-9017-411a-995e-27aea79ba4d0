{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "nodemon index.js", "start": "node index.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@supabase/supabase-js": "^2.50.0", "bcrypt": "^5.1.1", "body-parser": "^1.20.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "method-override": "^3.0.0", "mongoose": "^8.7.0", "node-fetch": "^2.7.0", "nodemailer": "^6.10.1", "nodemon": "^3.1.7", "passport": "^0.7.0", "passport-local": "^1.0.0", "passport-local-mongoose": "^8.0.0", "react-minimal-pie-chart": "^9.1.0", "react-signature-canvas": "^1.1.0-alpha.2", "shadcn-ui": "^0.9.5", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7"}, "packageManager": "pnpm@9.4.0+sha512.f549b8a52c9d2b8536762f99c0722205efc5af913e77835dbccc3b0b0b2ca9e7dc8022b78062c17291c48e88749c70ce88eb5a74f1fa8c4bf5e18bb46c8bd83a", "devDependencies": {"@playwright/test": "^1.52.0", "@types/node": "^22.15.21", "supabase": "^2.22.6"}}