import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { gameProjectTest, musicProjectTest, softwareProjectTest } from '../../../tests/agreement/test-cases';
import { NewAgreementGenerator } from '../../../utils/agreement/newAgreementGenerator';
import { markdownToHTML } from '../../../utils/pdf/pdfGenerator';

/**
 * Agreement Tester Component
 * 
 * This component allows testing the agreement generation functionality
 * with different project configurations directly in the browser.
 */
const AgreementTester = () => {
  const [templateText, setTemplateText] = useState('');
  const [selectedProject, setSelectedProject] = useState('game');
  const [generatedAgreement, setGeneratedAgreement] = useState('');
  const [loading, setLoading] = useState(false);
  const [viewMode, setViewMode] = useState('markdown'); // 'markdown' or 'html'
  const [customProject, setCustomProject] = useState(null);
  const [showCustomEditor, setShowCustomEditor] = useState(false);
  const [customProjectJson, setCustomProjectJson] = useState('');

  // Load the agreement template
  useEffect(() => {
    const loadTemplate = async () => {
      try {
        const response = await fetch('/example-cog-contributor-agreement.md');
        if (!response.ok) {
          throw new Error('Failed to load agreement template');
        }
        const template = await response.text();
        setTemplateText(template);
      } catch (error) {
        console.error('Error loading agreement template:', error);
        toast.error('Failed to load agreement template');
      }
    };

    loadTemplate();
  }, []);

  // Get the selected project data
  const getSelectedProjectData = () => {
    if (customProject) return customProject;
    
    switch (selectedProject) {
      case 'game':
        return gameProjectTest;
      case 'music':
        return musicProjectTest;
      case 'software':
        return softwareProjectTest;
      default:
        return gameProjectTest;
    }
  };

  // Generate the agreement
  const generateAgreement = async () => {
    if (!templateText) {
      toast.error('Agreement template not loaded');
      return;
    }

    setLoading(true);
    try {
      const projectData = getSelectedProjectData();
      
      // Create options for the agreement generator
      const options = {
        contributors: projectData.contributors || [],
        currentUser: {
          email: '<EMAIL>',
          user_metadata: { full_name: 'Test User' }
        },
        royaltyModel: projectData.royalty_model,
        milestones: projectData.milestones || [],
        fullName: 'Test User'
      };

      // Create a new agreement generator
      const generator = new NewAgreementGenerator();
      
      // Generate the agreement
      const agreement = generator.generateAgreement(templateText, projectData, options);
      
      setGeneratedAgreement(agreement);
      toast.success('Agreement generated successfully');
    } catch (error) {
      console.error('Error generating agreement:', error);
      toast.error('Failed to generate agreement');
    } finally {
      setLoading(false);
    }
  };

  // Handle custom project JSON changes
  const handleCustomProjectChange = (e) => {
    setCustomProjectJson(e.target.value);
    try {
      const projectData = JSON.parse(e.target.value);
      setCustomProject(projectData);
    } catch (error) {
      // Invalid JSON, don't update customProject
    }
  };

  // Initialize custom project editor with selected project
  const initializeCustomEditor = () => {
    const projectData = getSelectedProjectData();
    setCustomProject(projectData);
    setCustomProjectJson(JSON.stringify(projectData, null, 2));
    setShowCustomEditor(true);
  };

  // Download the generated agreement
  const downloadAgreement = () => {
    if (!generatedAgreement) {
      toast.error('No agreement generated');
      return;
    }

    const blob = new Blob([generatedAgreement], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${getSelectedProjectData().name}-agreement.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="agreement-tester">
      <div className="tester-header">
        <h2>Agreement Generator Tester</h2>
        <p>Test the agreement generation with different project configurations</p>
      </div>

      <div className="tester-controls">
        <div className="control-group">
          <label htmlFor="projectSelect">Select Project Type:</label>
          <select 
            id="projectSelect" 
            value={selectedProject} 
            onChange={(e) => {
              setSelectedProject(e.target.value);
              setCustomProject(null);
              setShowCustomEditor(false);
            }}
            disabled={loading}
          >
            <option value="game">Game Project</option>
            <option value="music">Music Project</option>
            <option value="software">Software Project</option>
            {customProject && <option value="custom">Custom Project</option>}
          </select>
        </div>

        <div className="control-group">
          <button 
            onClick={generateAgreement} 
            disabled={loading || !templateText}
            className="generate-btn"
          >
            {loading ? 'Generating...' : 'Generate Agreement'}
          </button>
          
          <button 
            onClick={initializeCustomEditor}
            disabled={loading}
            className="custom-btn"
          >
            Customize Project
          </button>
          
          {generatedAgreement && (
            <button 
              onClick={downloadAgreement} 
              className="download-btn"
            >
              Download Agreement
            </button>
          )}
        </div>

        {generatedAgreement && (
          <div className="view-controls">
            <button 
              className={viewMode === 'markdown' ? 'active' : ''}
              onClick={() => setViewMode('markdown')}
            >
              Markdown
            </button>
            <button 
              className={viewMode === 'html' ? 'active' : ''}
              onClick={() => setViewMode('html')}
            >
              HTML Preview
            </button>
          </div>
        )}
      </div>

      {showCustomEditor && (
        <div className="custom-editor">
          <h3>Custom Project Configuration</h3>
          <textarea
            value={customProjectJson}
            onChange={handleCustomProjectChange}
            rows={10}
            placeholder="Enter JSON project configuration..."
          />
          <div className="editor-actions">
            <button 
              onClick={() => setShowCustomEditor(false)}
              className="cancel-btn"
            >
              Cancel
            </button>
            <button 
              onClick={() => {
                setSelectedProject('custom');
                setShowCustomEditor(false);
                toast.success('Custom project configuration applied');
              }}
              disabled={!customProject}
              className="apply-btn"
            >
              Apply Configuration
            </button>
          </div>
        </div>
      )}

      {generatedAgreement && (
        <div className="agreement-preview">
          <h3>Generated Agreement</h3>
          
          {viewMode === 'markdown' ? (
            <pre className="markdown-preview">{generatedAgreement}</pre>
          ) : (
            <div 
              className="html-preview"
              dangerouslySetInnerHTML={{ __html: markdownToHTML(generatedAgreement) }}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default AgreementTester;
