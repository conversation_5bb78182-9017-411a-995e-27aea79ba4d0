/**
 * Updated Royaltea MVP Development Roadmap
 * This file contains a more granular and thorough roadmap based on actual development progress
 */

// Filter out any invalid phases (like Phase 0) before returning
export const getUpdatedRoadmapData = () => [
  {
    id: 1,
    title: "Foundation & User Management",
    timeframe: "Completed",
    expanded: true,
    sections: [
      {
        id: "1.1",
        title: "Project Setup & Configuration",
        tasks: [
          { id: "1.1.1", text: "Finalize tech stack (React, Supabase)", completed: true },
          { id: "1.1.2", text: "Set up development environment", completed: true },
          { id: "1.1.3", text: "Configure Netlify deployment", completed: true },
          { id: "1.1.4", text: "Set up Supabase project", completed: true },
          { id: "1.1.5", text: "Configure domain (royalty.technology)", completed: true },
          { id: "1.1.6", text: "Implement basic error logging", completed: true },
          { id: "1.1.7", text: "Set up staging environment", completed: false },
          { id: "1.1.8", text: "Configure CI/CD pipeline", completed: false }
        ]
      },
      {
        id: "1.2",
        title: "Authentication System",
        tasks: [
          { id: "1.2.1", text: "Implement email/password authentication", completed: true },
          { id: "1.2.2", text: "Add Google OAuth integration", completed: true },
          { id: "1.2.3", text: "Add GitHub OAuth integration", completed: true },
          { id: "1.2.4", text: "Create login/signup pages", completed: true },
          { id: "1.2.5", text: "Implement authentication context", completed: true },
          { id: "1.2.6", text: "Add protected routes", completed: true },
          { id: "1.2.7", text: "Implement logout functionality", completed: true },
          { id: "1.2.8", text: "Add password reset functionality", completed: false }
        ]
      },
      {
        id: "1.3",
        title: "User Profiles",
        tasks: [
          { id: "1.3.1", text: "Create user database schema", completed: true },
          { id: "1.3.2", text: "Implement profile creation on signup", completed: true },
          { id: "1.3.3", text: "Create profile page UI", completed: true },
          { id: "1.3.4", text: "Add profile editing functionality", completed: true },
          { id: "1.3.5", text: "Implement avatar uploads", completed: true },
          { id: "1.3.6", text: "Create public profile views", completed: true },
          { id: "1.3.7", text: "Add user search functionality", completed: true },
          { id: "1.3.8", text: "Implement user settings page", completed: false }
        ]
      },
      {
        id: "1.4",
        title: "Navigation & Layout",
        tasks: [
          { id: "1.4.1", text: "Design and implement modern navbar", completed: true },
          { id: "1.4.2", text: "Create responsive layout system", completed: true },
          { id: "1.4.3", text: "Implement footer component", completed: true },
          { id: "1.4.4", text: "Add loading animations", completed: true },
          { id: "1.4.5", text: "Implement toast notifications", completed: true },
          { id: "1.4.6", text: "Create error pages (404, etc.)", completed: true },
          { id: "1.4.7", text: "Add breadcrumb navigation", completed: false },
          { id: "1.4.8", text: "Implement dark mode toggle", completed: false }
        ]
      }
    ]
  },
  {
    id: 2,
    title: "Project Management",
    timeframe: "In Progress",
    expanded: true,
    sections: [
      {
        id: "2.1",
        title: "Project Creation Wizard",
        tasks: [
          { id: "2.1.1", text: "Design multi-step wizard UI", completed: true },
          { id: "2.1.2", text: "Implement wizard navigation", completed: true },
          { id: "2.1.3", text: "Create project database schema", completed: true },
          { id: "2.1.4", text: "Step 1: Project Basics (name, description, type)", completed: true },
          { id: "2.1.5", text: "Add project thumbnail uploads", completed: true },
          { id: "2.1.6", text: "Implement project timeline settings", completed: true },
          { id: "2.1.7", text: "Add project privacy settings", completed: true },
          { id: "2.1.8", text: "Implement auto-save functionality", completed: true }
        ]
      },
      {
        id: "2.2",
        title: "Team & Contributors Management",
        tasks: [
          { id: "2.2.1", text: "Create contributor database schema", completed: true },
          { id: "2.2.2", text: "Implement contributor invitation UI", completed: true },
          { id: "2.2.3", text: "Add permission level system", completed: true },
          { id: "2.2.4", text: "Create contributor list view", completed: true },
          { id: "2.2.5", text: "Implement contributor search", completed: true },
          { id: "2.2.6", text: "Add batch email invitations", completed: true },
          { id: "2.2.7", text: "Implement invitation acceptance flow", completed: false },
          { id: "2.2.8", text: "Add contributor removal functionality", completed: true }
        ]
      },
      {
        id: "2.3",
        title: "Royalty Model Configuration",
        tasks: [
          { id: "2.3.1", text: "Create royalty model database schema", completed: true },
          { id: "2.3.2", text: "Implement equal split model", completed: true },
          { id: "2.3.3", text: "Implement task-based model", completed: true },
          { id: "2.3.4", text: "Implement time-based model", completed: true },
          { id: "2.3.5", text: "Implement role-based model", completed: true },
          { id: "2.3.6", text: "Create custom CoG model (Tasks-Time-Difficulty)", completed: true },
          { id: "2.3.7", text: "Add pre/post expense option", completed: true },
          { id: "2.3.8", text: "Implement model visualization", completed: false }
        ]
      },
      {
        id: "2.4",
        title: "Revenue Tranches",
        tasks: [
          { id: "2.4.1", text: "Create revenue tranche database schema", completed: true },
          { id: "2.4.2", text: "Implement tranche creation UI", completed: true },
          { id: "2.4.3", text: "Add revenue source selection", completed: true },
          { id: "2.4.4", text: "Implement date range selection", completed: true },
          { id: "2.4.5", text: "Add distribution thresholds", completed: true },
          { id: "2.4.6", text: "Implement rollover configuration", completed: true },
          { id: "2.4.7", text: "Create tranche list view", completed: true },
          { id: "2.4.8", text: "Add 'All Sources' button", completed: true }
        ]
      },
      {
        id: "2.5",
        title: "Contribution Tracking",
        tasks: [
          { id: "2.5.1", text: "Create contribution tracking database schema", completed: true },
          { id: "2.5.2", text: "Implement task type configuration", completed: true },
          { id: "2.5.3", text: "Add difficulty scale settings", completed: true },
          { id: "2.5.4", text: "Create predefined task types by project type", completed: true },
          { id: "2.5.5", text: "Implement difficulty adjustment UI", completed: true },
          { id: "2.5.6", text: "Add custom task type creation", completed: true },
          { id: "2.5.7", text: "Create placeholder for external integrations", completed: true },
          { id: "2.5.8", text: "Implement contribution entry form (Phase 2)", completed: true }
        ]
      },
      {
        id: "2.6",
        title: "Milestones & Timeline",
        tasks: [
          { id: "2.6.1", text: "Create milestone database schema", completed: true },
          { id: "2.6.2", text: "Implement milestone creation UI", completed: true },
          { id: "2.6.3", text: "Add predefined milestones by project type", completed: true },
          { id: "2.6.4", text: "Implement deadline setting", completed: true },
          { id: "2.6.5", text: "Add deliverables tracking", completed: true },
          { id: "2.6.6", text: "Create milestone list view", completed: true },
          { id: "2.6.7", text: "Implement timeline visualization (Phase 2)", completed: false },
          { id: "2.6.8", text: "Add milestone completion tracking (Phase 2)", completed: false }
        ]
      },
      {
        id: "2.7",
        title: "Agreements & Documentation",
        tasks: [
          { id: "2.7.1", text: "Create agreement templates", completed: true },
          { id: "2.7.2", text: "Implement agreement generation", completed: true },
          { id: "2.7.3", text: "Add project configuration review", completed: true },
          { id: "2.7.4", text: "Create comprehensive test suite for agreements", completed: true },
          { id: "2.7.5", text: "Implement project type-specific terminology", completed: true },
          { id: "2.7.6", text: "Add verification system for generated agreements", completed: true },
          { id: "2.7.7", text: "Update project wizard for agreement data collection", completed: false },
          { id: "2.7.8", text: "Enhance agreement customization", completed: false },
          { id: "2.7.9", text: "Improve default value handling", completed: false },
          { id: "2.7.10", text: "Implement digital signature support", completed: false },
          { id: "2.7.11", text: "Add agreement storage and versioning", completed: false },
          { id: "2.7.12", text: "Implement agreement export (PDF)", completed: false },
          { id: "2.7.13", text: "Add email notifications for agreements", completed: false }
        ]
      },
      {
        id: "2.8",
        title: "Project Dashboard",
        tasks: [
          { id: "2.8.1", text: "Create project list view", completed: true },
          { id: "2.8.2", text: "Implement project card UI", completed: true },
          { id: "2.8.3", text: "Add project status indicators", completed: true },
          { id: "2.8.4", text: "Implement project filtering", completed: false },
          { id: "2.8.5", text: "Add project search functionality", completed: false },
          { id: "2.8.6", text: "Create project detail view", completed: false },
          { id: "2.8.7", text: "Implement project activity feed (Phase 2)", completed: false },
          { id: "2.8.8", text: "Add project analytics dashboard (Phase 2)", completed: true }
        ]
      },
      {
        id: "2.9",
        title: "Basic Task Management",
        tasks: [
          { id: "2.9.1", text: "Create task creation and assignment functionality", completed: true },
          { id: "2.9.2", text: "Implement task statuses (to-do, in progress, review, complete)", completed: true },
          { id: "2.9.3", text: "Add task priority levels and difficulty ratings", completed: true },
          { id: "2.9.4", text: "Build Kanban-style view for tasks", completed: true },
          { id: "2.9.5", text: "Implement drag-and-drop task management", completed: true },
          { id: "2.9.6", text: "Add task filtering and search", completed: true },
          { id: "2.9.7", text: "Create task statistics dashboard", completed: true },
          { id: "2.9.8", text: "Implement logged hours tracking", completed: true }
        ]
      }
    ]
  },
  {
    id: 3,
    title: "Contribution Tracking System",
    timeframe: "Phase 2",
    expanded: false,
    sections: [
      {
        id: "3.1",
        title: "Manual Contribution Entry",
        tasks: [
          { id: "3.1.1", text: "Design contribution entry forms", completed: true },
          { id: "3.1.2", text: "Implement time tracking functionality", completed: true },
          { id: "3.1.3", text: "Add task selection from configured types", completed: true },
          { id: "3.1.4", text: "Implement difficulty rating selection", completed: true },
          { id: "3.1.5", text: "Create contribution description field", completed: true },
          { id: "3.1.6", text: "Add date range selection", completed: true },
          { id: "3.1.7", text: "Implement file/asset attachment", completed: true },
          { id: "3.1.8", text: "Add contribution tags", completed: true }
        ]
      },
      {
        id: "3.2",
        title: "Contribution Validation",
        tasks: [
          { id: "3.2.1", text: "Design validation workflow", completed: true },
          { id: "3.2.2", text: "Implement contribution review UI", completed: true },
          { id: "3.2.3", text: "Add approval/rejection functionality", completed: true },
          { id: "3.2.4", text: "Implement feedback mechanism", completed: true },
          { id: "3.2.5", text: "Create validation notifications", completed: true },
          { id: "3.2.6", text: "Add bulk validation options", completed: true },
          { id: "3.2.7", text: "Implement validation history", completed: true },
          { id: "3.2.8", text: "Add validation metrics", completed: true },
          { id: "3.2.9", text: "Add file preview component for attachments", completed: true },
          { id: "3.2.10", text: "Implement notification system for validation status changes", completed: true },
          { id: "3.2.11", text: "Add ability to request specific changes to parts of a contribution", completed: true }
        ]
      },
      {
        id: "3.3",
        title: "External Integrations",
        tasks: [
          { id: "3.3.1", text: "Design integration architecture", completed: false },
          { id: "3.3.2", text: "Implement GitHub integration", completed: false },
          { id: "3.3.3", text: "Add Trello integration", completed: false },
          { id: "3.3.4", text: "Implement Jira integration", completed: false },
          { id: "3.3.5", text: "Add Discord integration", completed: false },
          { id: "3.3.6", text: "Implement Codecks integration", completed: false },
          { id: "3.3.7", text: "Create custom webhook support", completed: false },
          { id: "3.3.8", text: "Add integration management UI", completed: false }
        ]
      },
      {
        id: "3.4",
        title: "Contribution Analytics",
        tasks: [
          { id: "3.4.1", text: "Design analytics dashboard", completed: true },
          { id: "3.4.2", text: "Implement contribution charts", completed: true },
          { id: "3.4.3", text: "Add time tracking visualization", completed: true },
          { id: "3.4.4", text: "Create contributor comparison tools", completed: true },
          { id: "3.4.5", text: "Implement task type breakdown", completed: true },
          { id: "3.4.6", text: "Add trend analysis", completed: true },
          { id: "3.4.7", text: "Create export functionality", completed: true },
          { id: "3.4.8", text: "Implement custom report builder", completed: false }
        ]
      }
    ]
  },
  {
    id: 4,
    title: "Revenue & Royalty Distribution",
    timeframe: "Phase 2",
    expanded: false,
    sections: [
      {
        id: "4.1",
        title: "Revenue Entry",
        tasks: [
          { id: "4.1.1", text: "Design revenue entry forms", completed: true },
          { id: "4.1.2", text: "Implement revenue source selection", completed: true },
          { id: "4.1.3", text: "Add date range selection", completed: true },
          { id: "4.1.4", text: "Create currency conversion support", completed: true },
          { id: "4.1.5", text: "Implement expense tracking", completed: true },
          { id: "4.1.6", text: "Add receipt/proof upload", completed: true },
          { id: "4.1.7", text: "Create revenue categories", completed: true },
          { id: "4.1.8", text: "Implement revenue notes", completed: true }
        ]
      },
      {
        id: "4.2",
        title: "Royalty Calculation",
        tasks: [
          { id: "4.2.1", text: "Implement equal split calculation", completed: true },
          { id: "4.2.2", text: "Add task-based calculation", completed: true },
          { id: "4.2.3", text: "Implement time-based calculation", completed: true },
          { id: "4.2.4", text: "Add role-based calculation", completed: true },
          { id: "4.2.5", text: "Implement custom CoG model calculation", completed: true },
          { id: "4.2.6", text: "Create calculation preview", completed: true },
          { id: "4.2.7", text: "Add manual adjustment options", completed: true },
          { id: "4.2.8", text: "Implement calculation history", completed: false }
        ]
      },
      {
        id: "4.3",
        title: "Payment Processing",
        tasks: [
          { id: "4.3.1", text: "Design payment workflow", completed: false },
          { id: "4.3.2", text: "Implement payment method management", completed: false },
          { id: "4.3.3", text: "Add payment scheduling", completed: false },
          { id: "4.3.4", text: "Create payment notifications", completed: false },
          { id: "4.3.5", text: "Implement payment confirmation", completed: false },
          { id: "4.3.6", text: "Add payment history", completed: false },
          { id: "4.3.7", text: "Create payment reports", completed: false },
          { id: "4.3.8", text: "Implement tax documentation", completed: false }
        ]
      },
      {
        id: "4.4",
        title: "Financial Dashboard",
        tasks: [
          { id: "4.4.1", text: "Design financial dashboard", completed: false },
          { id: "4.4.2", text: "Implement revenue charts", completed: false },
          { id: "4.4.3", text: "Add expense tracking", completed: false },
          { id: "4.4.4", text: "Create royalty distribution visualization", completed: false },
          { id: "4.4.5", text: "Implement payment status tracking", completed: false },
          { id: "4.4.6", text: "Add financial forecasting", completed: false },
          { id: "4.4.7", text: "Create financial reports", completed: false },
          { id: "4.4.8", text: "Implement tax calculation helpers", completed: false }
        ]
      }
    ]
  },
  {
    id: 5,
    title: "Platform Enhancements",
    timeframe: "Phase 3",
    expanded: false,
    sections: [
      {
        id: "5.1",
        title: "Marketplace & Discovery",
        tasks: [
          { id: "5.1.1", text: "Design public project listings", completed: false },
          { id: "5.1.2", text: "Implement project discovery", completed: false },
          { id: "5.1.3", text: "Add project categories and tags", completed: false },
          { id: "5.1.4", text: "Create featured projects section", completed: false },
          { id: "5.1.5", text: "Implement project search", completed: false },
          { id: "5.1.6", text: "Add project recommendations", completed: false },
          { id: "5.1.7", text: "Create project showcase pages", completed: false },
          { id: "5.1.8", text: "Implement project following", completed: false }
        ]
      },
      {
        id: "5.2",
        title: "Talent Network",
        tasks: [
          { id: "5.2.1", text: "Design talent profiles", completed: false },
          { id: "5.2.2", text: "Implement skill-based search", completed: false },
          { id: "5.2.3", text: "Add availability indicators", completed: false },
          { id: "5.2.4", text: "Create collaboration requests", completed: false },
          { id: "5.2.5", text: "Implement portfolio showcase", completed: false },
          { id: "5.2.6", text: "Add endorsement system", completed: false },
          { id: "5.2.7", text: "Create talent recommendations", completed: false },
          { id: "5.2.8", text: "Implement talent matching algorithm", completed: false }
        ]
      },
      {
        id: "5.3",
        title: "Communication Tools",
        tasks: [
          { id: "5.3.1", text: "Design messaging system", completed: false },
          { id: "5.3.2", text: "Implement direct messaging", completed: false },
          { id: "5.3.3", text: "Add group chats for projects", completed: false },
          { id: "5.3.4", text: "Create notification center", completed: false },
          { id: "5.3.5", text: "Implement @mentions", completed: false },
          { id: "5.3.6", text: "Add file sharing", completed: false },
          { id: "5.3.7", text: "Create announcement system", completed: false },
          { id: "5.3.8", text: "Implement email digests", completed: false }
        ]
      },
      {
        id: "5.4",
        title: "Educational Resources",
        tasks: [
          { id: "5.4.1", text: "Design learning center", completed: false },
          { id: "5.4.2", text: "Create royalty model guides", completed: false },
          { id: "5.4.3", text: "Add contract templates", completed: false },
          { id: "5.4.4", text: "Implement best practices documentation", completed: false },
          { id: "5.4.5", text: "Create case studies", completed: false },
          { id: "5.4.6", text: "Add video tutorials", completed: false },
          { id: "5.4.7", text: "Implement FAQ system", completed: false },
          { id: "5.4.8", text: "Create community forum", completed: false }
        ]
      }
    ]
  },
  {
    id: 6,
    title: "Advanced Features & Scaling",
    timeframe: "Phase 3",
    expanded: false,
    sections: [
      {
        id: "6.1",
        title: "Enterprise Features",
        tasks: [
          { id: "6.1.1", text: "Design organization accounts", completed: false },
          { id: "6.1.2", text: "Implement team management", completed: false },
          { id: "6.1.3", text: "Add role-based permissions", completed: false },
          { id: "6.1.4", text: "Create audit logs", completed: false },
          { id: "6.1.5", text: "Implement SSO integration", completed: false },
          { id: "6.1.6", text: "Add custom branding options", completed: false },
          { id: "6.1.7", text: "Create enterprise analytics", completed: false },
          { id: "6.1.8", text: "Implement SLA options", completed: false }
        ]
      },
      {
        id: "6.2",
        title: "API & Integrations",
        tasks: [
          { id: "6.2.1", text: "Design public API", completed: false },
          { id: "6.2.2", text: "Implement API authentication", completed: false },
          { id: "6.2.3", text: "Create API documentation", completed: false },
          { id: "6.2.4", text: "Add developer portal", completed: false },
          { id: "6.2.5", text: "Implement webhook system", completed: false },
          { id: "6.2.6", text: "Add integration marketplace", completed: false },
          { id: "6.2.7", text: "Create SDK for common platforms", completed: false },
          { id: "6.2.8", text: "Implement API rate limiting", completed: false }
        ]
      },
      {
        id: "6.3",
        title: "Advanced Analytics",
        tasks: [
          { id: "6.3.1", text: "Design analytics platform", completed: false },
          { id: "6.3.2", text: "Implement custom dashboards", completed: false },
          { id: "6.3.3", text: "Add data visualization tools", completed: false },
          { id: "6.3.4", text: "Create export functionality", completed: false },
          { id: "6.3.5", text: "Implement trend analysis", completed: false },
          { id: "6.3.6", text: "Add predictive analytics", completed: false },
          { id: "6.3.7", text: "Create industry benchmarks", completed: false },
          { id: "6.3.8", text: "Implement custom reporting", completed: false }
        ]
      },
      {
        id: "6.4",
        title: "Platform Scaling",
        tasks: [
          { id: "6.4.1", text: "Design scalable architecture", completed: false },
          { id: "6.4.2", text: "Implement caching system", completed: false },
          { id: "6.4.3", text: "Add load balancing", completed: false },
          { id: "6.4.4", text: "Create database sharding", completed: false },
          { id: "6.4.5", text: "Implement CDN integration", completed: false },
          { id: "6.4.6", text: "Add performance monitoring", completed: false },
          { id: "6.4.7", text: "Create disaster recovery plan", completed: false },
          { id: "6.4.8", text: "Implement multi-region support", completed: false }
        ]
      }
    ]
  }
];

export default getUpdatedRoadmapData;
