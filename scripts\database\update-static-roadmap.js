// Script to update the static roadmap data
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Initialize Supabase client
const supabaseUrl = process.argv[2];
const supabaseKey = process.argv[3];

if (!supabaseUrl || !supabaseKey) {
  console.error('Usage: node update-static-roadmap.js <supabase-url> <supabase-key>');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function updateStaticRoadmap() {
  try {
    console.log('=== Updating Static Roadmap Data ===');
    
    // Get the latest roadmap data from Supabase
    const { data: roadmapData, error: roadmapError } = await supabase
      .from('roadmap')
      .select('*')
      .order('updated_at', { ascending: false })
      .limit(1);
    
    if (roadmapError) {
      console.error('Error fetching roadmap from Supabase:', roadmapError);
      return false;
    }
    
    if (!roadmapData || roadmapData.length === 0) {
      console.error('No roadmap data found in Supabase');
      return false;
    }
    
    // Get the roadmap data and latest feature
    const roadmap = roadmapData[0].data;
    const latestFeature = roadmapData[0].latest_feature;
    
    console.log(`Latest feature: "${latestFeature}"`);
    
    // Create the static roadmap data
    const staticRoadmapData = {
      data: roadmap,
      latest_feature: latestFeature,
      updated_at: new Date().toISOString()
    };
    
    // Write the data to a file
    const staticRoadmapPath = path.join(__dirname, 'static-roadmap.json');
    fs.writeFileSync(staticRoadmapPath, JSON.stringify(staticRoadmapData, null, 2));
    console.log(`Static roadmap data written to ${staticRoadmapPath}`);
    
    // Also write to the netlify-deploy directory if it exists
    try {
      const deployRoadmapPath = path.join(__dirname, 'netlify-deploy/static-roadmap.json');
      fs.writeFileSync(deployRoadmapPath, JSON.stringify(staticRoadmapData, null, 2));
      console.log(`Static roadmap data written to ${deployRoadmapPath}`);
    } catch (writeError) {
      console.warn('Could not write to netlify-deploy directory:', writeError);
    }
    
    console.log('=== Static Roadmap Data Updated Successfully ===');
    return true;
  } catch (error) {
    console.error('Error updating static roadmap data:', error);
    return false;
  }
}

// Run the update function
updateStaticRoadmap()
  .then(success => {
    if (success) {
      console.log('Static roadmap data updated successfully');
      process.exit(0);
    } else {
      console.error('Failed to update static roadmap data');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
