import React, { useState, useContext } from 'react';
import { Card, CardBody, Button, Input, Textarea, Select, SelectItem, Switch } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

// Import the new immersive alliance wizard
import ImmersiveAllianceWizard from '../../components/alliance/ImmersiveAllianceWizard';

/**
 * TeamCreation Section Component
 * Allows users to create new teams/alliances
 * Part of the experimental navigation system
 */
const TeamCreation = ({ canvasId, sectionId }) => {
  const { currentUser } = useContext(UserContext);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    alliance_type: 'emerging',
    is_business_entity: false,
    is_public: true,
    max_members: 10
  });
  const [loading, setLoading] = useState(false);
  const [immersiveMode, setImmersiveMode] = useState(true); // Default to immersive mode

  const allianceTypes = [
    { key: 'solo', label: '⭐ Solo Venture', description: 'Individual contributor working independently' },
    { key: 'emerging', label: '🌱 Emerging Alliance', description: 'Small team building momentum' },
    { key: 'established', label: '🏰 Established Alliance', description: 'Mature team with proven track record' }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCreateTeam = async () => {
    if (!formData.name.trim()) {
      toast.error('Team name is required');
      return;
    }

    setLoading(true);
    try {
      // Create the team
      const { data: team, error: teamError } = await supabase
        .from('teams')
        .insert({
          name: formData.name.trim(),
          description: formData.description.trim(),
          alliance_type: formData.alliance_type,
          is_business_entity: formData.is_business_entity,
          is_public: formData.is_public,
          max_members: formData.max_members,
          created_by: currentUser.id
        })
        .select()
        .single();

      if (teamError) {
        throw teamError;
      }

      // Add creator as admin member
      const { error: memberError } = await supabase
        .from('team_members')
        .insert({
          team_id: team.id,
          user_id: currentUser.id,
          role: 'admin',
          is_admin: true,
          joined_at: new Date().toISOString()
        });

      if (memberError) {
        throw memberError;
      }

      toast.success('Alliance created successfully!');

      // Reset form
      setFormData({
        name: '',
        description: '',
        alliance_type: 'emerging',
        is_business_entity: false,
        is_public: true,
        max_members: 10
      });

      // Navigate to the new team
      setTimeout(() => {
        window.location.href = `/teams/${team.id}`;
      }, 1500);

    } catch (error) {
      console.error('Error creating team:', error);
      toast.error('Failed to create alliance');
    } finally {
      setLoading(false);
    }
  };

  // Use immersive mode by default, traditional mode as fallback
  if (immersiveMode) {
    return (
      <ImmersiveAllianceWizard
        mode="create"
        onComplete={(result) => {
          if (result.redirectTo) {
            window.location.href = result.redirectTo;
          }
        }}
        onCancel={() => {
          // Allow users to switch to traditional mode if needed
          setImmersiveMode(false);
        }}
      />
    );
  }

  // Traditional mode (fallback)
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="p-6 space-y-6"
    >
      {/* Mode toggle */}
      <div className="text-center mb-4">
        <Button
          variant="bordered"
          size="sm"
          onClick={() => setImmersiveMode(true)}
          className="text-white border-white/30 hover:bg-white/10"
        >
          🚀 Try Immersive Mode
        </Button>
      </div>

      {/* Section Header */}
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-white mb-2">🏰 Create New Alliance</h2>
        <p className="text-white/70">
          Start your own team or alliance and invite collaborators
        </p>
      </div>

      {/* Creation Form */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardBody className="p-8 space-y-6">
          {/* Alliance Name */}
          <Input
            label="Alliance Name"
            placeholder="Enter your alliance name"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            variant="bordered"
            size="lg"
            classNames={{
              input: "text-white",
              label: "text-white/70",
              inputWrapper: "bg-white/10 border-white/20"
            }}
            startContent={<span className="text-white/70">🏰</span>}
          />

          {/* Description */}
          <Textarea
            label="Description"
            placeholder="Describe your alliance's mission and goals"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            variant="bordered"
            minRows={3}
            classNames={{
              input: "text-white",
              label: "text-white/70",
              inputWrapper: "bg-white/10 border-white/20"
            }}
          />

          {/* Alliance Type */}
          <Select
            label="Alliance Type"
            placeholder="Select alliance type"
            selectedKeys={[formData.alliance_type]}
            onSelectionChange={(keys) => handleInputChange('alliance_type', Array.from(keys)[0])}
            variant="bordered"
            classNames={{
              trigger: "bg-white/10 border-white/20",
              label: "text-white/70",
              value: "text-white"
            }}
          >
            {allianceTypes.map((type) => (
              <SelectItem key={type.key} value={type.key}>
                <div>
                  <div className="font-medium">{type.label}</div>
                  <div className="text-sm text-gray-500">{type.description}</div>
                </div>
              </SelectItem>
            ))}
          </Select>

          {/* Max Members */}
          <Input
            label="Maximum Members"
            type="number"
            placeholder="10"
            value={formData.max_members.toString()}
            onChange={(e) => handleInputChange('max_members', parseInt(e.target.value) || 10)}
            variant="bordered"
            classNames={{
              input: "text-white",
              label: "text-white/70",
              inputWrapper: "bg-white/10 border-white/20"
            }}
            startContent={<span className="text-white/70">👥</span>}
          />

          {/* Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-bold text-white">Alliance Settings</h3>

            <div className="flex items-center justify-between bg-white/5 rounded-lg p-4">
              <div>
                <h4 className="text-white font-medium">Public Alliance</h4>
                <p className="text-white/60 text-sm">Allow others to discover and request to join</p>
              </div>
              <Switch
                isSelected={formData.is_public}
                onValueChange={(value) => handleInputChange('is_public', value)}
                color="primary"
              />
            </div>

            <div className="flex items-center justify-between bg-white/5 rounded-lg p-4">
              <div>
                <h4 className="text-white font-medium">Business Entity</h4>
                <p className="text-white/60 text-sm">Register as a formal business entity</p>
              </div>
              <Switch
                isSelected={formData.is_business_entity}
                onValueChange={(value) => handleInputChange('is_business_entity', value)}
                color="success"
              />
            </div>
          </div>

          {/* Business Entity Notice */}
          {formData.is_business_entity && (
            <Card className="bg-yellow-500/10 border-yellow-500/20">
              <CardBody className="p-4">
                <div className="flex items-start space-x-3">
                  <span className="text-yellow-400 text-xl">⚠️</span>
                  <div>
                    <h4 className="text-yellow-300 font-medium">Business Entity Registration</h4>
                    <p className="text-yellow-200/80 text-sm mt-1">
                      Creating a business entity will require additional legal and tax information.
                      You'll be guided through the business registration process after creating your alliance.
                    </p>
                  </div>
                </div>
              </CardBody>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex gap-4 pt-4">
            <Button
              color="primary"
              size="lg"
              className="flex-1"
              isLoading={loading}
              onClick={handleCreateTeam}
              startContent={!loading && "🏰"}
            >
              {loading ? 'Creating Alliance...' : 'Create Alliance'}
            </Button>
            <Button
              color="secondary"
              variant="bordered"
              size="lg"
              onClick={() => {
                setFormData({
                  name: '',
                  description: '',
                  alliance_type: 'emerging',
                  is_business_entity: false,
                  is_public: true,
                  max_members: 10
                });
              }}
            >
              Reset
            </Button>
          </div>
        </CardBody>
      </Card>

      {/* Quick Templates */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardBody className="p-6">
          <h3 className="text-xl font-bold text-white mb-4">🚀 Quick Templates</h3>
          <div className="grid md:grid-cols-3 gap-4">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-white/5 rounded-lg p-4 cursor-pointer"
              onClick={() => {
                setFormData({
                  ...formData,
                  name: 'Creative Collective',
                  description: 'A collaborative space for creative professionals to work together on innovative projects.',
                  alliance_type: 'emerging',
                  max_members: 15
                });
              }}
            >
              <div className="text-3xl mb-2">🎨</div>
              <h4 className="text-white font-medium">Creative Collective</h4>
              <p className="text-white/60 text-sm">For artists and creators</p>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-white/5 rounded-lg p-4 cursor-pointer"
              onClick={() => {
                setFormData({
                  ...formData,
                  name: 'Tech Startup',
                  description: 'Building the next generation of technology solutions with a passionate team.',
                  alliance_type: 'emerging',
                  is_business_entity: true,
                  max_members: 20
                });
              }}
            >
              <div className="text-3xl mb-2">💻</div>
              <h4 className="text-white font-medium">Tech Startup</h4>
              <p className="text-white/60 text-sm">For technology ventures</p>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-white/5 rounded-lg p-4 cursor-pointer"
              onClick={() => {
                setFormData({
                  ...formData,
                  name: 'Solo Venture',
                  description: 'Independent work with occasional collaboration opportunities.',
                  alliance_type: 'solo',
                  max_members: 5
                });
              }}
            >
              <div className="text-3xl mb-2">⭐</div>
              <h4 className="text-white font-medium">Solo Venture</h4>
              <p className="text-white/60 text-sm">For independent work</p>
            </motion.div>
          </div>
        </CardBody>
      </Card>
    </motion.div>
  );
};

export default TeamCreation;
