# Bento Grid Design System
**Comprehensive Layout Rules for Royaltea Platform**

## 📐 **Grid Foundation**

### **Base Grid System**
- **Grid**: 12 columns × flexible rows
- **Gutter**: 16px between widgets
- **Margin**: 24px from screen edges
- **Responsive**: Adapts to screen sizes (mobile: 6 columns)

### **Widget Size Standards**
```
1×1: 180px × 120px (min)
2×1: 376px × 120px
3×1: 572px × 120px
4×1: 768px × 120px
6×1: 1160px × 120px

2×2: 376px × 256px
3×2: 572px × 256px
4×2: 768px × 256px
6×2: 1160px × 256px
```

---

## 🎯 **Content-Based Widget Sizing Rules**

### **1×1 Widgets - Quick Metrics**
**Use for:**
- Single KPI numbers (rank, streak, count)
- Status indicators (online/offline, health)
- Quick action buttons (create, settings)
- Simple progress indicators

**Examples:**
- Rank: #47
- Streak: 12 days
- Pool: 23% available
- Quick: Create Mission

### **2×1 Widgets - Basic Info**
**Use for:**
- Two related metrics with context
- Simple lists (3-5 items)
- Basic user profiles
- Standard progress with details

**Examples:**
- Treasury overview with balance
- Team member count with top contributors
- Mission status with completion rate
- Learning progress with course info

### **3×1 Widgets - Standard Content**
**Use for:**
- Detailed metrics with breakdowns
- Medium lists (5-8 items)
- Complex progress indicators
- Multi-metric displays

**Examples:**
- Analytics with multiple KPIs
- Venture list with progress bars
- Skill assessment with ratings
- Revenue breakdown by category

### **4×1 Widgets - Extended Content**
**Use for:**
- Complex metrics with context
- Long lists (8-12 items)
- Multi-step processes
- Detailed status panels
- Search and filter interfaces

**Examples:**
- Advanced search with multiple filters
- Detailed project information
- Extended team member lists
- Complex form interfaces

### **2×2 Widgets - Rich Content**
**Use for:**
- Charts with legends and context
- Complex forms with multiple fields
- Detailed user/project cards
- Interactive elements with actions

**Examples:**
- Financial dashboard with charts
- User profile with stats and actions
- Project status with detailed metrics
- ORB wallet with trading options

### **4×2 & 6×2 Widgets - Primary Content**
**Use for:**
- Data tables and lists
- Complex charts and visualizations
- Main activity feeds
- Detailed workflows
- Primary content areas

**Examples:**
- Mission marketplace with full listings
- Project status with comprehensive details
- Trading marketplace with live data
- Team management with full member info

---

## 🎨 **Consistency Rules Across Platform**

### **Widget Type Consistency**
- **Financial widgets**: Always use 2×2 for wallets, 4×1 for breakdowns
- **User lists**: 2×1 for summaries, 4×2 for detailed views
- **Charts**: 2×2 for simple, 4×2 for complex
- **Actions**: 1×1 for single, 2×1 for multiple
- **Search**: Always 4×1 for comprehensive filters

### **Priority-Based Placement**
```
High Priority (Top-Left):
- Primary content (4×2, 6×2)
- Key metrics (2×2)
- Main actions (2×1)

Medium Priority (Center):
- Supporting content (3×1, 4×1)
- Secondary metrics (2×1)
- Related actions (1×1)

Low Priority (Bottom-Right):
- Quick actions (1×1)
- Supplementary info (2×1)
- Settings/utilities (1×1)
```

### **Content Hierarchy**
1. **Primary Action/Content**: Largest widget (4×2 or 6×2)
2. **Key Metrics**: Medium widgets (2×2, 3×1)
3. **Supporting Info**: Standard widgets (2×1, 3×1)
4. **Quick Actions**: Small widgets (1×1, 2×1)

---

## 📱 **Responsive Behavior**

### **Desktop (12 columns)**
- Full widget sizes as specified
- Maximum 3 rows visible without scrolling
- Optimal: 6-9 widgets per screen

### **Tablet (8 columns)**
- 6×1 becomes 4×1
- 4×1 becomes 3×1
- 3×1 becomes 2×1
- Maintain aspect ratios

### **Mobile (6 columns)**
- All widgets become 6×1 (full width)
- Stack vertically
- Maintain content hierarchy
- Collapse complex widgets to essentials

---

## 🎮 **Platform-Specific Applications**

### **Dashboard Pages**
- **Hero widget**: 4×2 (main content)
- **Key metrics**: 2×2 (treasury, status)
- **Quick info**: 2×1 (teams, missions)
- **Actions**: 1×1 (create, settings)

### **Management Pages**
- **Primary content**: 6×2 (lists, tables)
- **Filters**: 4×1 (search, sort)
- **Metrics**: 2×1 (stats, counts)
- **Actions**: 1×1 (quick buttons)

### **Trading/Financial**
- **Wallet**: 2×2 (balance, actions)
- **Marketplace**: 6×2 (trading interface)
- **Charts**: 2×2 (price, trends)
- **Quick stats**: 1×1 (rank, pool)

---

## ✅ **Implementation Checklist**

### **Before Creating Widgets**
- [ ] Identify content type and complexity
- [ ] Determine user priority and frequency
- [ ] Choose appropriate widget size
- [ ] Plan responsive behavior
- [ ] Consider related widget grouping

### **Widget Design Standards**
- [ ] Clear visual hierarchy within widget
- [ ] Consistent padding (16px internal)
- [ ] Action buttons in bottom-right
- [ ] Status indicators in top-right
- [ ] Progress bars use consistent styling

### **Cross-Page Consistency**
- [ ] Similar content uses same widget sizes
- [ ] Consistent placement patterns
- [ ] Unified color and typography
- [ ] Standardized interaction patterns

**This design system ensures consistent, scalable, and user-friendly bento grid layouts across the entire Royaltea platform.**
