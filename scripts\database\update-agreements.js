// Script to update existing agreements in the database
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

// Initialize Supabase client with the production URL and service role key
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzgyMDU1OSwiZXhwIjoyMDU5Mzk2NTU5fQ.k5xvFBzBxNMCtPd0d8Ko9dAcT5Y3op3ZgFvlAf85LLs';
const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log('Updating agreements to remove Village references...\n');

    // Get all projects
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('*');

    if (projectsError) {
      console.error('Error fetching projects:', projectsError);
      return;
    }

    if (!projects || projects.length === 0) {
      console.log('No projects found');
      return;
    }

    console.log(`Found ${projects.length} projects`);

    // Process each project
    for (const project of projects) {
      console.log(`\nProcessing project: ${project.name} (${project.id})`);

      // Get agreements for this project
      const { data: agreements, error: agreementsError } = await supabase
        .from('contributor_agreements')
        .select('*')
        .eq('project_id', project.id);

      if (agreementsError) {
        console.error(`Error fetching agreements for project ${project.name}:`, agreementsError);
        continue;
      }

      if (!agreements || agreements.length === 0) {
        console.log(`No agreements found for project ${project.name}`);
        continue;
      }

      console.log(`Found ${agreements.length} agreements for project ${project.name}`);

      // Process each agreement
      for (const agreement of agreements) {
        console.log(`\n  Processing agreement ID: ${agreement.id}`);
        console.log(`  Version: ${agreement.version || 1}`);
        console.log(`  Status: ${agreement.status || 'Unknown'}`);

        // Check for Village references
        const villageReferences = checkForVillageReferences(agreement.agreement_text);

        if (villageReferences.length > 0) {
          console.log(`  ⚠️ Found ${villageReferences.length} Village references`);

          // Apply our replacement logic
          const cleanedText = cleanAgreementText(
            agreement.agreement_text,
            project.name,
            project.description || 'A collaborative project',
            project.project_type || 'software'
          );

          // Check if any village references remain
          const remainingRefs = checkForVillageReferences(cleanedText);
          if (remainingRefs.length > 0) {
            console.log(`  ⚠️ ${remainingRefs.length} Village references still remain after cleaning`);
            continue;
          }

          // Update the agreement in the database
          console.log('  Updating agreement in the database...');

          // Create a backup of the original agreement
          const backupFilename = `backup-agreement-${agreement.id}.md`;
          fs.writeFileSync(backupFilename, agreement.agreement_text);
          console.log(`  Original agreement backed up to ${backupFilename}`);

          // Update the agreement
          const { error: updateError } = await supabase
            .from('contributor_agreements')
            .update({ agreement_text: cleanedText })
            .eq('id', agreement.id);

          if (updateError) {
            console.error(`  Error updating agreement ${agreement.id}:`, updateError);
          } else {
            console.log(`  ✓ Agreement ${agreement.id} updated successfully!`);
          }
        } else {
          console.log(`  ✓ No Village references found in agreement ${agreement.id}`);
        }
      }
    }

    console.log('\nUpdate completed!');

  } catch (error) {
    console.error('Error:', error);
  }
}

// Helper function to check for Village references
function checkForVillageReferences(text) {
  if (!text) return [];

  const lines = text.split('\n');
  const references = [];

  const villagePatterns = [
    /village/i,
    /Village of The Ages/i,
    /Village of the Ages/i,
    /VOTA/i
  ];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    for (const pattern of villagePatterns) {
      if (pattern.test(line)) {
        // Get context (1 line before and after)
        const start = Math.max(0, i - 1);
        const end = Math.min(lines.length - 1, i + 1);
        const context = lines.slice(start, end + 1).join('\n');

        references.push({
          lineNumber: i + 1,
          line: line,
          context: context
        });

        // Only add each line once, even if it matches multiple patterns
        break;
      }
    }
  }

  return references;
}

// Helper function to clean agreement text
function cleanAgreementText(text, projectName, projectDescription, projectType) {
  if (!text) return '';

  let cleanedText = text;

  // Replace Village of The Ages references
  const villagePatterns = [
    /Village of The Ages/gi,
    /Village of the Ages/gi,
    /Village of The Age/gi,
    /Village of the Age/gi,
    /\bVOTA\b/gi,
    /\bvillage simulation game\b/gi,
    /\bvillage simulation\b/gi,
    /\bvillage layout\b/gi,
    /\bvillage building\b/gi,
    /\bbasic village layout\b/gi,
    /\bbasic village\b/gi,
    /\bvillagers\b/gi,
    /\bneighboring villages\b/gi,
    /\bvillage management\b/gi,
    /\bAI for villagers\b/gi,
    /\bInitial AI for villagers\b/gi
  ];

  // Replace all village patterns with appropriate project-specific content
  villagePatterns.forEach(pattern => {
    if (pattern.toString().includes('village simulation game')) {
      cleanedText = cleanedText.replace(pattern, projectDescription);
    } else if (pattern.toString().includes('AI for villagers')) {
      if (projectType === 'game') {
        cleanedText = cleanedText.replace(pattern, pattern.toString().replace('villagers', 'players').replace(/\/.*\/[gi]*/,''));
      } else {
        cleanedText = cleanedText.replace(pattern, pattern.toString().replace('villagers', 'participants').replace(/\/.*\/[gi]*/,''));
      }
    } else if (pattern.toString().includes('villagers')) {
      cleanedText = cleanedText.replace(pattern, projectType === 'game' ? 'players' : 'participants');
    } else if (pattern.toString().includes('neighboring villages')) {
      cleanedText = cleanedText.replace(pattern, 'external partners');
    } else if (pattern.toString().includes('village management') ||
               pattern.toString().includes('village building') ||
               pattern.toString().includes('village layout') ||
               pattern.toString().includes('basic village')) {
      // For game-specific terms, replace with more generic alternatives based on project type
      if (projectType === 'game') {
        cleanedText = cleanedText.replace(pattern, pattern.toString().replace('village', 'game').replace(/\/.*\/[gi]*/,''));
      } else {
        cleanedText = cleanedText.replace(pattern, pattern.toString().replace('village', 'project').replace(/\/.*\/[gi]*/,''));
      }
    } else if (pattern.toString().includes('Village of The Ages') ||
               pattern.toString().includes('Village of the Ages') ||
               pattern.toString().includes('VOTA')) {
      cleanedText = cleanedText.replace(pattern, projectName);
    } else if (pattern.toString().includes('village')) {
      cleanedText = cleanedText.replace(pattern, projectType === 'game' ? 'game' : 'project');
    }
  });

  // Additional specific description replacement
  cleanedText = cleanedText.replace(/village simulation game where players guide communities through historical progressions and manage resource-based challenges/gi, projectDescription);

  // Special case for VotA project - direct replacements
  if (projectName === 'VotA') {
    cleanedText = cleanedText.replace(/- Basic village layout and building system/gi, '- Basic game layout and building system');
    cleanedText = cleanedText.replace(/- Initial AI for villagers/gi, '- Initial AI for players');
  }

  // Final check for any remaining village references
  if (/\bvillage\b/i.test(cleanedText)) {
    console.log('  Warning: Some village references may still remain in the agreement.');
    // One final attempt to replace any remaining instances
    cleanedText = cleanedText.replace(/\bvillage\b/gi, projectType === 'game' ? 'game' : 'project');
  }

  return cleanedText;
}

main();
