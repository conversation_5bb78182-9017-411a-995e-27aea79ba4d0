// Agreement notification function
const { createClient } = require('@supabase/supabase-js');
const nodemailer = require('nodemailer');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Initialize email transporter
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST || 'smtp.gmail.com',
  port: process.env.EMAIL_PORT || 587,
  secure: process.env.EMAIL_SECURE === 'true',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS
  }
});

// Email templates
const emailTemplates = {
  newAgreement: {
    subject: 'New Agreement for Project: {{projectName}}',
    body: `
      <h2>New Agreement for {{projectName}}</h2>
      <p>Hello {{recipientName}},</p>
      <p>A new agreement has been created for the project <strong>{{projectName}}</strong>.</p>
      <p>Please review and sign the agreement at your earliest convenience.</p>
      <p><a href="{{agreementUrl}}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">View Agreement</a></p>
      <p>Thank you,<br>The Royaltea Team</p>
    `
  },
  agreementUpdated: {
    subject: 'Agreement Updated for Project: {{projectName}}',
    body: `
      <h2>Agreement Updated for {{projectName}}</h2>
      <p>Hello {{recipientName}},</p>
      <p>The agreement for project <strong>{{projectName}}</strong> has been updated to version {{version}}.</p>
      <p>Please review and sign the updated agreement at your earliest convenience.</p>
      <p><a href="{{agreementUrl}}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">View Agreement</a></p>
      <p>Thank you,<br>The Royaltea Team</p>
    `
  },
  agreementSigned: {
    subject: 'Agreement Signed for Project: {{projectName}}',
    body: `
      <h2>Agreement Signed for {{projectName}}</h2>
      <p>Hello {{recipientName}},</p>
      <p><strong>{{signerName}}</strong> has signed the agreement for project <strong>{{projectName}}</strong>.</p>
      <p><a href="{{agreementUrl}}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">View Agreement</a></p>
      <p>Thank you,<br>The Royaltea Team</p>
    `
  }
};

// Replace template variables
const replaceTemplateVars = (template, vars) => {
  let result = template;
  for (const [key, value] of Object.entries(vars)) {
    result = result.replace(new RegExp(`{{${key}}}`, 'g'), value);
  }
  return result;
};

// Send email notification
const sendEmailNotification = async (recipient, templateName, templateVars) => {
  const template = emailTemplates[templateName];
  
  if (!template) {
    throw new Error(`Template ${templateName} not found`);
  }
  
  const subject = replaceTemplateVars(template.subject, templateVars);
  const html = replaceTemplateVars(template.body, templateVars);
  
  const mailOptions = {
    from: process.env.EMAIL_FROM || '<EMAIL>',
    to: recipient,
    subject,
    html
  };
  
  try {
    const info = await transporter.sendMail(mailOptions);
    console.log('Email sent:', info.messageId);
    return { success: true, messageId: info.messageId };
  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
};

// Notify about new agreement
const notifyNewAgreement = async (agreementId) => {
  try {
    // Get agreement details
    const { data: agreement, error: agreementError } = await supabase
      .from('contributor_agreements')
      .select(`
        *,
        project:projects(*),
        contributor:project_contributors(*)
      `)
      .eq('id', agreementId)
      .single();
    
    if (agreementError) throw agreementError;
    
    // Get contributor user details
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', agreement.contributor.user_id)
      .single();
    
    if (userError) throw userError;
    
    // Send notification email
    await sendEmailNotification(
      user.email,
      'newAgreement',
      {
        recipientName: user.display_name || user.email,
        projectName: agreement.project.name || agreement.project.title,
        agreementUrl: `https://royalty.technology/project/${agreement.project_id}/agreements`
      }
    );
    
    return { success: true };
  } catch (error) {
    console.error('Error notifying about new agreement:', error);
    return { success: false, error: error.message };
  }
};

// Notify about updated agreement
const notifyAgreementUpdated = async (agreementId) => {
  try {
    // Get agreement details
    const { data: agreement, error: agreementError } = await supabase
      .from('contributor_agreements')
      .select(`
        *,
        project:projects(*),
        contributor:project_contributors(*)
      `)
      .eq('id', agreementId)
      .single();
    
    if (agreementError) throw agreementError;
    
    // Get contributor user details
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', agreement.contributor.user_id)
      .single();
    
    if (userError) throw userError;
    
    // Send notification email
    await sendEmailNotification(
      user.email,
      'agreementUpdated',
      {
        recipientName: user.display_name || user.email,
        projectName: agreement.project.name || agreement.project.title,
        version: agreement.version || 1,
        agreementUrl: `https://royalty.technology/project/${agreement.project_id}/agreements`
      }
    );
    
    return { success: true };
  } catch (error) {
    console.error('Error notifying about updated agreement:', error);
    return { success: false, error: error.message };
  }
};

// Notify about signed agreement
const notifyAgreementSigned = async (agreementId) => {
  try {
    // Get agreement details
    const { data: agreement, error: agreementError } = await supabase
      .from('contributor_agreements')
      .select(`
        *,
        project:projects(*),
        contributor:project_contributors(*)
      `)
      .eq('id', agreementId)
      .single();
    
    if (agreementError) throw agreementError;
    
    // Get project owner
    const { data: projectOwner, error: ownerError } = await supabase
      .from('project_contributors')
      .select(`
        *,
        user:users(*)
      `)
      .eq('project_id', agreement.project_id)
      .eq('permission_level', 'Owner')
      .single();
    
    if (ownerError) throw ownerError;
    
    // Get signer details
    const { data: signer, error: signerError } = await supabase
      .from('users')
      .select('*')
      .eq('id', agreement.contributor.user_id)
      .single();
    
    if (signerError) throw signerError;
    
    // Send notification email to project owner
    await sendEmailNotification(
      projectOwner.user.email,
      'agreementSigned',
      {
        recipientName: projectOwner.user.display_name || projectOwner.user.email,
        signerName: signer.display_name || signer.email,
        projectName: agreement.project.name || agreement.project.title,
        agreementUrl: `https://royalty.technology/project/${agreement.project_id}/agreements`
      }
    );
    
    return { success: true };
  } catch (error) {
    console.error('Error notifying about signed agreement:', error);
    return { success: false, error: error.message };
  }
};

// Main handler
exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS'
  };
  
  // Handle preflight request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers
    };
  }
  
  // Only allow POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }
  
  try {
    const body = JSON.parse(event.body);
    const { action, agreementId } = body;
    
    if (!action || !agreementId) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Missing required parameters' })
      };
    }
    
    let result;
    
    switch (action) {
      case 'new':
        result = await notifyNewAgreement(agreementId);
        break;
      case 'update':
        result = await notifyAgreementUpdated(agreementId);
        break;
      case 'sign':
        result = await notifyAgreementSigned(agreementId);
        break;
      default:
        return {
          statusCode: 400,
          headers,
          body: JSON.stringify({ error: 'Invalid action' })
        };
    }
    
    return {
      statusCode: result.success ? 200 : 500,
      headers,
      body: JSON.stringify(result)
    };
  } catch (error) {
    console.error('Error processing request:', error);
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: error.message })
    };
  }
};
