import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, Card, CardBody, CardHeader } from '@heroui/react';

/**
 * VentureReviewScreen Component
 * 
 * Final review screen showing venture configuration and agreement preview
 * Follows wireframe design with comprehensive review and agreement generation
 */
const VentureReviewScreen = ({ 
  questionData, 
  projectData, 
  onConfirm, 
  onBack, 
  isLoading 
}) => {
  const [agreementPreview, setAgreementPreview] = useState(null);
  const [validationStatus, setValidationStatus] = useState({
    legal: false,
    compliance: false,
    revenue: false,
    roles: false
  });

  // Generate agreement preview
  useEffect(() => {
    generateAgreementPreview();
    validateConfiguration();
  }, [questionData, projectData]);

  const generateAgreementPreview = async () => {
    // Simulate agreement generation
    const preview = {
      title: `${questionData.ventureName} Development Venture`,
      parties: 'Alliance Members',
      projectType: getProjectTypeDisplay(),
      revenueModel: 'CoG (Tasks-Time-Difficulty)',
      ipOwnership: 'Shared among contributors',
      pages: 12,
      template: getTemplateType()
    };
    
    setAgreementPreview(preview);
  };

  const validateConfiguration = () => {
    // Simulate validation checks
    setTimeout(() => {
      setValidationStatus({
        legal: true,
        compliance: true,
        revenue: true,
        roles: true
      });
    }, 1000);
  };

  const getProjectTypeDisplay = () => {
    const category = questionData.projectCategory;
    const subtype = questionData.projectSubtype;
    
    if (category === 'software' && subtype) {
      const subtypeMap = {
        'mobile': 'Mobile App',
        'web': 'Web Application',
        'desktop': 'Desktop Software',
        'tool': 'Developer Tool',
        'game': 'Game'
      };
      return subtypeMap[subtype] || 'Software';
    }
    
    const categoryMap = {
      'software': 'Software Development',
      'creative': 'Creative Work',
      'business': 'Business Service',
      'physical': 'Physical Product',
      'other': 'Custom Project'
    };
    
    return categoryMap[category] || 'Project';
  };

  const getTemplateType = () => {
    if (questionData.budget === 'funded') return 'Funded Startup - Professional';
    if (questionData.budget === 'revenue_first') return 'Revenue Share - Standard';
    return 'Small Business Compliant';
  };

  const getTimelineDisplay = () => {
    const timelineMap = {
      'quick': 'Quick Sprint (1-4 weeks)',
      'short': 'Short Project (1-3 months)',
      'medium': 'Medium Project (3-12 months)',
      'long': 'Long-term Vision (1+ years)',
      'flexible': 'Flexible Timeline'
    };
    return timelineMap[questionData.timeline] || 'Custom Timeline';
  };

  const getBudgetDisplay = () => {
    const budgetMap = {
      'funded': 'We have funding',
      'bootstrapped': 'Bootstrapped',
      'sweat_equity': 'Sweat Equity',
      'revenue_first': 'Revenue-First',
      'seeking_investment': 'Seeking Investment'
    };
    return budgetMap[questionData.budget] || 'Custom Budget';
  };

  const getSuccessDisplay = () => {
    const successMap = {
      'revenue': 'Revenue Target',
      'users': 'User Growth',
      'features': 'Feature Completion',
      'recognition': 'Recognition',
      'satisfaction': 'Personal Satisfaction'
    };
    return successMap[questionData.successMetrics] || 'Custom Success Metrics';
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4 }
    }
  };

  return (
    <motion.div
      className="min-h-screen flex items-center justify-center p-4"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="max-w-4xl mx-auto w-full">
        {/* Header */}
        <motion.div variants={itemVariants} className="text-center mb-8">
          <h1 className="text-5xl font-bold text-white mb-4">
            🚀 Review Your Venture
          </h1>
          <p className="text-xl text-white/80">
            Everything looks great! Ready to create your venture?
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Venture Summary */}
          <motion.div variants={itemVariants}>
            <Card className="bg-white/10 backdrop-blur-md border border-white/20">
              <CardHeader>
                <h2 className="text-2xl font-semibold text-white">
                  Venture Summary
                </h2>
              </CardHeader>
              <CardBody className="space-y-4 text-white">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{questionData.ventureIcon}</span>
                  <div>
                    <div className="font-semibold text-lg">{questionData.ventureName}</div>
                    <div className="text-white/70 text-sm">{getProjectTypeDisplay()}</div>
                  </div>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div><strong>Timeline:</strong> {getTimelineDisplay()}</div>
                  <div><strong>Budget:</strong> {getBudgetDisplay()}</div>
                  <div><strong>Success:</strong> {getSuccessDisplay()}</div>
                  <div><strong>Target:</strong> {questionData.targetAudience}</div>
                </div>

                <div className="pt-2">
                  <div className="text-sm text-white/70 mb-2">Description:</div>
                  <div className="text-sm">{questionData.ventureDescription}</div>
                </div>

                {questionData.ventureTags.length > 0 && (
                  <div className="pt-2">
                    <div className="text-sm text-white/70 mb-2">Tags:</div>
                    <div className="flex flex-wrap gap-1">
                      {questionData.ventureTags.map((tag, index) => (
                        <span
                          key={index}
                          className="bg-white/20 px-2 py-1 rounded text-xs"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </CardBody>
            </Card>
          </motion.div>

          {/* Agreement Preview */}
          <motion.div variants={itemVariants}>
            <Card className="bg-white/10 backdrop-blur-md border border-white/20">
              <CardHeader>
                <h2 className="text-2xl font-semibold text-white">
                  Generated Agreement
                </h2>
              </CardHeader>
              <CardBody className="space-y-4 text-white">
                {agreementPreview ? (
                  <>
                    <div className="bg-white/10 rounded-lg p-4 border border-white/20">
                      <div className="font-semibold mb-2">COLLABORATION AGREEMENT</div>
                      <div className="text-sm space-y-1 text-white/80">
                        <div>{agreementPreview.title}</div>
                        <div>Parties: {agreementPreview.parties}</div>
                        <div>Project: {agreementPreview.projectType}</div>
                        <div>Revenue Model: {agreementPreview.revenueModel}</div>
                        <div>IP Ownership: {agreementPreview.ipOwnership}</div>
                      </div>
                    </div>

                    <div className="space-y-2 text-sm">
                      <div><strong>🔧 Agreement Generator:</strong> Enhanced Generator</div>
                      <div><strong>📋 Template:</strong> {agreementPreview.template}</div>
                      <div><strong>⚖️ Legal Framework:</strong> Small Business Compliant</div>
                      <div><strong>📄 Document Length:</strong> {agreementPreview.pages} pages</div>
                    </div>

                    <div className="flex space-x-2">
                      <Button size="sm" variant="bordered" className="text-white border-white/30">
                        View Full Agreement
                      </Button>
                      <Button size="sm" variant="bordered" className="text-white border-white/30">
                        Customize
                      </Button>
                    </div>
                  </>
                ) : (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
                    <div className="text-white/70">Generating agreement...</div>
                  </div>
                )}
              </CardBody>
            </Card>
          </motion.div>
        </div>

        {/* Auto-Generated Components */}
        <motion.div variants={itemVariants} className="mt-8">
          <Card className="bg-white/10 backdrop-blur-md border border-white/20">
            <CardHeader>
              <h2 className="text-xl font-semibold text-white">
                ✅ Auto-Generated Components
              </h2>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-white text-sm">
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <span className="text-green-400">☑️</span>
                    <span>Collaboration Agreement ({agreementPreview?.pages || 12} pages)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-green-400">☑️</span>
                    <span>Revenue Sharing Structure (CoG model)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-green-400">☑️</span>
                    <span>IP and Ownership Terms (contributor-based)</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <span className="text-green-400">☑️</span>
                    <span>Role Definitions (based on team assignments)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-green-400">☑️</span>
                    <span>Project Milestones (timeline-based)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-green-400">☑️</span>
                    <span>Dispute Resolution Process</span>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>

        {/* Validation Status */}
        <motion.div variants={itemVariants} className="mt-6">
          <Card className="bg-white/10 backdrop-blur-md border border-white/20">
            <CardBody>
              <h3 className="text-lg font-semibold text-white mb-4">🔍 Validation Status</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                {Object.entries(validationStatus).map(([key, status]) => (
                  <div key={key} className="flex items-center space-x-2">
                    <span className={status ? "text-green-400" : "text-yellow-400"}>
                      {status ? "✅" : "⏳"}
                    </span>
                    <span className="text-white capitalize">
                      {key === 'legal' && 'Legal terms validated'}
                      {key === 'compliance' && 'Compliance checked'}
                      {key === 'revenue' && 'Revenue model verified'}
                      {key === 'roles' && 'Role assignments confirmed'}
                    </span>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </motion.div>

        {/* Action Buttons */}
        <motion.div variants={itemVariants} className="mt-8 flex justify-between">
          <Button
            size="lg"
            variant="bordered"
            onPress={onBack}
            disabled={isLoading}
            className="text-white border-white/30 hover:bg-white/10"
          >
            ← Back to Configuration
          </Button>

          <Button
            size="lg"
            onPress={onConfirm}
            disabled={isLoading || !Object.values(validationStatus).every(Boolean)}
            className="bg-white text-primary font-semibold px-12 py-4 text-lg hover:bg-white/90"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary mr-2"></div>
                Creating Venture...
              </>
            ) : (
              'Create Venture'
            )}
          </Button>
        </motion.div>

        {/* Footer Note */}
        <motion.div variants={itemVariants} className="mt-6 text-center">
          <p className="text-white/60 text-sm">
            Agreement can be reviewed and modified after creation.
          </p>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default VentureReviewScreen;
