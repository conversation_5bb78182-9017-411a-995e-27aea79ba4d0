# Manual Migration Instructions for Users Table

This migration will add the missing `updated_at` column to the `users` table. Follow these steps to apply the migration manually:

## Step 1: Access the SQL Editor

1. Log in to your Supabase dashboard
2. Select your project
3. Click on "SQL Editor" in the left sidebar
4. Click "New Query" to create a new SQL query

## Step 2: Run the Migration SQL

Copy and paste the following SQL into the editor:

```sql
-- Add updated_at column to users table if it doesn't exist
DO $$
BEGIN
    -- Check if the updated_at column exists
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'users'
        AND column_name = 'updated_at'
    ) THEN
        -- Add the updated_at column
        ALTER TABLE public.users
        ADD COLUMN updated_at TIMESTAMPTZ DEFAULT NOW();

        -- No need to update existing rows since we're using DEFAULT NOW()
        -- and the created_at column doesn't exist

        RAISE NOTICE 'Added updated_at column to users table';
    ELSE
        RAISE NOTICE 'updated_at column already exists in users table';
    END IF;
END $$;
```

Alternatively, you can use this simpler version if you prefer:

```sql
-- Simple version without checking if column exists
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT NOW();
```

## Step 3: Execute the Query

Click the "Run" button to execute the SQL query. You should see a success message in the results panel.

## Step 4: Verify the Changes

### Check the Table Structure

1. Go to the "Table Editor" in the left sidebar
2. Select the "users" table
3. Click on "Edit" to view the table structure
4. Verify that the `updated_at` column has been added

### Test the Authentication Flow

After successfully running the migration, test the authentication to ensure that:

1. New users can sign up with Google
2. User profiles are correctly created in the `users` table
3. The `updated_at` column is properly populated

## Troubleshooting

If you encounter any issues:

1. Check the error messages in the SQL Editor results panel
2. Make sure you have the necessary permissions to alter the `users` table
3. Verify that the `users` table exists and has the expected structure

For more help, refer to the Supabase documentation on [schema migrations](https://supabase.com/docs/guides/database/migrations).
