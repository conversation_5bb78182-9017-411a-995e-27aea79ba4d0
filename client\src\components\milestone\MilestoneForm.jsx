import React, { useState, useEffect } from 'react';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import DatePicker from 'react-datepicker';

const MilestoneForm = ({ projectId, onSuccess, initialData = null, isEditing = false }) => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    project_id: projectId || '',
    name: '',
    description: '',
    deliverables: [],
    approval_criteria: '',
    deadline: null,
    status: 'pending',
    weight: 1
  });
  
  const [newDeliverable, setNewDeliverable] = useState('');
  
  // Load existing milestone data if editing
  useEffect(() => {
    if (isEditing && initialData) {
      setFormData({
        ...initialData,
        deadline: initialData.deadline ? new Date(initialData.deadline) : null,
        deliverables: initialData.deliverables || []
      });
    }
  }, [isEditing, initialData]);
  
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleDateChange = (date) => {
    setFormData(prev => ({
      ...prev,
      deadline: date
    }));
  };
  
  const handleAddDeliverable = () => {
    if (newDeliverable.trim()) {
      setFormData(prev => ({
        ...prev,
        deliverables: [...prev.deliverables, newDeliverable.trim()]
      }));
      setNewDeliverable('');
    }
  };
  
  const handleRemoveDeliverable = (index) => {
    setFormData(prev => ({
      ...prev,
      deliverables: prev.deliverables.filter((_, i) => i !== index)
    }));
  };
  
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddDeliverable();
    }
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    setLoading(true);
    
    try {
      const milestoneData = {
        ...formData,
        project_id: projectId
      };
      
      let result;
      
      if (isEditing) {
        // Update existing milestone
        const { data, error } = await supabase
          .from('milestones')
          .update(milestoneData)
          .eq('id', initialData.id)
          .select()
          .single();
          
        if (error) throw error;
        result = data;
        toast.success('Milestone updated successfully');
      } else {
        // Create new milestone
        const { data, error } = await supabase
          .from('milestones')
          .insert([milestoneData])
          .select()
          .single();
          
        if (error) throw error;
        result = data;
        toast.success('Milestone added successfully');
      }
      
      // Reset form if not editing
      if (!isEditing) {
        setFormData({
          ...formData,
          name: '',
          description: '',
          deliverables: [],
          approval_criteria: '',
          deadline: null,
          status: 'pending',
          weight: 1
        });
      }
      
      // Call success callback
      if (onSuccess) {
        onSuccess(result);
      }
      
    } catch (error) {
      console.error('Error submitting milestone:', error);
      toast.error(error.message || 'Failed to submit milestone');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="milestone-form-container">
      <form onSubmit={handleSubmit} className="milestone-form">
        <div className="form-group">
          <label htmlFor="name">Milestone Name*</label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            className="form-control"
            required
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="description">Description</label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            className="form-control"
            rows="3"
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="status">Status*</label>
            <select
              id="status"
              name="status"
              value={formData.status}
              onChange={handleInputChange}
              className="form-control"
              required
            >
              <option value="pending">Pending</option>
              <option value="in-progress">In Progress</option>
              <option value="completed">Completed</option>
              <option value="blocked">Blocked</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="weight">Weight*</label>
            <select
              id="weight"
              name="weight"
              value={formData.weight}
              onChange={handleInputChange}
              className="form-control"
              required
            >
              <option value="1">1 - Low</option>
              <option value="2">2 - Medium</option>
              <option value="3">3 - High</option>
              <option value="5">5 - Critical</option>
            </select>
          </div>
        </div>
        
        <div className="form-group">
          <label htmlFor="deadline">Deadline</label>
          <DatePicker
            id="deadline"
            selected={formData.deadline}
            onChange={handleDateChange}
            className="form-control"
            dateFormat="MMMM d, yyyy"
            minDate={new Date()}
            isClearable
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="approval_criteria">Approval Criteria</label>
          <textarea
            id="approval_criteria"
            name="approval_criteria"
            value={formData.approval_criteria}
            onChange={handleInputChange}
            className="form-control"
            rows="2"
            placeholder="What needs to be true for this milestone to be considered complete?"
          />
        </div>
        
        <div className="form-group">
          <label>Deliverables</label>
          <div className="deliverables-input-group">
            <input
              type="text"
              value={newDeliverable}
              onChange={(e) => setNewDeliverable(e.target.value)}
              onKeyPress={handleKeyPress}
              className="form-control"
              placeholder="Add a deliverable and press Enter"
            />
            <button
              type="button"
              onClick={handleAddDeliverable}
              className="btn btn-outline-primary"
            >
              Add
            </button>
          </div>
          
          {formData.deliverables.length > 0 && (
            <ul className="deliverables-list">
              {formData.deliverables.map((deliverable, index) => (
                <li key={index} className="deliverable-item">
                  <span>{deliverable}</span>
                  <button
                    type="button"
                    onClick={() => handleRemoveDeliverable(index)}
                    className="btn-remove"
                    aria-label="Remove deliverable"
                  >
                    <i className="bi bi-x"></i>
                  </button>
                </li>
              ))}
            </ul>
          )}
        </div>
        
        <div className="form-actions">
          <button 
            type="button" 
            className="btn btn-secondary"
            onClick={() => onSuccess ? onSuccess() : null}
          >
            Cancel
          </button>
          <button 
            type="submit" 
            className="btn btn-primary"
            disabled={loading}
          >
            {loading ? 'Submitting...' : isEditing ? 'Update Milestone' : 'Add Milestone'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default MilestoneForm;
