// Create Plaid Tables Directly
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_KEY);

async function createPlaidTables() {
  console.log('🚀 Creating Plaid tables directly...');
  
  try {
    // Since we can't execute DDL via Supabase client, let's create a test table to verify connection
    // and then provide instructions for manual migration
    
    console.log('🔍 Testing database connection...');
    
    // Test connection with a simple query
    const { data: testQuery, error: testError } = await supabase
      .from('users')
      .select('id')
      .limit(1);
    
    if (testError) {
      console.log('❌ Database connection failed:', testError.message);
      return;
    }
    
    console.log('✅ Database connection successful');
    
    // Check if any Plaid tables already exist
    console.log('\n🔍 Checking for existing Plaid tables...');
    
    const tablesToCheck = [
      'plaid_accounts',
      'payment_transactions', 
      'payment_preferences',
      'escrow_accounts',
      'payment_routing_rules'
    ];
    
    const existingTables = [];
    const missingTables = [];
    
    for (const table of tablesToCheck) {
      try {
        const { error } = await supabase
          .from(table)
          .select('*')
          .limit(0);
        
        if (error) {
          missingTables.push(table);
        } else {
          existingTables.push(table);
        }
      } catch (err) {
        missingTables.push(table);
      }
    }
    
    console.log(`✅ Existing tables: ${existingTables.length > 0 ? existingTables.join(', ') : 'None'}`);
    console.log(`❌ Missing tables: ${missingTables.length > 0 ? missingTables.join(', ') : 'None'}`);
    
    if (missingTables.length > 0) {
      console.log('\n📋 Manual Migration Required:');
      console.log('Since Supabase client cannot execute DDL statements, please:');
      console.log('1. Go to Supabase Dashboard > SQL Editor');
      console.log('2. Copy and paste the migration file: supabase/migrations/20240116000002_plaid_integration.sql');
      console.log('3. Execute the migration manually');
      console.log('4. Re-run this script to verify');
      
      console.log('\n🔧 Alternative: Use Supabase CLI');
      console.log('1. Install Supabase CLI: npm install -g supabase');
      console.log('2. Run: supabase db push');
      console.log('3. This will apply all pending migrations');
    } else {
      console.log('\n🎉 All Plaid tables exist! Ready for API development.');
      await testPlaidTableStructure();
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

async function testPlaidTableStructure() {
  console.log('\n🧪 Testing Plaid table structure...');
  
  try {
    // Test creating a sample plaid account
    const testUserId = '2a033231-d173-4292-aa36-90f4d735bcf3';
    
    const testAccount = {
      user_id: testUserId,
      plaid_account_id: 'test_account_123',
      plaid_item_id: 'test_item_123',
      plaid_access_token: 'test_token_123',
      account_name: 'Test Checking Account',
      account_type: 'checking',
      institution_name: 'Test Bank',
      institution_id: 'test_bank_123',
      supports_ach: true,
      supports_same_day_ach: true,
      is_verified: true,
      available_balance: 1000.00,
      current_balance: 1000.00
    };
    
    const { data: account, error: accountError } = await supabase
      .from('plaid_accounts')
      .insert([testAccount])
      .select()
      .single();
    
    if (accountError) {
      console.log('❌ Account creation test failed:', accountError.message);
      return;
    }
    
    console.log('✅ Test account created:', account.id);
    
    // Test creating payment preferences
    const testPreferences = {
      user_id: testUserId,
      preferred_payment_method: 'ach_standard',
      preferred_account_id: account.id,
      notify_on_payment_received: true
    };
    
    const { data: preferences, error: prefError } = await supabase
      .from('payment_preferences')
      .insert([testPreferences])
      .select()
      .single();
    
    if (prefError) {
      console.log('❌ Preferences creation test failed:', prefError.message);
    } else {
      console.log('✅ Test preferences created:', preferences.id);
    }
    
    // Test creating a payment transaction
    const testTransaction = {
      from_user_id: testUserId,
      to_user_id: testUserId,
      from_account_id: account.id,
      amount: 100.00,
      currency: 'USD',
      payment_method: 'ach_standard',
      payment_direction: 'internal',
      description: 'Test payment transaction',
      reference_type: 'test'
    };
    
    const { data: transaction, error: transError } = await supabase
      .from('payment_transactions')
      .insert([testTransaction])
      .select()
      .single();
    
    if (transError) {
      console.log('❌ Transaction creation test failed:', transError.message);
    } else {
      console.log('✅ Test transaction created:', transaction.id);
    }
    
    // Cleanup test data
    console.log('\n🧹 Cleaning up test data...');
    
    if (transaction) await supabase.from('payment_transactions').delete().eq('id', transaction.id);
    if (preferences) await supabase.from('payment_preferences').delete().eq('id', preferences.id);
    if (account) await supabase.from('plaid_accounts').delete().eq('id', account.id);
    
    console.log('✅ Test data cleaned up');
    console.log('\n🎉 Plaid table structure test completed successfully!');
    
  } catch (testError) {
    console.log('❌ Table structure test failed:', testError.message);
  }
}

createPlaidTables();
