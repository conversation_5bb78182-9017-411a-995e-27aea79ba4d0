// Setup Test Authentication
// Create test users and get authentication tokens for testing

const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  'https://hqqlrrqvjcetoxbdjgzx.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MjA1NTksImV4cCI6MjA1OTM5NjU1OX0.e0oaDNVbA563SZJHPm6UTFOXcLbWorKn42a_brChpKQ'
);

async function setupTestAuth() {
  console.log('🔑 Setting up test authentication...\n');

  // Test user credentials
  const testUsers = [
    {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      name: 'Test User',
      role: 'admin'
    },
    {
      email: '<EMAIL>',
      password: 'VRCPassword123!',
      name: 'VRC Admin',
      role: 'company_admin'
    },
    {
      email: '<EMAIL>',
      password: 'SalesPassword123!',
      name: 'Sales Rep',
      role: 'sales_rep'
    }
  ];

  for (const user of testUsers) {
    try {
      console.log(`Creating/signing in user: ${user.email}`);

      // Try to sign in first
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email: user.email,
        password: user.password
      });

      if (signInData?.user) {
        console.log(`✅ User ${user.email} signed in successfully`);
        console.log(`   User ID: ${signInData.user.id}`);
        console.log(`   Access Token: ${signInData.session.access_token.substring(0, 50)}...`);

        // Store token for testing
        console.log(`   Full Token: ${signInData.session.access_token}\n`);

      } else if (signInError?.message.includes('Invalid login credentials')) {
        console.log(`User doesn't exist, creating: ${user.email}`);

        // Create new user
        const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
          email: user.email,
          password: user.password,
          options: {
            data: {
              full_name: user.name,
              role: user.role
            }
          }
        });

        if (signUpData?.user) {
          console.log(`✅ User ${user.email} created successfully`);
          console.log(`   User ID: ${signUpData.user.id}`);

          if (signUpData.session) {
            console.log(`   Access Token: ${signUpData.session.access_token.substring(0, 50)}...`);
            console.log(`   Full Token: ${signUpData.session.access_token}\n`);
          } else {
            console.log(`   ⚠️ Email confirmation required\n`);
          }
        } else {
          console.log(`❌ Failed to create user: ${signUpError?.message}\n`);
        }
      } else {
        console.log(`❌ Sign in error: ${signInError?.message}\n`);
      }

    } catch (error) {
      console.log(`❌ Error with user ${user.email}: ${error.message}\n`);
    }
  }

  // Test database connection
  console.log('🔍 Testing database connection...');
  try {
    const { data, error } = await supabase
      .from('teams')
      .select('id, name')
      .limit(5);

    if (error) {
      console.log(`❌ Database error: ${error.message}`);
    } else {
      console.log(`✅ Database connected, found ${data?.length || 0} teams`);
      if (data?.length > 0) {
        console.log(`   Sample teams:`, data.map(t => t.name));
      }
    }
  } catch (error) {
    console.log(`❌ Database connection error: ${error.message}`);
  }
}

setupTestAuth();
