-- SQL script to disable <PERSON><PERSON> on project_contributors table
-- Run this in the Supabase SQL Editor

-- First, disable <PERSON><PERSON> on the table
ALTER TABLE public.project_contributors DISABLE ROW LEVEL SECURITY;

-- Drop any existing policies
DROP POLICY IF EXISTS "Users can view their own invitations" ON public.project_contributors;
DROP POLICY IF EXISTS "Users can update their own invitations" ON public.project_contributors;
DROP POLICY IF EXISTS "Project creators can update any invitation" ON public.project_contributors;
DROP POLICY IF EXISTS "Users can insert invitations for their projects" ON public.project_contributors;
DROP POLICY IF EXISTS "Users can delete invitations for their projects" ON public.project_contributors;
DROP POLICY IF EXISTS "Temporary wide-open policy" ON public.project_contributors;
DROP POLICY IF EXISTS "Wide open policy" ON public.project_contributors;

-- Create a wide-open policy for all operations (temporary)
CREATE POLICY "Wide open policy"
ON public.project_contributors
FOR ALL
USING (true)
WITH CHECK (true);

-- Verify the policy was created
SELECT * FROM pg_policies WHERE tablename = 'project_contributors';
