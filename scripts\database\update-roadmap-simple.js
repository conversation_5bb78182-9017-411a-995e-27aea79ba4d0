// Script to update roadmap items in the database
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function updateRoadmapItems() {
  try {
    // Update bulk validation item
    const { data: bulkValidation, error: bulkValidationError } = await supabase
      .from('roadmap_items')
      .update({ completed: true })
      .eq('item_id', '3.2.6');

    console.log('Bulk validation update result:', bulkValidationError ? bulkValidationError : 'Success');

    // Update validation notifications item
    const { data: validationNotifications, error: validationNotificationsError } = await supabase
      .from('roadmap_items')
      .update({ completed: true })
      .eq('item_id', '3.2.8');

    console.log('Validation notifications update result:', validationNotificationsError ? validationNotificationsError : 'Success');

    console.log('Roadmap updates completed');
  } catch (error) {
    console.error('Error updating roadmap items:', error);
  }
}

// Run the update function
updateRoadmapItems();
