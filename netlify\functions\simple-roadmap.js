// Simple CORS-enabled Roadmap API
// This function returns static roadmap data with CORS headers

exports.handler = async function(event, context) {
  // Set CORS headers for all responses
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Content-Type': 'application/json'
  };
  
  // Handle OPTIONS request for CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 204, // No content
      headers,
      body: ''
    };
  }
  
  try {
    // Static roadmap data
    const roadmapData = [
      {
        id: 1,
        title: "Foundation & User Management",
        timeframe: "Completed",
        expanded: true,
        sections: [
          {
            id: "1.1",
            title: "Project Setup & Configuration",
            tasks: [
              { id: "1.1.1", text: "Finalize tech stack (React, Supabase)", completed: true },
              { id: "1.1.2", text: "Set up development environment", completed: true },
              { id: "1.1.3", text: "Configure Netlify deployment", completed: true }
            ]
          }
        ]
      },
      {
        id: 2,
        title: "Project Creation & Management",
        timeframe: "Phase 1",
        expanded: false,
        sections: [
          {
            id: "2.1",
            title: "Project Wizard",
            tasks: [
              { id: "2.1.1", text: "Design project creation flow", completed: true },
              { id: "2.1.2", text: "Implement project basics form", completed: true },
              { id: "2.1.3", text: "Add team & contributors section", completed: true }
            ]
          }
        ]
      },
      {
        id: 3,
        title: "Contribution Tracking System",
        timeframe: "Phase 2",
        expanded: false,
        sections: [
          {
            id: "3.1",
            title: "Manual Contribution Entry",
            tasks: [
              { id: "3.1.1", text: "Design contribution entry forms", completed: true },
              { id: "3.1.2", text: "Implement time tracking functionality", completed: true },
              { id: "3.1.3", text: "Add task selection from configured types", completed: true },
              { id: "3.1.4", text: "Implement difficulty rating selection", completed: true },
              { id: "3.1.5", text: "Create contribution description field", completed: true },
              { id: "3.1.6", text: "Add date range selection", completed: true },
              { id: "3.1.7", text: "Implement file/asset attachment", completed: false }
            ]
          }
        ]
      },
      {
        id: 4,
        title: "Revenue & Royalty Distribution",
        timeframe: "Phase 2",
        expanded: false,
        sections: [
          {
            id: "4.1",
            title: "Revenue Entry",
            tasks: [
              { id: "4.1.1", text: "Design revenue entry forms", completed: true },
              { id: "4.1.2", text: "Implement revenue source selection", completed: true },
              { id: "4.1.3", text: "Add date range selection", completed: true },
              { id: "4.1.4", text: "Create currency conversion support", completed: true },
              { id: "4.1.5", text: "Implement expense tracking", completed: true },
              { id: "4.1.6", text: "Add receipt/proof upload", completed: true },
              { id: "4.1.7", text: "Create revenue categories", completed: true },
              { id: "4.1.8", text: "Implement revenue notes", completed: true }
            ]
          },
          {
            id: "4.2",
            title: "Royalty Calculation",
            tasks: [
              { id: "4.2.1", text: "Implement equal split calculation", completed: true },
              { id: "4.2.2", text: "Add task-based calculation", completed: true },
              { id: "4.2.3", text: "Implement time-based calculation", completed: true },
              { id: "4.2.4", text: "Add role-based calculation", completed: true },
              { id: "4.2.5", text: "Implement custom CoG model calculation", completed: true },
              { id: "4.2.6", text: "Create calculation preview", completed: true },
              { id: "4.2.7", text: "Add manual adjustment options", completed: true },
              { id: "4.2.8", text: "Implement calculation history", completed: true }
            ]
          }
        ]
      }
    ];
    
    // Calculate stats
    const stats = calculateStats(roadmapData);
    
    // Return the data
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        data: roadmapData,
        stats: stats,
        source: 'simple-function'
      })
    };
  } catch (error) {
    console.error('Error in simple-roadmap function:', error);
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    };
  }
};

// Function to calculate stats
function calculateStats(phases) {
  let totalTasks = 0;
  let completedTasks = 0;
  let phaseStats = [];

  phases.forEach(phase => {
    let phaseTotalTasks = 0;
    let phaseCompletedTasks = 0;
    
    phase.sections.forEach(section => {
      phaseTotalTasks += section.tasks.length;
      phaseCompletedTasks += section.tasks.filter(task => task.completed).length;
    });
    
    totalTasks += phaseTotalTasks;
    completedTasks += phaseCompletedTasks;
    
    phaseStats.push({
      id: phase.id,
      title: phase.title,
      timeframe: phase.timeframe,
      progress: phaseTotalTasks > 0 ? Math.round((phaseCompletedTasks / phaseTotalTasks) * 100) : 0
    });
  });

  return {
    totalTasks,
    completedTasks,
    progressPercentage: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
    phases: phaseStats
  };
}
