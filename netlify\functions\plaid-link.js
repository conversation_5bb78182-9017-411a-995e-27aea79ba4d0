// Plaid Link Token API
// Backend Specialist: Plaid integration for bank account linking
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Plaid configuration
const PLAID_CONFIG = {
  client_id: process.env.PLAID_CLIENT_ID,
  secret: process.env.PLAID_SECRET,
  environment: process.env.PLAID_ENV || 'sandbox', // sandbox, development, production
  products: ['auth', 'transactions', 'transfer'],
  country_codes: ['US'],
  webhook: process.env.PLAID_WEBHOOK_URL || 'https://royalty.technology/.netlify/functions/plaid-webhook'
};

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Create Plaid Link Token
const createLinkToken = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const data = JSON.parse(event.body);
    
    // Get user information for Plaid
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, display_name, email')
      .eq('id', userId)
      .single();

    if (userError) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'User not found' })
      };
    }

    // Prepare Plaid Link Token request
    const linkTokenRequest = {
      client_id: PLAID_CONFIG.client_id,
      secret: PLAID_CONFIG.secret,
      client_name: 'Royaltea Platform',
      country_codes: data.country_codes || PLAID_CONFIG.country_codes,
      language: data.language || 'en',
      user: {
        client_user_id: userId,
        legal_name: user.display_name || user.email,
        email_address: user.email
      },
      products: data.products || PLAID_CONFIG.products,
      webhook: PLAID_CONFIG.webhook,
      redirect_uri: data.redirect_uri || null
    };

    // For now, return a mock response since we don't have Plaid credentials
    // In production, this would make an actual API call to Plaid
    const mockLinkToken = `link-sandbox-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    console.log('Plaid Link Token Request:', linkTokenRequest);
    
    // TODO: Replace with actual Plaid API call
    // const plaidResponse = await fetch('https://production.plaid.com/link/token/create', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(linkTokenRequest)
    // });

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        link_token: mockLinkToken,
        expiration: new Date(Date.now() + 4 * 60 * 60 * 1000).toISOString(), // 4 hours
        request_id: `req-${Date.now()}`,
        environment: PLAID_CONFIG.environment
      })
    };

  } catch (error) {
    console.error('Create link token error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to create link token' })
    };
  }
};

// Exchange Public Token for Access Token
const exchangePublicToken = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const data = JSON.parse(event.body);
    
    if (!data.public_token) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'public_token is required' })
      };
    }

    // Prepare Plaid token exchange request
    const exchangeRequest = {
      client_id: PLAID_CONFIG.client_id,
      secret: PLAID_CONFIG.secret,
      public_token: data.public_token
    };

    console.log('Plaid Token Exchange Request:', exchangeRequest);

    // For now, return mock response
    // TODO: Replace with actual Plaid API call
    const mockAccessToken = `access-sandbox-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const mockItemId = `item-${Date.now()}`;
    
    const mockAccounts = [
      {
        account_id: `account-${Date.now()}-1`,
        balances: {
          available: 1000.00,
          current: 1000.00,
          iso_currency_code: 'USD'
        },
        mask: '0000',
        name: 'Plaid Checking',
        official_name: 'Plaid Gold Standard 0% Interest Checking',
        type: 'depository',
        subtype: 'checking'
      }
    ];

    // Store account information in database (when plaid_accounts table exists)
    try {
      // For now, store in financial_transactions as a record of the connection
      const connectionRecord = {
        company_id: null, // Individual user account
        transaction_type: 'account_connection',
        transaction_category: 'system',
        gross_amount: 0,
        currency: 'USD',
        payee_user_id: userId,
        description: `Plaid account connected: ${mockAccounts[0].name}`,
        reference_number: mockItemId,
        created_by: userId
      };

      const { error: recordError } = await supabase
        .from('financial_transactions')
        .insert([connectionRecord]);

      if (recordError) {
        console.warn('Failed to record account connection:', recordError.message);
      }

    } catch (storeError) {
      console.warn('Account storage error:', storeError.message);
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        access_token: mockAccessToken,
        item_id: mockItemId,
        accounts: mockAccounts,
        request_id: `req-${Date.now()}`
      })
    };

  } catch (error) {
    console.error('Exchange public token error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to exchange public token' })
    };
  }
};

// Get Account Balances
const getAccountBalances = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const data = JSON.parse(event.body);
    
    if (!data.access_token) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'access_token is required' })
      };
    }

    // For now, return mock balance data
    const mockBalances = {
      accounts: [
        {
          account_id: data.account_id || 'account-123',
          balances: {
            available: 1250.75,
            current: 1250.75,
            iso_currency_code: 'USD',
            last_updated_datetime: new Date().toISOString()
          },
          name: 'Plaid Checking',
          type: 'depository',
          subtype: 'checking'
        }
      ],
      request_id: `req-${Date.now()}`
    };

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(mockBalances)
    };

  } catch (error) {
    console.error('Get account balances error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to get account balances' })
    };
  }
};

// Check Payment Capabilities
const checkPaymentCapabilities = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const data = JSON.parse(event.body);
    
    // Return mock capabilities based on account type
    const capabilities = {
      ach: true,
      same_day_ach: true,
      rtp: false, // Real-time payments typically limited
      wire: true,
      international_wire: false,
      max_ach_amount: 500000, // $500k daily limit
      max_wire_amount: 1000000, // $1M daily limit
      fees: {
        ach_standard: 0.25,
        ach_same_day: 1.50,
        wire_domestic: 15.00,
        wire_international: 45.00
      }
    };

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(capabilities)
    };

  } catch (error) {
    console.error('Check payment capabilities error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to check payment capabilities' })
    };
  }
};

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/plaid-link', '');

  try {
    let response;

    if (event.httpMethod === 'POST') {
      if (path === '/create-link-token' || path === '') {
        response = await createLinkToken(event);
      } else if (path === '/exchange-token') {
        response = await exchangePublicToken(event);
      } else if (path === '/balances') {
        response = await getAccountBalances(event);
      } else if (path === '/capabilities') {
        response = await checkPaymentCapabilities(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('Plaid Link API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
