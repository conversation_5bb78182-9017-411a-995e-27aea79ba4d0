// Friend Request Management API
// Backend Specialist: Complete friend/ally request system
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Send Friend Request
const sendFriendRequest = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const data = JSON.parse(event.body);
    
    // Validate required fields
    if (!data.recipient_id && !data.recipient_email) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          error: 'Either recipient_id or recipient_email is required' 
        })
      };
    }

    // Check if user is trying to send request to themselves
    if (data.recipient_id === userId) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Cannot send friend request to yourself' })
      };
    }

    // Check if users are already connected
    if (data.recipient_id) {
      const { data: existingConnection } = await supabase
        .from('user_allies')
        .select('id, status')
        .or(`and(user_id.eq.${userId},ally_id.eq.${data.recipient_id}),and(user_id.eq.${data.recipient_id},ally_id.eq.${userId})`)
        .single();

      if (existingConnection) {
        const statusMessage = existingConnection.status === 'accepted' 
          ? 'You are already allies with this user'
          : `Friend request already ${existingConnection.status}`;
        
        return {
          statusCode: 400,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ error: statusMessage })
        };
      }

      // Check for existing pending request
      const { data: existingRequest } = await supabase
        .from('friend_requests')
        .select('id, status')
        .eq('sender_id', userId)
        .eq('recipient_id', data.recipient_id)
        .eq('status', 'pending')
        .single();

      if (existingRequest) {
        return {
          statusCode: 400,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ error: 'Friend request already sent to this user' })
        };
      }
    }

    // Create friend request
    const requestData = {
      sender_id: userId,
      recipient_id: data.recipient_id || null,
      recipient_email: data.recipient_email || null,
      message: data.message || '',
      request_type: data.request_type || 'friend',
      status: 'pending'
    };

    const { data: request, error: requestError } = await supabase
      .from('friend_requests')
      .insert([requestData])
      .select(`
        *,
        sender:users!friend_requests_sender_id_fkey(
          id,
          display_name,
          avatar_url
        ),
        recipient:users!friend_requests_recipient_id_fkey(
          id,
          display_name,
          avatar_url
        )
      `)
      .single();

    if (requestError) {
      throw new Error(`Failed to send friend request: ${requestError.message}`);
    }

    // Send notification to recipient (if they exist on platform)
    if (data.recipient_id) {
      await notifyFriendRequest(request);
    }

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        request: {
          id: request.id,
          recipient_id: request.recipient_id,
          recipient_email: request.recipient_email,
          message: request.message,
          request_type: request.request_type,
          status: request.status,
          sent_at: request.sent_at,
          expires_at: request.expires_at,
          sender: request.sender,
          recipient: request.recipient
        }
      })
    };

  } catch (error) {
    console.error('Send friend request error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to send friend request' })
    };
  }
};

// Get Friend Requests (Incoming and Outgoing)
const getFriendRequests = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const type = queryParams.get('type') || 'all'; // 'incoming', 'outgoing', 'all'
    const status = queryParams.get('status') || 'pending';

    let query = supabase
      .from('friend_requests')
      .select(`
        *,
        sender:users!friend_requests_sender_id_fkey(
          id,
          display_name,
          avatar_url,
          is_premium
        ),
        recipient:users!friend_requests_recipient_id_fkey(
          id,
          display_name,
          avatar_url,
          is_premium
        )
      `);

    // Filter by type
    if (type === 'incoming') {
      query = query.eq('recipient_id', userId);
    } else if (type === 'outgoing') {
      query = query.eq('sender_id', userId);
    } else {
      query = query.or(`sender_id.eq.${userId},recipient_id.eq.${userId}`);
    }

    // Filter by status
    if (status !== 'all') {
      query = query.eq('status', status);
    }

    const { data: requests, error: requestsError } = await query
      .order('sent_at', { ascending: false })
      .limit(50);

    if (requestsError) {
      throw new Error(`Failed to fetch friend requests: ${requestsError.message}`);
    }

    // Transform requests for response
    const transformedRequests = requests.map(request => ({
      id: request.id,
      sender: request.sender,
      recipient: request.recipient,
      recipient_email: request.recipient_email,
      message: request.message,
      request_type: request.request_type,
      status: request.status,
      sent_at: request.sent_at,
      responded_at: request.responded_at,
      expires_at: request.expires_at,
      direction: request.sender_id === userId ? 'outgoing' : 'incoming'
    }));

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        requests: transformedRequests,
        total: transformedRequests.length
      })
    };

  } catch (error) {
    console.error('Get friend requests error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch friend requests' })
    };
  }
};

// Respond to Friend Request (Accept/Decline)
const respondToFriendRequest = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const requestId = event.path.split('/').pop();
    const data = JSON.parse(event.body);
    
    // Validate action
    if (!['accept', 'decline'].includes(data.action)) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Action must be accept or decline' })
      };
    }

    // Get friend request
    const { data: request, error: requestError } = await supabase
      .from('friend_requests')
      .select('*')
      .eq('id', requestId)
      .eq('recipient_id', userId)
      .eq('status', 'pending')
      .single();

    if (requestError) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Friend request not found or already responded to' })
      };
    }

    const newStatus = data.action === 'accept' ? 'accepted' : 'declined';
    
    // Update friend request status
    const { error: updateError } = await supabase
      .from('friend_requests')
      .update({ 
        status: newStatus,
        responded_at: new Date().toISOString()
      })
      .eq('id', requestId);

    if (updateError) {
      throw new Error(`Failed to update friend request: ${updateError.message}`);
    }

    // If accepted, create ally connection
    if (data.action === 'accept') {
      const allyData = {
        user_id: userId,
        ally_id: request.sender_id,
        status: 'accepted',
        connection_type: request.request_type,
        created_by: request.sender_id,
        request_message: request.message,
        accepted_at: new Date().toISOString()
      };

      const { error: allyError } = await supabase
        .from('user_allies')
        .insert([allyData]);

      if (allyError) {
        console.error('Failed to create ally connection:', allyError.message);
        // Don't fail the request, just log the error
      }
    }

    // Notify sender of response
    await notifyFriendRequestResponse(request, data.action);

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        request: {
          id: requestId,
          status: newStatus,
          responded_at: new Date().toISOString(),
          action: data.action
        }
      })
    };

  } catch (error) {
    console.error('Respond to friend request error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to respond to friend request' })
    };
  }
};

// Cancel Friend Request
const cancelFriendRequest = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const requestId = event.path.split('/').pop();

    // Update request status to cancelled
    const { data: request, error: updateError } = await supabase
      .from('friend_requests')
      .update({ status: 'cancelled' })
      .eq('id', requestId)
      .eq('sender_id', userId)
      .eq('status', 'pending')
      .select()
      .single();

    if (updateError) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Friend request not found or cannot be cancelled' })
      };
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        request: {
          id: requestId,
          status: 'cancelled'
        }
      })
    };

  } catch (error) {
    console.error('Cancel friend request error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to cancel friend request' })
    };
  }
};

// Helper notification functions
const notifyFriendRequest = async (request) => {
  try {
    console.log('Friend request notification:', {
      request_id: request.id,
      sender: request.sender?.display_name,
      recipient: request.recipient?.display_name || request.recipient_email
    });
    
    // TODO: Implement actual notification system
    // - Email notification
    // - In-app notification
    // - Push notification
    
  } catch (error) {
    console.error('Notify friend request error:', error);
  }
};

const notifyFriendRequestResponse = async (request, action) => {
  try {
    console.log('Friend request response notification:', {
      request_id: request.id,
      action: action
    });
    
    // TODO: Implement notification to sender
    
  } catch (error) {
    console.error('Notify response error:', error);
  }
};

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/friend-requests', '');

  try {
    let response;

    if (event.httpMethod === 'POST') {
      if (path === '' || path === '/') {
        response = await sendFriendRequest(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'GET') {
      response = await getFriendRequests(event);
    } else if (event.httpMethod === 'PUT') {
      if (path.includes('/respond')) {
        response = await respondToFriendRequest(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'DELETE') {
      response = await cancelFriendRequest(event);
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('Friend Requests API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
