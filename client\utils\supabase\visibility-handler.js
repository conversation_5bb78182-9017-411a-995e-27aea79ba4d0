/**
 * Utility functions to handle page visibility for Supabase operations
 */

// Queue for operations that need to be performed when the page becomes visible again
let operationQueue = [];

// Flag to track if the visibility event listener is already set up
let listenerInitialized = false;

/**
 * Initialize the visibility change listener
 */
const initVisibilityListener = () => {
  if (listenerInitialized) return;
  
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
      // Process queued operations when page becomes visible
      processQueue();
    }
  });
  
  listenerInitialized = true;
};

/**
 * Process the operation queue
 */
const processQueue = () => {
  if (operationQueue.length === 0) return;
  
  console.log(`Processing ${operationQueue.length} queued operations`);
  
  // Process each operation in the queue
  const operations = [...operationQueue];
  operationQueue = [];
  
  operations.forEach(operation => {
    try {
      operation();
    } catch (error) {
      console.error('Error processing queued operation:', error);
    }
  });
};

/**
 * Queue an operation to be performed when the page becomes visible
 * @param {Function} operation - The operation to queue
 */
export const queueOperation = (operation) => {
  if (!listenerInitialized) {
    initVisibilityListener();
  }
  
  if (document.visibilityState === 'visible') {
    // If page is visible, perform the operation immediately
    operation();
  } else {
    // Otherwise, queue it for later
    operationQueue.push(operation);
  }
};

/**
 * Clear the operation queue
 */
export const clearQueue = () => {
  operationQueue = [];
};

/**
 * Check if the page is currently visible
 * @returns {boolean} - True if the page is visible, false otherwise
 */
export const isPageVisible = () => {
  return document.visibilityState === 'visible';
};

export default {
  queueOperation,
  clearQueue,
  isPageVisible
};
