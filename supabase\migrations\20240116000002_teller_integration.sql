-- Migration: Teller Integration
-- Description: Complete payment system with Teller integration for bank account linking, ACH transfers, and escrow management
-- Created: 2024-01-16
-- Updated: 2024-12-16 (Migrated from Plaid to Tell<PERSON>)

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types for payment system
CREATE TYPE account_type AS ENUM (
  'checking',
  'savings',
  'credit_card',
  'investment',
  'loan',
  'other'
);

CREATE TYPE payment_method_type AS ENUM (
  'ach_standard',
  'ach_same_day',
  'wire',
  'rtp',
  'check'
);

CREATE TYPE transaction_status AS ENUM (
  'pending',
  'processing',
  'completed',
  'failed',
  'cancelled',
  'reversed'
);

CREATE TYPE escrow_status AS ENUM (
  'active',
  'released',
  'disputed',
  'cancelled'
);

-- Teller linked accounts table
CREATE TABLE teller_accounts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Teller identifiers
  teller_account_id TEXT NOT NULL,
  teller_item_id TEXT NOT NULL,
  teller_access_token TEXT NOT NULL,
  
  -- Account details
  account_name TEXT NOT NULL,
  account_type account_type NOT NULL,
  account_subtype TEXT,
  institution_name TEXT,
  institution_id TEXT,
  
  -- Account capabilities
  supports_ach BOOLEAN DEFAULT true,
  supports_same_day_ach BOOLEAN DEFAULT false,
  supports_rtp BOOLEAN DEFAULT false,
  supports_wire BOOLEAN DEFAULT false,
  
  -- Account status
  is_verified BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  verification_method TEXT,
  
  -- Balance information (cached)
  available_balance DECIMAL(15,2),
  current_balance DECIMAL(15,2),
  balance_last_updated TIMESTAMP WITH TIME ZONE,
  
  -- Account metadata
  account_mask TEXT,
  routing_number TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Payment transactions table
CREATE TABLE payment_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Transaction parties
  from_user_id UUID NOT NULL REFERENCES auth.users(id),
  to_user_id UUID NOT NULL REFERENCES auth.users(id),
  from_account_id UUID REFERENCES teller_accounts(id),
  to_account_id UUID REFERENCES teller_accounts(id),
  
  -- Transaction details
  amount DECIMAL(15,2) NOT NULL,
  currency TEXT DEFAULT 'USD',
  payment_method payment_method_type DEFAULT 'ach_standard',
  
  -- Transaction status
  status transaction_status DEFAULT 'pending',
  teller_transaction_id TEXT,
  
  -- Transaction metadata
  description TEXT,
  reference_id TEXT, -- Link to project, royalty, etc.
  reference_type TEXT, -- 'project_payment', 'royalty_distribution', 'escrow_release'
  
  -- Timing information
  initiated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expected_settlement_date DATE,
  actual_settlement_date DATE,
  
  -- Fee information
  teller_fee DECIMAL(10,2) DEFAULT 0,
  platform_fee DECIMAL(10,2) DEFAULT 0,
  total_fees DECIMAL(10,2) DEFAULT 0,
  
  -- Error handling
  failure_reason TEXT,
  retry_count INTEGER DEFAULT 0,
  max_retries INTEGER DEFAULT 3,
  
  -- Compliance and audit
  compliance_data JSONB DEFAULT '{}'::jsonb,
  audit_trail JSONB DEFAULT '[]'::jsonb,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Escrow accounts table
CREATE TABLE escrow_accounts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Escrow details
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  escrow_name TEXT NOT NULL,
  total_amount DECIMAL(15,2) NOT NULL,
  current_balance DECIMAL(15,2) NOT NULL DEFAULT 0,
  
  -- Escrow parties
  depositor_user_id UUID NOT NULL REFERENCES auth.users(id),
  beneficiary_user_id UUID NOT NULL REFERENCES auth.users(id),
  
  -- Escrow conditions
  release_conditions JSONB NOT NULL DEFAULT '{}'::jsonb,
  milestone_requirements JSONB DEFAULT '[]'::jsonb,
  
  -- Escrow status
  status escrow_status DEFAULT 'active',
  
  -- Timing
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expected_release_date DATE,
  actual_release_date DATE,
  
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Escrow releases table
CREATE TABLE escrow_releases (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  escrow_account_id UUID NOT NULL REFERENCES escrow_accounts(id) ON DELETE CASCADE,
  
  -- Release details
  recipient_user_id UUID NOT NULL REFERENCES auth.users(id),
  recipient_account_id UUID NOT NULL REFERENCES teller_accounts(id),
  amount DECIMAL(15,2) NOT NULL,
  
  -- Release metadata
  release_reason TEXT,
  milestone_reference TEXT,
  approved_by UUID REFERENCES auth.users(id),
  approved_at TIMESTAMP WITH TIME ZONE,
  
  -- Payment processing
  payment_transaction_id UUID REFERENCES payment_transactions(id),
  payment_method payment_method_type DEFAULT 'ach_standard',
  
  status TEXT DEFAULT 'pending', -- pending, approved, processing, completed, failed
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Payment preferences table
CREATE TABLE payment_preferences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Default payment methods
  default_payment_account_id UUID REFERENCES teller_accounts(id),
  preferred_payment_method payment_method_type DEFAULT 'ach_standard',
  
  -- Notification preferences
  notify_on_payment_received BOOLEAN DEFAULT true,
  notify_on_payment_sent BOOLEAN DEFAULT true,
  notify_on_escrow_release BOOLEAN DEFAULT true,
  
  -- Auto-payment settings
  auto_accept_payments BOOLEAN DEFAULT false,
  auto_accept_threshold DECIMAL(10,2),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id)
);

-- Payment routing rules table
CREATE TABLE payment_routing_rules (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Rule conditions
  rule_name TEXT NOT NULL,
  amount_min DECIMAL(15,2),
  amount_max DECIMAL(15,2),
  payment_type TEXT, -- 'royalty', 'project_payment', 'escrow_release'
  
  -- Rule actions
  preferred_account_id UUID REFERENCES teller_accounts(id),
  preferred_method payment_method_type,
  
  -- Rule metadata
  is_active BOOLEAN DEFAULT true,
  priority INTEGER DEFAULT 0,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Teller webhooks table for logging and debugging
CREATE TABLE teller_webhooks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Webhook details
  webhook_type TEXT NOT NULL,
  webhook_code TEXT,
  item_id TEXT,
  
  -- Webhook data
  webhook_data JSONB NOT NULL,
  
  -- Processing status
  processed BOOLEAN DEFAULT false,
  processed_at TIMESTAMP WITH TIME ZONE,
  processing_error TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_teller_accounts_user_id ON teller_accounts(user_id);
CREATE INDEX idx_teller_accounts_teller_item_id ON teller_accounts(teller_item_id);
CREATE INDEX idx_payment_transactions_from_user ON payment_transactions(from_user_id);
CREATE INDEX idx_payment_transactions_to_user ON payment_transactions(to_user_id);
CREATE INDEX idx_payment_transactions_status ON payment_transactions(status);
CREATE INDEX idx_payment_transactions_reference ON payment_transactions(reference_id, reference_type);
CREATE INDEX idx_escrow_accounts_project_id ON escrow_accounts(project_id);
CREATE INDEX idx_escrow_releases_escrow_id ON escrow_releases(escrow_account_id);
CREATE INDEX idx_payment_preferences_user_id ON payment_preferences(user_id);
CREATE INDEX idx_payment_routing_rules_user_id ON payment_routing_rules(user_id);
CREATE INDEX idx_teller_webhooks_processed ON teller_webhooks(processed);

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_teller_accounts_updated_at BEFORE UPDATE ON teller_accounts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_payment_transactions_updated_at BEFORE UPDATE ON payment_transactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_escrow_accounts_updated_at BEFORE UPDATE ON escrow_accounts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_escrow_releases_updated_at BEFORE UPDATE ON escrow_releases FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_payment_preferences_updated_at BEFORE UPDATE ON payment_preferences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_payment_routing_rules_updated_at BEFORE UPDATE ON payment_routing_rules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE teller_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE escrow_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE escrow_releases ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_preferences ENABLE ROW LEVEL SECURITY;

-- RLS policies
CREATE POLICY "Users can manage their own accounts" ON teller_accounts FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view their payment transactions" ON payment_transactions FOR SELECT USING (
  auth.uid() = from_user_id OR auth.uid() = to_user_id
);
CREATE POLICY "Users can create payment transactions" ON payment_transactions FOR INSERT WITH CHECK (
  auth.uid() = from_user_id
);
CREATE POLICY "Users can view escrow accounts they're involved in" ON escrow_accounts FOR SELECT USING (
  auth.uid() = depositor_user_id OR auth.uid() = beneficiary_user_id
);
CREATE POLICY "Users can manage their payment preferences" ON payment_preferences FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view escrow releases they're involved in" ON escrow_releases FOR SELECT USING (
  auth.uid() = recipient_user_id OR 
  auth.uid() IN (SELECT depositor_user_id FROM escrow_accounts WHERE id = escrow_account_id) OR
  auth.uid() IN (SELECT beneficiary_user_id FROM escrow_accounts WHERE id = escrow_account_id)
);

-- Table comments
COMMENT ON TABLE teller_accounts IS 'Stores Teller-linked bank accounts with capabilities and verification status';
COMMENT ON TABLE payment_transactions IS 'Records all payment transactions processed through Teller';
COMMENT ON TABLE escrow_accounts IS 'Manages escrow accounts for project funding and milestone releases';
COMMENT ON TABLE escrow_releases IS 'Tracks individual releases from escrow accounts';
COMMENT ON TABLE payment_preferences IS 'User preferences for payment methods and notifications';
COMMENT ON TABLE payment_routing_rules IS 'Rules for automatically selecting optimal payment methods';
COMMENT ON TABLE teller_webhooks IS 'Log of Teller webhook events for processing and debugging';
