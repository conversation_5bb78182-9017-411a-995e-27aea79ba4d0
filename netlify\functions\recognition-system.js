// Recognition System API
// Backend Specialist: User recognition, achievements, and ranking system
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Get User Achievements
const getUserAchievements = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const targetUserId = queryParams.get('user_id') || userId;
    const achievementType = queryParams.get('type');
    const featuredOnly = queryParams.get('featured_only') === 'true';

    let query = supabase
      .from('user_achievements')
      .select(`
        id,
        achievement_type,
        achievement_level,
        achievement_title,
        achievement_description,
        achievement_score,
        earned_at,
        expires_at,
        is_featured,
        is_public,
        verified_by,
        verified_at,
        evidence_data
      `)
      .eq('user_id', targetUserId)
      .order('earned_at', { ascending: false });

    // Apply filters
    if (achievementType) {
      query = query.eq('achievement_type', achievementType);
    }
    if (featuredOnly) {
      query = query.eq('is_featured', true);
    }

    // If viewing another user's achievements, only show public ones
    if (targetUserId !== userId) {
      query = query.eq('is_public', true);
    }

    const { data: achievements, error: achievementsError } = await query;

    if (achievementsError) {
      throw new Error(`Failed to fetch achievements: ${achievementsError.message}`);
    }

    // Group achievements by type for better organization
    const achievementsByType = {};
    achievements?.forEach(achievement => {
      if (!achievementsByType[achievement.achievement_type]) {
        achievementsByType[achievement.achievement_type] = [];
      }
      achievementsByType[achievement.achievement_type].push(achievement);
    });

    // Calculate achievement statistics
    const stats = {
      total_achievements: achievements?.length || 0,
      featured_achievements: achievements?.filter(a => a.is_featured).length || 0,
      verified_achievements: achievements?.filter(a => a.verified_at).length || 0,
      achievement_types: Object.keys(achievementsByType).length,
      highest_level: Math.max(...(achievements?.map(a => a.achievement_level) || [0])),
      total_score: achievements?.reduce((sum, a) => sum + (a.achievement_score || 0), 0) || 0
    };

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        achievements: achievements || [],
        achievements_by_type: achievementsByType,
        statistics: stats
      })
    };

  } catch (error) {
    console.error('Get user achievements error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch achievements' })
    };
  }
};

// Get Recognition Rankings
const getRecognitionRankings = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const category = queryParams.get('category') || 'top_allies';
    const period = queryParams.get('period') || 'monthly';
    const limit = parseInt(queryParams.get('limit') || '50');

    // Get current period rankings
    const { data: rankings, error: rankingsError } = await supabase
      .from('recognition_rankings')
      .select(`
        user_id,
        current_rank,
        previous_rank,
        rank_change,
        score,
        percentile,
        ranking_data,
        user:users!recognition_rankings_user_id_fkey(
          id,
          display_name,
          avatar_url,
          is_premium
        )
      `)
      .eq('ranking_category', category)
      .eq('ranking_period', period)
      .order('current_rank', { ascending: true })
      .limit(limit);

    if (rankingsError) {
      throw new Error(`Failed to fetch rankings: ${rankingsError.message}`);
    }

    // Get current user's ranking
    const userRanking = rankings?.find(r => r.user_id === userId);

    // Calculate ranking statistics
    const stats = {
      total_participants: rankings?.length || 0,
      user_rank: userRanking?.current_rank || null,
      user_percentile: userRanking?.percentile || null,
      user_score: userRanking?.score || null,
      rank_change: userRanking?.rank_change || null,
      top_score: rankings?.[0]?.score || 0,
      average_score: rankings?.reduce((sum, r) => sum + r.score, 0) / (rankings?.length || 1)
    };

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        rankings: rankings || [],
        user_ranking: userRanking,
        statistics: stats,
        category,
        period
      })
    };

  } catch (error) {
    console.error('Get recognition rankings error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch rankings' })
    };
  }
};

// Award Achievement
const awardAchievement = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const data = JSON.parse(event.body);
    
    if (!data.user_id || !data.achievement_type || !data.achievement_title) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          error: 'user_id, achievement_type, and achievement_title are required' 
        })
      };
    }

    // Validate achievement type
    const validTypes = [
      'top_ally', 'collaboration_master', 'skill_expert', 'project_leader',
      'mentor', 'innovator', 'reliable_contributor', 'network_builder',
      'quality_champion', 'deadline_hero', 'team_player', 'problem_solver'
    ];

    if (!validTypes.includes(data.achievement_type)) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Invalid achievement type' })
      };
    }

    // Check if achievement already exists
    const { data: existingAchievement } = await supabase
      .from('user_achievements')
      .select('id')
      .eq('user_id', data.user_id)
      .eq('achievement_type', data.achievement_type)
      .eq('achievement_level', data.achievement_level || 1)
      .single();

    if (existingAchievement) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Achievement already exists for this user' })
      };
    }

    // Create achievement
    const achievementData = {
      user_id: data.user_id,
      achievement_type: data.achievement_type,
      achievement_level: data.achievement_level || 1,
      achievement_title: data.achievement_title,
      achievement_description: data.achievement_description || '',
      achievement_score: data.achievement_score || 0,
      criteria_met: data.criteria_met || {},
      evidence_data: data.evidence_data || {},
      is_featured: data.is_featured || false,
      is_public: data.is_public !== false, // Default to true
      verified_by: userId, // System verification
      verified_at: new Date().toISOString()
    };

    const { data: achievement, error: achievementError } = await supabase
      .from('user_achievements')
      .insert([achievementData])
      .select()
      .single();

    if (achievementError) {
      throw new Error(`Failed to award achievement: ${achievementError.message}`);
    }

    // Create activity feed entry
    await createAchievementActivity(achievement);

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ achievement })
    };

  } catch (error) {
    console.error('Award achievement error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to award achievement' })
    };
  }
};

// Get Top Allies
const getTopAllies = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const period = queryParams.get('period') || 'monthly';
    const category = queryParams.get('category') || 'collaboration_score';
    const limit = parseInt(queryParams.get('limit') || '20');

    // Get top performers from rankings
    const { data: topPerformers, error: performersError } = await supabase
      .from('recognition_rankings')
      .select(`
        user_id,
        current_rank,
        score,
        ranking_data,
        user:users!recognition_rankings_user_id_fkey(
          id,
          display_name,
          avatar_url,
          is_premium
        )
      `)
      .eq('ranking_category', category)
      .eq('ranking_period', period)
      .order('current_rank', { ascending: true })
      .limit(limit);

    if (performersError) {
      throw new Error(`Failed to fetch top performers: ${performersError.message}`);
    }

    // Enhance with additional metrics
    const enhancedPerformers = await Promise.all(
      (topPerformers || []).map(async (performer) => {
        // Get recent achievements
        const { data: recentAchievements } = await supabase
          .from('user_achievements')
          .select('achievement_type, achievement_level, earned_at')
          .eq('user_id', performer.user_id)
          .eq('is_public', true)
          .order('earned_at', { ascending: false })
          .limit(3);

        // Get collaboration metrics
        const { data: metrics } = await supabase
          .from('collaboration_metrics')
          .select('collaboration_success_rate, total_collaborations, network_growth_rate')
          .eq('user_id', performer.user_id)
          .eq('metric_period', period)
          .order('period_start', { ascending: false })
          .limit(1)
          .single();

        return {
          ...performer,
          recent_achievements: recentAchievements || [],
          collaboration_metrics: metrics,
          achievement_count: recentAchievements?.length || 0
        };
      })
    );

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        top_allies: enhancedPerformers,
        category,
        period,
        total: enhancedPerformers.length
      })
    };

  } catch (error) {
    console.error('Get top allies error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch top allies' })
    };
  }
};

// Update Achievement
const updateAchievement = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const achievementId = event.path.split('/').pop();
    const data = JSON.parse(event.body);

    // Verify user owns the achievement
    const { data: achievement, error: achievementError } = await supabase
      .from('user_achievements')
      .select('user_id')
      .eq('id', achievementId)
      .single();

    if (achievementError) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Achievement not found' })
      };
    }

    if (achievement.user_id !== userId) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Cannot update another user\'s achievement' })
      };
    }

    // Update allowed fields
    const updateData = {};
    if (data.is_featured !== undefined) updateData.is_featured = data.is_featured;
    if (data.is_public !== undefined) updateData.is_public = data.is_public;
    updateData.updated_at = new Date().toISOString();

    const { data: updatedAchievement, error: updateError } = await supabase
      .from('user_achievements')
      .update(updateData)
      .eq('id', achievementId)
      .select()
      .single();

    if (updateError) {
      throw new Error(`Failed to update achievement: ${updateError.message}`);
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ achievement: updatedAchievement })
    };

  } catch (error) {
    console.error('Update achievement error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to update achievement' })
    };
  }
};

// Helper function to create achievement activity
const createAchievementActivity = async (achievement) => {
  try {
    const activityData = {
      activity_type: 'achievement_earned',
      activity_title: `Achievement Earned: ${achievement.achievement_title}`,
      activity_description: achievement.achievement_description,
      actor_id: achievement.user_id,
      visibility: achievement.is_public ? 'public' : 'private',
      metadata: {
        achievement_id: achievement.id,
        achievement_type: achievement.achievement_type,
        achievement_level: achievement.achievement_level,
        achievement_score: achievement.achievement_score
      }
    };

    await supabase
      .from('activity_feeds')
      .insert([activityData]);

  } catch (error) {
    console.error('Create achievement activity error:', error);
  }
};

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/recognition-system', '');

  try {
    let response;

    if (event.httpMethod === 'GET') {
      if (path === '/achievements' || path === '/achievements/') {
        response = await getUserAchievements(event);
      } else if (path === '/rankings' || path === '/rankings/') {
        response = await getRecognitionRankings(event);
      } else if (path === '/top-allies' || path === '/top-allies/') {
        response = await getTopAllies(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'POST') {
      if (path === '/achievements' || path === '/achievements/') {
        response = await awardAchievement(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'PUT') {
      if (path.includes('/achievements/')) {
        response = await updateAchievement(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('Recognition System API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
