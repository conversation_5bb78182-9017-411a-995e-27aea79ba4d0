# Supabase Database Access Information

## Project Details
- Project Reference: `hqqlrrqvjcetoxbdjgzx`
- Supabase URL: `https://hqqlrrqvjcetoxbdjgzx.supabase.co`
- Custom Domain: `https://auth.royalty.technology`

## Access Methods

### Supabase CLI
To use the Supabase CLI for database migrations:

1. Set the access token:
```powershell
$env:SUPABASE_ACCESS_TOKEN = "********************************************"
```

2. Link the project:
```powershell
npx supabase link --project-ref hqqlrrqvjcetoxbdjgzx
```

3. Push migrations (will prompt for database password):
```powershell
npx supabase db push
```

4. Database password: `E6welg22749i5QFG`

### JavaScript Client
To access the database programmatically:

```javascript
const { createClient } = require('@supabase/supabase-js');
const config = require('./supabase-assistant-config');

// Initialize Supabase client
const supabaseUrl = "https://hqqlrrqvjcetoxbdjgzx.supabase.co";
const supabaseKey = config.supabaseKey;
const supabase = createClient(supabaseUrl, supabaseKey);

// Example query
async function queryDatabase() {
  const { data, error } = await supabase
    .from('table_name')
    .select('*');
  
  if (error) {
    console.error('Error:', error);
    return;
  }
  
  console.log('Data:', data);
}
```

## Migration Files
- Migration files should follow the naming pattern: `<timestamp>_name.sql`
- Example: `20240414000001_add_category_to_contributions.sql`

## Important Notes
- Always use the Supabase CLI for database migrations when possible
- For quick checks or simple operations, the JavaScript client can be used
- Keep sensitive information secure and do not commit it to public repositories
