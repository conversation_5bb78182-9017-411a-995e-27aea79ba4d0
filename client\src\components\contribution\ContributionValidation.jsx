import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { validateContribution, getValidationHistory } from '../../utils/validationHandler';
import { toast } from 'react-hot-toast';
import { Button } from '../ui/heroui';

/**
 * Component for validating contributions
 * @param {Object} props - Component props
 * @param {string} props.contributionId - ID of the contribution to validate
 * @param {string} props.projectId - ID of the project the contribution belongs to
 * @param {string} props.currentStatus - Current validation status of the contribution
 * @param {Function} props.onValidationComplete - Callback function when validation is complete
 */
const ContributionValidation = ({
  contributionId,
  projectId,
  currentStatus = 'pending',
  onValidationComplete
}) => {
  const { currentUser } = useContext(UserContext);
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const [validating, setValidating] = useState(false);
  const [feedback, setFeedback] = useState('');
  const [validationHistory, setValidationHistory] = useState([]);
  const [showHistory, setShowHistory] = useState(false);

  // Check if the current user is a project admin
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!currentUser || !projectId) {
        setLoading(false);
        return;
      }

      try {
        const { data, error } = await supabase
          .from('project_contributors')
          .select('permission_level, is_admin')
          .eq('project_id', projectId)
          .eq('user_id', currentUser.id)
          .single();

        if (error) {
          console.error('Error checking admin status:', error);
          setIsAdmin(false);
        } else {
          setIsAdmin(
            data.is_admin ||
            ['Owner', 'Admin'].includes(data.permission_level)
          );
        }
      } catch (error) {
        console.error('Error in checkAdminStatus:', error);
        setIsAdmin(false);
      } finally {
        setLoading(false);
      }
    };

    checkAdminStatus();
  }, [currentUser, projectId]);

  // Fetch validation history
  useEffect(() => {
    const fetchValidationHistory = async () => {
      if (!contributionId) return;

      try {
        const { success, data } = await getValidationHistory(contributionId);
        if (success && data) {
          setValidationHistory(data);
        }
      } catch (error) {
        console.error('Error fetching validation history:', error);
      }
    };

    fetchValidationHistory();
  }, [contributionId]);

  // Handle validation action
  const handleValidate = async (status) => {
    if (!contributionId || !isAdmin) return;

    // Require feedback for rejections and change requests
    if ((status === 'rejected' || status === 'pending_changes') && !feedback.trim()) {
      toast.error(`Please provide feedback when ${status === 'rejected' ? 'rejecting' : 'requesting changes for'} a contribution`);
      return;
    }

    setValidating(true);
    try {
      const result = await validateContribution(contributionId, status, feedback);

      if (result.success) {
        toast.success(`Contribution ${status.replace('_', ' ')}`);

        // Refresh validation history
        const { success, data } = await getValidationHistory(contributionId);
        if (success && data) {
          setValidationHistory(data);
        }

        // Call the callback function if provided
        if (onValidationComplete) {
          onValidationComplete(status);
        }
      } else {
        toast.error(result.error || 'Validation failed');
      }
    } catch (error) {
      console.error('Error validating contribution:', error);
      toast.error(error.message || 'Validation failed');
    } finally {
      setValidating(false);
      // Only clear feedback after successful approval
      if (status === 'approved') {
        setFeedback('');
      }
    }
  };

  // Render validation status badge
  const renderStatusBadge = (status) => {
    const statusClasses = {
      pending: 'badge-warning',
      approved: 'badge-success',
      rejected: 'badge-danger',
      pending_changes: 'badge-info'
    };

    const statusLabels = {
      pending: 'Pending',
      approved: 'Approved',
      rejected: 'Rejected',
      pending_changes: 'Changes Requested'
    };

    return (
      <span className={`validation-badge ${statusClasses[status] || 'badge-secondary'}`}>
        {statusLabels[status] || status}
      </span>
    );
  };

  // If loading, show loading indicator
  if (loading) {
    return <div className="validation-loading">Loading validation status...</div>;
  }

  return (
    <div className="contribution-validation">
      <div className="validation-header">
        <h5 className="validation-title">Validation</h5>
        <div className="validation-status">
          Current Status: {renderStatusBadge(currentStatus)}
        </div>
      </div>

      {isAdmin ? (
        <div className="validation-actions">
          <div className="feedback-input">
            <label htmlFor="validation-feedback">
              Feedback {currentStatus === 'rejected' || currentStatus === 'pending_changes' ?
                '(required for rejected/changes requested)' : '(optional for approval)'}:
            </label>
            <textarea
              id="validation-feedback"
              className={`form-control ${currentStatus === 'rejected' || currentStatus === 'pending_changes' ? 'feedback-required' : ''}`}
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              placeholder={currentStatus === 'rejected' || currentStatus === 'pending_changes' ?
                "Please explain why this contribution was rejected or needs changes..." :
                "Provide feedback about this contribution..."}
              disabled={validating}
            />
            {(currentStatus === 'rejected' || currentStatus === 'pending_changes') && !feedback.trim() && (
              <div className="feedback-warning">
                <i className="bi bi-exclamation-triangle"></i>
                Feedback is required when rejecting or requesting changes
              </div>
            )}
          </div>

          <div className="flex gap-2 flex-wrap">
            <Button
              variant="success"
              onClick={() => handleValidate('approved')}
              disabled={validating || currentStatus === 'approved'}
            >
              {validating ? 'Processing...' : 'Approve'}
            </Button>
            <Button
              variant="destructive"
              onClick={() => handleValidate('rejected')}
              disabled={validating || currentStatus === 'rejected'}
            >
              {validating ? 'Processing...' : 'Reject'}
            </Button>
            <Button
              variant="info"
              onClick={() => handleValidate('pending_changes')}
              disabled={validating || currentStatus === 'pending_changes'}
            >
              {validating ? 'Processing...' : 'Request Changes'}
            </Button>
          </div>
        </div>
      ) : (
        <div className="validation-message">
          <p>Only project administrators can validate contributions.</p>
        </div>
      )}

      {validationHistory.length > 0 && (
        <div className="validation-history-section">
          <div className="history-header" onClick={() => setShowHistory(!showHistory)}>
            <h6>Validation History ({validationHistory.length})</h6>
            <button className="btn-toggle">
              <i className={`bi bi-chevron-${showHistory ? 'up' : 'down'}`}></i>
            </button>
          </div>

          {showHistory && (
            <div className="validation-history">
              {validationHistory.map((entry, index) => (
                <div key={index} className="history-entry">
                  <div className="history-entry-header">
                    <div className="history-entry-status">
                      {renderStatusBadge(entry.status)}
                    </div>
                    <div className="history-entry-date">
                      {new Date(entry.created_at).toLocaleString()}
                    </div>
                  </div>
                  <div className="history-entry-validator">
                    Validated by: {entry.validator?.display_name || entry.validator?.email || 'Unknown'}
                  </div>
                  {entry.feedback && (
                    <div className="history-entry-feedback">
                      <p>{entry.feedback}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ContributionValidation;
