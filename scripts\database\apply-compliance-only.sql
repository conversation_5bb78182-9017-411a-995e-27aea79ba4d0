-- Apply only compliance migrations
-- Day 3 - Apply critical business compliance schema

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create companies table
CREATE TABLE IF NOT EXISTS public.companies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    legal_name TEXT NOT NULL,
    tax_id TEXT NOT NULL UNIQUE,
    company_type TEXT NOT NULL CHECK (company_type IN ('corporation', 'llc', 'partnership', 'sole_proprietorship')),
    incorporation_state TEXT,
    incorporation_country TEXT DEFAULT 'US',
    incorporation_date DATE,
    doing_business_as TEXT,
    industry_classification TEXT,
    business_description TEXT,
    website_url TEXT,
    primary_address JSONB NOT NULL,
    mailing_address JSONB,
    primary_email TEXT NOT NULL,
    primary_phone TEXT,
    fiscal_year_end DATE DEFAULT (CURRENT_DATE + INTERVAL '1 year'),
    accounting_method TEXT DEFAULT 'accrual' CHECK (accounting_method IN ('accrual', 'cash')),
    base_currency TEXT DEFAULT 'USD',
    is_active BOOLEAN DEFAULT true,
    dissolution_date DATE,
    compliance_status TEXT DEFAULT 'active' CHECK (compliance_status IN ('active', 'suspended', 'dissolved')),
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add company fields to teams table
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teams' AND column_name = 'company_id') THEN
        ALTER TABLE public.teams ADD COLUMN company_id UUID REFERENCES public.companies(id) ON DELETE SET NULL;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teams' AND column_name = 'is_business_entity') THEN
        ALTER TABLE public.teams ADD COLUMN is_business_entity BOOLEAN DEFAULT false;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teams' AND column_name = 'alliance_type') THEN
        ALTER TABLE public.teams ADD COLUMN alliance_type TEXT DEFAULT 'emerging' CHECK (alliance_type IN ('emerging', 'established', 'solo'));
    END IF;
END $$;

-- Create financial_transactions table
CREATE TABLE IF NOT EXISTS public.financial_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
    project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
    team_id UUID REFERENCES public.teams(id) ON DELETE SET NULL,
    transaction_type TEXT NOT NULL CHECK (transaction_type IN ('commission', 'recurring_fee', 'royalty', 'expense', 'refund', 'bonus', 'salary')),
    transaction_category TEXT DEFAULT 'business_payment' CHECK (transaction_category IN ('business_payment', 'contractor_payment', 'employee_payment', 'expense_reimbursement')),
    gross_amount DECIMAL(12,2) NOT NULL CHECK (gross_amount >= 0),
    tax_amount DECIMAL(12,2) DEFAULT 0 CHECK (tax_amount >= 0),
    net_amount DECIMAL(12,2) NOT NULL CHECK (net_amount >= 0),
    currency TEXT DEFAULT 'USD',
    exchange_rate DECIMAL(10,6) DEFAULT 1.0,
    tax_category TEXT CHECK (tax_category IN ('1099-NEC', '1099-MISC', 'W2', 'exempt', 'international')),
    requires_1099 BOOLEAN DEFAULT false,
    requires_w2 BOOLEAN DEFAULT false,
    backup_withholding_rate DECIMAL(5,2) DEFAULT 0 CHECK (backup_withholding_rate >= 0 AND backup_withholding_rate <= 100),
    tax_year INTEGER DEFAULT EXTRACT(YEAR FROM CURRENT_DATE),
    payer_company_id UUID REFERENCES public.companies(id),
    payee_user_id UUID REFERENCES auth.users(id),
    payee_company_id UUID REFERENCES public.companies(id),
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'processing', 'paid', 'failed', 'cancelled', 'disputed')),
    processed_at TIMESTAMP WITH TIME ZONE,
    payment_method TEXT CHECK (payment_method IN ('ach', 'wire', 'check', 'paypal', 'stripe', 'manual')),
    external_transaction_id TEXT,
    approval_required BOOLEAN DEFAULT true,
    approved_by UUID REFERENCES auth.users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    approval_notes TEXT,
    description TEXT NOT NULL,
    reference_number TEXT,
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Enable RLS
ALTER TABLE public.companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.financial_transactions ENABLE ROW LEVEL SECURITY;

-- Create basic RLS policies
DROP POLICY IF EXISTS "Users can view companies they have access to" ON public.companies;
CREATE POLICY "Users can view companies they have access to" ON public.companies
    FOR SELECT USING (
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM public.teams t 
            JOIN public.team_members tm ON t.id = tm.team_id 
            WHERE t.company_id = companies.id AND tm.user_id = auth.uid()
        )
    );

DROP POLICY IF EXISTS "Users can create companies" ON public.companies;
CREATE POLICY "Users can create companies" ON public.companies
    FOR INSERT WITH CHECK (created_by = auth.uid());

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON public.companies TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.financial_transactions TO authenticated;

-- Insert VRC sample data
INSERT INTO public.companies (
    legal_name, tax_id, company_type, incorporation_state, incorporation_country,
    doing_business_as, industry_classification, business_description, website_url,
    primary_address, primary_email, primary_phone, fiscal_year_end, accounting_method
) VALUES (
    'VRC Entertainment LLC', '12-3456789', 'llc', 'CA', 'US',
    'VRC Films', '512110', 'Independent film production and talent management company',
    'https://vrcfilms.com',
    '{"street": "123 Hollywood Blvd", "city": "Los Angeles", "state": "CA", "zip": "90028", "country": "US"}',
    '<EMAIL>', '******-123-4567', '2024-12-31', 'accrual'
) ON CONFLICT (tax_id) DO UPDATE SET
    business_description = EXCLUDED.business_description,
    updated_at = now();
