# Task Cards Component Wireframe
**Mission/Quest Display Components**

## 📋 Component Information
- **Component Type**: Data Display Card
- **Usage**: Mission Board, Bounty Board, Task Lists
- **Variants**: Mission Card, Bounty Card, Quest Card
- **States**: Available, In Progress, Completed, Overdue
- **Responsive**: Desktop, Tablet, Mobile

---

## 🎯 **Component Purpose**

Task cards are the primary interface for displaying missions, quests, and bounties throughout the Royaltea platform. They provide at-a-glance information about tasks while maintaining the gamified terminology and visual hierarchy.

### **Core Information Display**
- **Task Identity**: Title, type, difficulty
- **Progress Status**: Current state, completion percentage
- **Financial Details**: Reward amount, payment status
- **Team Information**: Assignees, contributors
- **Timeline**: Deadlines, duration estimates

---

## 🎮 **Card Variants**

### **1. Mission Card (Team Tasks)**
```
┌─────────────────────────────────────────────────────────────┐
│ ⚔️ MISSION                                    🔥 URGENT      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ Implement User Authentication System                        │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Progress: ████████░░░░░░░░░░ 40%                        │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 💰 Reward: $1,200    ⏱️ Due: 3 days    ⭐ Difficulty: 7/10 │
│                                                             │
│ 👥 Assigned to:                                             │
│ ┌─────┐ ┌─────┐ ┌─────┐                                    │
│ │ JD  │ │ SM  │ │ +2  │  John, Sarah, +2 others            │
│ └─────┘ └─────┘ └─────┘                                    │
│                                                             │
│ 🏷️ Tags: Frontend, React, Authentication                   │
│                                                             │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐           │
│ │ [View]  │ │ [Edit]  │ │ [Join]  │ │ [...]   │           │
│ └─────────┘ └─────────┘ └─────────┘ └─────────┘           │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **2. Bounty Card (Public Tasks)**
```
┌─────────────────────────────────────────────────────────────┐
│ 🎯 BOUNTY                                   💎 PREMIUM       │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ Design Landing Page Mockups                                 │
│                                                             │
│ 💰 Bounty: $800      ⏱️ Deadline: 5 days   ⭐ Level: 5/10   │
│                                                             │
│ 📋 Requirements:                                            │
│ • Figma mockups for 3 pages                                │
│ • Mobile responsive design                                  │
│ • Brand guideline compliance                               │
│                                                             │
│ 🏆 Hunters: 3 active   👀 Watchers: 12                     │
│                                                             │
│ 🏷️ Skills: UI/UX, Figma, Design                           │
│                                                             │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐           │
│ │ [Hunt]  │ │ [Watch] │ │ [Share] │ │ [...]   │           │
│ └─────────┘ └─────────┘ └─────────┘ └─────────┘           │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **3. Quest Card (Learning Tasks)**
```
┌─────────────────────────────────────────────────────────────┐
│ 🎓 QUEST                                    ✨ LEARNING      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ Master React Hooks Fundamentals                            │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Progress: ██████████████████░░ 90%                      │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 🎖️ XP Reward: 500    ⏱️ Est. Time: 2 hours  📚 Modules: 8  │
│                                                             │
│ 📖 Current Module: Advanced useEffect Patterns             │
│                                                             │
│ 🏆 Achievements:                                            │
│ ✅ useState Master    ✅ useEffect Novice    ⏳ Custom Hooks │
│                                                             │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐           │
│ │[Continue│ │ [Reset] │ │ [Share] │ │ [...]   │           │
│ │   Quest]│ │         │ │         │ │         │           │
│ └─────────┘ └─────────┘ └─────────┘ └─────────┘           │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

---

## 🎨 **Card States**

### **Available State**
```
┌─────────────────────────────────────────────────────────────┐
│ ⚔️ MISSION                                    🟢 AVAILABLE   │
│ Border: Solid blue                                          │
│ Background: Light gradient                                  │
│ Actions: [View] [Join] [Watch]                              │
└─────────────────────────────────────────────────────────────┘
```

### **In Progress State**
```
┌─────────────────────────────────────────────────────────────┐
│ ⚔️ MISSION                                    🟡 IN PROGRESS │
│ Border: Solid orange                                        │
│ Background: Warm gradient                                   │
│ Progress bar: Animated                                      │
│ Actions: [View] [Update] [Complete]                         │
└─────────────────────────────────────────────────────────────┘
```

### **Completed State**
```
┌─────────────────────────────────────────────────────────────┐
│ ⚔️ MISSION                                    ✅ COMPLETED   │
│ Border: Solid green                                         │
│ Background: Success gradient                                │
│ Overlay: Subtle checkmark pattern                          │
│ Actions: [View] [Review] [Archive]                          │
└─────────────────────────────────────────────────────────────┘
```

### **Overdue State**
```
┌─────────────────────────────────────────────────────────────┐
│ ⚔️ MISSION                                    🔴 OVERDUE     │
│ Border: Solid red, pulsing                                  │
│ Background: Alert gradient                                  │
│ Warning icon: Animated                                      │
│ Actions: [View] [Extend] [Escalate]                         │
└─────────────────────────────────────────────────────────────┘
```

---

## 📱 **Responsive Behavior**

### **Desktop (Full Card)**
- **Full information** display
- **Hover effects** with additional details
- **Drag and drop** functionality
- **Context menus** on right-click

### **Tablet (Condensed Card)**
```
┌─────────────────────────────────────┐
│ ⚔️ Implement Authentication         │
│ 💰 $1,200  ⏱️ 3 days  ⭐ 7/10      │
│ Progress: ████████░░░░░░░░░░ 40%    │
│ 👥 JD, SM, +2                      │
│ [View] [Edit] [Join]               │
└─────────────────────────────────────┘
```

### **Mobile (Minimal Card)**
```
┌─────────────────────────┐
│ ⚔️ Authentication       │
│ $1,200 • 3 days • 40%  │
│ 👥 4 members            │
│ [View] [Join]           │
└─────────────────────────┘
```

---

## 🎮 **Interactive Elements**

### **Progress Indicators**
```mermaid
graph LR
    A[Not Started] --> B[In Progress]
    B --> C[Review]
    C --> D[Completed]
    
    style A fill:#f9f9f9
    style B fill:#fff3cd
    style C fill:#d1ecf1
    style D fill:#d4edda
```

### **Action Buttons**
- **Primary Actions**: View, Join, Complete
- **Secondary Actions**: Edit, Watch, Share
- **Contextual Actions**: Based on user role and task state
- **Overflow Menu**: Additional options in dropdown

### **Hover States**
```
Default Card:
┌─────────────────────────┐
│ Standard appearance     │
└─────────────────────────┘

Hover Card:
┌─────────────────────────┐
│ ↗️ Elevated shadow       │
│ 🔍 Preview tooltip      │
│ ⚡ Quick actions visible │
└─────────────────────────┘
```

---

## 🔧 **Technical Specifications**

### **Component Props**
```typescript
interface TaskCardProps {
  task: {
    id: string;
    title: string;
    type: 'mission' | 'bounty' | 'quest';
    status: 'available' | 'in_progress' | 'completed' | 'overdue';
    reward: number;
    deadline: Date;
    difficulty: number;
    progress?: number;
    assignees?: User[];
    tags?: string[];
    requirements?: string[];
  };
  variant?: 'full' | 'condensed' | 'minimal';
  onAction: (action: string, taskId: string) => void;
  showActions?: boolean;
  draggable?: boolean;
}
```

### **Accessibility Features**
- **ARIA labels** for all interactive elements
- **Keyboard navigation** support
- **Screen reader** compatibility
- **High contrast** mode support
- **Focus indicators** for all actionable items

### **Performance Optimizations**
- **Lazy loading** for card images
- **Virtualization** for large lists
- **Memoization** for expensive calculations
- **Debounced interactions** for rapid actions

---

## 🎨 **Visual Design System**

### **Color Coding**
- **Mission Cards**: Blue theme (#3B82F6)
- **Bounty Cards**: Purple theme (#8B5CF6)
- **Quest Cards**: Green theme (#10B981)
- **Priority Indicators**: Red (urgent), Orange (high), Yellow (medium)

### **Typography Hierarchy**
- **Title**: 18px, Bold, Primary color
- **Metadata**: 14px, Medium, Secondary color
- **Description**: 16px, Regular, Text color
- **Tags**: 12px, Medium, Muted color

### **Spacing & Layout**
- **Card Padding**: 16px all sides
- **Element Spacing**: 12px between sections
- **Button Spacing**: 8px between actions
- **Border Radius**: 8px for modern appearance

---

**These task cards form the foundation of task management throughout the Royaltea platform, providing consistent, accessible, and engaging interfaces for all types of work items.**
