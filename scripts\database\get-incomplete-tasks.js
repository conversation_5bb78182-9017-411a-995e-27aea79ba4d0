const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL || 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || process.argv[2];

if (!supabaseKey) {
  console.error('Error: Supabase key is required. Please provide it as an environment variable or command line argument.');
  process.exit(1);
}

console.log(`Using Supabase URL: ${supabaseUrl}`);
console.log('=== Roadmap Task Analysis ===\n');

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Function to recursively find incomplete tasks
function findIncompleteTasks(sections, parentPath = '') {
  let incompleteTasks = [];

  if (!sections) return incompleteTasks;

  sections.forEach((section, sectionIndex) => {
    const currentPath = parentPath ? `${parentPath}.${sectionIndex + 1}` : `${sectionIndex + 1}`;

    if (section.tasks) {
      section.tasks.forEach((task, taskIndex) => {
        const taskPath = `${currentPath}.${taskIndex + 1}`;
        if (!task.completed) {
          incompleteTasks.push({
            path: taskPath,
            title: task.title,
            description: task.description || '',
            priority: task.priority || 'normal'
          });
        }
      });
    }

    if (section.sections) {
      const childIncompleteTasks = findIncompleteTasks(section.sections, currentPath);
      incompleteTasks = incompleteTasks.concat(childIncompleteTasks);
    }
  });

  return incompleteTasks;
}

async function getRoadmapData() {
  try {
    // Get the roadmap data
    const { data: roadmapData, error } = await supabase
      .from('roadmap')
      .select('*')
      .single();

    if (error) {
      console.error('Error fetching roadmap:', error);
      return null;
    }

    console.log('Found roadmap data');
    return roadmapData;
  } catch (error) {
    console.error('Unexpected error:', error);
    return null;
  }
}

async function analyzeRoadmap() {
  const roadmapData = await getRoadmapData();

  if (!roadmapData || !roadmapData.data) {
    console.error('No roadmap data found');
    return;
  }

  // Filter out the metadata entry
  const phases = roadmapData.data.filter(item => item.id && !item.type);

  console.log('=== Phase Analysis ===');

  // Analyze all phases
  for (const phase of phases) {
    console.log(`\nPhase ${phase.id}: ${phase.title}`);
    console.log(`Status: ${phase.stats ? `${phase.stats.percentage}% complete` : 'Unknown'}`);
    console.log(`Timeframe: ${phase.timeframe || 'Not specified'}`);

    const incompleteTasks = findIncompleteTasks(phase.sections);
    console.log(`Incomplete Tasks: ${incompleteTasks.length}`);

    if (incompleteTasks.length > 0) {
      console.log('\n=== Incomplete Tasks ===');

      // Sort by path to maintain hierarchy
      incompleteTasks.sort((a, b) => {
        const aParts = a.path.split('.').map(Number);
        const bParts = b.path.split('.').map(Number);

        for (let i = 0; i < Math.min(aParts.length, bParts.length); i++) {
          if (aParts[i] !== bParts[i]) {
            return aParts[i] - bParts[i];
          }
        }

        return aParts.length - bParts.length;
      });

      // Group by top-level section
      const tasksBySection = {};
      incompleteTasks.forEach(task => {
        const topSection = task.path.split('.')[1];
        if (!tasksBySection[topSection]) {
          tasksBySection[topSection] = [];
        }
        tasksBySection[topSection].push(task);
      });

      // Print tasks by section
      Object.keys(tasksBySection).sort((a, b) => Number(a) - Number(b)).forEach(sectionKey => {
        const sectionTasks = tasksBySection[sectionKey];
        const sectionIndex = Number(sectionKey) - 1;
        const sectionTitle = phase.sections && phase.sections[sectionIndex] && phase.sections[sectionIndex].title
                            ? phase.sections[sectionIndex].title
                            : `Section ${sectionKey}`;

        console.log(`\nSection ${phase.id}.${sectionKey}: ${sectionTitle}`);
        console.log('-'.repeat(50));

        sectionTasks.forEach(task => {
          console.log(`[${task.path}] ${task.title}`);
          if (task.description) {
            console.log(`   Description: ${task.description}`);
          }
          console.log(`   Priority: ${task.priority}`);
        });
      });
    }
  }

  // Find the phase with the most incomplete tasks
  let phaseWithMostIncompleteTasks = null;
  let maxIncompleteTasks = 0;

  for (const phase of phases) {
    const incompleteTasks = findIncompleteTasks(phase.sections);
    if (incompleteTasks.length > maxIncompleteTasks) {
      maxIncompleteTasks = incompleteTasks.length;
      phaseWithMostIncompleteTasks = phase;
    }
  }

  if (phaseWithMostIncompleteTasks) {
    console.log('\n=== Recommendation ===');
    console.log(`Focus on Phase ${phaseWithMostIncompleteTasks.id}: ${phaseWithMostIncompleteTasks.title}`);
    console.log(`This phase has the most incomplete tasks (${maxIncompleteTasks})`);
  }

  if (phaseWithMostIncompleteTasks) {
    console.log('\n=== Next Tasks Recommendation ===');

    // Get incomplete tasks for the recommended phase
    const phaseIncompleteTasks = findIncompleteTasks(phaseWithMostIncompleteTasks.sections);

    // Find high priority tasks
    const highPriorityTasks = phaseIncompleteTasks.filter(task => task.priority === 'high');
    if (highPriorityTasks.length > 0) {
      console.log('High Priority Tasks:');
      highPriorityTasks.forEach(task => {
        console.log(`- [${task.path}] ${task.title}`);
      });
    } else {
      // If no high priority tasks, recommend the first few incomplete tasks
      console.log('Recommended Next Tasks:');
      phaseIncompleteTasks.slice(0, 5).forEach(task => {
        console.log(`- [${task.path}] ${task.title}`);
      });
    }
  }
}

analyzeRoadmap()
  .then(() => {
    console.log('\n=== Roadmap Analysis Complete ===');
  })
  .catch(error => {
    console.error('Error in roadmap analysis:', error);
  });
