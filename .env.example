# Royaltea Environment Variables Template
# Copy this file to client/.env.local and fill in your actual values

# Supabase Configuration (Required)
VITE_SUPABASE_URL=https://hqqlrrqvjcetoxbdjgzx.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Supabase Service Key (For server-side operations)
SUPABASE_SERVICE_KEY=your_supabase_service_key_here

# Database Configuration (Required for direct database access)
supabase_database_password=your_database_password_here

# Site Configuration
SITE_URL=https://royalty.technology

# Analytics (Optional)
VITE_GA_TRACKING_ID=G-S7SFML469V

# Development Settings (Optional)
NODE_ENV=development
VITE_DEBUG=false

# Plaid Configuration (Required for payment processing)
PLAID_CLIENT_ID=your_plaid_client_id_here
PLAID_SECRET=your_plaid_secret_here
PLAID_ENV=sandbox
PLAID_WEBHOOK_URL=https://royalty.technology/.netlify/functions/plaid-webhook

# Payment Processing Configuration
PAYMENT_WEBHOOK_SECRET=your_webhook_secret_here
PAYMENT_ENCRYPTION_KEY=your_encryption_key_here

# Instructions:
# 1. Copy this file to client/.env.local
# 2. Replace placeholder values with actual keys from Supabase dashboard
# 3. Get keys from: https://supabase.com/dashboard/project/hqqlrrqvjcetoxbdjgzx/settings/api
# 4. Get Plaid keys from: https://dashboard.plaid.com/team/keys
# 5. Never commit the actual .env.local file to version control
