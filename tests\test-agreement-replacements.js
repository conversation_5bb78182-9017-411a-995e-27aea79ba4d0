// Simple test script for agreement replacements
const fs = require('fs');

// Test data
const testProjects = [
  {
    name: 'Dream Project',
    description: 'A collaborative game development project',
    project_type: 'game'
  },
  {
    name: 'Symphony',
    description: 'A collaborative music production project',
    project_type: 'music'
  },
  {
    name: 'Screenplay',
    description: 'A collaborative film production project',
    project_type: 'film'
  }
];

// Test text with Village references
const testText = `
# CONTRIBUTOR AGREEMENT

This project involves development work on "Village of The Ages," a village simulation game where players guide communities through historical progressions and manage resource-based challenges.

## EXHIBIT I
### SPECIFICATIONS

**Village of The Ages - Game Specifications**

**Game Overview:**
Village of The Ages is a village simulation game where players guide communities through historical progressions and manage resource-based challenges. The game features dynamic challenges based on resource scarcity and natural disasters, creating an engaging gameplay loop.

**Core Features:**

1. **Village Building & Management**
   - Resource gathering and management
   - Building placement and construction
   - Population growth and management
   - Cultural evolution systems

2. **Historical Progression**
   - Players guide their community through multiple historical eras
   - Technology tree advancement
   - Cultural and social development
   - Architectural evolution

## EXHIBIT II
### PRODUCT ROADMAP

**Village of The Ages - Development Roadmap**

**Phase 1: Core Gameplay Development (Months 1-2)**
- Basic village layout and building system
- Core resource gathering mechanics
- Initial AI for villagers
- Basic UI framework
- First playable prototype with one historical era
`;

// Define replacement terms based on project type
const replacementTerms = {
  'game': {
    'village': 'game',
    'villagers': 'players',
    'neighboring villages': 'external partners',
    'village building': 'game building',
    'village management': 'game management',
    'village layout': 'game layout',
    'basic village': 'basic game',
    'AI for villagers': 'AI for players',
    'Initial AI for villagers': 'Initial AI for players',
    'village building & management': 'game building & management',
    'trading systems with neighboring villages': 'trading systems with external partners',
    'basic village layout and building system': 'basic game layout and building system'
  },
  'music': {
    'village': 'music project',
    'villagers': 'listeners',
    'neighboring villages': 'collaborating artists',
    'village building': 'music composition',
    'village management': 'production management',
    'village layout': 'track arrangement',
    'basic village': 'basic composition',
    'AI for villagers': 'audio processing',
    'Initial AI for villagers': 'initial audio processing',
    'village building & management': 'composition & production',
    'trading systems with neighboring villages': 'collaboration with other artists',
    'basic village layout and building system': 'basic track arrangement and composition system'
  },
  'film': {
    'village': 'film project',
    'villagers': 'viewers',
    'neighboring villages': 'production partners',
    'village building': 'scene development',
    'village management': 'production management',
    'village layout': 'set design',
    'basic village': 'basic scene',
    'AI for villagers': 'special effects',
    'Initial AI for villagers': 'initial special effects',
    'village building & management': 'production & direction',
    'trading systems with neighboring villages': 'collaboration with production partners',
    'basic village layout and building system': 'basic set design and scene development'
  }
};

// Function to clean agreement text
function cleanAgreementText(text, project) {
  let cleaned = text;
  const projectName = project.name;
  const projectDescription = project.description;
  const projectType = project.project_type;

  // Create a new agreement from scratch based on project type
  if (projectType === 'game') {
    // Game project
    cleaned = `
# CONTRIBUTOR AGREEMENT

This project involves development work on "${projectName}," ${projectDescription}.

## EXHIBIT I
### SPECIFICATIONS

**${projectName} - Specifications**

**Game Overview:**
${projectName} is ${projectDescription}. The game features engaging gameplay mechanics and systems designed to provide an immersive player experience.

**Core Features:**

1. **Game Building & Management**
   - Core gameplay mechanics
   - Visual and audio elements
   - User interface and experience
   - Content and progression

2. **Development Progression**
   - Iterative gameplay refinement
   - Feature implementation and testing
   - Performance optimization
   - Player feedback incorporation

## EXHIBIT II
### PRODUCT ROADMAP

**${projectName} - Development Roadmap**

**Phase 1: Core Gameplay Development (Months 1-2)**
- Basic game systems and mechanics
- Core feature implementation
- Initial player controls and UI
- Basic framework and architecture
- First playable prototype
`;
  }
  else if (projectType === 'music') {
    // Music project
    cleaned = `
# CONTRIBUTOR AGREEMENT

This project involves development work on "${projectName}," ${projectDescription}.

## EXHIBIT I
### SPECIFICATIONS

**${projectName} - Specifications**

**Project Overview:**
${projectName} is ${projectDescription}. The project encompasses composition, recording, production, and distribution of original music content.

**Core Features:**

1. **Composition & Production**
   - Composition and arrangement
   - Recording and production
   - Mixing and mastering
   - Distribution and promotion

2. **Production Progression**
   - Artists develop the project through multiple production phases
   - Production quality advancement
   - Artistic and creative development
   - Arrangement evolution

## EXHIBIT II
### PRODUCT ROADMAP

**${projectName} - Development Roadmap**

**Phase 1: Core Production Development (Months 1-2)**
- Basic track arrangement and composition
- Core recording sessions
- Initial audio processing
- Basic production framework
- First demo recordings
`;
  }
  else if (projectType === 'film') {
    // Film project
    cleaned = `
# CONTRIBUTOR AGREEMENT

This project involves development work on "${projectName}," ${projectDescription}.

## EXHIBIT I
### SPECIFICATIONS

**${projectName} - Specifications**

**Project Overview:**
${projectName} is ${projectDescription}. The project encompasses pre-production, filming, post-production, and distribution of visual content.

**Core Features:**

1. **Production & Direction**
   - Script and story development
   - Production and filming
   - Editing and post-production
   - Distribution and exhibition

2. **Production Progression**
   - Filmmakers develop the project through multiple production phases
   - Production quality advancement
   - Narrative and character development
   - Visual style evolution

## EXHIBIT II
### PRODUCT ROADMAP

**${projectName} - Development Roadmap**

**Phase 1: Core Production Development (Months 1-2)**
- Basic set design and scene development
- Core filming sessions
- Initial special effects
- Basic production framework
- First rough cut
`;
  }
  else {
    // Generic project (fallback)
    cleaned = `
# CONTRIBUTOR AGREEMENT

This project involves development work on "${projectName}," ${projectDescription}.

## EXHIBIT I
### SPECIFICATIONS

**${projectName} - Specifications**

**Project Overview:**
${projectName} is ${projectDescription}. The project is designed to meet specific goals and requirements through collaborative development.

**Core Features:**

1. **Project Development & Management**
   - Core functionality implementation
   - User experience design
   - Quality assurance
   - Deployment and distribution

2. **Development Progression**
   - Iterative development process
   - Feature implementation and testing
   - Performance optimization
   - User feedback incorporation

## EXHIBIT II
### PRODUCT ROADMAP

**${projectName} - Development Roadmap**

**Phase 1: Core Development (Months 1-2)**
- Basic project framework
- Core feature implementation
- Initial user interface
- Quality assurance testing
- First functional prototype
`;
  }

  return cleaned;
}

// Helper function to check for Village references
function checkForVillageReferences(text) {
  if (!text) return [];

  const lines = text.split('\n');
  const references = [];

  const villagePatterns = [
    /village/i,
    /Village of The Ages/i,
    /Village of the Ages/i,
    /VOTA/i
  ];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    for (const pattern of villagePatterns) {
      if (pattern.test(line)) {
        // Get context (1 line before and after)
        const start = Math.max(0, i - 1);
        const end = Math.min(lines.length - 1, i + 1);
        const context = lines.slice(start, end + 1).join('\n');

        references.push({
          lineNumber: i + 1,
          line: line,
          context: context
        });

        // Only add each line once, even if it matches multiple patterns
        break;
      }
    }
  }

  return references;
}

// Test the agreement generation for each project
function testAgreementCleaning() {
  console.log('Testing agreement generation for different project types...\n');

  for (const project of testProjects) {
    console.log(`\nGenerating agreement for: ${project.name} (${project.project_type})`);

    // Generate a new agreement for this project
    const generatedAgreement = cleanAgreementText('', project);

    // Save the generated agreement
    const outputPath = `./generated-${project.project_type}.md`;
    fs.writeFileSync(outputPath, generatedAgreement);
    console.log(`Generated agreement saved to ${outputPath}`);

    // Check for Village references
    const villageReferences = checkForVillageReferences(generatedAgreement);

    if (villageReferences.length > 0) {
      console.log(`\n⚠️ Found ${villageReferences.length} Village references:`);
      villageReferences.forEach((ref, index) => {
        if (index < 5) { // Only show the first 5 references
          console.log(`\n${index + 1}. Line ${ref.lineNumber}: ${ref.line}`);
          console.log(`   Context: ${ref.context}`);
        }
      });

      if (villageReferences.length > 5) {
        console.log(`\n... and ${villageReferences.length - 5} more references`);
      }
    } else {
      console.log(`✓ No Village references found in the generated agreement!`);
    }

    // Check for project-specific content
    const projectName = project.name;
    const projectType = project.project_type;

    console.log('\nChecking for project-specific content:');
    console.log(`  Project name: ${generatedAgreement.includes(projectName) ? '✓' : '✗'}`);
    console.log(`  Project type: ${generatedAgreement.includes(projectType) ? '✓' : '✗'}`);

    // Check for project-type specific terms
    if (projectType === 'game') {
      console.log('  Game-specific terms:');
      console.log(`    Gameplay: ${generatedAgreement.includes('gameplay') ? '✓' : '✗'}`);
      console.log(`    Players: ${generatedAgreement.includes('player') ? '✓' : '✗'}`);
    } else if (projectType === 'music') {
      console.log('  Music-specific terms:');
      console.log(`    Composition: ${generatedAgreement.includes('Composition') ? '✓' : '✗'}`);
      console.log(`    Recording: ${generatedAgreement.includes('Recording') ? '✓' : '✗'}`);
    } else if (projectType === 'film') {
      console.log('  Film-specific terms:');
      console.log(`    Production: ${generatedAgreement.includes('Production') ? '✓' : '✗'}`);
      console.log(`    Filming: ${generatedAgreement.includes('filming') ? '✓' : '✗'}`);
    }
  }

  console.log('\nTest completed!');
}

// Run the test
testAgreementCleaning();
