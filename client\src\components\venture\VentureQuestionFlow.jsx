import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@heroui/react';
import VentureQuestionStep from './VentureQuestionStep';

/**
 * VentureQuestionFlow Component
 * 
 * Manages the 8-question immersive flow for venture creation
 * Each question is displayed full-screen with minimal UI
 * Follows the wireframe specifications exactly
 */
const VentureQuestionFlow = ({ 
  initialData = {}, 
  onComplete, 
  onBack, 
  onCancel 
}) => {
  const [currentQuestion, setCurrentQuestion] = useState(1);
  const [answers, setAnswers] = useState({
    projectCategory: '',
    projectSubtype: '',
    targetAudience: '',
    timeline: '',
    budget: '',
    successMetrics: '',
    ventureName: '',
    ventureDescription: '',
    ventureIcon: '🚀',
    ventureTags: [],
    initialTeamRoles: {},
    ...initialData
  });

  const totalQuestions = 8;

  // Question definitions following wireframe specifications
  const questions = [
    {
      id: 1,
      title: "What are you building?",
      type: "category_selection",
      options: [
        {
          value: "software",
          icon: "💻",
          title: "Software or App",
          description: "Website, mobile app, or software tool"
        },
        {
          value: "creative",
          icon: "🎨", 
          title: "Creative Work",
          description: "Art, music, video, writing, or design"
        },
        {
          value: "business",
          icon: "🤝",
          title: "Business Service", 
          description: "Consulting, marketing, or professional service"
        },
        {
          value: "physical",
          icon: "📦",
          title: "Physical Product",
          description: "Something you can touch and ship"
        },
        {
          value: "other",
          icon: "❓",
          title: "Something else",
          description: "I'll describe it myself"
        }
      ]
    },
    {
      id: 2,
      title: "What kind of software?",
      type: "subtype_selection",
      condition: (answers) => answers.projectCategory === "software",
      options: [
        {
          value: "mobile",
          icon: "📱",
          title: "Mobile App",
          description: "iOS, Android, or cross-platform mobile app"
        },
        {
          value: "web",
          icon: "🌐",
          title: "Web Application", 
          description: "Website or web-based software platform"
        },
        {
          value: "desktop",
          icon: "🖥️",
          title: "Desktop Software",
          description: "Windows, Mac, or Linux desktop application"
        },
        {
          value: "tool",
          icon: "🔧",
          title: "Developer Tool",
          description: "Library, framework, or tool for other developers"
        },
        {
          value: "game",
          icon: "🎮",
          title: "Game",
          description: "Video game for any platform"
        }
      ]
    },
    {
      id: 3,
      title: "Who is this for?",
      type: "audience_selection",
      options: [
        {
          value: "general",
          icon: "👥",
          title: "General Public",
          description: "Anyone can use it"
        },
        {
          value: "business",
          icon: "🏢",
          title: "Businesses",
          description: "Companies and organizations"
        },
        {
          value: "developers",
          icon: "👨‍💻",
          title: "Developers",
          description: "Other programmers and technical people"
        },
        {
          value: "industry",
          icon: "🎯",
          title: "Specific Industry",
          description: "Healthcare, education, finance, etc."
        },
        {
          value: "internal",
          icon: "👤",
          title: "Just for us",
          description: "Internal tool for our team/company"
        }
      ]
    },
    {
      id: 4,
      title: "What's your timeline?",
      type: "timeline_selection",
      options: [
        {
          value: "quick",
          icon: "⚡",
          title: "Quick Sprint (1-4 weeks)",
          description: "Small project, get it done fast"
        },
        {
          value: "short",
          icon: "🎯",
          title: "Short Project (1-3 months)",
          description: "Focused scope with clear end date"
        },
        {
          value: "medium",
          icon: "🏗️",
          title: "Medium Project (3-12 months)",
          description: "Substantial project with multiple phases"
        },
        {
          value: "long",
          icon: "🌟",
          title: "Long-term Vision (1+ years)",
          description: "Big ambitious project, ongoing development"
        },
        {
          value: "flexible",
          icon: "🤷",
          title: "We'll see how it goes",
          description: "No fixed timeline, work at our own pace"
        }
      ]
    },
    {
      id: 5,
      title: "What's your budget situation?",
      type: "budget_selection",
      options: [
        {
          value: "funded",
          icon: "💰",
          title: "We have funding",
          description: "Budget available for tools, services, payments"
        },
        {
          value: "bootstrapped",
          icon: "🎯",
          title: "Bootstrapped",
          description: "Self-funded, keeping costs minimal"
        },
        {
          value: "sweat_equity",
          icon: "🤝",
          title: "Sweat Equity",
          description: "Everyone contributes time, no money upfront"
        },
        {
          value: "revenue_first",
          icon: "📈",
          title: "Revenue-First",
          description: "Plan to make money quickly to fund development"
        },
        {
          value: "seeking_investment",
          icon: "🔍",
          title: "Seeking Investment",
          description: "Looking for investors or grants"
        }
      ]
    },
    {
      id: 6,
      title: "How will you know you've succeeded?",
      type: "success_selection",
      options: [
        {
          value: "revenue",
          icon: "💰",
          title: "Revenue Target",
          description: "Specific income or profit goals"
        },
        {
          value: "users",
          icon: "👥",
          title: "User Growth",
          description: "Number of users, downloads, or customers"
        },
        {
          value: "features",
          icon: "🎯",
          title: "Feature Completion",
          description: "Building specific features or capabilities"
        },
        {
          value: "recognition",
          icon: "🏆",
          title: "Recognition",
          description: "Awards, press coverage, industry recognition"
        },
        {
          value: "satisfaction",
          icon: "😊",
          title: "Personal Satisfaction",
          description: "Learning, portfolio building, having fun"
        }
      ]
    },
    {
      id: 7,
      title: "What should we call your Venture?",
      type: "venture_details",
      fields: [
        {
          name: "ventureName",
          label: "Venture Name",
          type: "text",
          placeholder: "TaskMaster Pro",
          required: true
        },
        {
          name: "ventureDescription", 
          label: "Short Description",
          type: "textarea",
          placeholder: "A mobile app that helps teams manage tasks and track productivity with smart automation...",
          required: true
        },
        {
          name: "ventureIcon",
          label: "Choose an icon",
          type: "icon_picker",
          options: ["📱", "💻", "🎯", "🚀", "⚡", "🔧", "💡", "🌟"]
        },
        {
          name: "ventureTags",
          label: "Tags (help others find your project)",
          type: "tag_input",
          placeholder: "#mobile #productivity #automation #startup"
        }
      ]
    },
    {
      id: 8,
      title: "Who will do what?",
      type: "team_roles",
      description: "Based on your Alliance and project type, here are suggested roles for your team:"
    }
  ];

  // Handle answer selection
  const handleAnswer = (questionId, answer) => {
    setAnswers(prev => {
      const updated = { ...prev };
      
      // Handle different question types
      if (questionId === 1) {
        updated.projectCategory = answer;
      } else if (questionId === 2) {
        updated.projectSubtype = answer;
      } else if (questionId === 3) {
        updated.targetAudience = answer;
      } else if (questionId === 4) {
        updated.timeline = answer;
      } else if (questionId === 5) {
        updated.budget = answer;
      } else if (questionId === 6) {
        updated.successMetrics = answer;
      } else if (questionId === 7) {
        // Handle venture details (multiple fields)
        Object.assign(updated, answer);
      } else if (questionId === 8) {
        updated.initialTeamRoles = answer;
      }
      
      return updated;
    });
  };

  // Navigate to next question
  const handleNext = () => {
    if (currentQuestion < totalQuestions) {
      setCurrentQuestion(currentQuestion + 1);
    } else {
      // Complete the flow
      onComplete(answers);
    }
  };

  // Navigate to previous question
  const handlePrevious = () => {
    if (currentQuestion > 1) {
      setCurrentQuestion(currentQuestion - 1);
    } else {
      onBack();
    }
  };

  // Get current question data
  const getCurrentQuestion = () => {
    const question = questions.find(q => q.id === currentQuestion);
    
    // Check if question has a condition
    if (question?.condition && !question.condition(answers)) {
      // Skip this question
      if (currentQuestion < totalQuestions) {
        setCurrentQuestion(currentQuestion + 1);
        return null;
      }
    }
    
    return question;
  };

  const currentQuestionData = getCurrentQuestion();
  
  if (!currentQuestionData) {
    return null; // Will trigger useEffect to move to next question
  }

  // Check if current question is answered
  const isAnswered = () => {
    switch (currentQuestion) {
      case 1: return !!answers.projectCategory;
      case 2: return !!answers.projectSubtype;
      case 3: return !!answers.targetAudience;
      case 4: return !!answers.timeline;
      case 5: return !!answers.budget;
      case 6: return !!answers.successMetrics;
      case 7: return !!answers.ventureName && !!answers.ventureDescription;
      case 8: return true; // Team roles are optional
      default: return false;
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      {/* Exit button */}
      {onCancel && (
        <div className="absolute top-6 right-6 z-10">
          <Button
            variant="light"
            size="lg"
            onPress={onCancel}
            isIconOnly
            className="text-white hover:bg-white/10"
          >
            <i className="bi bi-x-lg text-2xl"></i>
          </Button>
        </div>
      )}

      {/* Back button */}
      <div className="absolute top-6 left-6 z-10">
        <Button
          variant="light"
          size="lg"
          onPress={handlePrevious}
          className="text-white hover:bg-white/10"
        >
          <i className="bi bi-arrow-left text-xl mr-2"></i>
          Back
        </Button>
      </div>

      {/* Step indicator */}
      <div className="absolute top-6 right-20 z-10">
        <span className="text-white/80 text-lg">
          Step {currentQuestion}/{totalQuestions}
        </span>
      </div>

      {/* Question content */}
      <div className="max-w-4xl mx-auto w-full">
        <AnimatePresence mode="wait">
          <VentureQuestionStep
            key={currentQuestion}
            question={currentQuestionData}
            answer={answers}
            onAnswer={handleAnswer}
            onNext={handleNext}
            isAnswered={isAnswered()}
          />
        </AnimatePresence>
      </div>

      {/* Progress dots */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
        <div className="flex space-x-2">
          {Array.from({ length: totalQuestions }, (_, i) => (
            <div
              key={i}
              className={`w-3 h-3 rounded-full transition-colors ${
                i + 1 === currentQuestion
                  ? 'bg-white'
                  : i + 1 < currentQuestion
                  ? 'bg-white/60'
                  : 'bg-white/20'
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default VentureQuestionFlow;
