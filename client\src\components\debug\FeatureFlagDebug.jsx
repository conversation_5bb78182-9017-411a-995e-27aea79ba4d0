import React from 'react';
import { useFeatureFlags } from '../../contexts/feature-flags.context';

/**
 * FeatureFlagDebug Component
 * 
 * A debugging component that displays the current state of feature flags.
 * This is useful for troubleshooting feature flag issues.
 */
const FeatureFlagDebug = () => {
  const {
    flags,
    loading,
    isFeatureEnabled
  } = useFeatureFlags();

  if (loading) {
    return <div>Loading feature flags...</div>;
  }

  return (
    <div style={{ 
      position: 'fixed', 
      bottom: '10px', 
      right: '10px', 
      zIndex: 9999,
      background: '#f8f9fa',
      border: '1px solid #dee2e6',
      borderRadius: '4px',
      padding: '10px',
      boxShadow: '0 0 10px rgba(0,0,0,0.1)',
      maxWidth: '300px'
    }}>
      <h5 style={{ marginTop: 0 }}>Feature Flag Debug</h5>
      <div>
        <strong>new-ui:</strong> {isFeatureEnabled('new-ui') ? '✅ Enabled' : '❌ Disabled'}
      </div>
      <div style={{ marginTop: '10px' }}>
        <strong>All Flags:</strong>
        <pre style={{ 
          background: '#f1f1f1', 
          padding: '5px', 
          borderRadius: '4px',
          fontSize: '12px',
          overflow: 'auto',
          maxHeight: '100px'
        }}>
          {JSON.stringify(flags, null, 2)}
        </pre>
      </div>
      <div style={{ marginTop: '10px', fontSize: '12px', color: '#6c757d' }}>
        localStorage: {localStorage.getItem('featureFlags') || 'not set'}
      </div>
    </div>
  );
};

export default FeatureFlagDebug;
