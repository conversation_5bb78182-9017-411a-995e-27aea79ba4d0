import React, { useState, useEffect, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { Card, CardBody, Button, Chip } from '@heroui/react';
import { UserContext } from '../../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../../utils/supabase/supabase.utils.js';
import { toast } from 'react-hot-toast';

/**
 * Onboarding to Project Bridge Component
 *
 * Seamlessly transitions users from onboarding completion to project creation,
 * pre-populating the wizard with selections from the onboarding flow.
 * Follows the spatial-first design philosophy with smooth transitions.
 */
const OnboardingToProjectBridge = ({
  selectedPath,
  isVisible = false,
  onClose,
  onCreateProject
}) => {
  const navigate = useNavigate();
  const { currentUser } = useContext(UserContext);
  const [showDetails, setShowDetails] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  // Authentication validation
  useEffect(() => {
    if (isVisible && !currentUser) {
      toast.error('Please log in to create a project');
      navigate('/login');
      return;
    }
  }, [isVisible, currentUser, navigate]);

  // Auto-show details after a brief delay
  useEffect(() => {
    if (isVisible && currentUser) {
      const timer = setTimeout(() => setShowDetails(true), 800);
      return () => clearTimeout(timer);
    }
  }, [isVisible, currentUser]);

  // Get project type suggestion based on selected path
  const getProjectSuggestion = () => {
    if (!selectedPath) return null;

    const { canvas, interests, goals } = selectedPath;

    // Map interests and goals to project types
    const projectTypeMapping = {
      'game-development': 'game',
      'software-development': 'software',
      'creative-content': 'creative',
      'research': 'research',
      'business': 'business'
    };

    // Determine suggested project type
    let suggestedType = 'software'; // default
    if (interests?.includes('game-development')) suggestedType = 'game';
    else if (interests?.includes('creative-content')) suggestedType = 'creative';
    else if (interests?.includes('research')) suggestedType = 'research';
    else if (interests?.includes('business')) suggestedType = 'business';

    return {
      type: suggestedType,
      title: getProjectTypeTitle(suggestedType),
      description: getProjectTypeDescription(suggestedType),
      icon: getProjectTypeIcon(suggestedType)
    };
  };

  const getProjectTypeTitle = (type) => {
    const titles = {
      game: 'Game Development Project',
      software: 'Software Development Project',
      creative: 'Creative Content Project',
      research: 'Research Project',
      business: 'Business Project'
    };
    return titles[type] || 'New Project';
  };

  const getProjectTypeDescription = (type) => {
    const descriptions = {
      game: 'Create games with collaborative development and revenue sharing',
      software: 'Build software applications with team-based contribution tracking',
      creative: 'Produce creative content with fair royalty distribution',
      research: 'Conduct research projects with transparent contribution metrics',
      business: 'Launch business ventures with equity-like revenue sharing'
    };
    return descriptions[type] || 'Collaborative project with revenue sharing';
  };

  const getProjectTypeIcon = (type) => {
    const icons = {
      game: '🎮',
      software: '💻',
      creative: '🎨',
      research: '🔬',
      business: '🚀'
    };
    return icons[type] || '📁';
  };

  // Handle project creation with database persistence
  const handleCreateProject = async () => {
    if (!currentUser) {
      toast.error('Please log in to create a project');
      navigate('/login');
      return;
    }

    setIsCreating(true);

    try {
      const suggestion = getProjectSuggestion();

      // Create project data based on onboarding selections
      const projectData = {
        name: `My ${suggestion?.title || 'New Project'}`,
        description: suggestion?.description || 'Project created from onboarding',
        project_type: suggestion?.type || 'software',
        created_by: currentUser.id,
        is_public: true,
        estimated_duration: 6,
        start_date: new Date().toISOString(),
        status: 'active',
        created_at: new Date().toISOString()
      };

      // Save project to database
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .insert([projectData])
        .select()
        .single();

      if (projectError) throw projectError;

      // Add user as project owner/contributor
      const { error: contributorError } = await supabase
        .from('project_contributors')
        .insert([{
          project_id: project.id,
          user_id: currentUser.id,
          role: 'owner',
          status: 'active',
          joined_at: new Date().toISOString()
        }]);

      if (contributorError) throw contributorError;

      toast.success('Project created successfully!');

      // Navigate to the new project dashboard
      navigate(`/project/${project.id}`);

      if (onCreateProject) {
        onCreateProject(project);
      }
    } catch (error) {
      console.error('Error creating project:', error);
      toast.error('Failed to create project. Please try again.');
    } finally {
      setIsCreating(false);
    }
  };

  // Handle skip - go to dashboard
  const handleSkip = () => {
    navigate('/');
    if (onClose) onClose();
  };

  const suggestion = getProjectSuggestion();

  // Authentication check
  if (!currentUser) {
    return null;
  }

  if (!isVisible) return null;

  return (
    <motion.div
      className="fixed inset-0 z-50 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        className="max-w-2xl mx-auto px-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3, duration: 0.5 }}
      >
        <Card className="bg-white/10 backdrop-blur-md border border-white/20">
          <CardBody className="p-8">
            {/* Header */}
            <motion.div
              className="text-center mb-8"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6 }}
            >
              <div className="text-6xl mb-4">🎉</div>
              <h2 className="text-3xl font-bold text-white mb-2">
                Welcome to Royaltea!
              </h2>
              <p className="text-white/80 text-lg">
                Ready to start your first project?
              </p>
            </motion.div>

            {/* Project Suggestion */}
            <AnimatePresence>
              {showDetails && suggestion && (
                <motion.div
                  className="mb-8"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <Card className="bg-gradient-to-br from-blue-500/20 to-purple-500/20 border border-white/10">
                    <CardBody className="p-6">
                      <div className="flex items-start gap-4">
                        <div className="text-4xl">{suggestion.icon}</div>
                        <div className="flex-1">
                          <h3 className="text-xl font-semibold text-white mb-2">
                            Suggested: {suggestion.title}
                          </h3>
                          <p className="text-white/80 mb-4">
                            {suggestion.description}
                          </p>

                          {/* Based on selections */}
                          <div className="flex flex-wrap gap-2">
                            <Chip size="sm" variant="flat" className="bg-white/10 text-white">
                              Based on your interests
                            </Chip>
                            {selectedPath?.interests?.map((interest, index) => (
                              <Chip
                                key={index}
                                size="sm"
                                variant="flat"
                                className="bg-blue-500/20 text-blue-200"
                              >
                                {interest.replace('-', ' ')}
                              </Chip>
                            ))}
                          </div>
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Action Buttons */}
            <motion.div
              className="flex gap-4 justify-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: showDetails ? 1.2 : 0.8 }}
            >
              <Button
                onClick={handleCreateProject}
                className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-8 py-3 text-lg font-medium"
                size="lg"
                isLoading={isCreating}
                disabled={isCreating || !currentUser}
              >
                {isCreating ? 'Creating Project...' : '🚀 Create Your First Project'}
              </Button>

              <Button
                onClick={handleSkip}
                variant="ghost"
                className="text-white/70 hover:text-white hover:bg-white/10 px-6 py-3"
                size="lg"
              >
                Skip for now
              </Button>
            </motion.div>

            {/* Additional Info */}
            <motion.div
              className="mt-8 text-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: showDetails ? 1.5 : 1.1 }}
            >
              <p className="text-white/60 text-sm">
                Don't worry - you can always create projects later from the dashboard
              </p>
            </motion.div>
          </CardBody>
        </Card>
      </motion.div>
    </motion.div>
  );
};

export default OnboardingToProjectBridge;
