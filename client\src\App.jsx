import React, { useState as reactUseState } from "react";
import { Routes, Route, Navigate, useLocation } from "react-router-dom";
import ExperimentalNavigation from "./components/navigation/ExperimentalNavigation";
import SimpleLogin from "./pages/user/SimpleLogin";
import ModernDashboard from "./pages/user/ModernDashboard";
import ProfilePage from "./pages/user/ProfilePage";
import PublicProfile from "./pages/user/PublicProfile";
import RetroProfilePage from "./pages/user/RetroProfilePage";
import PasswordResetPage from "./pages/user/PasswordResetPage";
import UpdatePasswordPage from "./pages/user/UpdatePasswordPage";
import SettingsPage from "./pages/user/SettingsPage";
import AuthCallback from "./pages/auth/Callback";
import RoadmapPage from "./pages/admin/Roadmap";
import RoadmapManagerPage from "./pages/admin/RoadmapManager";
import ProfileMigration from "./pages/admin/ProfileMigration";
import AdminDashboard from "./pages/admin/AdminDashboard";
import ComponentPlayground from "./pages/admin/ComponentPlayground";
import BugManager from "./pages/admin/BugManager";
import BugReportPage from "./pages/bug/BugReportPage";
import ProjectWizard from "./pages/project/ProjectWizard";
// ModernProjectCreator has been replaced by ProjectWizard
import ProjectDetail from "./pages/project/ProjectDetail";
import ProjectsList from "./pages/project/ProjectsList";
import ContributionTrackerPage from "./pages/contribution/ContributionTrackerPage";
import ContributionValidatePage from "./pages/contribution/validate";
import ValidationMetricsPage from "./pages/validation/ValidationMetricsPage";
import ContributionAnalyticsPage from "./pages/analytics/ContributionAnalyticsPage";
import RevenuePage from "./pages/revenue/RevenuePage";
import RoyaltyCalculatorPage from "./pages/royalty/RoyaltyCalculatorPage";
import KanbanPage from "./pages/kanban/KanbanPage";
import ProjectAgreements from "./pages/project/ProjectAgreements";
import AuthDebugPage from "./pages/debug/AuthDebugPage";
import NotFound from "./pages/NotFound";
import TrackPage from "./pages/track/TrackPage";
import StartPage from "./pages/start/StartPage";
import EarnPage from "./pages/earn/EarnPage";
import LearnPage from "./pages/learn/LearnPage";
import NotificationsPage from "./pages/notification/NotificationsPage";
import InvitationsPage from "./pages/invitation/InvitationsPage";
import TeamList from "./components/team/TeamList";
import TeamDetail from "./components/team/TeamDetail";
import TeamManage from "./components/team/TeamManage";
import TeamInvitations from "./components/team/TeamInvitations";
import AuthRoute from "./components/auth/AuthRoute";
import TestUI from "./components/ui/test-ui";
import ButtonTest from "./pages/test/ButtonTest";
import axios from "axios";
import { useContext, useEffect } from "react";
import { UserContext } from "../contexts/supabase-auth.context.jsx";
import { DataSyncProvider } from "./contexts/DataSyncContext";
import { Toaster } from "react-hot-toast";
import SimpleLoading from "./components/layout/SimpleLoading";
import Footer from "./components/layout/Footer";
import { fixRouting } from "./utils/routing-fix";

// Use Netlify Functions for API
axios.defaults.baseURL = '/.netlify/functions';
axios.defaults.withCredentials = true;

const navLinks = [
  { title: "Home", path: "/" },
];

// ScrollToTop component to reset scroll position on navigation
function ScrollToTop() {
  const { pathname } = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  return null;
}

// Disable focus-aware loading hooks - they can cause issues when tabbing
// We'll use the original React.useState directly

function App() {
  const { currentUser, isLoading } = useContext(UserContext);
  const location = useLocation();

  // Log navigation for debugging and fix routing issues
  useEffect(() => {
    console.log('Current location:', location.pathname);
    // Fix routing issues
    fixRouting();
  }, [location]);

  // Track loading state changes with specific debug text
  useEffect(() => {
    console.log('🔍 APP.JSX: isLoading state changed to:', isLoading);

    // Add stack trace to see where the loading state is being changed
    if (isLoading) {
      console.log('🔍 APP.JSX: Loading triggered from:', new Error().stack);
    }
  }, [isLoading]);

  // Simple loading approach - just show the loading indicator when needed
  if (isLoading) {
    console.log('🔍 APP.JSX: Rendering loading screen due to isLoading=true');
    return <SimpleLoading text="APP.JSX Loading..." fullPage={true} />;
  }

  // Add loading debugger in development mode
  const showDebugger = process.env.NODE_ENV === 'development' || window.location.search.includes('debug=true');
  return (
    <>
      <ScrollToTop />
      <Toaster position="bottom-right" toastOptions={{ duration: 5000 }} />
      <DataSyncProvider>
        <ExperimentalNavigation currentUser={currentUser} />
      </DataSyncProvider>
      <Footer navLinks={navLinks} />
    </>
  );
}

export default App;
