/* Alliance Management Styles - Gamified Business Compliance */
/* Day 2 - Styling for alliance components with business theming */

.alliance-manage-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  min-height: 100vh;
  color: #ffffff;
}

.alliance-manage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #3d4f7a;
}

.alliance-manage-header h2 {
  font-size: 2.5rem;
  font-weight: bold;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
}

.back-button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Alliance Information Section */
.alliance-info-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: #ffd700;
}

.edit-button, .save-button, .cancel-button {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: none;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.edit-button {
  background: #4f46e5;
  color: white;
}

.edit-button:hover {
  background: #4338ca;
  transform: translateY(-1px);
}

.save-button {
  background: #10b981;
  color: white;
}

.save-button:hover {
  background: #059669;
}

.cancel-button {
  background: #6b7280;
  color: white;
  margin-left: 0.5rem;
}

.cancel-button:hover {
  background: #4b5563;
}

/* Form Styles */
.edit-alliance-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #e5e7eb;
}

.form-group input,
.form-group select,
.form-group textarea {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  padding: 0.75rem;
  color: #ffffff;
  font-size: 1rem;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #ffd700;
  box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.2);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

/* Alliance Info Display */
.alliance-info {
  display: grid;
  gap: 1rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-label {
  font-weight: 500;
  color: #9ca3af;
}

.info-value {
  color: #ffffff;
  font-weight: 400;
}

/* Business Entity Section */
.business-entity-section {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.1));
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.register-company-button {
  background: linear-gradient(45deg, #10b981, #059669);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.register-company-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.no-company-registered {
  text-align: center;
  padding: 2rem;
}

.no-company-registered p {
  margin-bottom: 1rem;
  color: #e5e7eb;
}

.no-company-registered ul {
  list-style: none;
  padding: 0;
  margin: 1.5rem 0;
}

.no-company-registered li {
  padding: 0.5rem 0;
  color: #10b981;
}

/* Alliance Members Section */
.alliance-members-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.members-list {
  display: grid;
  gap: 1rem;
  margin-top: 1rem;
}

.member-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.member-info {
  flex: 1;
}

.member-name {
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.25rem;
}

.member-email {
  color: #9ca3af;
  font-size: 0.875rem;
}

.member-meta {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.role-badge {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #1a1a2e;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
}

.role-badge.founder {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
}

.role-badge.admin {
  background: linear-gradient(45deg, #8b5cf6, #7c3aed);
  color: white;
}

.role-badge.member {
  background: linear-gradient(45deg, #6b7280, #4b5563);
  color: white;
}

.admin-badge {
  background: #3b82f6;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
}

.member-actions {
  display: flex;
  gap: 0.5rem;
}

.role-button, .remove-button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.role-button {
  background: #3b82f6;
  color: white;
}

.role-button:hover {
  background: #2563eb;
}

.remove-button {
  background: #ef4444;
  color: white;
}

.remove-button:hover {
  background: #dc2626;
}

/* Company Registration Styles */
.company-registration {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem;
  margin: 1rem 0;
}

.registration-header {
  text-align: center;
  margin-bottom: 2rem;
}

.registration-header h3 {
  font-size: 1.75rem;
  color: #ffd700;
  margin-bottom: 0.5rem;
}

.registration-header p {
  color: #9ca3af;
  margin-bottom: 1.5rem;
}

.step-indicator {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.step-indicator span {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  color: #9ca3af;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.step-indicator span.active {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #1a1a2e;
  font-weight: 600;
}

.registration-form {
  max-width: 600px;
  margin: 0 auto;
}

.step-content {
  margin-bottom: 2rem;
}

.step-content h4 {
  color: #ffd700;
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
}

.error-text {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.form-group input.error,
.form-group select.error {
  border-color: #ef4444;
}

/* Review Section */
.review-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.review-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.review-item:last-child {
  border-bottom: none;
}

.compliance-notice {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.1));
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1rem;
}

.compliance-notice h5 {
  color: #10b981;
  margin-bottom: 1rem;
}

.compliance-notice ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.compliance-notice li {
  padding: 0.25rem 0;
  color: #e5e7eb;
}

/* Responsive Design */
@media (max-width: 768px) {
  .alliance-manage-container {
    padding: 1rem;
  }
  
  .alliance-manage-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .member-card {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .member-actions {
    justify-content: center;
  }
}
