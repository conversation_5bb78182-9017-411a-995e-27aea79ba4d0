// Script to check database tables and structure
const { createClient } = require('@supabase/supabase-js');

// Supabase connection details from the database-access-info.md file
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
// Using the anon key from the client code
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE2OTg5NTM2NTksImV4cCI6MjAxNDUyOTY1OX0.S2oJGZCLnB-jkBgTwgBpQJP9-RIrHzHbFBm7_zXpnwo';

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkDatabase() {
  try {
    // Check if royalty_models table exists by trying to query it
    console.log('Checking royalty_models table...');
    const { data: royaltyModels, error: royaltyModelsError } = await supabase
      .from('royalty_models')
      .select('*')
      .limit(1);

    if (royaltyModelsError) {
      console.error('Error querying royalty_models table:', royaltyModelsError);
      console.log('royalty_models table might not exist');
    } else {
      console.log('royalty_models table exists');
      console.log('Sample data:', royaltyModels);
    }

    // Check if project_settings table exists
    console.log('\nChecking project_settings table...');
    const { data: projectSettings, error: projectSettingsError } = await supabase
      .from('project_settings')
      .select('*')
      .limit(1);

    if (projectSettingsError) {
      console.error('Error querying project_settings table:', projectSettingsError);
      console.log('project_settings table might not exist');
    } else {
      console.log('project_settings table exists');
      console.log('Sample data:', projectSettings);
    }

    // Check a specific project to see how royalty model is stored
    console.log('\nChecking a specific project...');
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('*')
      .limit(1);

    if (projectsError) {
      console.error('Error querying projects table:', projectsError);
    } else if (projects && projects.length > 0) {
      const projectId = projects[0].id;
      console.log(`Found project with ID: ${projectId}`);

      // Check if this project has a royalty model in royalty_models table
      const { data: projectRoyaltyModel, error: projectRoyaltyModelError } = await supabase
        .from('royalty_models')
        .select('*')
        .eq('project_id', projectId)
        .single();

      if (projectRoyaltyModelError) {
        console.log(`No royalty model found in royalty_models table for project ${projectId}`);
      } else {
        console.log('Royalty model from royalty_models table:', projectRoyaltyModel);
      }

      // Check if this project has settings in project_settings table
      const { data: projectSettingsData, error: projectSettingsDataError } = await supabase
        .from('project_settings')
        .select('*')
        .eq('project_id', projectId)
        .eq('setting_type', 'royalty_model');

      if (projectSettingsDataError) {
        console.log(`No royalty model found in project_settings table for project ${projectId}`);
      } else {
        console.log('Royalty model from project_settings table:', projectSettingsData);
      }
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

checkDatabase();
