import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { supabase } from '../../../utils/supabase/supabase.utils';

import axios from 'axios';

const AuthDebugger = () => {
  const { currentUser, loginWithGoogle } = useContext(UserContext);
  const [debugInfo, setDebugInfo] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchDebugInfo = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.get('/api/auth-debug');
      setDebugInfo(response.data);
    } catch (err) {
      console.error('Error fetching debug info:', err);
      setError(err.message || 'Failed to fetch debug info');
    } finally {
      setLoading(false);
    }
  };

  const testGoogleAuth = async () => {
    try {
      setLoading(true);
      setError(null);

      await loginWithGoogle();
      // The redirect will happen automatically
    } catch (err) {
      console.error('Error initiating Google auth:', err);
      setError(err.message || 'Failed to initiate Google authentication');
      setLoading(false);
    }
  };

  const checkSession = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: sessionError } = await supabase.auth.getSession();

      if (sessionError) {
        throw sessionError;
      }

      setDebugInfo({
        session: data.session ? {
          user: {
            id: data.session.user.id,
            email: data.session.user.email,
            metadata: data.session.user.user_metadata
          },
          expires_at: data.session.expires_at,
          token_type: data.session.token_type
        } : null
      });
    } catch (err) {
      console.error('Error checking session:', err);
      setError(err.message || 'Failed to check session');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="mb-4">
      <Card.Header as="h5">Authentication Debugger</Card.Header>
      <Card.Body>
        {error && (
          <Alert variant="danger">
            {error}
          </Alert>
        )}

        <div className="d-flex gap-2 mb-3">
          <Button
            variant="primary"
            onClick={fetchDebugInfo}
            disabled={loading}
          >
            {loading ? 'Loading...' : 'Fetch Debug Info'}
          </Button>

          <Button
            variant="success"
            onClick={testGoogleAuth}
            disabled={loading}
          >
            {loading ? 'Loading...' : 'Test Google Auth'}
          </Button>

          <Button
            variant="info"
            onClick={checkSession}
            disabled={loading}
          >
            {loading ? 'Loading...' : 'Check Session'}
          </Button>
        </div>

        <h6>Current User:</h6>
        {currentUser ? (
          <ListGroup className="mb-3">
            <ListGroup.Item>
              <strong>ID:</strong> {currentUser.id}
            </ListGroup.Item>
            <ListGroup.Item>
              <strong>Email:</strong> {currentUser.email}
            </ListGroup.Item>
            <ListGroup.Item>
              <strong>Provider:</strong> {currentUser.app_metadata?.provider || 'unknown'}
            </ListGroup.Item>
          </ListGroup>
        ) : (
          <p>No user is currently logged in.</p>
        )}

        {debugInfo && (
          <>
            <h6>Debug Information:</h6>
            <pre className="bg-light p-3 rounded" style={{ maxHeight: '300px', overflow: 'auto' }}>
              {JSON.stringify(debugInfo, null, 2)}
            </pre>
          </>
        )}
      </Card.Body>
    </Card>
  );
};

export default AuthDebugger;
