import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { supabase } from '../../../utils/supabase/supabase.utils';
import NotificationBadge from './NotificationBadge';
import NotificationDropdown from './NotificationDropdown';
import { Button } from '../ui/heroui';
import { Bell } from 'lucide-react';

const NotificationButton = () => {
  const { currentUser } = useContext(UserContext);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch notifications
  const fetchNotifications = async () => {
    if (!currentUser) return;

    setIsLoading(true);

    try {
      // Get notifications for current user, ordered by creation date (newest first)
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', currentUser.id)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;

      setNotifications(data || []);

      // Count unread notifications
      const unread = data ? data.filter(notification => !notification.is_read).length : 0;
      setUnreadCount(unread);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Set up real-time subscription for new notifications
  useEffect(() => {
    if (!currentUser) return;

    // Initial fetch
    fetchNotifications();

    // Subscribe to changes
    const subscription = supabase
      .channel('notifications-channel')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'notifications',
        filter: `user_id=eq.${currentUser.id}`
      }, (payload) => {
        // Add new notification to the list
        setNotifications(prev => [payload.new, ...prev]);
        // Increment unread count
        setUnreadCount(prev => prev + 1);
      })
      .subscribe();

    return () => {
      supabase.removeChannel(subscription);
    };
  }, [currentUser]);

  // Toggle dropdown
  const toggleDropdown = () => {
    setIsDropdownOpen(prev => !prev);
  };

  // Close dropdown
  const closeDropdown = () => {
    setIsDropdownOpen(false);
  };

  // Mark notification as read
  const markAsRead = async (notificationId) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId);

      if (error) throw error;

      // Update local state
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === notificationId
            ? { ...notification, is_read: true }
            : notification
        )
      );

      // Decrement unread count
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('user_id', currentUser.id)
        .eq('is_read', false);

      if (error) throw error;

      // Update local state
      setNotifications(prev =>
        prev.map(notification => ({ ...notification, is_read: true }))
      );

      // Reset unread count
      setUnreadCount(0);
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  if (!currentUser) return null;

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="sm"
        className="w-9 h-9 rounded-full p-0 relative"
        onClick={toggleDropdown}
        aria-label="Notifications"
      >
        <Bell className="h-4 w-4" />
        <NotificationBadge count={unreadCount} />
      </Button>

      <NotificationDropdown
        notifications={notifications}
        unreadCount={unreadCount}
        onMarkAsRead={markAsRead}
        onMarkAllAsRead={markAllAsRead}
        isOpen={isDropdownOpen}
        onClose={closeDropdown}
      />
    </div>
  );
};

export default NotificationButton;
