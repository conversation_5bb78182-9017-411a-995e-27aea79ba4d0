// Script to extract the Retro Profile migration SQL
const fs = require('fs');
const path = require('path');

// Path to the migration file
const migrationPath = path.join(__dirname, 'supabase/migrations/20240701000003_create_retro_profile_system.sql');

// Read the migration file
try {
  const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
  console.log('Migration SQL:');
  console.log('=============');
  console.log(migrationSQL);
  
  // Save to a new file for easy access
  const outputPath = path.join(__dirname, 'retro-profile-migration.sql');
  fs.writeFileSync(outputPath, migrationSQL);
  console.log(`\nMigration SQL saved to: ${outputPath}`);
} catch (error) {
  console.error('Error reading migration file:', error.message);
}
