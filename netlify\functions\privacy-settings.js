// Privacy Settings Management API
// Backend Specialist: User privacy controls for ally discovery and connections
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Get User Privacy Settings
const getPrivacySettings = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const { data: settings, error: settingsError } = await supabase
      .from('user_privacy_settings')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (settingsError && settingsError.code !== 'PGRST116') {
      throw new Error(`Failed to fetch privacy settings: ${settingsError.message}`);
    }

    // If no settings exist, create default ones
    if (!settings) {
      const defaultSettings = {
        user_id: userId,
        discoverable_by_email: true,
        discoverable_by_name: true,
        discoverable_by_skills: true,
        show_in_recommendations: true,
        allow_friend_requests: true,
        require_mutual_connections: false,
        auto_accept_from_allies: false,
        profile_visibility: 'public',
        show_ally_count: true,
        show_project_count: true,
        show_skills: true,
        show_activity_status: true,
        email_on_friend_request: true,
        email_on_request_accepted: true,
        push_notifications: true
      };

      const { data: newSettings, error: createError } = await supabase
        .from('user_privacy_settings')
        .insert([defaultSettings])
        .select()
        .single();

      if (createError) {
        throw new Error(`Failed to create privacy settings: ${createError.message}`);
      }

      return {
        statusCode: 200,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ settings: newSettings })
      };
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ settings })
    };

  } catch (error) {
    console.error('Get privacy settings error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to get privacy settings' })
    };
  }
};

// Update User Privacy Settings
const updatePrivacySettings = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const data = JSON.parse(event.body);

    // Validate allowed fields
    const allowedFields = [
      'discoverable_by_email',
      'discoverable_by_name', 
      'discoverable_by_skills',
      'show_in_recommendations',
      'allow_friend_requests',
      'require_mutual_connections',
      'auto_accept_from_allies',
      'profile_visibility',
      'show_ally_count',
      'show_project_count',
      'show_skills',
      'show_activity_status',
      'email_on_friend_request',
      'email_on_request_accepted',
      'push_notifications'
    ];

    const updateData = {};
    for (const field of allowedFields) {
      if (data[field] !== undefined) {
        updateData[field] = data[field];
      }
    }

    if (Object.keys(updateData).length === 0) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'No valid fields provided for update' })
      };
    }

    updateData.updated_at = new Date().toISOString();

    // Update or insert privacy settings
    const { data: settings, error: updateError } = await supabase
      .from('user_privacy_settings')
      .upsert([{ user_id: userId, ...updateData }])
      .select()
      .single();

    if (updateError) {
      throw new Error(`Failed to update privacy settings: ${updateError.message}`);
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        settings,
        message: 'Privacy settings updated successfully'
      })
    };

  } catch (error) {
    console.error('Update privacy settings error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to update privacy settings' })
    };
  }
};

// Get Privacy Impact Analysis
const getPrivacyImpact = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    // Get current privacy settings
    const { data: settings } = await supabase
      .from('user_privacy_settings')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (!settings) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Privacy settings not found' })
      };
    }

    // Calculate impact metrics
    const impact = {
      discoverability: {
        level: 'high', // high, medium, low
        factors: []
      },
      connection_potential: {
        level: 'high',
        factors: []
      },
      profile_visibility: {
        level: settings.profile_visibility,
        visible_to: []
      },
      recommendations: {
        enabled: settings.show_in_recommendations,
        estimated_weekly: 0
      }
    };

    // Analyze discoverability
    if (settings.discoverable_by_name && settings.discoverable_by_email && settings.discoverable_by_skills) {
      impact.discoverability.level = 'high';
      impact.discoverability.factors.push('Discoverable by name, email, and skills');
    } else if (settings.discoverable_by_name || settings.discoverable_by_email) {
      impact.discoverability.level = 'medium';
      impact.discoverability.factors.push('Limited discoverability options enabled');
    } else {
      impact.discoverability.level = 'low';
      impact.discoverability.factors.push('Most discovery options disabled');
    }

    // Analyze connection potential
    if (settings.allow_friend_requests && !settings.require_mutual_connections) {
      impact.connection_potential.level = 'high';
      impact.connection_potential.factors.push('Open to all friend requests');
    } else if (settings.allow_friend_requests && settings.require_mutual_connections) {
      impact.connection_potential.level = 'medium';
      impact.connection_potential.factors.push('Only accepts requests from mutual connections');
    } else {
      impact.connection_potential.level = 'low';
      impact.connection_potential.factors.push('Friend requests disabled');
    }

    // Analyze profile visibility
    switch (settings.profile_visibility) {
      case 'public':
        impact.profile_visibility.visible_to.push('All users', 'Search engines', 'Non-registered visitors');
        break;
      case 'allies_only':
        impact.profile_visibility.visible_to.push('Your allies only');
        break;
      case 'private':
        impact.profile_visibility.visible_to.push('Only you');
        break;
    }

    // Estimate recommendation frequency
    if (settings.show_in_recommendations) {
      // Simple estimation based on discoverability
      const baseRecommendations = impact.discoverability.level === 'high' ? 10 : 
                                  impact.discoverability.level === 'medium' ? 5 : 2;
      impact.recommendations.estimated_weekly = baseRecommendations;
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ impact })
    };

  } catch (error) {
    console.error('Get privacy impact error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to analyze privacy impact' })
    };
  }
};

// Get Blocked Users
const getBlockedUsers = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const { data: blockedConnections, error: blockedError } = await supabase
      .from('user_allies')
      .select(`
        id,
        blocked_at,
        notes,
        ally:users!user_allies_ally_id_fkey(
          id,
          display_name,
          avatar_url
        ),
        user:users!user_allies_user_id_fkey(
          id,
          display_name,
          avatar_url
        )
      `)
      .or(`user_id.eq.${userId},ally_id.eq.${userId}`)
      .eq('status', 'blocked')
      .order('blocked_at', { ascending: false });

    if (blockedError) {
      throw new Error(`Failed to fetch blocked users: ${blockedError.message}`);
    }

    // Transform to show the blocked user
    const blockedUsers = blockedConnections?.map(connection => {
      const blockedUser = connection.user.id === userId ? connection.ally : connection.user;
      
      return {
        id: connection.id,
        blocked_user: blockedUser,
        blocked_at: connection.blocked_at,
        reason: connection.notes
      };
    }) || [];

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        blocked_users: blockedUsers,
        total: blockedUsers.length
      })
    };

  } catch (error) {
    console.error('Get blocked users error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to get blocked users' })
    };
  }
};

// Unblock User
const unblockUser = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const blockedUserId = event.path.split('/').pop();

    // Remove the blocked connection
    const { data: connection, error: deleteError } = await supabase
      .from('user_allies')
      .delete()
      .or(`and(user_id.eq.${userId},ally_id.eq.${blockedUserId}),and(user_id.eq.${blockedUserId},ally_id.eq.${userId})`)
      .eq('status', 'blocked')
      .select()
      .single();

    if (deleteError) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Blocked user not found' })
      };
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: 'User unblocked successfully',
        unblocked_user_id: blockedUserId
      })
    };

  } catch (error) {
    console.error('Unblock user error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to unblock user' })
    };
  }
};

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/privacy-settings', '');

  try {
    let response;

    if (event.httpMethod === 'GET') {
      if (path === '' || path === '/') {
        response = await getPrivacySettings(event);
      } else if (path === '/impact') {
        response = await getPrivacyImpact(event);
      } else if (path === '/blocked') {
        response = await getBlockedUsers(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'PUT') {
      if (path === '' || path === '/') {
        response = await updatePrivacySettings(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'DELETE') {
      if (path.includes('/unblock/')) {
        response = await unblockUser(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('Privacy Settings API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
