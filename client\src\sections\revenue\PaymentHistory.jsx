import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardBody, 
  CardHeader,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Chip,
  Button,
  Input,
  Select,
  SelectItem,
  Pagination,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure
} from '@heroui/react';
import { 
  Search, 
  Filter, 
  Download, 
  Eye,
  ArrowUpRight,
  ArrowDownRight,
  Calendar
} from 'lucide-react';
import { useSupabase } from '../../hooks/useSupabase';
import { formatCurrency, formatDate, formatDateTime } from '../../utils/formatters';

const PaymentHistory = () => {
  const { supabase, user } = useSupabase();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedTransaction, setSelectedTransaction] = useState(null);
  
  const [filters, setFilters] = useState({
    search: '',
    status: 'all',
    method: 'all',
    direction: 'all',
    dateRange: '30'
  });
  
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0
  });
  
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      fetchTransactions();
    }
  }, [user, filters, pagination.page]);

  const fetchTransactions = async () => {
    setLoading(true);
    try {
      let query = supabase
        .from('payment_transactions')
        .select(`
          *,
          from_user:from_user_id(full_name, avatar_url),
          to_user:to_user_id(full_name, avatar_url),
          from_account:from_account_id(account_name, institution_name),
          to_account:to_account_id(account_name, institution_name)
        `, { count: 'exact' })
        .or(`from_user_id.eq.${user.id},to_user_id.eq.${user.id}`)
        .order('created_at', { ascending: false });

      // Apply filters
      if (filters.status !== 'all') {
        query = query.eq('status', filters.status);
      }
      
      if (filters.method !== 'all') {
        query = query.eq('payment_method', filters.method);
      }
      
      if (filters.direction !== 'all') {
        if (filters.direction === 'inbound') {
          query = query.eq('to_user_id', user.id);
        } else {
          query = query.eq('from_user_id', user.id);
        }
      }
      
      if (filters.dateRange !== 'all') {
        const days = parseInt(filters.dateRange);
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        query = query.gte('created_at', startDate.toISOString());
      }
      
      if (filters.search) {
        query = query.or(`description.ilike.%${filters.search}%,reference_id.ilike.%${filters.search}%`);
      }

      // Apply pagination
      const from = (pagination.page - 1) * pagination.limit;
      const to = from + pagination.limit - 1;
      query = query.range(from, to);

      const { data, error, count } = await query;
      
      if (error) throw error;

      setTransactions(data || []);
      setPagination(prev => ({ ...prev, total: count || 0 }));
      
    } catch (error) {
      console.error('Error fetching transactions:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleViewTransaction = (transaction) => {
    setSelectedTransaction(transaction);
    onOpen();
  };

  const handleExport = async () => {
    // Implementation for exporting transaction history
    console.log('Exporting transaction history...');
  };

  const getStatusColor = (status) => {
    const colors = {
      completed: 'success',
      pending: 'warning',
      processing: 'primary',
      failed: 'danger',
      cancelled: 'default',
      returned: 'warning',
      reversed: 'danger'
    };
    return colors[status] || 'default';
  };

  const getDirectionIcon = (transaction) => {
    const isInbound = transaction.to_user_id === user.id;
    return isInbound ? 
      <ArrowDownRight className="w-4 h-4 text-green-500" /> : 
      <ArrowUpRight className="w-4 h-4 text-red-500" />;
  };

  const getTransactionAmount = (transaction) => {
    const amount = parseFloat(transaction.amount);
    const isInbound = transaction.to_user_id === user.id;
    return {
      formatted: formatCurrency(amount),
      sign: isInbound ? '+' : '-',
      color: isInbound ? 'text-green-600' : 'text-red-600'
    };
  };

  const getPaymentMethodLabel = (method) => {
    const labels = {
      ach_standard: 'ACH Standard',
      ach_same_day: 'ACH Same Day',
      rtp: 'Real-Time Payment',
      wire_domestic: 'Wire Transfer',
      wire_international: 'International Wire'
    };
    return labels[method] || method;
  };

  const totalPages = Math.ceil(pagination.total / pagination.limit);

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Payment History</h3>
            <Button 
              color="primary" 
              variant="flat" 
              startContent={<Download className="w-4 h-4" />}
              onClick={handleExport}
            >
              Export
            </Button>
          </div>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            <Input
              placeholder="Search transactions..."
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              startContent={<Search className="w-4 h-4 text-gray-400" />}
              className="lg:col-span-2"
            />
            
            <Select
              placeholder="Status"
              selectedKeys={[filters.status]}
              onSelectionChange={(keys) => setFilters(prev => ({ ...prev, status: Array.from(keys)[0] }))}
            >
              <SelectItem key="all">All Status</SelectItem>
              <SelectItem key="completed">Completed</SelectItem>
              <SelectItem key="pending">Pending</SelectItem>
              <SelectItem key="processing">Processing</SelectItem>
              <SelectItem key="failed">Failed</SelectItem>
              <SelectItem key="cancelled">Cancelled</SelectItem>
            </Select>
            
            <Select
              placeholder="Method"
              selectedKeys={[filters.method]}
              onSelectionChange={(keys) => setFilters(prev => ({ ...prev, method: Array.from(keys)[0] }))}
            >
              <SelectItem key="all">All Methods</SelectItem>
              <SelectItem key="ach_standard">ACH Standard</SelectItem>
              <SelectItem key="ach_same_day">ACH Same Day</SelectItem>
              <SelectItem key="rtp">Real-Time Payment</SelectItem>
              <SelectItem key="wire_domestic">Wire Transfer</SelectItem>
            </Select>
            
            <Select
              placeholder="Direction"
              selectedKeys={[filters.direction]}
              onSelectionChange={(keys) => setFilters(prev => ({ ...prev, direction: Array.from(keys)[0] }))}
            >
              <SelectItem key="all">All Directions</SelectItem>
              <SelectItem key="inbound">Received</SelectItem>
              <SelectItem key="outbound">Sent</SelectItem>
            </Select>
            
            <Select
              placeholder="Date Range"
              selectedKeys={[filters.dateRange]}
              onSelectionChange={(keys) => setFilters(prev => ({ ...prev, dateRange: Array.from(keys)[0] }))}
            >
              <SelectItem key="7">Last 7 days</SelectItem>
              <SelectItem key="30">Last 30 days</SelectItem>
              <SelectItem key="90">Last 90 days</SelectItem>
              <SelectItem key="365">Last year</SelectItem>
              <SelectItem key="all">All time</SelectItem>
            </Select>
          </div>
        </CardBody>
      </Card>

      {/* Transactions Table */}
      <Card>
        <CardBody className="p-0">
          <Table 
            aria-label="Payment history table"
            classNames={{
              wrapper: "min-h-[400px]",
            }}
          >
            <TableHeader>
              <TableColumn>TRANSACTION</TableColumn>
              <TableColumn>AMOUNT</TableColumn>
              <TableColumn>METHOD</TableColumn>
              <TableColumn>STATUS</TableColumn>
              <TableColumn>DATE</TableColumn>
              <TableColumn>ACTIONS</TableColumn>
            </TableHeader>
            <TableBody 
              items={transactions}
              isLoading={loading}
              emptyContent="No transactions found"
            >
              {(transaction) => {
                const amount = getTransactionAmount(transaction);
                const isInbound = transaction.to_user_id === user.id;
                
                return (
                  <TableRow key={transaction.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        {getDirectionIcon(transaction)}
                        <div>
                          <p className="font-medium">
                            {transaction.description || `${getPaymentMethodLabel(transaction.payment_method)} Transfer`}
                          </p>
                          <p className="text-sm text-gray-600">
                            {isInbound ? 'From' : 'To'}: {
                              isInbound 
                                ? transaction.from_user?.full_name || 'Unknown'
                                : transaction.to_user?.full_name || 'Unknown'
                            }
                          </p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className={`font-semibold ${amount.color}`}>
                        {amount.sign}{amount.formatted}
                      </span>
                    </TableCell>
                    <TableCell>
                      <Chip size="sm" variant="flat">
                        {getPaymentMethodLabel(transaction.payment_method)}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      <Chip 
                        size="sm" 
                        color={getStatusColor(transaction.status)}
                        variant="flat"
                      >
                        {transaction.status}
                      </Chip>
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="text-sm">{formatDate(transaction.created_at)}</p>
                        <p className="text-xs text-gray-500">{formatDateTime(transaction.created_at).split(' ')[1]}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Button
                        size="sm"
                        variant="light"
                        isIconOnly
                        onClick={() => handleViewTransaction(transaction)}
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                );
              }}
            </TableBody>
          </Table>
          
          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center p-4">
              <Pagination
                total={totalPages}
                page={pagination.page}
                onChange={(page) => setPagination(prev => ({ ...prev, page }))}
                showControls
                showShadow
              />
            </div>
          )}
        </CardBody>
      </Card>

      {/* Transaction Detail Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="2xl">
        <ModalContent>
          <ModalHeader>Transaction Details</ModalHeader>
          <ModalBody>
            {selectedTransaction && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Transaction ID</p>
                    <p className="font-mono text-sm">{selectedTransaction.id}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Amount</p>
                    <p className="font-semibold text-lg">
                      {formatCurrency(selectedTransaction.amount)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Status</p>
                    <Chip 
                      color={getStatusColor(selectedTransaction.status)}
                      variant="flat"
                    >
                      {selectedTransaction.status}
                    </Chip>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Payment Method</p>
                    <p>{getPaymentMethodLabel(selectedTransaction.payment_method)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Created</p>
                    <p>{formatDateTime(selectedTransaction.created_at)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Reference</p>
                    <p className="font-mono text-sm">
                      {selectedTransaction.reference_id || 'N/A'}
                    </p>
                  </div>
                </div>
                
                {selectedTransaction.description && (
                  <div>
                    <p className="text-sm text-gray-600 mb-1">Description</p>
                    <p>{selectedTransaction.description}</p>
                  </div>
                )}
                
                {selectedTransaction.failure_reason && (
                  <div>
                    <p className="text-sm text-gray-600 mb-1">Failure Reason</p>
                    <p className="text-red-600">{selectedTransaction.failure_reason}</p>
                  </div>
                )}
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onClose}>
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default PaymentHistory;
