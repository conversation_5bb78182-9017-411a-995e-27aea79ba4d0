// Quick API endpoint test
const fetch = require('node-fetch');

const SITE_URL = 'https://royalty.technology';

async function testEndpoints() {
  const endpoints = [
    '/.netlify/functions/companies',
    '/.netlify/functions/financial-transactions',
    '/.netlify/functions/hello',
    '/.netlify/functions/test-function'
  ];
  
  for (const endpoint of endpoints) {
    try {
      console.log(`\n🔍 Testing ${endpoint}...`);
      const response = await fetch(`${SITE_URL}${endpoint}`);
      console.log(`Status: ${response.status}`);
      console.log(`Content-Type: ${response.headers.get('content-type')}`);
      
      const text = await response.text();
      console.log(`Response: ${text.substring(0, 200)}...`);
      
      // Try to parse as JSON
      try {
        const json = JSON.parse(text);
        console.log(`✅ Valid JSON response`);
      } catch (e) {
        console.log(`❌ Not valid JSON`);
      }
      
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
  }
}

testEndpoints();
