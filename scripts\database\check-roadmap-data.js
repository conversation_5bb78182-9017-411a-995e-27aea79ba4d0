// Script to check roadmap data in Supabase
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: './client/.env.local' });

// Initialize Supabase client with service key for admin access
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Make sure VITE_SUPABASE_URL and SUPABASE_SERVICE_KEY are set in client/.env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkRoadmapData() {
  console.log('=== Checking Roadmap Data ===');
  
  try {
    // Check the roadmap table
    console.log('\nChecking roadmap table:');
    const { data: roadmapData, error: roadmapError } = await supabase
      .from('roadmap')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (roadmapError) {
      console.error('Error fetching roadmap data:', roadmapError);
    } else {
      console.log(`Found ${roadmapData.length} roadmap entries`);
      if (roadmapData.length > 0) {
        // Check if there's a Phase 0 in the data
        const phases = roadmapData[0].data;
        if (Array.isArray(phases)) {
          console.log(`Roadmap has ${phases.length} phases`);
          
          // Look for Phase 0
          const phase0 = phases.find(phase => phase.id === 0);
          if (phase0) {
            console.log('Found Phase 0:', phase0);
          } else {
            console.log('No Phase 0 found in the main data');
          }
          
          // Check all phases
          console.log('\nAll phases:');
          phases.forEach(phase => {
            console.log(`- Phase ${phase.id}: ${phase.title} (${phase.timeframe}) - ${phase.sections?.length || 0} sections`);
          });
        } else {
          console.log('Roadmap data is not an array');
        }
      }
    }
    
    // Check the roadmap_phases table
    console.log('\nChecking roadmap_phases table:');
    const { data: phasesData, error: phasesError } = await supabase
      .from('roadmap_phases')
      .select('*')
      .order('id');
    
    if (phasesError) {
      console.error('Error fetching roadmap_phases data:', phasesError);
    } else {
      console.log(`Found ${phasesData.length} phases in roadmap_phases table`);
      
      // Look for Phase 0
      const phase0 = phasesData.find(phase => phase.id === 0);
      if (phase0) {
        console.log('Found Phase 0 in roadmap_phases table:', phase0);
      } else {
        console.log('No Phase 0 found in roadmap_phases table');
      }
      
      // Check all phases
      console.log('\nAll phases in roadmap_phases table:');
      phasesData.forEach(phase => {
        console.log(`- Phase ${phase.id}: ${phase.title} (${phase.timeframe})`);
      });
    }
    
    // Check the roadmap_sections table
    console.log('\nChecking roadmap_sections table:');
    const { data: sectionsData, error: sectionsError } = await supabase
      .from('roadmap_sections')
      .select('*')
      .order('id');
    
    if (sectionsError) {
      console.error('Error fetching roadmap_sections data:', sectionsError);
    } else {
      console.log(`Found ${sectionsData.length} sections in roadmap_sections table`);
      
      // Group sections by phase
      const sectionsByPhase = {};
      sectionsData.forEach(section => {
        if (!sectionsByPhase[section.phase_id]) {
          sectionsByPhase[section.phase_id] = [];
        }
        sectionsByPhase[section.phase_id].push(section);
      });
      
      // Check if there are sections for Phase 0
      if (sectionsByPhase[0]) {
        console.log(`Found ${sectionsByPhase[0].length} sections for Phase 0:`, sectionsByPhase[0]);
      } else {
        console.log('No sections found for Phase 0');
      }
    }
    
  } catch (error) {
    console.error('Error checking roadmap data:', error);
  }
}

// Run the function
checkRoadmapData();
