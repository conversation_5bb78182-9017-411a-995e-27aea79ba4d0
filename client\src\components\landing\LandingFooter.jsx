import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@heroui/react';

/**
 * Landing Footer Component - Complete Footer with Links and Information
 * 
 * Features:
 * - Company information and branding
 * - Navigation links organized by category
 * - Social media links
 * - Legal and compliance information
 * - Newsletter signup
 */
const LandingFooter = () => {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.8,
        staggerChildren: 0.1
      }
    }
  };

  const sectionVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  };

  // Footer navigation data
  const footerSections = [
    {
      title: 'Platform',
      links: [
        { label: 'How It Works', href: '#how-it-works' },
        { label: 'Pricing', href: '#pricing' },
        { label: 'Features', href: '#features' },
        { label: 'Success Stories', href: '#success-stories' }
      ]
    },
    {
      title: 'For Creators',
      links: [
        { label: 'Individual Creators', href: '/creators' },
        { label: 'Creative Alliances', href: '/alliances' },
        { label: 'Businesses', href: '/business' },
        { label: 'Skill Verification', href: '/skills' }
      ]
    },
    {
      title: 'Resources',
      links: [
        { label: 'Documentation', href: '/docs' },
        { label: 'API Reference', href: '/api' },
        { label: 'Help Center', href: '/help' },
        { label: 'Community', href: '/community' }
      ]
    },
    {
      title: 'Company',
      links: [
        { label: 'About Us', href: '/about' },
        { label: 'Careers', href: '/careers' },
        { label: 'Press Kit', href: '/press' },
        { label: 'Contact', href: '/contact' }
      ]
    }
  ];

  const socialLinks = [
    { label: 'Twitter', href: 'https://twitter.com/royaltea', icon: '🐦' },
    { label: 'LinkedIn', href: 'https://linkedin.com/company/royaltea', icon: '💼' },
    { label: 'Discord', href: 'https://discord.gg/royaltea', icon: '💬' },
    { label: 'GitHub', href: 'https://github.com/royaltea', icon: '🐙' }
  ];

  return (
    <footer className="bg-gradient-to-br from-gray-900 to-slate-900 text-white py-16">
      <motion.div
        className="max-w-7xl mx-auto px-6"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
      >
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 lg:grid-cols-6 gap-12 mb-12">
          {/* Company Info */}
          <motion.div variants={sectionVariants} className="lg:col-span-2">
            <div className="mb-6">
              <h3 className="text-3xl font-bold mb-4">🏰 ROYALTEA</h3>
              <p className="text-gray-300 text-lg leading-relaxed">
                Keep the Tea. Keep the Creative Power.
              </p>
              <p className="text-gray-400 mt-4">
                Fair, transparent collaboration for creative industries. 
                Building the future of creative work, one alliance at a time.
              </p>
            </div>

            {/* Newsletter Signup */}
            <div className="mb-6">
              <h4 className="text-lg font-semibold mb-3">Stay Updated</h4>
              <div className="flex gap-2">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-1 px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-400 transition-colors"
                />
                <Button
                  color="primary"
                  className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                >
                  Subscribe
                </Button>
              </div>
            </div>

            {/* Social Links */}
            <div>
              <h4 className="text-lg font-semibold mb-3">Follow Us</h4>
              <div className="flex gap-4">
                {socialLinks.map((social) => (
                  <motion.a
                    key={social.label}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-white/20 transition-colors"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    title={social.label}
                  >
                    <span className="text-xl">{social.icon}</span>
                  </motion.a>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Navigation Links */}
          {footerSections.map((section) => (
            <motion.div key={section.title} variants={sectionVariants}>
              <h4 className="text-lg font-semibold mb-4">{section.title}</h4>
              <ul className="space-y-3">
                {section.links.map((link) => (
                  <li key={link.label}>
                    <a
                      href={link.href}
                      className="text-gray-300 hover:text-white transition-colors duration-200"
                    >
                      {link.label}
                    </a>
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>

        {/* Bottom Section */}
        <motion.div variants={sectionVariants}>
          <div className="border-t border-white/10 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              {/* Copyright */}
              <div className="text-gray-400">
                © 2025 Royaltea. All rights reserved.
              </div>

              {/* Legal Links */}
              <div className="flex gap-6 text-sm">
                <a href="/privacy" className="text-gray-400 hover:text-white transition-colors">
                  Privacy Policy
                </a>
                <a href="/terms" className="text-gray-400 hover:text-white transition-colors">
                  Terms of Service
                </a>
                <a href="/cookies" className="text-gray-400 hover:text-white transition-colors">
                  Cookie Policy
                </a>
                <a href="/security" className="text-gray-400 hover:text-white transition-colors">
                  Security
                </a>
              </div>

              {/* Status */}
              <div className="flex items-center gap-2 text-sm">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-gray-400">All systems operational</span>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Final CTA */}
        <motion.div variants={sectionVariants} className="text-center mt-12">
          <div className="p-8 bg-gradient-to-r from-purple-900/50 to-blue-900/50 rounded-2xl backdrop-blur-sm border border-white/10">
            <h3 className="text-2xl font-bold mb-4">Ready to Transform Your Creative Business?</h3>
            <p className="text-gray-300 mb-6">
              Join thousands of creators who have already made the switch to fair, transparent collaboration.
            </p>
            <Button
              size="lg"
              className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold px-8 py-4 text-lg shadow-lg hover:shadow-xl transition-all duration-300"
              onPress={() => {
                // Scroll to top or navigate to signup
                window.scrollTo({ top: 0, behavior: 'smooth' });
              }}
            >
              Get Started Free
            </Button>
          </div>
        </motion.div>
      </motion.div>
    </footer>
  );
};

export default LandingFooter;
