-- COMPLETE DATABASE MIGRATION
-- Day 3 - Complete all compliance tables and fix RLS policies

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create companies table
CREATE TABLE IF NOT EXISTS public.companies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    legal_name TEXT NOT NULL,
    tax_id TEXT NOT NULL UNIQUE,
    company_type TEXT NOT NULL CHECK (company_type IN ('corporation', 'llc', 'partnership', 'sole_proprietorship')),
    incorporation_state TEXT,
    incorporation_country TEXT DEFAULT 'US',
    incorporation_date DATE,
    doing_business_as TEXT,
    industry_classification TEXT,
    business_description TEXT,
    website_url TEXT,
    primary_address JSONB NOT NULL,
    mailing_address JSONB,
    primary_email TEXT NOT NULL,
    primary_phone TEXT,
    fiscal_year_end DATE DEFAULT (CURRENT_DATE + INTERVAL '1 year'),
    accounting_method TEXT DEFAULT 'accrual' CHECK (accounting_method IN ('accrual', 'cash')),
    base_currency TEXT DEFAULT 'USD',
    is_active BOOLEAN DEFAULT true,
    dissolution_date DATE,
    compliance_status TEXT DEFAULT 'active' CHECK (compliance_status IN ('active', 'suspended', 'dissolved')),
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add company fields to teams table
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teams' AND column_name = 'company_id') THEN
        ALTER TABLE public.teams ADD COLUMN company_id UUID REFERENCES public.companies(id) ON DELETE SET NULL;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teams' AND column_name = 'is_business_entity') THEN
        ALTER TABLE public.teams ADD COLUMN is_business_entity BOOLEAN DEFAULT false;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teams' AND column_name = 'alliance_type') THEN
        ALTER TABLE public.teams ADD COLUMN alliance_type TEXT DEFAULT 'emerging' CHECK (alliance_type IN ('emerging', 'established', 'solo'));
    END IF;
END $$;

-- Create financial_transactions table
CREATE TABLE IF NOT EXISTS public.financial_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
    project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
    team_id UUID REFERENCES public.teams(id) ON DELETE SET NULL,
    transaction_type TEXT NOT NULL CHECK (transaction_type IN ('commission', 'recurring_fee', 'royalty', 'expense', 'refund', 'bonus', 'salary')),
    transaction_category TEXT DEFAULT 'business_payment' CHECK (transaction_category IN ('business_payment', 'contractor_payment', 'employee_payment', 'expense_reimbursement')),
    gross_amount DECIMAL(12,2) NOT NULL CHECK (gross_amount >= 0),
    tax_amount DECIMAL(12,2) DEFAULT 0 CHECK (tax_amount >= 0),
    net_amount DECIMAL(12,2) NOT NULL CHECK (net_amount >= 0),
    currency TEXT DEFAULT 'USD',
    exchange_rate DECIMAL(10,6) DEFAULT 1.0,
    tax_category TEXT CHECK (tax_category IN ('1099-NEC', '1099-MISC', 'W2', 'exempt', 'international')),
    requires_1099 BOOLEAN DEFAULT false,
    requires_w2 BOOLEAN DEFAULT false,
    backup_withholding_rate DECIMAL(5,2) DEFAULT 0 CHECK (backup_withholding_rate >= 0 AND backup_withholding_rate <= 100),
    tax_year INTEGER DEFAULT EXTRACT(YEAR FROM CURRENT_DATE),
    payer_company_id UUID REFERENCES public.companies(id),
    payee_user_id UUID REFERENCES auth.users(id),
    payee_company_id UUID REFERENCES public.companies(id),
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'processing', 'paid', 'failed', 'cancelled', 'disputed')),
    processed_at TIMESTAMP WITH TIME ZONE,
    payment_method TEXT CHECK (payment_method IN ('ach', 'wire', 'check', 'paypal', 'stripe', 'manual')),
    external_transaction_id TEXT,
    approval_required BOOLEAN DEFAULT true,
    approved_by UUID REFERENCES auth.users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    approval_notes TEXT,
    description TEXT NOT NULL,
    reference_number TEXT,
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create commission_payments table
CREATE TABLE IF NOT EXISTS public.commission_payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    financial_transaction_id UUID NOT NULL REFERENCES public.financial_transactions(id) ON DELETE CASCADE,
    sales_amount DECIMAL(12,2) NOT NULL CHECK (sales_amount >= 0),
    commission_rate DECIMAL(5,2) NOT NULL CHECK (commission_rate >= 0 AND commission_rate <= 100),
    commission_amount DECIMAL(12,2) NOT NULL CHECK (commission_amount >= 0),
    sale_date DATE NOT NULL,
    product_or_service TEXT NOT NULL,
    client_reference TEXT,
    sales_rep_id UUID NOT NULL REFERENCES auth.users(id),
    payment_due_date DATE,
    payment_terms TEXT DEFAULT 'net_30' CHECK (payment_terms IN ('immediate', 'net_15', 'net_30', 'net_60')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create recurring_fees table
CREATE TABLE IF NOT EXISTS public.recurring_fees (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
    payee_user_id UUID NOT NULL REFERENCES auth.users(id),
    fee_type TEXT NOT NULL CHECK (fee_type IN ('talent_fee', 'subscription', 'retainer', 'maintenance')),
    amount DECIMAL(12,2) NOT NULL CHECK (amount >= 0),
    currency TEXT DEFAULT 'USD',
    frequency TEXT NOT NULL CHECK (frequency IN ('weekly', 'monthly', 'quarterly', 'annually')),
    start_date DATE NOT NULL,
    end_date DATE,
    next_payment_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    paused_until DATE,
    description TEXT NOT NULL,
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create tax_documents table
CREATE TABLE IF NOT EXISTS public.tax_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
    payee_user_id UUID NOT NULL REFERENCES auth.users(id),
    payee_company_id UUID REFERENCES public.companies(id),
    document_type TEXT NOT NULL CHECK (document_type IN ('1099-NEC', '1099-MISC', 'W2', '1042-S')),
    tax_year INTEGER NOT NULL,
    total_amount DECIMAL(12,2) NOT NULL CHECK (total_amount >= 0),
    nonemployee_compensation DECIMAL(12,2) DEFAULT 0,
    rents DECIMAL(12,2) DEFAULT 0,
    royalties DECIMAL(12,2) DEFAULT 0,
    other_income DECIMAL(12,2) DEFAULT 0,
    backup_withholding DECIMAL(12,2) DEFAULT 0,
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'generated', 'sent', 'corrected')),
    generated_at TIMESTAMP WITH TIME ZONE,
    sent_at TIMESTAMP WITH TIME ZONE,
    document_url TEXT,
    correction_of UUID REFERENCES public.tax_documents(id),
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_companies_tax_id ON public.companies(tax_id);
CREATE INDEX IF NOT EXISTS idx_companies_active ON public.companies(is_active);
CREATE INDEX IF NOT EXISTS idx_teams_company_id ON public.teams(company_id);
CREATE INDEX IF NOT EXISTS idx_financial_transactions_company ON public.financial_transactions(company_id, tax_year);
CREATE INDEX IF NOT EXISTS idx_financial_transactions_payee ON public.financial_transactions(payee_user_id, tax_year);
CREATE INDEX IF NOT EXISTS idx_commission_payments_sales_rep ON public.commission_payments(sales_rep_id, sale_date);
CREATE INDEX IF NOT EXISTS idx_recurring_fees_company ON public.recurring_fees(company_id, is_active);
CREATE INDEX IF NOT EXISTS idx_recurring_fees_next_payment ON public.recurring_fees(next_payment_date) WHERE is_active = true;

-- Enable RLS
ALTER TABLE public.companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.financial_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.commission_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.recurring_fees ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tax_documents ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can view companies they have access to" ON public.companies;
DROP POLICY IF EXISTS "Users can create companies" ON public.companies;
DROP POLICY IF EXISTS "Company creators and admins can update companies" ON public.companies;

-- Create simple, non-recursive RLS policies
CREATE POLICY "companies_select_policy" ON public.companies
    FOR SELECT USING (
        created_by = auth.uid() OR
        id IN (
            SELECT t.company_id 
            FROM public.teams t 
            JOIN public.team_members tm ON t.id = tm.team_id 
            WHERE tm.user_id = auth.uid() AND t.company_id IS NOT NULL
        )
    );

CREATE POLICY "companies_insert_policy" ON public.companies
    FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "companies_update_policy" ON public.companies
    FOR UPDATE USING (
        created_by = auth.uid() OR
        id IN (
            SELECT t.company_id 
            FROM public.teams t 
            JOIN public.team_members tm ON t.id = tm.team_id 
            WHERE tm.user_id = auth.uid() AND tm.is_admin = true AND t.company_id IS NOT NULL
        )
    );

-- Financial transactions policies
CREATE POLICY "financial_transactions_select_policy" ON public.financial_transactions
    FOR SELECT USING (
        created_by = auth.uid() OR
        payee_user_id = auth.uid() OR
        company_id IN (
            SELECT t.company_id 
            FROM public.teams t 
            JOIN public.team_members tm ON t.id = tm.team_id 
            WHERE tm.user_id = auth.uid() AND t.company_id IS NOT NULL
        )
    );

CREATE POLICY "financial_transactions_insert_policy" ON public.financial_transactions
    FOR INSERT WITH CHECK (created_by = auth.uid());

-- Commission payments policies
CREATE POLICY "commission_payments_select_policy" ON public.commission_payments
    FOR SELECT USING (
        sales_rep_id = auth.uid() OR
        financial_transaction_id IN (
            SELECT ft.id FROM public.financial_transactions ft
            WHERE ft.created_by = auth.uid() OR ft.payee_user_id = auth.uid()
        )
    );

-- Recurring fees policies
CREATE POLICY "recurring_fees_select_policy" ON public.recurring_fees
    FOR SELECT USING (
        created_by = auth.uid() OR
        payee_user_id = auth.uid() OR
        company_id IN (
            SELECT t.company_id 
            FROM public.teams t 
            JOIN public.team_members tm ON t.id = tm.team_id 
            WHERE tm.user_id = auth.uid() AND t.company_id IS NOT NULL
        )
    );

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON public.companies TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.financial_transactions TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.commission_payments TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.recurring_fees TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.tax_documents TO authenticated;

-- Insert VRC sample data
INSERT INTO public.companies (
    legal_name, tax_id, company_type, incorporation_state, incorporation_country,
    doing_business_as, industry_classification, business_description, website_url,
    primary_address, primary_email, primary_phone, fiscal_year_end, accounting_method
) VALUES (
    'VRC Entertainment LLC', '12-3456789', 'llc', 'CA', 'US',
    'VRC Films', '512110', 'Independent film production and talent management company',
    'https://vrcfilms.com',
    '{"street": "123 Hollywood Blvd", "city": "Los Angeles", "state": "CA", "zip": "90028", "country": "US"}',
    '<EMAIL>', '******-123-4567', '2024-12-31', 'accrual'
) ON CONFLICT (tax_id) DO UPDATE SET
    business_description = EXCLUDED.business_description,
    updated_at = now();

-- Add comments
COMMENT ON TABLE public.companies IS 'Legal business entities with full tax compliance';
COMMENT ON TABLE public.financial_transactions IS 'Tax-compliant financial transactions with automatic 1099 tracking';
COMMENT ON TABLE public.commission_payments IS 'Commission payment tracking for sales teams';
COMMENT ON TABLE public.recurring_fees IS 'Recurring fee management for talent and subscriptions';
