// Script to update the roadmap with PDF preview improvements
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
// You'll need to replace this with your actual service key
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

// Check if we have the key
if (!supabaseKey) {
  console.error('Error: SUPABASE_SERVICE_KEY environment variable is not set');
  console.log('Please set the SUPABASE_SERVICE_KEY environment variable or hardcode it in this script');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

console.log(`Using Supabase URL: ${supabaseUrl}`);
console.log('=== Roadmap Management ===\n');

// Function to get the current roadmap data
async function getCurrentRoadmap() {
  try {
    console.log('=== Getting current roadmap data ===');

    // Get the latest roadmap entry
    const { data: roadmapData, error: roadmapError } = await supabase
      .from('roadmap')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1);

    if (roadmapError) {
      console.error('Error fetching roadmap from Supabase:', roadmapError);
      return null;
    }

    if (!roadmapData || roadmapData.length === 0) {
      console.log('No roadmap data found in the database');
      return null;
    }

    console.log('Found roadmap data:', roadmapData[0]);
    return roadmapData[0];
  } catch (error) {
    console.error('Error in getCurrentRoadmap:', error);
    return null;
  }
}

// Function to update the latest feature
async function updateLatestFeature(latestFeature) {
  try {
    console.log(`\n=== Updating latest feature to: "${latestFeature}" ===`);

    // Get the latest roadmap entry
    const { data: roadmapData, error: roadmapError } = await supabase
      .from('roadmap')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1);

    if (roadmapError) {
      console.error('Error fetching roadmap from Supabase:', roadmapError);
      return false;
    }

    if (!roadmapData || roadmapData.length === 0) {
      console.log('No roadmap data found in the database');
      return false;
    }

    // Update both the deprecated field and the metadata object
    const roadmap = roadmapData[0].data;

    // Find the metadata item
    const metadataIndex = roadmap.findIndex(item => item.type === 'metadata');

    if (metadataIndex !== -1) {
      // Update the latest_feature object in the metadata
      roadmap[metadataIndex].latest_feature = {
        title: "PDF Preview Improvements",
        description: "Enhanced PDF preview with left-justified text and fixed automatic download issues. PDFs now display properly and only download when explicitly requested by the user.",
        date: new Date().toISOString(),
        author: "Development Team",
        version: "1.0.1"
      };
    }

    // Update the roadmap
    const { data: updatedData, error: updateError } = await supabase
      .from('roadmap')
      .update({
        data: roadmap,
        latest_feature: latestFeature, // This is the deprecated field
        last_updated: new Date().toISOString()
      })
      .eq('id', roadmapData[0].id)
      .select();

    if (updateError) {
      console.error('Error updating roadmap:', updateError);
      return false;
    }

    console.log('Updated latest feature successfully:', updatedData);
    return true;
  } catch (error) {
    console.error('Error in updateLatestFeature:', error);
    return false;
  }
}

// Function to update a task status
async function updateTaskStatus(taskId, completed) {
  try {
    console.log(`\n=== Updating task ${taskId} to ${completed ? 'completed' : 'not completed'} ===`);

    // Get the latest roadmap entry
    const { data: roadmapData, error: roadmapError } = await supabase
      .from('roadmap')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1);

    if (roadmapError) {
      console.error('Error fetching roadmap from Supabase:', roadmapError);
      return false;
    }

    if (!roadmapData || roadmapData.length === 0) {
      console.log('No roadmap data found in the database');
      return false;
    }

    const roadmap = roadmapData[0].data;

    // Find the task by ID
    let found = false;
    for (const phase of roadmap) {
      if (phase.type === 'metadata' || !phase.sections) continue;

      for (const section of phase.sections) {
        const task = section.tasks.find(t => t.id === taskId);
        if (task) {
          task.completed = completed;
          found = true;
          break;
        }
      }
      if (found) break;
    }

    if (!found) {
      console.log(`Task ${taskId} not found in the roadmap`);
      return false;
    }

    // Update the roadmap
    const { data: updatedData, error: updateError } = await supabase
      .from('roadmap')
      .update({
        data: roadmap,
        last_updated: new Date().toISOString()
      })
      .eq('id', roadmapData[0].id)
      .select();

    if (updateError) {
      console.error('Error updating roadmap:', updateError);
      return false;
    }

    console.log('Updated roadmap successfully');
    return true;
  } catch (error) {
    console.error('Error in updateTaskStatus:', error);
    return false;
  }
}

// Function to add a new task to the roadmap
async function addTask(phaseId, sectionId, taskText, completed = false) {
  try {
    console.log(`\n=== Adding new task to phase ${phaseId}, section ${sectionId}: "${taskText}" ===`);

    // Get the latest roadmap entry
    const { data: roadmapData, error: roadmapError } = await supabase
      .from('roadmap')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1);

    if (roadmapError) {
      console.error('Error fetching roadmap from Supabase:', roadmapError);
      return false;
    }

    if (!roadmapData || roadmapData.length === 0) {
      console.log('No roadmap data found in the database');
      return false;
    }

    const roadmap = roadmapData[0].data;

    // Find the phase and section
    const phase = roadmap.find(p => p.id === phaseId);
    if (!phase || !phase.sections) {
      console.log(`Phase ${phaseId} not found in the roadmap`);
      return false;
    }

    const section = phase.sections.find(s => s.id === sectionId);
    if (!section) {
      console.log(`Section ${sectionId} not found in phase ${phaseId}`);
      return false;
    }

    // Generate a new task ID
    const lastTaskId = section.tasks.length > 0
      ? section.tasks[section.tasks.length - 1].id
      : `${sectionId}.0`;

    const lastIdParts = lastTaskId.split('.');
    const lastIdNumber = parseInt(lastIdParts[lastIdParts.length - 1]);
    const newIdNumber = lastIdNumber + 1;
    const newTaskId = `${sectionId}.${newIdNumber}`;

    // Add the new task
    section.tasks.push({
      id: newTaskId,
      text: taskText,
      completed: completed
    });

    // Update the roadmap
    const { data: updatedData, error: updateError } = await supabase
      .from('roadmap')
      .update({
        data: roadmap,
        last_updated: new Date().toISOString()
      })
      .eq('id', roadmapData[0].id)
      .select();

    if (updateError) {
      console.error('Error updating roadmap:', updateError);
      return false;
    }

    console.log(`Added new task with ID ${newTaskId} successfully`);
    return true;
  } catch (error) {
    console.error('Error in addTask:', error);
    return false;
  }
}

// Main function
async function main() {
  try {
    // Get current roadmap
    const roadmap = await getCurrentRoadmap();
    if (!roadmap) {
      console.error('Failed to get current roadmap');
      return;
    }

    // Calculate and display roadmap stats
    const stats = calculateStats(roadmap.data);
    console.log('\n=== Roadmap Stats ===');
    console.log(`Total Tasks: ${stats.totalTasks}`);
    console.log(`Completed Tasks: ${stats.completedTasks}`);
    console.log(`Progress Percentage: ${stats.progressPercentage}%`);

    console.log('\n=== Phase Progress ===');
    stats.phases.forEach(phase => {
      console.log(`Phase ${phase.id}: ${phase.title} - ${phase.progress}%`);
    });

    // Update the latest feature
    await updateLatestFeature("PDF Preview Improvements");

    // Mark PDF preview tasks as completed
    await updateTaskStatus("5.3.4", true); // Improve PDF preview formatting
    await updateTaskStatus("5.3.5", true); // Fix PDF download issues

    // Add new task for agreement customization
    await addTask(5, "5.3", "Enhance agreement customization for project-specific details", false);

    console.log('\n=== Roadmap Management Complete ===');
  } catch (error) {
    console.error('Error in main function:', error);
  }
}

// Function to calculate roadmap stats
function calculateStats(phases) {
  let totalTasks = 0;
  let completedTasks = 0;
  let phaseStats = [];

  phases.forEach(phase => {
    if (phase.type === 'metadata' || !phase.sections) return;

    let phaseTotalTasks = 0;
    let phaseCompletedTasks = 0;

    phase.sections.forEach(section => {
      phaseTotalTasks += section.tasks.length;
      phaseCompletedTasks += section.tasks.filter(task => task.completed).length;
    });

    totalTasks += phaseTotalTasks;
    completedTasks += phaseCompletedTasks;

    phaseStats.push({
      id: phase.id,
      title: phase.title,
      timeframe: phase.timeframe,
      progress: phaseTotalTasks > 0 ? Math.round((phaseCompletedTasks / phaseTotalTasks) * 100) : 0
    });
  });

  const progressPercentage = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

  return {
    totalTasks,
    completedTasks,
    progressPercentage,
    phases: phaseStats
  };
}

// Run the main function
main();
