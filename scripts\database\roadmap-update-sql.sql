-- Roadmap Update SQL for PDF Preview Improvements

-- 1. Update the latest feature in the metadata
WITH current_roadmap AS (
  SELECT id, data
  FROM roadmap
  ORDER BY created_at DESC
  LIMIT 1
)
UPDATE roadmap
SET 
  data = jsonb_set(
    data, 
    '{6,latest_feature}', 
    '{"title":"PDF Preview Improvements","description":"Enhanced PDF preview with left-justified text and fixed automatic download issues. PDFs now display properly and only download when explicitly requested by the user.","date":"2025-05-05T01:29:20.685Z","author":"Development Team","version":"1.0.1"}'::jsonb
  ),
  last_updated = NOW()
FROM current_roadmap
WHERE roadmap.id = current_roadmap.id;

-- 2. Mark task 5.3.4 (Improve PDF preview formatting) as completed
WITH current_roadmap AS (
  SELECT id, data
  FROM roadmap
  ORDER BY created_at DESC
  LIMIT 1
)
UPDATE roadmap
SET 
  data = jsonb_set(
    data, 
    '{4,sections,2,tasks,3,completed}', 
    'true'
  ),
  last_updated = NOW()
FROM current_roadmap
WHERE roadmap.id = current_roadmap.id;

-- 3. Mark task 5.3.5 (Fix PDF download issues) as completed
WITH current_roadmap AS (
  SELECT id, data
  FROM roadmap
  ORDER BY created_at DESC
  LIMIT 1
)
UPDATE roadmap
SET 
  data = jsonb_set(
    data, 
    '{4,sections,2,tasks,4,completed}', 
    'true'
  ),
  last_updated = NOW()
FROM current_roadmap
WHERE roadmap.id = current_roadmap.id;

-- 4. Add a new task for agreement customization
WITH current_roadmap AS (
  SELECT id, data
  FROM roadmap
  ORDER BY created_at DESC
  LIMIT 1
)
UPDATE roadmap
SET 
  data = jsonb_set(
    data, 
    '{4,sections,2,tasks}', 
    (
      SELECT jsonb_agg(task)
      FROM (
        SELECT *
        FROM jsonb_array_elements((SELECT data#>'{4,sections,2,tasks}' FROM current_roadmap)) AS task
        UNION ALL
        SELECT '{"id":"5.3.6","text":"Enhance agreement customization for project-specific details","completed":false}'::jsonb
      ) AS tasks
    )
  ),
  last_updated = NOW()
FROM current_roadmap
WHERE roadmap.id = current_roadmap.id;

-- 5. Also update the deprecated top-level latest_feature field for backward compatibility
WITH current_roadmap AS (
  SELECT id, data
  FROM roadmap
  ORDER BY created_at DESC
  LIMIT 1
)
UPDATE roadmap
SET 
  latest_feature = 'PDF Preview Improvements',
  last_updated = NOW()
FROM current_roadmap
WHERE roadmap.id = current_roadmap.id;
