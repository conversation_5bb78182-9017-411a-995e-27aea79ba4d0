// Latest Feature API function using ES modules
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const initSupabase = () => {
  const supabaseUrl = Netlify.env.get("SUPABASE_URL");
  const supabaseKey = Netlify.env.get("SUPABASE_SERVICE_KEY") || Netlify.env.get("SUPABASE_ANON_KEY");
  
  if (!supabaseUrl || !supabaseKey) {
    throw new Error('Missing Supabase credentials');
  }
  
  return createClient(supabaseUrl, supabaseKey);
};

// Main function handler
export default async (req, context) => {
  // Set CORS headers for cross-origin access
  const headers = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, OPTIONS'
  };
  
  // Handle OPTIONS request for CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers
    });
  }
  
  try {
    // Initialize Supabase client
    const supabase = initSupabase();
    
    // First try to get the latest feature from the dedicated column
    const { data: roadmapData, error: roadmapError } = await supabase
      .from('roadmap')
      .select('latest_feature, data')
      .order('updated_at', { ascending: false })
      .limit(1);
    
    if (roadmapError) {
      console.error('Error fetching latest feature from Supabase:', roadmapError);
      throw roadmapError;
    }
    
    if (!roadmapData || roadmapData.length === 0) {
      throw new Error('No roadmap data found in Supabase');
    }
    
    // Get the latest feature from the dedicated column if available, otherwise from the data array
    let latestFeature = roadmapData[0].latest_feature;
    
    // If latest_feature column is empty, try to get it from the data array
    if (!latestFeature) {
      const roadmap = roadmapData[0].data;
      const metadataItem = roadmap.find(item => item.type === 'metadata');
      if (metadataItem && metadataItem.latest_feature) {
        latestFeature = metadataItem.latest_feature;
      }
    }
    
    // Return the latest feature
    return new Response(
      JSON.stringify({
        success: true,
        latest_feature: latestFeature
      }),
      { headers }
    );
  } catch (error) {
    console.error('Error in latest-feature function:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      { 
        status: 500,
        headers
      }
    );
  }
};

// Configure the function path
export const config = {
  path: "/api/latest-feature"
};
