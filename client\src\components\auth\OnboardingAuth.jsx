import React, { useState, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardBody, Button, Input } from '@heroui/react';
import { UserContext } from '../../../contexts/supabase-auth.context';

/**
 * OnboardingAuth Component
 * 
 * Provides a smooth authentication experience with slide transitions
 * between login and signup forms, styled to match the experimental navigation.
 */
const OnboardingAuth = ({ onAuthSuccess }) => {
  const { login, signup, loginWithGoogle, loginWithGithub, isLoading } = useContext(UserContext);
  
  const [isSignUp, setIsSignUp] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    displayName: '',
    confirmPassword: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) setError(''); // Clear error when user starts typing
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      if (isSignUp) {
        // Validate passwords match
        if (formData.password !== formData.confirmPassword) {
          throw new Error('Passwords do not match');
        }
        
        // Validate display name
        if (!formData.displayName.trim()) {
          throw new Error('Display name is required');
        }

        await signup(formData.email, formData.password, { 
          full_name: formData.displayName 
        });
      } else {
        await login(formData.email, formData.password);
      }
      
      onAuthSuccess();
    } catch (err) {
      console.error('Auth error:', err);
      setError(err.message || 'An error occurred during authentication');
    } finally {
      setLoading(false);
    }
  };

  const handleSocialAuth = async (provider) => {
    setError('');
    setLoading(true);
    
    try {
      if (provider === 'google') {
        await loginWithGoogle();
      } else if (provider === 'github') {
        await loginWithGithub();
      }
      onAuthSuccess();
    } catch (err) {
      console.error('Social auth error:', err);
      setError(err.message || `Failed to sign in with ${provider}`);
    } finally {
      setLoading(false);
    }
  };

  const toggleMode = () => {
    setIsSignUp(!isSignUp);
    setError('');
    setFormData({
      email: '',
      password: '',
      displayName: '',
      confirmPassword: ''
    });
  };

  const slideVariants = {
    enter: (direction) => ({
      x: direction > 0 ? 300 : -300,
      opacity: 0
    }),
    center: {
      zIndex: 1,
      x: 0,
      opacity: 1
    },
    exit: (direction) => ({
      zIndex: 0,
      x: direction < 0 ? 300 : -300,
      opacity: 0
    })
  };

  return (
    <div className="fixed inset-0 z-50 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
      <div className="w-full max-w-md mx-auto px-6">
        {/* Header */}
        <motion.div 
          className="text-center mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl font-bold text-white mb-2">Royaltea</h1>
          <p className="text-white text-opacity-80">
            Empowering creators with fair compensation and transparent royalty tracking
          </p>
        </motion.div>

        {/* Auth Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Card className="bg-white bg-opacity-10 backdrop-blur-md border border-white border-opacity-20">
            <CardBody className="p-8">
              <div className="relative overflow-hidden">
                <AnimatePresence mode="wait" custom={isSignUp ? 1 : -1}>
                  <motion.div
                    key={isSignUp ? 'signup' : 'login'}
                    custom={isSignUp ? 1 : -1}
                    variants={slideVariants}
                    initial="enter"
                    animate="center"
                    exit="exit"
                    transition={{
                      x: { type: "spring", stiffness: 300, damping: 30 },
                      opacity: { duration: 0.2 }
                    }}
                  >
                    <form onSubmit={handleSubmit} className="space-y-6">
                      <div className="text-center mb-6">
                        <h2 className="text-2xl font-bold text-white">
                          {isSignUp ? 'Create Account' : 'Welcome Back'}
                        </h2>
                        <p className="text-white text-opacity-70 mt-1">
                          {isSignUp 
                            ? 'Join the creative revolution' 
                            : 'Sign in to continue your journey'
                          }
                        </p>
                      </div>

                      {error && (
                        <motion.div
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="bg-red-500 bg-opacity-20 border border-red-500 border-opacity-50 rounded-lg p-3"
                        >
                          <p className="text-red-200 text-sm">{error}</p>
                        </motion.div>
                      )}

                      {isSignUp && (
                        <Input
                          label="Display Name"
                          placeholder="How should we call you?"
                          value={formData.displayName}
                          onChange={(e) => handleInputChange('displayName', e.target.value)}
                          required
                          className="text-white"
                          classNames={{
                            input: "text-white placeholder:text-white/50",
                            inputWrapper: "bg-white/10 border-white/20 hover:border-white/40"
                          }}
                        />
                      )}

                      <Input
                        type="email"
                        label="Email"
                        placeholder="<EMAIL>"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        required
                        className="text-white"
                        classNames={{
                          input: "text-white placeholder:text-white/50",
                          inputWrapper: "bg-white/10 border-white/20 hover:border-white/40"
                        }}
                      />

                      <Input
                        type="password"
                        label="Password"
                        placeholder="Enter your password"
                        value={formData.password}
                        onChange={(e) => handleInputChange('password', e.target.value)}
                        required
                        className="text-white"
                        classNames={{
                          input: "text-white placeholder:text-white/50",
                          inputWrapper: "bg-white/10 border-white/20 hover:border-white/40"
                        }}
                      />

                      {isSignUp && (
                        <Input
                          type="password"
                          label="Confirm Password"
                          placeholder="Confirm your password"
                          value={formData.confirmPassword}
                          onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                          required
                          className="text-white"
                          classNames={{
                            input: "text-white placeholder:text-white/50",
                            inputWrapper: "bg-white/10 border-white/20 hover:border-white/40"
                          }}
                        />
                      )}

                      <Button
                        type="submit"
                        className="w-full bg-white bg-opacity-20 backdrop-blur-md text-white border border-white border-opacity-30"
                        size="lg"
                        isLoading={loading || isLoading}
                      >
                        {isSignUp ? 'Create Account' : 'Sign In'}
                      </Button>

                      <div className="relative">
                        <div className="absolute inset-0 flex items-center">
                          <div className="w-full border-t border-white border-opacity-20"></div>
                        </div>
                        <div className="relative flex justify-center text-sm">
                          <span className="px-2 bg-transparent text-white text-opacity-70">Or continue with</span>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-3">
                        <Button
                          type="button"
                          variant="bordered"
                          className="border-white border-opacity-30 text-white"
                          onClick={() => handleSocialAuth('google')}
                          isDisabled={loading || isLoading}
                        >
                          <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                            <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                          </svg>
                          Google
                        </Button>
                        <Button
                          type="button"
                          variant="bordered"
                          className="border-white border-opacity-30 text-white"
                          onClick={() => handleSocialAuth('github')}
                          isDisabled={loading || isLoading}
                        >
                          <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                          </svg>
                          GitHub
                        </Button>
                      </div>

                      <div className="text-center">
                        <button
                          type="button"
                          onClick={toggleMode}
                          className="text-white text-opacity-70 hover:text-opacity-100 transition-colors"
                        >
                          {isSignUp 
                            ? 'Already have an account? Sign in' 
                            : "Don't have an account? Sign up"
                          }
                        </button>
                      </div>
                    </form>
                  </motion.div>
                </AnimatePresence>
              </div>
            </CardBody>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default OnboardingAuth;
