import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import ContributionEntryForm from './ContributionEntryForm';
import BulkContributionForm from './BulkContributionForm';

const ContributionTracker = ({ projectId }) => {
  const { currentUser } = useContext(UserContext);
  const [contributions, setContributions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [formMode, setFormMode] = useState('single'); // 'single' or 'bulk'
  const [editingContribution, setEditingContribution] = useState(null);
  const [project, setProject] = useState(null);
  const [contributors, setContributors] = useState({});
  const [stats, setStats] = useState({
    totalHours: 0,
    totalContributions: 0,
    byCategory: {},
    byUser: {}
  });

  // Fetch project data
  useEffect(() => {
    const fetchProject = async () => {
      if (!projectId) return;

      try {
        const { data, error } = await supabase
          .from('projects')
          .select('*')
          .eq('id', projectId)
          .single();

        if (error) throw error;

        setProject(data);
      } catch (error) {
        console.error('Error fetching project:', error);
        toast.error('Failed to load project data');
      }
    };

    fetchProject();
  }, [projectId]);

  // Fetch contributions
  useEffect(() => {
    const fetchContributions = async () => {
      if (!projectId) return;

      try {
        setLoading(true);

        // Fetch contributions for this project
        let query = supabase
          .from('contributions')
          .select('*')
          .eq('project_id', projectId)
          .order('created_at', { ascending: false });

        // Try to include milestone data if the relationship exists
        try {
          query = supabase
            .from('contributions')
            .select('*, milestone:milestones(name)')
            .eq('project_id', projectId)
            .order('created_at', { ascending: false });
        } catch (e) {
          console.log('Could not include milestone data, falling back to basic query');
        }

        const { data, error } = await query;

        if (error) throw error;

        setContributions(data || []);

        // Calculate stats
        calculateStats(data);

        // Fetch contributor information
        await fetchContributors(data);
      } catch (error) {
        console.error('Error fetching contributions:', error);
        toast.error('Failed to load contributions');
      } finally {
        setLoading(false);
      }
    };

    fetchContributions();
  }, [projectId]);

  // Calculate contribution statistics
  const calculateStats = (contributionsData) => {
    if (!contributionsData || contributionsData.length === 0) {
      setStats({
        totalHours: 0,
        totalContributions: 0,
        byCategory: {},
        byUser: {}
      });
      return;
    }

    const totalHours = contributionsData.reduce((sum, contrib) => sum + parseFloat(contrib.hours_spent), 0);
    const totalContributions = contributionsData.length;

    // Group by category
    const byCategory = contributionsData.reduce((acc, contrib) => {
      const category = contrib.category;
      if (!acc[category]) {
        acc[category] = {
          count: 0,
          hours: 0
        };
      }

      acc[category].count += 1;
      acc[category].hours += parseFloat(contrib.hours_spent);

      return acc;
    }, {});

    // Group by user
    const byUser = contributionsData.reduce((acc, contrib) => {
      const userId = contrib.user_id;
      if (!acc[userId]) {
        acc[userId] = {
          count: 0,
          hours: 0
        };
      }

      acc[userId].count += 1;
      acc[userId].hours += parseFloat(contrib.hours_spent);

      return acc;
    }, {});

    setStats({
      totalHours,
      totalContributions,
      byCategory,
      byUser
    });
  };

  // Fetch contributor information
  const fetchContributors = async (contributionsData) => {
    if (!contributionsData || contributionsData.length === 0) return;

    // Get unique user IDs
    const userIds = [...new Set(contributionsData.map(c => c.user_id))];

    try {
      // Fetch user data
      const { data, error } = await supabase
        .from('users')
        .select('id, display_name, email')
        .in('id', userIds);

      if (error) throw error;

      // Create a map of user ID to user data
      const contributorsMap = data.reduce((acc, user) => {
        acc[user.id] = user;
        return acc;
      }, {});

      setContributors(contributorsMap);
    } catch (error) {
      console.error('Error fetching contributors:', error);
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get the date to display (use created_at if date_performed is not available)
  const getDisplayDate = (contribution) => {
    return contribution.date_performed || contribution.created_at;
  };

  // Get contributor name
  const getContributorName = (userId) => {
    if (!userId || !contributors[userId]) return 'Unknown';
    return contributors[userId].display_name || contributors[userId].email || 'Unknown';
  };

  // Handle form submission success
  const handleFormSuccess = () => {
    setShowForm(false);
    setEditingContribution(null);

    // Refresh contributions
    fetchContributions();
  };

  // Handle edit button click
  const handleEdit = (contribution) => {
    setEditingContribution(contribution);
    setShowForm(true);

    // Scroll to form
    setTimeout(() => {
      document.getElementById('contribution-form-section')?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  // Handle delete button click
  const handleDelete = async (contributionId) => {
    if (!confirm('Are you sure you want to delete this contribution?')) return;

    try {
      const { error } = await supabase
        .from('contributions')
        .delete()
        .eq('id', contributionId);

      if (error) throw error;

      toast.success('Contribution deleted successfully');

      // Refresh contributions
      setContributions(prev => prev.filter(c => c.id !== contributionId));

      // Recalculate stats
      calculateStats(contributions.filter(c => c.id !== contributionId));
    } catch (error) {
      console.error('Error deleting contribution:', error);
      toast.error('Failed to delete contribution');
    }
  };

  // Fetch contributions (for refreshing)
  const fetchContributions = async () => {
    if (!projectId) return;

    try {
      setLoading(true);

      // Fetch contributions for this project
      let query = supabase
        .from('contributions')
        .select('*')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });

      // Try to include milestone data if the relationship exists
      try {
        query = supabase
          .from('contributions')
          .select('*, milestone:milestones(name)')
          .eq('project_id', projectId)
          .order('created_at', { ascending: false });
      } catch (e) {
        console.log('Could not include milestone data, falling back to basic query');
      }

      const { data, error } = await query;

      if (error) throw error;

      setContributions(data || []);

      // Calculate stats
      calculateStats(data);

      // Fetch contributor information
      await fetchContributors(data);
    } catch (error) {
      console.error('Error fetching contributions:', error);
      toast.error('Failed to load contributions');
    } finally {
      setLoading(false);
    }
  };

  if (loading && contributions.length === 0) {
    return <div className="loading-container">Loading contributions...</div>;
  }

  return (
    <div className="contribution-tracker">
      <div className="contribution-tracker-header">
        <h2 className="section-title">Contribution Tracking</h2>

        <button
          className="add-contribution-button"
          onClick={() => {
            setShowForm(!showForm);
            setEditingContribution(null);
          }}
        >
          {showForm ? (
            <>
              <i className="bi bi-x-lg"></i> Cancel
            </>
          ) : (
            <>
              <i className="bi bi-plus-lg"></i> Add Contribution
            </>
          )}
        </button>
      </div>

      {/* Contribution Stats */}
      <div className="contribution-stats">
        <div className="stat-card">
          <div className="stat-value">{stats.totalContributions}</div>
          <div className="stat-label">Total Contributions</div>
        </div>

        <div className="stat-card">
          <div className="stat-value">{stats.totalHours.toFixed(1)}</div>
          <div className="stat-label">Total Hours</div>
        </div>

        <div className="stat-card">
          <div className="stat-value">{Object.keys(stats.byCategory).length}</div>
          <div className="stat-label">Categories</div>
        </div>

        <div className="stat-card">
          <div className="stat-value">{Object.keys(stats.byUser).length}</div>
          <div className="stat-label">Contributors</div>
        </div>
      </div>

      {/* Contribution Form */}
      {showForm && (
        <div id="contribution-form-section" className="contribution-form-section">
          <div className="form-header-with-tabs">
            <h3 className="section-subtitle">
              {editingContribution ? 'Edit Contribution' : 'Add New Contribution'}
            </h3>

            {!editingContribution && (
              <div className="form-mode-tabs">
                <button
                  className={`form-mode-tab ${formMode === 'single' ? 'active' : ''}`}
                  onClick={() => setFormMode('single')}
                >
                  Single Entry
                </button>
                <button
                  className={`form-mode-tab ${formMode === 'bulk' ? 'active' : ''}`}
                  onClick={() => setFormMode('bulk')}
                >
                  Bulk Entry
                </button>
              </div>
            )}
          </div>

          {formMode === 'single' || editingContribution ? (
            <ContributionEntryForm
              projectId={projectId}
              onSuccess={handleFormSuccess}
              onCancel={() => {
                setShowForm(false);
                setEditingContribution(null);
              }}
              initialData={editingContribution}
            />
          ) : (
            <BulkContributionForm
              projectId={projectId}
              onSuccess={handleFormSuccess}
              onCancel={() => {
                setShowForm(false);
                setEditingContribution(null);
              }}
            />
          )}
        </div>
      )}

      {/* Contributions List */}
      <div className="contributions-list-section">
        <h3 className="section-subtitle">Recent Contributions</h3>

        {contributions.length === 0 ? (
          <div className="no-contributions">
            <p>No contributions recorded yet. Add your first contribution to get started!</p>
          </div>
        ) : (
          <div className="contributions-list">
            {contributions.map(contribution => (
              <div key={contribution.id} className="contribution-card">
                <div className="contribution-header">
                  <div className="contribution-title">{contribution.task_name}</div>
                  <div className="contribution-meta">
                    <span className="contribution-date">{formatDate(getDisplayDate(contribution))}</span>
                    <span className="contribution-hours">{contribution.hours_spent} hours</span>
                  </div>
                </div>

                <div className="contribution-details">
                  <div className="contribution-type-category">
                    <span className="contribution-type">{contribution.task_type}</span>
                    <span className="contribution-category">{contribution.category}</span>
                  </div>

                  <div className="contribution-difficulty">
                    <span className="difficulty-label">Difficulty:</span>
                    <span className="difficulty-value">{contribution.difficulty}</span>
                  </div>
                </div>

                {contribution.description && (
                  <div className="contribution-description">
                    {contribution.description}
                  </div>
                )}

                <div className="contribution-footer">
                  <div className="contribution-contributor">
                    <i className="bi bi-person"></i>
                    <span>{getContributorName(contribution.user_id)}</span>
                  </div>

                  {contribution.milestone_id && (
                    <div className="contribution-milestone">
                      <i className="bi bi-flag"></i>
                      <span>
                        {contribution.milestone ? contribution.milestone.name : 'Milestone ' + contribution.milestone_id}
                      </span>
                    </div>
                  )}

                  {/* Actions - only show for the user's own contributions */}
                  {currentUser && contribution.user_id === currentUser.id && (
                    <div className="contribution-actions">
                      <button
                        className="action-button edit"
                        onClick={() => handleEdit(contribution)}
                        title="Edit"
                      >
                        <i className="bi bi-pencil"></i>
                      </button>

                      <button
                        className="action-button delete"
                        onClick={() => handleDelete(contribution.id)}
                        title="Delete"
                      >
                        <i className="bi bi-trash"></i>
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ContributionTracker;
