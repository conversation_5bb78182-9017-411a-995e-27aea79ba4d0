// Script to update the roadmap data with tech stack update TODOs
require('dotenv').config({ path: './client/.env.local' });
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseKey) {
  console.error('Error: SUPABASE_SERVICE_KEY environment variable is not set');
  process.exit(1);
}

console.log('Using Supabase URL:', supabaseUrl);

const supabase = createClient(supabaseUrl, supabaseKey);

// Helper function to find a phase by ID
function findPhase(roadmapData, phaseId) {
  return roadmapData.find(phase => phase.id === phaseId);
}

// Helper function to find a section by ID within a phase
function findSection(roadmapData, phaseId, sectionId) {
  const phase = findPhase(roadmapData, phaseId);
  if (!phase || !phase.sections) return null;
  return phase.sections.find(section => section.id === sectionId);
}

// Function to add a new section to a phase
function addNewSection(roadmapData, phaseId, sectionTitle) {
  const phase = findPhase(roadmapData, phaseId);
  if (!phase) {
    console.log(`Phase ${phaseId} not found`);
    return false;
  }

  if (!phase.sections) {
    phase.sections = [];
  }

  // Generate a new section ID
  const lastSectionId = phase.sections.length > 0
    ? phase.sections[phase.sections.length - 1].id
    : `${phaseId}.0`;

  const lastIdParts = lastSectionId.split('.');
  const lastIdNumber = parseInt(lastIdParts[lastIdParts.length - 1]);
  const newIdNumber = lastIdNumber + 1;
  const newSectionId = `${phaseId}.${newIdNumber}`;

  // Add the new section
  phase.sections.push({
    id: newSectionId,
    title: sectionTitle,
    tasks: []
  });

  console.log(`Added new section with ID ${newSectionId}: "${sectionTitle}"`);
  return newSectionId;
}

// Function to add a new task to a section
function addNewTask(roadmapData, phaseId, sectionId, taskText, completed = false) {
  const section = findSection(roadmapData, phaseId, sectionId);
  if (!section) {
    console.log(`Section ${sectionId} not found in phase ${phaseId}`);
    return false;
  }

  // Generate a new task ID
  const lastTaskId = section.tasks.length > 0
    ? section.tasks[section.tasks.length - 1].id
    : `${sectionId}.0`;

  const lastIdParts = lastTaskId.split('.');
  const lastIdNumber = parseInt(lastIdParts[lastIdParts.length - 1]);
  const newIdNumber = lastIdNumber + 1;
  const newTaskId = `${sectionId}.${newIdNumber}`;

  // Add the new task
  section.tasks.push({
    id: newTaskId,
    text: taskText,
    completed: completed
  });

  console.log(`Added new task with ID ${newTaskId}: "${taskText}"`);
  return newTaskId;
}

// Function to update the roadmap
async function updateRoadmap(roadmapId, roadmapData) {
  try {
    console.log(`\n=== Updating roadmap with ID: ${roadmapId} ===`);

    const { data: updatedData, error: updateError } = await supabase
      .from('roadmap')
      .update({
        data: roadmapData,
        last_updated: new Date().toISOString()
      })
      .eq('id', roadmapId)
      .select();

    if (updateError) {
      console.error('Error updating roadmap:', updateError);
      return false;
    }

    console.log('Updated roadmap successfully');
    return true;
  } catch (error) {
    console.error('Error in updateRoadmap:', error);
    return false;
  }
}

// Function to update the latest feature in the roadmap metadata
function updateLatestFeature(roadmapData) {
  // Find the metadata phase (usually has type: 'metadata')
  const metadataPhase = roadmapData.find(phase => phase.type === 'metadata');

  if (!metadataPhase) {
    // If no metadata phase exists, check if the first phase has a stats property
    if (roadmapData[0] && roadmapData[0].stats) {
      if (!roadmapData[0].stats.latest_update) {
        roadmapData[0].stats.latest_update = {};
      }

      roadmapData[0].stats.latest_update = {
        date: new Date().toISOString(),
        title: "Tech Stack Update Planning",
        author: "Development Team",
        version: "1.0.0",
        description: "Added tech stack update planning to the roadmap. This includes evaluating current technologies, researching alternatives, and planning for future upgrades."
      };

      console.log('Updated latest feature in first phase stats');
      return true;
    }

    console.log('No metadata phase or stats found in roadmap');
    return false;
  }

  // Update the metadata
  if (!metadataPhase.stats) {
    metadataPhase.stats = {};
  }

  if (!metadataPhase.stats.latest_update) {
    metadataPhase.stats.latest_update = {};
  }

  metadataPhase.stats.latest_update = {
    date: new Date().toISOString(),
    title: "Tech Stack Update Planning",
    author: "Development Team",
    version: "1.0.0",
    description: "Added tech stack update planning to the roadmap. This includes evaluating current technologies, researching alternatives, and planning for future upgrades."
  };

  console.log('Updated latest feature in metadata phase');
  return true;
}

// Main function
async function updateRoadmapWithTechStackTodos() {
  try {
    console.log('=== Fetching current roadmap data ===');

    // Get the latest roadmap entry
    const { data: roadmapData, error: roadmapError } = await supabase
      .from('roadmap')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1);

    if (roadmapError) {
      console.error('Error fetching roadmap from Supabase:', roadmapError);
      return;
    }

    if (!roadmapData || roadmapData.length === 0) {
      console.log('No roadmap data found in the database');
      return;
    }

    const roadmap = roadmapData[0];
    const phases = roadmap.data;

    console.log(`Found roadmap with ID: ${roadmap.id}`);

    // Track if we made any changes
    let updated = false;

    // Find or create a "Tech Stack & Infrastructure" phase
    let techPhase = findPhase(phases, 5);
    if (!techPhase) {
      // If phase 5 doesn't exist, create it
      phases.push({
        id: 5,
        title: "Tech Stack & Infrastructure",
        timeframe: "Q3-Q4 2024",
        expanded: true,
        sections: []
      });
      techPhase = phases[phases.length - 1];
      console.log('Created new Tech Stack & Infrastructure phase');
      updated = true;
    }

    // Add a section for tech stack evaluation if it doesn't exist
    let techEvalSectionId = null;
    if (techPhase.sections) {
      const techEvalSection = techPhase.sections.find(s => s.title.includes("Tech Stack Evaluation"));
      if (techEvalSection) {
        techEvalSectionId = techEvalSection.id;
      }
    }

    if (!techEvalSectionId) {
      techEvalSectionId = addNewSection(phases, 5, "Tech Stack Evaluation & Planning");
      updated = true;
    }

    // Add tech stack update tasks
    if (techEvalSectionId) {
      const tasks = [
        "Evaluate current tech stack components and identify areas for improvement",
        "Research modern alternatives for frontend framework (Next.js, Remix, etc.)",
        "Investigate backend service options (serverless functions, edge functions)",
        "Plan database migration strategy for improved performance",
        "Create proof-of-concept for updated tech stack",
        "Develop migration plan with minimal disruption to users"
      ];

      for (const task of tasks) {
        const added = addNewTask(phases, 5, techEvalSectionId, task);
        if (added) updated = true;
      }
    }

    // Update the latest feature metadata
    const updatedFeature = updateLatestFeature(phases);
    if (updatedFeature) updated = true;

    // Update the roadmap if changes were made
    if (updated) {
      const success = await updateRoadmap(roadmap.id, phases);
      if (success) {
        console.log('\n=== Roadmap updated successfully with tech stack TODOs ===');
      } else {
        console.error('\n=== Failed to update roadmap ===');
      }
    } else {
      console.log('\n=== No changes were made to the roadmap ===');
    }

  } catch (error) {
    console.error('Error updating roadmap with tech stack TODOs:', error);
  }
}

// Run the function
updateRoadmapWithTechStackTodos();
