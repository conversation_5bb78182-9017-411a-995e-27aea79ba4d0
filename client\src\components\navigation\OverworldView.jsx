import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody } from '@heroui/react';

/**
 * Overworld View Component
 *
 * Displays navigation canvases as connected nodes in a game-like overworld map.
 * Users can see connections between different areas and navigate by clicking or dragging.
 */
const OverworldView = ({ canvases, currentCanvas, onNavigate, onDragStart, onCardHover, onCardHoverEnd }) => {
  const [clickStartPos, setClickStartPos] = React.useState(null);
  const [clickThreshold] = React.useState(5); // pixels
  const [hoveredCard, setHoveredCard] = React.useState(null);
  const [showContextMenu, setShowContextMenu] = React.useState(null);
  const [hoverTimeout, setHoverTimeout] = React.useState(null);
  const [cardSizes, setCardSizes] = React.useState({});
  const [resizing, setResizing] = React.useState(null);
  const [resizeStart, setResizeStart] = React.useState(null);

  // Handle mouse down on canvas card
  const handleCardMouseDown = (e, canvasId) => {
    // Prevent event bubbling to background
    e.stopPropagation();
    setClickStartPos({ x: e.clientX, y: e.clientY, canvasId });
  };

  // Handle mouse up on canvas card
  const handleCardMouseUp = (e, canvasId) => {
    if (!clickStartPos || clickStartPos.canvasId !== canvasId) return;

    const distance = Math.sqrt(
      Math.pow(e.clientX - clickStartPos.x, 2) +
      Math.pow(e.clientY - clickStartPos.y, 2)
    );

    // If movement is below threshold, treat as click
    if (distance < clickThreshold) {
      // Single-click: navigate to canvas content
      onNavigate(canvasId);
      setClickStartPos(null);
    } else {
      setClickStartPos(null);
    }
  };

  // Handle mouse enter on canvas card
  const handleCardMouseEnter = (canvasId) => {
    setHoveredCard(canvasId);

    // Notify parent component for debug UI
    if (onCardHover) {
      onCardHover(canvasId);
    }

    // Clear any existing timeout
    if (hoverTimeout) {
      clearTimeout(hoverTimeout);
    }

    // Set timeout for context menu
    const timeout = setTimeout(() => {
      setShowContextMenu(canvasId);
    }, 1000); // Show context menu after 1 second

    setHoverTimeout(timeout);
  };

  // Handle mouse leave on canvas card
  const handleCardMouseLeave = () => {
    setHoveredCard(null);

    // Notify parent component for debug UI
    if (onCardHoverEnd) {
      onCardHoverEnd();
    }

    // Clear timeout
    if (hoverTimeout) {
      clearTimeout(hoverTimeout);
      setHoverTimeout(null);
    }

    // Hide context menu after a short delay
    setTimeout(() => {
      setShowContextMenu(null);
    }, 200);
  };

  // Get card size
  const getCardSize = (canvasId) => {
    return cardSizes[canvasId] || { width: 160, height: 160 }; // Default 40 * 4 = 160px
  };

  // Handle resize
  const handleResize = (canvasId, newSize) => {
    setCardSizes(prev => ({
      ...prev,
      [canvasId]: newSize
    }));
  };

  // Handle resize start
  const handleResizeStart = (e, canvasId) => {
    e.stopPropagation();
    e.preventDefault();

    const currentSize = getCardSize(canvasId);
    setResizing(canvasId);
    setResizeStart({
      x: e.clientX,
      y: e.clientY,
      width: currentSize.width,
      height: currentSize.height
    });
  };

  // Handle resize move
  const handleResizeMove = React.useCallback((e) => {
    if (!resizing || !resizeStart) return;

    const deltaX = e.clientX - resizeStart.x;
    const deltaY = e.clientY - resizeStart.y;

    const newWidth = Math.max(80, resizeStart.width + deltaX); // Min 80px
    const newHeight = Math.max(80, resizeStart.height + deltaY); // Min 80px

    handleResize(resizing, { width: newWidth, height: newHeight });
  }, [resizing, resizeStart]);

  // Handle resize end
  const handleResizeEnd = React.useCallback(() => {
    setResizing(null);
    setResizeStart(null);
  }, []);

  // Add resize event listeners
  React.useEffect(() => {
    if (resizing) {
      document.addEventListener('mousemove', handleResizeMove);
      document.addEventListener('mouseup', handleResizeEnd);

      return () => {
        document.removeEventListener('mousemove', handleResizeMove);
        document.removeEventListener('mouseup', handleResizeEnd);
      };
    }
  }, [resizing, handleResizeMove, handleResizeEnd]);

  // Connection lines removed for cleaner, more immersive experience

  // Check for collision between two cards
  const checkCollision = (pos1, size1, pos2, size2) => {
    const margin = 20; // Minimum spacing between cards

    const left1 = pos1.x - size1.width / 2 - margin;
    const right1 = pos1.x + size1.width / 2 + margin;
    const top1 = pos1.y - size1.height / 2 - margin;
    const bottom1 = pos1.y + size1.height / 2 + margin;

    const left2 = pos2.x - size2.width / 2;
    const right2 = pos2.x + size2.width / 2;
    const top2 = pos2.y - size2.height / 2;
    const bottom2 = pos2.y + size2.height / 2;

    return !(left1 > right2 || right1 < left2 || top1 > bottom2 || bottom1 < top2);
  };

  // Adjust positions to prevent overlaps
  const adjustPositions = React.useMemo(() => {
    const adjustedCanvases = { ...canvases };
    const canvasArray = Object.values(adjustedCanvases);

    // Sort by priority (home first, then by distance from center)
    canvasArray.sort((a, b) => {
      if (a.id === 'home') return -1;
      if (b.id === 'home') return 1;

      const distA = Math.sqrt(a.position.x * a.position.x + a.position.y * a.position.y);
      const distB = Math.sqrt(b.position.x * b.position.x + b.position.y * b.position.y);
      return distA - distB;
    });

    // Adjust positions to prevent overlaps
    for (let i = 0; i < canvasArray.length; i++) {
      const currentCanvas = canvasArray[i];
      const currentSize = getCardSize(currentCanvas.id);

      for (let j = 0; j < i; j++) {
        const otherCanvas = canvasArray[j];
        const otherSize = getCardSize(otherCanvas.id);

        if (checkCollision(currentCanvas.position, currentSize, otherCanvas.position, otherSize)) {
          // Move current canvas away from collision
          const dx = currentCanvas.position.x - otherCanvas.position.x;
          const dy = currentCanvas.position.y - otherCanvas.position.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance === 0) {
            // If positions are identical, move in a random direction
            const angle = Math.random() * 2 * Math.PI;
            currentCanvas.position.x += Math.cos(angle) * 200;
            currentCanvas.position.y += Math.sin(angle) * 200;
          } else {
            // Move away from the other canvas
            const minDistance = (currentSize.width + otherSize.width) / 2 + 40; // 40px margin
            const moveDistance = minDistance - distance + 20;

            currentCanvas.position.x += (dx / distance) * moveDistance;
            currentCanvas.position.y += (dy / distance) * moveDistance;
          }
        }
      }
    }

    return adjustedCanvases;
  }, [canvases, cardSizes]);

  // Render canvas nodes
  const renderCanvases = () => {
    return Object.values(adjustPositions).map(canvas => {
      const isHovered = hoveredCard === canvas.id;
      const hasContextMenu = showContextMenu === canvas.id;
      const cardSize = getCardSize(canvas.id);

      return (
        <motion.div
          key={canvas.id}
          className="absolute"
          style={{
            left: canvas.position.x - cardSize.width / 2,
            top: canvas.position.y - cardSize.height / 2,
            zIndex: isHovered ? 30 : 10,
          }}
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.1 * Object.keys(canvases).indexOf(canvas.id), duration: 0.4 }}
        >
          <Card
            data-canvas-card
            className={`
              cursor-pointer transition-all duration-300 hover:scale-110 relative
              ${isHovered
                ? 'ring-2 ring-white/50 shadow-xl shadow-white/20'
                : 'hover:ring-1 hover:ring-white/30 opacity-80 hover:opacity-100'
              }
            `}
            style={{
              width: cardSize.width,
              height: cardSize.height
            }}
            onMouseDown={(e) => {
              handleCardMouseDown(e, canvas.id);
            }}
            onMouseUp={(e) => handleCardMouseUp(e, canvas.id)}
            onMouseEnter={() => handleCardMouseEnter(canvas.id)}
            onMouseLeave={handleCardMouseLeave}
          >
            <CardBody className={`
              p-0 relative overflow-hidden bg-gradient-to-br ${canvas.color}
              flex flex-col items-center justify-center text-white
            `}>
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-20">
                <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1),transparent_50%)]" />
                <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.05)_25%,rgba(255,255,255,0.05)_50%,transparent_50%,transparent_75%,rgba(255,255,255,0.05)_75%)] bg-[length:20px_20px]" />
              </div>

              {/* Content */}
              <div className="relative z-10 text-center">
                <motion.div
                  className="text-4xl mb-2"
                  animate={isHovered ? { scale: [1, 1.1, 1] } : {}}
                  transition={{ duration: 1, repeat: isHovered ? Infinity : 0 }}
                >
                  {canvas.icon}
                </motion.div>
                <h3 className="font-bold text-lg mb-1">{canvas.title}</h3>
                <p className="text-xs opacity-80 px-2">{canvas.description}</p>
              </div>



              {/* Resize Widget */}
              {isHovered && (
                <motion.div
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="absolute bottom-1 right-1 w-4 h-4 bg-white/20 hover:bg-white/40 rounded cursor-se-resize backdrop-blur-sm border border-white/30"
                  onMouseDown={(e) => handleResizeStart(e, canvas.id)}
                >
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-2 h-2 border-r border-b border-white/60"></div>
                  </div>
                </motion.div>
              )}

              {/* Context Menu */}
              {hasContextMenu && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8, y: 10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 bg-black bg-opacity-80 backdrop-blur-md rounded-lg border border-white border-opacity-20 p-2 min-w-[120px] z-50"
                  style={{ backdropFilter: 'blur(12px)', WebkitBackdropFilter: 'blur(12px)' }}
                >
                  <div className="space-y-1">
                    <button
                      className="w-full text-left px-3 py-2 text-sm text-white hover:bg-white hover:bg-opacity-10 rounded transition-colors"
                      onClick={() => onNavigate(canvas.id)}
                    >
                      🚀 Navigate
                    </button>
                    <button
                      className="w-full text-left px-3 py-2 text-sm text-white hover:bg-white hover:bg-opacity-10 rounded transition-colors"
                      onClick={() => {
                        // Reset to default size
                        handleResize(canvas.id, { width: 160, height: 160 });
                      }}
                    >
                      📏 Reset Size
                    </button>
                  </div>

                  {/* Arrow pointing up */}
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-black" style={{ borderBottomColor: 'rgba(0,0,0,0.8)' }}></div>
                </motion.div>
              )}
            </CardBody>
          </Card>
        </motion.div>
      );
    });
  };

  return (
    <motion.div
      className="relative w-full h-full flex items-center justify-center"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Endless Draggable Background - Covers everything */}
      <div
        className="absolute inset-0 cursor-grab active:cursor-grabbing"
        style={{
          zIndex: 0,
          width: '100%',
          height: '100%',
          minWidth: '100vw',
          minHeight: '100vh',
          touchAction: 'none' // Prevent default touch behaviors
        }}
        onMouseDown={(e) => {
          // Always allow dragging from background
          onDragStart(e);
        }}
        onTouchStart={(e) => {
          // Handle touch events for mobile
          if (e.touches.length === 1) {
            const touch = e.touches[0];
            const mouseEvent = new MouseEvent('mousedown', {
              clientX: touch.clientX,
              clientY: touch.clientY,
              bubbles: true
            });
            onDragStart(mouseEvent);
          }
        }}
      />

      {/* Background Grid */}
      <div className="absolute inset-0 opacity-10 pointer-events-none">
        <div
          className="w-full h-full"
          style={{
            backgroundImage: `
              linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px'
          }}
        />
      </div>

      {/* Canvas Nodes */}
      <div className="absolute inset-0" style={{ zIndex: 20 }}>
        {renderCanvases()}
      </div>
    </motion.div>
  );
};

export default OverworldView;
