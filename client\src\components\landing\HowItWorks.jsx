import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, Button } from '@heroui/react';

/**
 * How It Works Component - Start → Track → Earn Process
 * 
 * Features:
 * - Three-step process visualization
 * - Clear explanations of the CoG model
 * - Visual flow with animations
 * - Call-to-action integration
 */
const HowItWorks = ({ onGetStarted }) => {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.8,
        staggerChildren: 0.3
      }
    }
  };

  const stepVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  };

  const arrowVariants = {
    hidden: { opacity: 0, scale: 0 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.5, delay: 0.3 }
    }
  };

  // Process steps data
  const steps = [
    {
      number: 1,
      title: 'START',
      icon: '🚀',
      subtitle: 'Form Alliances • Create Ventures • Plan Missions',
      description: 'Set up your creative organization and define projects with clear revenue models. Invite allies, recruit talent, or post public bounties.',
      timeframe: '⏱️ 5 minutes to first project setup',
      gradient: 'from-blue-500 to-cyan-500',
      bgGradient: 'from-blue-50 to-cyan-50',
      darkBgGradient: 'from-blue-900/20 to-cyan-900/20'
    },
    {
      number: 2,
      title: 'TRACK',
      icon: '📊',
      subtitle: 'Log Time • Rate Difficulty • Monitor Progress',
      description: 'Track contributions with our CoG (Contribution of Greatness) model. Every hour and every challenge level is fairly calculated and validated.',
      timeframe: '📈 Real-time contribution tracking and validation',
      gradient: 'from-purple-500 to-pink-500',
      bgGradient: 'from-purple-50 to-pink-50',
      darkBgGradient: 'from-purple-900/20 to-pink-900/20'
    },
    {
      number: 3,
      title: 'EARN',
      icon: '💰',
      subtitle: 'Revenue Entry • Automatic Calculation • Secure Payments',
      description: 'When projects generate revenue, earnings are automatically distributed based on verified contributions. Fair, transparent, and immediate.',
      timeframe: '🔒 Secure escrow system with milestone-based releases',
      gradient: 'from-green-500 to-emerald-500',
      bgGradient: 'from-green-50 to-emerald-50',
      darkBgGradient: 'from-green-900/20 to-emerald-900/20'
    }
  ];

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 py-20">
      <motion.div
        className="max-w-7xl mx-auto px-6"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
      >
        {/* Section Header */}
        <motion.div variants={stepVariants} className="text-center mb-16">
          <h2 className="text-5xl md:text-6xl font-bold text-white mb-6">
            🚀 Start → Track → Earn
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Our simple three-step process ensures fair compensation for every contribution, 
            from the first hour of work to the final revenue distribution.
          </p>
        </motion.div>

        {/* Process Steps */}
        <div className="space-y-12">
          {steps.map((step, index) => (
            <div key={step.number} className="relative">
              {/* Step Card */}
              <motion.div variants={stepVariants}>
                <Card className={`bg-gradient-to-br ${step.bgGradient} dark:${step.darkBgGradient} border-2 border-white/10 shadow-2xl`}>
                  <CardBody className="p-8 md:p-12">
                    <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 items-center">
                      {/* Step Number and Icon */}
                      <div className="lg:col-span-2 text-center lg:text-left">
                        <div className={`inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-r ${step.gradient} text-white text-3xl font-bold mb-4`}>
                          {step.number}
                        </div>
                        <div className="text-6xl">{step.icon}</div>
                      </div>

                      {/* Step Content */}
                      <div className="lg:col-span-10">
                        <div className="text-center lg:text-left">
                          <h3 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
                            {step.number}. {step.title}
                          </h3>
                          
                          <div className="text-xl md:text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-6">
                            {step.subtitle}
                          </div>
                          
                          <p className="text-lg text-gray-600 dark:text-gray-400 mb-6 max-w-4xl">
                            "{step.description}"
                          </p>
                          
                          <div className="inline-flex items-center px-6 py-3 bg-white/20 dark:bg-black/20 rounded-full text-gray-700 dark:text-gray-300 font-medium">
                            {step.timeframe}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </motion.div>

              {/* Arrow to Next Step */}
              {index < steps.length - 1 && (
                <motion.div
                  variants={arrowVariants}
                  className="flex justify-center my-8"
                >
                  <div className="text-6xl text-white/60">
                    ⬇️
                  </div>
                </motion.div>
              )}
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <motion.div variants={stepVariants} className="text-center mt-16">
          <div className="max-w-4xl mx-auto">
            <h3 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Start Your Journey?
            </h3>
            <p className="text-xl text-gray-300 mb-8">
              Join thousands of creators who have already transformed their creative businesses 
              with fair, transparent collaboration.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center max-w-md mx-auto">
              <Button
                size="lg"
                color="primary"
                className="w-full sm:w-auto bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold px-8 py-4 text-lg shadow-lg hover:shadow-xl transition-all duration-300"
                onPress={onGetStarted}
              >
                Start Your Journey
              </Button>
              
              <Button
                size="lg"
                variant="bordered"
                className="w-full sm:w-auto border-2 border-white/30 text-white hover:bg-white/10 font-semibold px-8 py-4 text-lg backdrop-blur-sm transition-all duration-300"
                onPress={() => {
                  // Scroll to success stories or open demo
                  document.getElementById('success-stories')?.scrollIntoView({ behavior: 'smooth' });
                }}
              >
                See Live Demo
              </Button>
            </div>
          </div>
        </motion.div>

        {/* Background Decorations */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          {/* Floating Elements */}
          {[...Array(10)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-4 h-4 bg-white/10 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -50, 0],
                opacity: [0.2, 0.8, 0.2]
              }}
              transition={{
                duration: 4 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default HowItWorks;
