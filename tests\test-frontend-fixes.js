// Test Frontend Fixes
// Test the routing fixes with URL parameters

const fetch = require('node-fetch');

const SITE_URL = 'https://royalty.technology';

async function testFrontendFixes() {
  console.log('🔧 Testing frontend routing fixes...\n');
  
  // Test different URL parameters and routes
  const testUrls = [
    // Test bypass parameters
    `${SITE_URL}/?skip_onboarding=true`,
    `${SITE_URL}/?test_mode=true`,
    `${SITE_URL}/teams?skip_onboarding=true`,
    `${SITE_URL}/teams?test_mode=true`,
    
    // Test specific routes with bypass
    `${SITE_URL}/teams/test-id/manage?skip_onboarding=true`,
    `${SITE_URL}/projects?test_mode=true`,
    `${SITE_URL}/track?test_mode=true`,
    `${SITE_URL}/earn?test_mode=true`,
    
    // Test without parameters (should show onboarding)
    `${SITE_URL}/teams`,
    `${SITE_URL}/projects`
  ];
  
  for (const url of testUrls) {
    try {
      console.log(`Testing: ${url}`);
      
      const response = await fetch(url);
      const html = await response.text();
      
      console.log(`Status: ${response.status}`);
      console.log(`Content length: ${html.length}`);
      
      // Check for different content patterns
      const hasOnboarding = html.includes('Skip Tutorial') || html.includes('Welcome to Royaltea');
      const hasCanvas = html.includes('<canvas') || html.includes('canvas');
      const hasReactRoot = html.includes('id="root"');
      const hasAllianceContent = html.includes('Alliance') || html.includes('alliance');
      const hasTeamContent = html.includes('Team') || html.includes('team');
      
      console.log(`  Has onboarding: ${hasOnboarding}`);
      console.log(`  Has canvas: ${hasCanvas}`);
      console.log(`  Has React root: ${hasReactRoot}`);
      console.log(`  Has alliance content: ${hasAllianceContent}`);
      console.log(`  Has team content: ${hasTeamContent}`);
      
      // Check if content is different from the standard 395 characters
      const isDifferentContent = html.length !== 2162; // The standard HTML length
      console.log(`  Has different content: ${isDifferentContent}`);
      
      if (html.length < 3000) {
        console.log(`  Content preview: ${html.substring(0, 200)}...`);
      }
      
      console.log('---\n');
      
    } catch (error) {
      console.log(`❌ Error testing ${url}: ${error.message}\n`);
    }
  }
  
  // Test with authentication simulation
  console.log('🔑 Testing with simulated authentication...\n');
  
  const authTestUrl = `${SITE_URL}/teams?skip_onboarding=true`;
  
  try {
    const response = await fetch(authTestUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      }
    });
    
    const html = await response.text();
    
    console.log(`Auth test URL: ${authTestUrl}`);
    console.log(`Status: ${response.status}`);
    console.log(`Content length: ${html.length}`);
    
    // Look for specific React components or content
    const hasModernContent = html.includes('React') || 
                            html.includes('useState') || 
                            html.includes('useEffect') ||
                            html.includes('component');
    
    console.log(`Has modern content: ${hasModernContent}`);
    
    // Check for script tags that might load React components
    const scriptMatches = html.match(/<script[^>]*src="[^"]*"[^>]*>/g);
    console.log(`Script tags found: ${scriptMatches ? scriptMatches.length : 0}`);
    
    if (scriptMatches && scriptMatches.length > 0) {
      console.log(`Sample scripts:`);
      scriptMatches.slice(0, 3).forEach((script, index) => {
        console.log(`  ${index + 1}. ${script}`);
      });
    }
    
  } catch (error) {
    console.log(`❌ Auth test error: ${error.message}`);
  }
}

testFrontendFixes();
