import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

/**
 * Smart View Enhancer
 * 
 * Reorganizes the grid view based on user's selected path from onboarding
 * and provides visual weighting/highlighting for recommended areas.
 * Follows the spatial-first design philosophy by using position to convey meaning.
 */
const SmartViewEnhancer = ({ 
  canvases, 
  selectedPath, 
  isActive = false,
  onLayoutChange 
}) => {
  const [enhancedLayout, setEnhancedLayout] = useState(null);
  const [recommendations, setRecommendations] = useState([]);

  // Calculate smart layout based on selected path
  useEffect(() => {
    if (!selectedPath || !isActive) {
      setEnhancedLayout(null);
      setRecommendations([]);
      return;
    }

    const layout = calculateSmartLayout(canvases, selectedPath);
    setEnhancedLayout(layout);
    setRecommendations(layout.recommendations);
    
    if (onLayoutChange) {
      onLayoutChange(layout);
    }
  }, [selectedPath, isActive, canvases, onLayoutChange]);

  // Calculate smart layout based on user's journey preference
  const calculateSmartLayout = (canvases, path) => {
    const { canvas: targetCanvas, interests, goals } = path;
    
    // Define journey-based weightings
    const journeyWeights = {
      'start': {
        primary: ['wizard', 'projects', 'learn', 'agreements'],
        secondary: ['home', 'profile', 'teams'],
        tertiary: ['track', 'earn', 'analytics']
      },
      'track': {
        primary: ['contributions', 'validation', 'kanban', 'analytics'],
        secondary: ['home', 'projects', 'insights'],
        tertiary: ['earn', 'revenue', 'start']
      },
      'earn': {
        primary: ['revenue', 'royalty', 'escrow', 'analytics'],
        secondary: ['home', 'contributions', 'validation'],
        tertiary: ['start', 'projects', 'learn']
      }
    };

    // Get base journey from target canvas
    let baseJourney = 'start';
    if (['contributions', 'validation', 'kanban'].includes(targetCanvas)) {
      baseJourney = 'track';
    } else if (['revenue', 'royalty', 'escrow'].includes(targetCanvas)) {
      baseJourney = 'earn';
    }

    const weights = journeyWeights[baseJourney];
    
    // Calculate recommendations
    const recommendations = [];
    
    // Add primary recommendations
    weights.primary.forEach((canvasId, index) => {
      if (canvases[canvasId]) {
        recommendations.push({
          canvasId,
          priority: 'high',
          reason: getRecommendationReason(canvasId, baseJourney),
          weight: 1.0 - (index * 0.1)
        });
      }
    });

    // Add secondary recommendations
    weights.secondary.forEach((canvasId, index) => {
      if (canvases[canvasId]) {
        recommendations.push({
          canvasId,
          priority: 'medium',
          reason: getRecommendationReason(canvasId, baseJourney),
          weight: 0.6 - (index * 0.1)
        });
      }
    });

    return {
      targetCanvas,
      baseJourney,
      recommendations,
      layout: 'weighted-proximity' // Could be 'grid-reorder', 'cluster', etc.
    };
  };

  // Get recommendation reason text
  const getRecommendationReason = (canvasId, journey) => {
    const reasons = {
      start: {
        wizard: "Create your first project",
        projects: "Manage your work",
        learn: "Get up to speed",
        agreements: "Set up legal framework"
      },
      track: {
        contributions: "Log your work",
        validation: "Get feedback",
        kanban: "Organize tasks",
        analytics: "Track progress"
      },
      earn: {
        revenue: "Monitor earnings",
        royalty: "Calculate payments",
        escrow: "Manage funds",
        analytics: "Analyze performance"
      }
    };

    return reasons[journey]?.[canvasId] || "Recommended for your journey";
  };

  // Render recommendation indicators
  const renderRecommendations = () => {
    if (!recommendations.length) return null;

    return recommendations.map((rec) => {
      const canvas = canvases[rec.canvasId];
      if (!canvas) return null;

      return (
        <motion.div
          key={rec.canvasId}
          className="absolute pointer-events-none"
          style={{
            left: canvas.position.x - 10,
            top: canvas.position.y - 10,
            zIndex: 5
          }}
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0 }}
          transition={{ delay: 0.1 * recommendations.indexOf(rec) }}
        >
          {/* Priority indicator */}
          <div className={`
            w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold
            ${rec.priority === 'high' 
              ? 'bg-green-500 text-white' 
              : rec.priority === 'medium'
              ? 'bg-yellow-500 text-white'
              : 'bg-blue-500 text-white'
            }
          `}>
            {rec.priority === 'high' ? '!' : rec.priority === 'medium' ? '•' : '·'}
          </div>

          {/* Recommendation tooltip */}
          <div className="absolute top-8 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity">
            {rec.reason}
          </div>
        </motion.div>
      );
    });
  };

  // Render connection lines between recommended areas
  const renderConnectionLines = () => {
    if (!recommendations.length || recommendations.length < 2) return null;

    const primaryRecs = recommendations.filter(r => r.priority === 'high').slice(0, 3);
    if (primaryRecs.length < 2) return null;

    return (
      <svg className="absolute inset-0 pointer-events-none" style={{ zIndex: 1 }}>
        {primaryRecs.map((rec, index) => {
          if (index === primaryRecs.length - 1) return null;
          
          const currentCanvas = canvases[rec.canvasId];
          const nextCanvas = canvases[primaryRecs[index + 1].canvasId];
          
          if (!currentCanvas || !nextCanvas) return null;

          return (
            <motion.line
              key={`${rec.canvasId}-${primaryRecs[index + 1].canvasId}`}
              x1={currentCanvas.position.x}
              y1={currentCanvas.position.y}
              x2={nextCanvas.position.x}
              y2={nextCanvas.position.y}
              stroke="rgba(34, 197, 94, 0.3)"
              strokeWidth="2"
              strokeDasharray="5,5"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 1, delay: 0.5 + index * 0.2 }}
            />
          );
        })}
      </svg>
    );
  };

  if (!isActive || !enhancedLayout) return null;

  return (
    <div className="absolute inset-0 pointer-events-none">
      {/* Connection lines */}
      {renderConnectionLines()}
      
      {/* Recommendation indicators */}
      {renderRecommendations()}
      
      {/* Smart View indicator */}
      <motion.div
        className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-green-500/20 backdrop-blur-md border border-green-500/50 rounded-lg px-3 py-1"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
      >
        <div className="text-green-400 text-sm font-medium flex items-center gap-2">
          <span>✨</span>
          <span>Smart View: {enhancedLayout.baseJourney.charAt(0).toUpperCase() + enhancedLayout.baseJourney.slice(1)} Journey</span>
        </div>
      </motion.div>
    </div>
  );
};

export default SmartViewEnhancer;
