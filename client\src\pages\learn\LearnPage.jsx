import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@heroui/react';

const LearnPage = () => {
  const trainingModules = [
    {
      id: 1,
      title: 'Getting Started with Royaltea',
      description: 'Learn the basics of Royaltea and how to navigate the platform.',
      duration: '15 minutes',
      level: 'Beginner',
      icon: 'bi-stars'
    },
    {
      id: 2,
      title: 'Understanding Royalty Models',
      description: 'Explore different royalty models and how they work in Royaltea.',
      duration: '30 minutes',
      level: 'Intermediate',
      icon: 'bi-graph-up'
    },
    {
      id: 3,
      title: 'Project Management Basics',
      description: 'Learn how to create and manage projects effectively.',
      duration: '25 minutes',
      level: 'Beginner',
      icon: 'bi-kanban'
    },
    {
      id: 4,
      title: 'Advanced Contribution Tracking',
      description: 'Master the art of tracking contributions for fair compensation.',
      duration: '45 minutes',
      level: 'Advanced',
      icon: 'bi-clipboard-data'
    }
  ];

  return (
    <div className="learn-page min-h-screen bg-background py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-foreground mb-4">Learning Center</h1>
          <p className="text-xl text-muted-foreground leading-relaxed max-w-3xl mx-auto">
            Enhance your Royaltea skills with our comprehensive training modules.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 mb-12">
          {trainingModules.map(module => (
            <Card key={module.id} className="training-module-card h-full shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                    <svg className="w-6 h-6 text-primary" fill="currentColor" viewBox="0 0 20 20">
                      {module.icon === 'bi-stars' && (
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                      )}
                      {module.icon === 'bi-graph-up' && (
                        <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"/>
                      )}
                      {module.icon === 'bi-kanban' && (
                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                      )}
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-white">{module.title}</h3>
                    <p className="text-white/80">{module.description}</p>
                  </div>
                </div>
              </CardHeader>
              <CardBody className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center text-sm text-white/70">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Duration: {module.duration}
                  </div>
                  <div className="flex items-center text-sm text-white/70">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    Level: {module.level}
                  </div>
                </div>
                <Button className="w-full bg-blue-500 hover:bg-blue-600 text-white">Start Module</Button>
              </CardBody>
            </Card>
          ))}
        </div>

        <div className="text-center">
          <Card className="max-w-2xl mx-auto bg-white/5 border border-white/10">
            <CardBody className="p-8">
              <h3 className="text-2xl font-bold text-white mb-4">More Training Modules Coming Soon!</h3>
              <p className="text-white/70">We're constantly developing new educational content to help you get the most out of Royaltea.</p>
            </CardBody>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default LearnPage;
