// Script to check if there are any projects on the production site
const fetch = require('node-fetch');

async function main() {
  try {
    console.log('Checking for projects on the production site...\n');
    
    // Fetch the homepage
    const response = await fetch('https://royalty.technology');
    const html = await response.text();
    
    // Look for project names or IDs in the HTML
    const projectMatches = html.match(/project\/([a-f0-9-]+)/g);
    
    if (projectMatches && projectMatches.length > 0) {
      console.log(`Found ${projectMatches.length} project references on the homepage:`);
      
      const uniqueProjects = [...new Set(projectMatches)];
      uniqueProjects.forEach(project => {
        console.log(`  ${project}`);
      });
      
      // Try to fetch each project page
      for (const projectRef of uniqueProjects) {
        const projectUrl = `https://royalty.technology/${projectRef}`;
        console.log(`\nFetching project: ${projectUrl}`);
        
        try {
          const projectResponse = await fetch(projectUrl);
          const projectHtml = await projectResponse.text();
          
          // Look for project name
          const nameMatch = projectHtml.match(/<h1[^>]*>([^<]+)<\/h1>/);
          if (nameMatch) {
            console.log(`  Project name: ${nameMatch[1].trim()}`);
          }
          
          // Look for agreement references
          const agreementMatches = projectHtml.match(/agreement\/([a-f0-9-]+)/g);
          if (agreementMatches && agreementMatches.length > 0) {
            console.log(`  Found ${agreementMatches.length} agreement references:`);
            [...new Set(agreementMatches)].forEach(agreement => {
              console.log(`    ${agreement}`);
            });
          } else {
            console.log(`  No agreement references found`);
          }
        } catch (e) {
          console.log(`  Error fetching project: ${e.message}`);
        }
      }
    } else {
      console.log('No project references found on the homepage.');
    }
    
    console.log('\nCheck completed.');
    
  } catch (error) {
    console.error('Error:', error);
  }
}

main();
