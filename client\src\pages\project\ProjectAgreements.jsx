import React, { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import LoadingAnimation from '../../components/layout/LoadingAnimation';
import AgreementManager from '../../components/agreement/AgreementManager';

/**
 * ProjectAgreements Page
 * 
 * A page for managing project agreements, including:
 * - Viewing agreement details
 * - Signing agreements
 * - Viewing agreement history
 * - Downloading agreements as PDF
 */
const ProjectAgreements = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { currentUser } = useContext(UserContext);
  const [project, setProject] = useState(null);
  const [loading, setLoading] = useState(true);
  const [userRole, setUserRole] = useState(null);

  // Fetch project data
  useEffect(() => {
    const fetchProjectData = async () => {
      if (!id || !currentUser) {
        navigate('/projects');
        return;
      }

      try {
        setLoading(true);

        // Fetch project details
        const { data: projectData, error: projectError } = await supabase
          .from('projects')
          .select('*')
          .eq('id', id)
          .single();

        if (projectError) throw projectError;
        setProject(projectData);

        // Check user's role in the project
        const { data: contributor, error: contributorError } = await supabase
          .from('project_contributors')
          .select('*')
          .eq('project_id', id)
          .eq('user_id', currentUser.id)
          .single();

        if (contributorError && contributorError.code !== 'PGRST116') {
          throw contributorError;
        }

        if (contributor) {
          setUserRole(contributor.permission_level);
        } else {
          // User is not a contributor, redirect to projects page
          toast.error('You do not have access to this project');
          navigate('/projects');
        }
      } catch (error) {
        console.error('Error fetching project data:', error);
        toast.error('Failed to load project data');
        navigate('/projects');
      } finally {
        setLoading(false);
      }
    };

    fetchProjectData();
  }, [id, currentUser, navigate]);

  if (loading) {
    return <LoadingAnimation />;
  }

  if (!project) {
    return (
      <div className="project-not-found">
        <h2>Project Not Found</h2>
        <p>The project you're looking for doesn't exist or you don't have access to it.</p>
        <Link to="/projects" className="btn btn-primary">
          Back to Projects
        </Link>
      </div>
    );
  }

  return (
    <div className="project-agreements-page">
      <div className="page-header">
        <div className="header-content">
          <h1 className="page-title">Project Agreements</h1>
          <div className="project-info">
            <Link to={`/project/${id}`} className="project-name-link">
              {project.name}
            </Link>
            <div className="project-meta">
              <span className="project-type">{project.project_type}</span>
              <span className="project-status">{project.is_active ? 'Active' : 'Inactive'}</span>
            </div>
          </div>
        </div>
        <div className="header-actions">
          <Link to={`/project/${id}`} className="btn btn-secondary">
            <i className="bi bi-arrow-left"></i> Back to Project
          </Link>
        </div>
      </div>

      <div className="page-content">
        <AgreementManager projectId={id} />
      </div>
    </div>
  );
};

export default ProjectAgreements;
