// Script to check the VotA agreement for remaining Village references
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

// Initialize Supabase client with the production URL and service role key
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzgyMDU1OSwiZXhwIjoyMDU5Mzk2NTU5fQ.k5xvFBzBxNMCtPd0d8Ko9dAcT5Y3op3ZgFvlAf85LLs';
const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log('Checking VotA agreement for remaining Village references...\n');
    
    // Get the VotA project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('*')
      .eq('name', 'VotA')
      .single();
    
    if (projectError) {
      console.error('Error fetching VotA project:', projectError);
      return;
    }
    
    if (!project) {
      console.log('VotA project not found');
      return;
    }
    
    console.log(`Found project: ${project.name} (${project.id})`);
    
    // Get the agreement for VotA
    const { data: agreements, error: agreementsError } = await supabase
      .from('contributor_agreements')
      .select('*')
      .eq('project_id', project.id);
    
    if (agreementsError) {
      console.error('Error fetching agreements:', agreementsError);
      return;
    }
    
    if (!agreements || agreements.length === 0) {
      console.log('No agreements found for VotA');
      return;
    }
    
    const agreement = agreements[0];
    console.log(`\nChecking agreement ID: ${agreement.id}`);
    console.log(`Version: ${agreement.version || 1}`);
    console.log(`Status: ${agreement.status || 'Unknown'}`);
    
    // Check for Village references
    const villageReferences = checkForVillageReferences(agreement.agreement_text);
    
    if (villageReferences.length > 0) {
      console.log(`\n⚠️ Found ${villageReferences.length} Village references:`);
      villageReferences.forEach((ref, index) => {
        console.log(`\n${index + 1}. Line ${ref.lineNumber}: ${ref.line}`);
        console.log(`   Context: ${ref.context}`);
      });
      
      // Save the agreement text to a file for inspection
      fs.writeFileSync('vota-agreement.md', agreement.agreement_text);
      console.log('\nAgreement text saved to vota-agreement.md for inspection');
    } else {
      console.log('✓ No Village references found in the agreement!');
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

// Helper function to check for Village references
function checkForVillageReferences(text) {
  if (!text) return [];
  
  const lines = text.split('\n');
  const references = [];
  
  const villagePatterns = [
    /village/i,
    /Village of The Ages/i,
    /Village of the Ages/i,
    /VOTA/i
  ];
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    for (const pattern of villagePatterns) {
      if (pattern.test(line)) {
        // Get context (1 line before and after)
        const start = Math.max(0, i - 1);
        const end = Math.min(lines.length - 1, i + 1);
        const context = lines.slice(start, end + 1).join('\n');
        
        references.push({
          lineNumber: i + 1,
          line: line,
          context: context
        });
        
        // Only add each line once, even if it matches multiple patterns
        break;
      }
    }
  }
  
  return references;
}

main();
