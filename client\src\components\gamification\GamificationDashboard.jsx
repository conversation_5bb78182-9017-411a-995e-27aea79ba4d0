import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Progress, Avatar, Badge } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

// Import enhanced gamification components
import ORBWallet from './ORBWallet';
import AchievementGallery from './AchievementGallery';
import ProgressDashboard from './ProgressDashboard';
import Leaderboard from './Leaderboard';
import ChallengeSystem from './ChallengeSystem';

/**
 * Gamification Dashboard - Main bento grid layout for all gamification features
 * 
 * Features:
 * - Bento grid layout with responsive widgets
 * - ORB wallet and transaction management
 * - Achievement tracking and celebration
 * - User progression and skill development
 * - Leaderboards and competitive elements
 * - Real-time data updates
 */
const GamificationDashboard = ({ className = "" }) => {
  const { currentUser } = useContext(UserContext);
  
  // State management
  const [loading, setLoading] = useState(true);
  const [gamificationData, setGamificationData] = useState({
    orbBalance: 1247,
    totalXp: 12450,
    currentLevel: 12,
    achievements: 8,
    currentRank: 4,
    monthlyXp: 890,
    recentTransactions: [
      { amount: 50, source: 'Project Alpha', timeAgo: '2 hours ago' },
      { amount: 25, source: 'Code Review', timeAgo: '1 day ago' },
      { amount: 75, source: 'Alliance Creation', timeAgo: '3 days ago' }
    ],
    recentAchievements: [
      { achievement_id: 'first_alliance', unlocked_at: new Date().toISOString() },
      { achievement_id: 'tasks_10', unlocked_at: new Date(Date.now() - 86400000).toISOString() },
      { achievement_id: 'collaborations_5', unlocked_at: new Date(Date.now() - 172800000).toISOString() }
    ],
    userProgress: {
      currentLevel: 12,
      totalXp: 12450,
      xpToNextLevel: 3200,
      xpForNextLevel: 12800,
      monthlyXp: 890,
      skills: [
        { name: 'React', level: 'Expert', progress: 100, color: 'primary' },
        { name: 'Node.js', level: 'Advanced', progress: 80, color: 'success' },
        { name: 'TypeScript', level: 'Intermediate', progress: 60, color: 'warning' }
      ]
    }
  });

  // Load gamification data
  const loadGamificationData = async () => {
    if (!currentUser) return;

    setLoading(true);
    try {
      // Load user progression data
      const { data: progression, error: progressionError } = await supabase
        .from('user_progression')
        .select('*')
        .eq('user_id', currentUser.id)
        .single();

      if (progressionError && progressionError.code !== 'PGRST116') {
        console.error('Error loading progression:', progressionError);
      }

      // Load user achievements
      const { data: achievements, error: achievementsError } = await supabase
        .from('user_achievements')
        .select('*')
        .eq('user_id', currentUser.id)
        .order('unlocked_at', { ascending: false });

      if (achievementsError) {
        console.error('Error loading achievements:', achievementsError);
      }

      // Update state with real data if available
      if (progression) {
        setGamificationData(prev => ({
          ...prev,
          orbBalance: progression.orb_balance || prev.orbBalance,
          totalXp: progression.total_xp || prev.totalXp,
          currentLevel: progression.current_level || prev.currentLevel,
          achievements: achievements?.length || prev.achievements,
          recentAchievements: achievements?.slice(0, 3) || prev.recentAchievements
        }));
      }
    } catch (error) {
      console.error('Error loading gamification data:', error);
      toast.error('Failed to load gamification data');
    } finally {
      setLoading(false);
    }
  };

  // Handle ORB cash out
  const handleCashOut = async (amount) => {
    try {
      // TODO: Implement actual cash out logic
      setGamificationData(prev => ({
        ...prev,
        orbBalance: prev.orbBalance - amount
      }));
      toast.success(`Cashed out ${amount} ORBs successfully!`);
    } catch (error) {
      console.error('Cash out error:', error);
      toast.error('Failed to cash out ORBs');
    }
  };

  // Handle achievement sharing
  const handleShareAchievement = (achievement) => {
    // TODO: Implement social sharing
    toast.success('Achievement shared!');
  };

  // Handle goal setting
  const handleSetGoal = (goal) => {
    // TODO: Implement goal setting
    toast.success('Goal set successfully!');
  };

  // Initialize component
  useEffect(() => {
    loadGamificationData();
  }, [currentUser]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-default-600">Loading gamification data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`gamification-dashboard ${className}`}>
      {/* Dashboard Header - Following Wireframe */}
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent mb-2">
            💎 Gamification Hub
          </h1>
          <p className="text-default-600">
            Track your progress, earn ORBs, and compete with other developers
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="flat" startContent="🏪">
            ORB Store
          </Button>
          <Button variant="flat" isIconOnly>
            ⚙️
          </Button>
        </div>
      </div>

      {/* Enhanced Bento Grid Layout - Following Wireframe Specifications */}
      <div className="space-y-6">
        {/* Top Row: ORB Wallet + Achievements + Progress */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* ORB Wallet Component */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <ORBWallet
              currentUser={currentUser}
              orbBalance={gamificationData.orbBalance}
              recentTransactions={gamificationData.recentTransactions}
              onCashOut={handleCashOut}
              onViewHistory={() => toast.info('Transaction history coming soon!')}
            />
          </motion.div>

          {/* Achievement Gallery Component */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            <AchievementGallery
              achievements={gamificationData.recentAchievements}
              recentUnlocks={gamificationData.recentAchievements}
              onShareAchievement={handleShareAchievement}
              onViewAll={() => toast.info('Achievement gallery coming soon!')}
            />
          </motion.div>

          {/* Progress Dashboard Component */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
          >
            <ProgressDashboard
              currentUser={currentUser}
              userProgress={gamificationData.userProgress}
              onSetGoal={handleSetGoal}
              onViewSkillTree={() => toast.info('Skill tree coming soon!')}
            />
          </motion.div>
        </div>
        {/* Leaderboard Section - Full Width */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <Leaderboard
            currentUser={currentUser}
            userRank={gamificationData.currentRank}
            totalUsers={247}
            onViewFullLeaderboard={() => toast.info('Full leaderboard coming soon!')}
            onViewAllianceRankings={() => toast.info('Alliance rankings coming soon!')}
          />
        </motion.div>

        {/* Active Challenges Section - Full Width */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.5 }}
        >
          <ChallengeSystem
            onCreateChallenge={(challenge) => toast.success('Challenge created!')}
            onViewAllChallenges={() => toast.info('All challenges coming soon!')}
          />
        </motion.div>
      </div>
    </div>
  );
};

export default GamificationDashboard;
