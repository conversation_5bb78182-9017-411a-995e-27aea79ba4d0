import React from 'react';
import FreelanceMarketplace from '../../components/marketplace/FreelanceMarketplace';

/**
 * MissionBoardPage Component
 *
 * Main page for the Freelance Marketplace - the heart of Royaltea's gig economy
 * This is where freelancers discover external gigs and submit proposals
 */
const MissionBoardPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-blue-900">
      {/* Three-Column Bento Grid Layout */}
      <div className="flex min-h-screen">
        {/* Left Helper Sidebar */}
        <div className="w-16 bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm border-r border-default-200 flex flex-col items-center py-4 space-y-4">
          <button
            className="p-3 rounded-lg hover:bg-default-100 transition-colors"
            title="Notifications"
          >
            🔔
          </button>
          <button
            className="p-3 rounded-lg hover:bg-default-100 transition-colors"
            title="Messages"
          >
            📧
          </button>
          <button
            className="p-3 rounded-lg hover:bg-default-100 transition-colors"
            title="Saved Gigs"
          >
            💾
          </button>
          <button
            className="p-3 rounded-lg hover:bg-default-100 transition-colors"
            title="My Network"
          >
            👥
          </button>
          <button
            className="p-3 rounded-lg hover:bg-default-100 transition-colors"
            title="Settings"
          >
            ⚙️
          </button>
        </div>

        {/* Center Content Area */}
        <div className="flex-1 p-6">
          <FreelanceMarketplace />
        </div>

        {/* Right Context Sidebar */}
        <div className="w-16 bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm border-l border-default-200 flex flex-col items-center py-4 space-y-4">
          <button
            className="p-3 rounded-lg hover:bg-default-100 transition-colors"
            title="Post Gig"
          >
            📝
          </button>
          <button
            className="p-3 rounded-lg hover:bg-default-100 transition-colors"
            title="My Proposals"
          >
            📋
          </button>
          <button
            className="p-3 rounded-lg hover:bg-default-100 transition-colors"
            title="Active Work"
          >
            💼
          </button>
          <button
            className="p-3 rounded-lg hover:bg-default-100 transition-colors"
            title="Earnings"
          >
            💰
          </button>
          <button
            className="p-3 rounded-lg hover:bg-default-100 transition-colors"
            title="Profile"
          >
            👤
          </button>
        </div>
      </div>
    </div>
  );
};

export default MissionBoardPage;
