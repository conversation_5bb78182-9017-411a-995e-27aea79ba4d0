// <PERSON>ript to verify the roadmap update
const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';

// Read the key from .env.local file
const envPath = path.join(__dirname, 'client', '.env.local');
let supabaseKey;

try {
  const envContent = fs.readFileSync(envPath, 'utf8');
  const keyMatch = envContent.match(/SUPABASE_SERVICE_KEY=(.+)/);
  if (keyMatch && keyMatch[1]) {
    supabaseKey = keyMatch[1].trim();
  } else {
    throw new Error('SUPABASE_SERVICE_KEY not found in .env.local');
  }
} catch (error) {
  console.error(`Error reading key from ${envPath}:`, error.message);
  console.log('Please provide the Supabase service key as an environment variable');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

console.log(`Using Supabase URL: ${supabaseUrl}`);
console.log('=== Verifying Roadmap Update ===\n');

// Function to get the current roadmap data
async function getCurrentRoadmap() {
  try {
    console.log('=== Getting current roadmap data ===');

    // Get the latest roadmap entry
    const { data: roadmapData, error: roadmapError } = await supabase
      .from('roadmap')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1);

    if (roadmapError) {
      console.error('Error fetching roadmap from Supabase:', roadmapError);
      return null;
    }

    if (!roadmapData || roadmapData.length === 0) {
      console.log('No roadmap data found in the database');
      return null;
    }

    console.log('Found roadmap data with ID:', roadmapData[0].id);
    return roadmapData[0];
  } catch (error) {
    console.error('Error in getCurrentRoadmap:', error);
    return null;
  }
}

// Function to verify PDF preview tasks
function verifyPDFPreviewTasks(roadmapData) {
  console.log('\n=== Verifying PDF Preview Tasks ===');

  // Find the Platform Enhancements phase (index 4)
  const platformPhase = roadmapData.data.find(phase => phase.id === 5);

  if (!platformPhase) {
    console.log('Platform Enhancements phase not found');
    return false;
  }

  // Find the Document Management section (index 2)
  const docSection = platformPhase.sections.find(section => section.id === "5.3");

  if (!docSection) {
    console.log('Document Management section not found');
    return false;
  }

  // Check task 5.3.4 (Improve PDF preview formatting)
  const task1 = docSection.tasks.find(task => task.id === "5.3.4");
  if (task1) {
    console.log(`Task 5.3.4 "${task1.text}" - Completed: ${task1.completed}`);
  } else {
    console.log('Task 5.3.4 not found');
  }

  // Check task 5.3.5 (Fix PDF download issues)
  const task2 = docSection.tasks.find(task => task.id === "5.3.5");
  if (task2) {
    console.log(`Task 5.3.5 "${task2.text}" - Completed: ${task2.completed}`);
  } else {
    console.log('Task 5.3.5 not found');
  }

  // Check new task for agreement customization
  const newTask = docSection.tasks.find(task =>
    task.text.includes("Enhance agreement customization") ||
    task.text.includes("agreement customization")
  );

  if (newTask) {
    console.log(`New task ${newTask.id} "${newTask.text}" - Completed: ${newTask.completed}`);
  } else {
    console.log('New task for agreement customization not found');
  }

  return true;
}

// Function to verify tech stack update tasks
function verifyTechStackTasks(roadmapData) {
  console.log('\n=== Verifying Tech Stack Update Tasks ===');

  // Find the Tech Stack Modernization phase (Phase 2)
  const techPhase = roadmapData.data.find(phase => phase.id === 2);

  if (!techPhase) {
    console.log('Tech Stack Modernization phase (Phase 2) not found');
    return false;
  }

  console.log(`Phase 2: ${techPhase.title} (${techPhase.timeframe})`);

  // Find the Tech Stack Evaluation section
  const techEvalSection = techPhase.sections.find(section =>
    section.title.includes("Tech Stack Evaluation")
  );

  if (!techEvalSection) {
    console.log('Tech Stack Evaluation section not found in Phase 2');
    return false;
  }

  console.log(`\nSection ${techEvalSection.id}: ${techEvalSection.title}`);
  console.log('\nTasks:');

  // Display all tasks in the section
  techEvalSection.tasks.forEach(task => {
    console.log(`- [${task.completed ? 'X' : ' '}] ${task.id}: ${task.text}`);
  });

  // Find the Implementation & Migration section
  const implSection = techPhase.sections.find(section =>
    section.title.includes("Implementation & Migration")
  );

  if (implSection) {
    console.log(`\nSection ${implSection.id}: ${implSection.title}`);
    console.log('\nTasks:');

    // Display all tasks in the section
    implSection.tasks.forEach(task => {
      console.log(`- [${task.completed ? 'X' : ' '}] ${task.id}: ${task.text}`);
    });
  }

  return true;
}

// Function to verify latest feature update
function verifyLatestFeature(roadmapData) {
  console.log('\n=== Verifying Latest Feature Update ===');

  // Check top-level latest_feature (deprecated)
  console.log(`Top-level latest_feature: "${roadmapData.latest_feature}"`);

  // Find the metadata item
  const metadataItem = roadmapData.data.find(item => item.type === 'metadata');

  if (!metadataItem) {
    console.log('Metadata item not found');
    return false;
  }

  // Check latest_feature in metadata
  const latestFeature = metadataItem.latest_feature;

  if (latestFeature) {
    console.log('Latest feature in metadata:');
    console.log(`- Title: "${latestFeature.title}"`);
    console.log(`- Description: "${latestFeature.description}"`);
    console.log(`- Date: ${latestFeature.date}`);
    console.log(`- Author: "${latestFeature.author}"`);
    console.log(`- Version: "${latestFeature.version}"`);
  } else {
    console.log('latest_feature not found in metadata');
  }

  return true;
}

// Main function
async function main() {
  try {
    // Get current roadmap
    const roadmap = await getCurrentRoadmap();
    if (!roadmap) {
      console.error('Failed to get current roadmap');
      return;
    }

    // Verify PDF preview tasks
    verifyPDFPreviewTasks(roadmap);

    // Verify tech stack update tasks
    verifyTechStackTasks(roadmap);

    // Verify latest feature update
    verifyLatestFeature(roadmap);

    console.log('\n=== Verification Complete ===');
  } catch (error) {
    console.error('Error in main function:', error);
  }
}

// Run the main function
main();
