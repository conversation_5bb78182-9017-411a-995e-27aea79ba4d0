// Test Track/Contributions Functionality
const { chromium } = require('playwright');

const SITE_URL = 'https://royalty.technology';

async function testTrackContributions() {
  console.log('🔍 Testing Track/Contributions functionality...\n');

  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Use exact Playwright authentication method
    console.log('🔐 Authenticating with Playwright method...');

    await page.goto(SITE_URL);
    await page.waitForLoadState('networkidle');

    const emailInput = page.locator('input[type="email"]').first();
    const needsAuth = await emailInput.isVisible();

    if (needsAuth) {
      console.log('📝 Authentication required, logging in...');
      await emailInput.fill('<EMAIL>');
      await page.fill('input[type="password"]', 'TestPassword123!');
      await page.click('button[type="submit"]');

      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);

      const stillNeedsAuth = await page.locator('input[type="email"]').isVisible();
      if (stillNeedsAuth) {
        throw new Error('Authentication <NAME_EMAIL>');
      }

      console.log('✅ Authentication successful');
    } else {
      console.log('✅ Already authenticated');
    }

    // Test direct /track route
    console.log('📍 Testing /track route...');
    await page.goto(`${SITE_URL}/track?test_mode=true`);
    await page.waitForTimeout(3000);

    const trackContent = await page.textContent('body');
    console.log(`Track page content length: ${trackContent.length}`);
    console.log(`Track content preview: "${trackContent.substring(0, 200)}"`);

    // Check if it redirected to contributions
    const currentUrl = page.url();
    console.log(`Current URL: ${currentUrl}`);

    if (currentUrl.includes('/contributions')) {
      console.log('✅ Track page correctly redirected to /contributions');

      // Test contributions page content
      const contributionsContent = await page.textContent('body');
      console.log(`Contributions page content length: ${contributionsContent.length}`);
      console.log(`Contributions content preview: "${contributionsContent.substring(0, 200)}"`);

      // Check for contribution-related content
      const hasContributionContent = contributionsContent.includes('contribution') ||
                                   contributionsContent.includes('track') ||
                                   contributionsContent.includes('task') ||
                                   contributionsContent.includes('project');

      console.log(`Has contribution content: ${hasContributionContent}`);

      // Check for interactive elements
      const buttons = await page.locator('button').count();
      const links = await page.locator('a').count();
      const forms = await page.locator('form').count();

      console.log(`Interactive elements: ${buttons} buttons, ${links} links, ${forms} forms`);

    } else {
      console.log('❌ Track page did not redirect to contributions');
    }

    // Test direct /contributions route
    console.log('\n📍 Testing direct /contributions route...');
    await page.goto(`${SITE_URL}/contributions?test_mode=true`);
    await page.waitForTimeout(3000);

    const directContributionsContent = await page.textContent('body');
    console.log(`Direct contributions content length: ${directContributionsContent.length}`);
    console.log(`Direct contributions preview: "${directContributionsContent.substring(0, 200)}"`);

    // Check for contribution tracker functionality
    const hasTracker = directContributionsContent.includes('tracker') ||
                      directContributionsContent.includes('Tracker') ||
                      directContributionsContent.includes('contribution') ||
                      directContributionsContent.includes('Contribution');

    console.log(`Has tracker functionality: ${hasTracker}`);

    // Test canvas toggle buttons if they exist
    console.log('\n🗺️ Testing canvas toggle functionality...');

    const canvasToggleButton = await page.locator('button[title*="Navigation Canvas"]').first();
    const canvasToggleExists = await canvasToggleButton.count() > 0;

    console.log(`Canvas toggle button exists: ${canvasToggleExists}`);

    if (canvasToggleExists) {
      console.log('🔘 Clicking canvas toggle button...');
      await canvasToggleButton.click();
      await page.waitForTimeout(2000);

      // Check if canvas is now visible
      const canvasElement = await page.locator('[class*="canvas"], [class*="grid"], [class*="overworld"]').first();
      const canvasVisible = await canvasElement.count() > 0;

      console.log(`Canvas visible after toggle: ${canvasVisible}`);

      if (canvasVisible) {
        console.log('✅ Canvas toggle working!');

        // Test back to content button
        const backButton = await page.locator('button[title*="Back to Content"]').first();
        const backButtonExists = await backButton.count() > 0;

        console.log(`Back to content button exists: ${backButtonExists}`);

        if (backButtonExists) {
          console.log('🔙 Testing back to content...');
          await backButton.click();
          await page.waitForTimeout(2000);

          const contentVisible = await page.textContent('body');
          console.log(`Content restored: ${contentVisible.length > 1000}`);
        }
      }
    }

  } catch (error) {
    console.error('❌ Error during testing:', error.message);
  } finally {
    await browser.close();
  }
}

testTrackContributions();
