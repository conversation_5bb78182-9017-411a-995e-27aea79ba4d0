// Comprehensive Authenticated Testing
// Day 3 - Complete end-to-end testing with proper authentication

import { test, expect } from '@playwright/test';

const SITE_URL = 'https://royalty.technology';

// Test credentials - using working confirmed users
const TEST_USERS = {
  admin: {
    email: '<EMAIL>',
    password: 'TestPassword123!'
  },
  vrc: {
    email: '<EMAIL>',
    password: 'VRCPassword123!'
  },
  sales: {
    email: '<EMAIL>',
    password: 'SalesPassword123!'
  }
};

// Helper function to authenticate
async function authenticate(page, userType = 'admin') {
  const user = TEST_USERS[userType];

  console.log(`🔑 Authenticating as ${userType}: ${user.email}`);

  await page.goto(SITE_URL);
  await page.waitForLoadState('networkidle');

  // Check if login is needed
  const emailInput = page.locator('input[type="email"]').first();
  const needsAuth = await emailInput.isVisible();

  if (needsAuth) {
    await emailInput.fill(user.email);
    await page.fill('input[type="password"]', user.password);
    await page.click('button[type="submit"]');

    // Wait for authentication
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    // Verify authentication
    const stillNeedsAuth = await page.locator('input[type="email"]').isVisible();
    if (stillNeedsAuth) {
      throw new Error(`Authentication failed for ${user.email}`);
    }

    console.log(`✅ Authentication successful for ${user.email}`);
  } else {
    console.log(`✅ Already authenticated`);
  }

  return true;
}

// Helper function to get auth token
async function getAuthToken(page) {
  return await page.evaluate(() => {
    const token = localStorage.getItem('supabase.auth.token') ||
                 localStorage.getItem('sb-hqqlrrqvjcetoxbdjgzx-auth-token');
    return token ? JSON.parse(token).access_token : null;
  });
}

test.describe('Comprehensive Alliance System Testing', () => {
  test('should test complete navigation flow with authentication', async ({ page }) => {
    console.log('🧭 Starting comprehensive navigation test...');

    // Authenticate
    await authenticate(page);

    // Test experimental navigation
    console.log('🎮 Testing experimental navigation...');
    await page.goto(SITE_URL);
    await page.waitForLoadState('networkidle');

    const hasCanvas = await page.locator('canvas').isVisible();
    console.log('Has experimental navigation:', hasCanvas);

    if (hasCanvas) {
      // Test keyboard navigation
      await page.keyboard.press('ArrowDown');
      await page.waitForTimeout(1000);
      await page.keyboard.press('ArrowDown');
      await page.waitForTimeout(1000);

      // Look for teams card
      const teamsCard = page.locator('text=Teams').first();
      const teamsVisible = await teamsCard.isVisible();
      console.log('Teams card visible in grid:', teamsVisible);

      if (teamsVisible) {
        await teamsCard.click();
        await page.waitForLoadState('networkidle');
        console.log('Navigated to:', page.url());
      }
    }

    // Test direct navigation to teams
    console.log('🏰 Testing teams page...');
    await page.goto(`${SITE_URL}/teams`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    const teamsContent = await page.textContent('body');
    console.log('Teams page content length:', teamsContent.length);

    // Look for team management elements
    const hasTeamElements = teamsContent.toLowerCase().includes('team') ||
                           teamsContent.toLowerCase().includes('alliance') ||
                           teamsContent.toLowerCase().includes('create');
    console.log('Has team elements:', hasTeamElements);

    // Test alliance management route
    console.log('⚔️ Testing alliance management...');
    await page.goto(`${SITE_URL}/teams/test-id/manage`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    const manageContent = await page.textContent('body');
    console.log('Management page content length:', manageContent.length);

    // Check for alliance management components
    const allianceIndicators = [
      'Alliance Information',
      'Business Entity',
      'Alliance Permissions',
      'Manage Alliance'
    ];

    console.log('🔍 Alliance management features:');
    for (const indicator of allianceIndicators) {
      const found = manageContent.includes(indicator);
      console.log(`  ${indicator}: ${found}`);
    }
  });

  test('should test payment APIs with authentication', async ({ page }) => {
    console.log('💰 Testing payment APIs...');

    await authenticate(page);
    const authToken = await getAuthToken(page);

    console.log('Auth token available:', !!authToken);

    if (authToken) {
      const endpoints = [
        '/.netlify/functions/companies',
        '/.netlify/functions/financial-transactions',
        '/.netlify/functions/commission-payments',
        '/.netlify/functions/recurring-fees'
      ];

      for (const endpoint of endpoints) {
        console.log(`\nTesting ${endpoint}...`);

        const response = await page.request.get(`${SITE_URL}${endpoint}`, {
          headers: {
            'Authorization': `Bearer ${authToken}`
          }
        });

        console.log(`Status: ${response.status()}`);

        if (response.status() === 200) {
          try {
            const data = await response.json();
            console.log(`✅ Success:`, data);
          } catch (e) {
            console.log(`✅ Success (non-JSON response)`);
          }
        } else if (response.status() === 500) {
          try {
            const errorData = await response.json();
            console.log(`⚠️ Server error:`, errorData.error);
          } catch (e) {
            const errorText = await response.text();
            console.log(`⚠️ Server error:`, errorText.substring(0, 100));
          }
        } else {
          console.log(`⚠️ Unexpected status: ${response.status()}`);
        }
      }
    }
  });

  test('should test alliance component loading', async ({ page }) => {
    console.log('🔧 Testing alliance component loading...');

    await authenticate(page);

    // Monitor network requests for alliance components
    const allianceRequests = [];
    page.on('request', request => {
      if (request.url().includes('Alliance') || request.url().includes('alliance')) {
        allianceRequests.push(request.url());
      }
    });

    // Navigate to alliance management
    await page.goto(`${SITE_URL}/teams/test-id/manage`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    console.log('Alliance-related requests:', allianceRequests.length);
    allianceRequests.forEach(url => console.log(`  ${url}`));

    // Check if alliance components are in the DOM
    const hasAllianceContainer = await page.locator('.alliance-manage-container').count() > 0;
    const hasBusinessSection = await page.locator('.business-entity-section').count() > 0;
    const hasPermissionsSection = await page.locator('.alliance-permissions-section').count() > 0;

    console.log('Alliance components in DOM:');
    console.log(`  Alliance container: ${hasAllianceContainer}`);
    console.log(`  Business section: ${hasBusinessSection}`);
    console.log(`  Permissions section: ${hasPermissionsSection}`);

    // Check for alliance-specific text
    const pageContent = await page.textContent('body');
    const hasAllianceText = pageContent.includes('Alliance') || pageContent.includes('alliance');
    const hasBusinessText = pageContent.includes('Business Entity') || pageContent.includes('Register Business');

    console.log('Alliance content:');
    console.log(`  Alliance text: ${hasAllianceText}`);
    console.log(`  Business text: ${hasBusinessText}`);
  });

  test('should test business entity registration flow', async ({ page }) => {
    console.log('🏢 Testing business entity registration...');

    await authenticate(page);

    // Navigate to alliance management
    await page.goto(`${SITE_URL}/teams/test-id/manage`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    // Look for business registration elements
    const registerButton = page.locator('text=Register Business').first();
    const hasRegisterButton = await registerButton.isVisible();

    console.log('Register business button visible:', hasRegisterButton);

    if (hasRegisterButton) {
      await registerButton.click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);

      // Check if registration form loads
      const formContent = await page.textContent('body');
      const hasRegistrationForm = formContent.includes('Legal Name') ||
                                 formContent.includes('Tax ID') ||
                                 formContent.includes('Business Type');

      console.log('Registration form loaded:', hasRegistrationForm);

      if (hasRegistrationForm) {
        // Test form fields
        const legalNameField = page.locator('input[name="legal_name"], input[id="legal_name"]').first();
        const taxIdField = page.locator('input[name="tax_id"], input[id="tax_id"]').first();

        const hasLegalNameField = await legalNameField.isVisible();
        const hasTaxIdField = await taxIdField.isVisible();

        console.log('Form fields:');
        console.log(`  Legal name field: ${hasLegalNameField}`);
        console.log(`  Tax ID field: ${hasTaxIdField}`);

        if (hasLegalNameField && hasTaxIdField) {
          // Test filling out the form
          await legalNameField.fill('Test Company LLC');
          await taxIdField.fill('12-3456789');

          console.log('✅ Form fields can be filled');
        }
      }
    }
  });

  test('should test CSS and styling integration', async ({ page }) => {
    console.log('🎨 Testing CSS and styling...');

    await authenticate(page);

    // Navigate to alliance management
    await page.goto(`${SITE_URL}/teams/test-id/manage`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    // Check if alliance styles are applied
    const hasAllianceStyles = await page.evaluate(() => {
      const testElement = document.createElement('div');
      testElement.className = 'alliance-manage-container';
      document.body.appendChild(testElement);

      const styles = window.getComputedStyle(testElement);
      const hasCustomStyling = styles.maxWidth !== 'none' ||
                              styles.padding !== '0px' ||
                              styles.background !== 'rgba(0, 0, 0, 0)';

      document.body.removeChild(testElement);
      return hasCustomStyling;
    });

    console.log('Alliance styles applied:', hasAllianceStyles);

    // Check for loaded stylesheets
    const stylesheets = await page.evaluate(() => {
      return Array.from(document.styleSheets).map(sheet => {
        try {
          return sheet.href || 'inline';
        } catch (e) {
          return 'blocked';
        }
      });
    });

    console.log('Loaded stylesheets:', stylesheets.length);

    // Look for alliance-specific CSS
    const hasAllianceCSS = stylesheets.some(sheet =>
      sheet.includes('Alliance') || sheet.includes('alliance')
    );

    console.log('Alliance-specific CSS loaded:', hasAllianceCSS);
  });
});
