// Simple Test Mode Authentication Test
const { chromium } = require('playwright');

const SITE_URL = 'https://royalty.technology';

async function testSimpleAuth() {
  console.log('🧪 Testing simple test mode authentication...\n');

  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Use exact Playwright authentication method
    console.log('🔐 Authenticating with Playwright method...');

    await page.goto(SITE_URL);
    await page.waitForLoadState('networkidle');

    const emailInput = page.locator('input[type="email"]').first();
    const needsAuth = await emailInput.isVisible();

    if (needsAuth) {
      console.log('📝 Authentication required, logging in...');
      await emailInput.fill('<EMAIL>');
      await page.fill('input[type="password"]', 'TestPassword123!');
      await page.click('button[type="submit"]');

      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);

      const stillNeedsAuth = await page.locator('input[type="email"]').isVisible();
      if (stillNeedsAuth) {
        throw new Error('Authentication <NAME_EMAIL>');
      }

      console.log('✅ Authentication successful');
    } else {
      console.log('✅ Already authenticated');
    }

    // Now test home page with test mode
    console.log('📍 Testing home page with test_mode=true...');
    await page.goto(`${SITE_URL}/?test_mode=true`);
    await page.waitForTimeout(5000);

    const content = await page.textContent('body');
    console.log(`Content length: ${content.length}`);
    console.log(`Content preview: "${content.substring(0, 300)}"`);

    // Check for specific content types
    const hasTestModeIndicator = content.includes('Test Mode Active') ||
                                content.includes('🧪') ||
                                content.includes('test mode');

    const hasLoginContent = content.includes('Welcome Back') ||
                           content.includes('Sign in') ||
                           content.includes('Password');

    const hasTeamsContent = content.includes('Teams') ||
                           content.includes('Alliance') ||
                           content.includes('My Teams');

    const hasDashboardContent = content.includes('Welcome back') ||
                               content.includes('Active Projects') ||
                               content.includes('Quick Actions');

    console.log(`Has test mode indicator: ${hasTestModeIndicator}`);
    console.log(`Has login content: ${hasLoginContent}`);
    console.log(`Has teams content: ${hasTeamsContent}`);
    console.log(`Has dashboard content: ${hasDashboardContent}`);

    // Test canvas toggle functionality
    console.log('\n🗺️ Testing canvas toggle functionality...');

    // Look for canvas toggle button
    const canvasToggleButton = await page.locator('button[title*="Navigation Canvas"], button[title*="Show Navigation"]').first();
    const canvasToggleExists = await canvasToggleButton.count() > 0;

    console.log(`Canvas toggle button exists: ${canvasToggleExists}`);

    if (canvasToggleExists) {
      console.log('🔘 Clicking canvas toggle button...');
      await canvasToggleButton.click();
      await page.waitForTimeout(3000);

      // Check if canvas is now visible
      const canvasElement = await page.locator('canvas, [class*="canvas"], [class*="grid"], [class*="overworld"]').first();
      const canvasVisible = await canvasElement.count() > 0;

      console.log(`Canvas visible after toggle: ${canvasVisible}`);

      if (canvasVisible) {
        console.log('✅ Canvas toggle working!');

        // Test back to content button
        const backButton = await page.locator('button[title*="Back to Content"]').first();
        const backButtonExists = await backButton.count() > 0;

        console.log(`Back to content button exists: ${backButtonExists}`);

        if (backButtonExists) {
          console.log('🔙 Testing back to content...');
          await backButton.click();
          await page.waitForTimeout(2000);

          const contentAfterBack = await page.textContent('body');
          console.log(`Content restored: ${contentAfterBack.length > 1000}`);
        }
      }
    } else {
      // Test ESC key toggle
      console.log('⌨️ Testing ESC key toggle...');
      await page.keyboard.press('Escape');
      await page.waitForTimeout(2000);

      const canvasAfterEsc = await page.locator('canvas, [class*="canvas"], [class*="grid"], [class*="overworld"]').first();
      const canvasVisibleAfterEsc = await canvasAfterEsc.count() > 0;

      console.log(`Canvas visible after ESC: ${canvasVisibleAfterEsc}`);
    }

    // Check current URL
    const currentUrl = page.url();
    console.log(`Current URL: ${currentUrl}`);

    // Check for any error messages in console
    const logs = [];
    page.on('console', msg => logs.push(msg.text()));

    await page.waitForTimeout(2000);

    if (logs.length > 0) {
      console.log('\n📝 Console logs:');
      logs.forEach(log => console.log(`  ${log}`));
    }

  } catch (error) {
    console.error('❌ Error during testing:', error.message);
  } finally {
    await browser.close();
  }
}

testSimpleAuth();
