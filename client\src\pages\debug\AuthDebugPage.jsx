import React from 'react';
// import { Container, <PERSON>, <PERSON>, <PERSON>, Alert } from 'react-bootstrap';
import AuthDebugger from '../../components/debug/AuthDebugger';

const AuthDebugPage = () => {
  return (
    <Container className="mt-4">
      <Row>
        <Col>
          <h1>Authentication Debugging</h1>
          <Alert variant="info">
            This page helps diagnose authentication issues with Google and other providers.
          </Alert>

          <AuthDebugger />

          <Card className="mb-4">
            <Card.Header as="h5">Authentication Flow</Card.Header>
            <Card.Body>
              <h6>Expected Flow:</h6>
              <ol>
                <li>User clicks "Login with Google"</li>
                <li>User is redirected to Google login page</li>
                <li>After successful login, Google redirects back to <code>/auth/callback</code></li>
                <li>The callback page processes the authentication and redirects to the dashboard</li>
              </ol>

              <h6>Common Issues:</h6>
              <ul>
                <li>
                  <strong>404 on callback:</strong> The <code>/auth/callback</code> route isn't properly
                  configured in Netlify redirects
                </li>
                <li>
                  <strong>Redirect URI mismatch:</strong> The redirect URI in Google OAuth settings
                  doesn't match your site's callback URL
                </li>
                <li>
                  <strong>Missing environment variables:</strong> Supabase URL or anon key is missing or incorrect
                </li>
                <li>
                  <strong>CORS issues:</strong> Browser security preventing the callback from completing
                </li>
              </ul>
            </Card.Body>
          </Card>

          <Card>
            <Card.Header as="h5">Callback URL Information</Card.Header>
            <Card.Body>
              <p>
                Your callback URL should be: <code>{window.location.origin}/auth/callback</code>
              </p>
              <p>
                Make sure this exact URL is configured in:
              </p>
              <ol>
                <li>Supabase Dashboard &gt; Authentication &gt; URL Configuration &gt; Redirect URLs</li>
                <li>Google Cloud Console &gt; APIs &amp; Services &gt; Credentials &gt; OAuth 2.0 Client IDs &gt; Authorized redirect URIs</li>
              </ol>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default AuthDebugPage;
