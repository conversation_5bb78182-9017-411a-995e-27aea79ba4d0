import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as Router } from "react-router-dom";
import { HeroUIProvider } from "@heroui/react";
import { UserProvider } from "../contexts/supabase-auth.context.jsx";
import ThemeProvider from "./contexts/theme.context.jsx";
import FeatureFlagProvider from "./contexts/feature-flags.context.jsx";
import App from "./App.jsx";

// Import CSS
import "./styles/tailwind.css";

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <Router>
      <UserProvider>
        <ThemeProvider>
          <HeroUIProvider>
            <FeatureFlagProvider>
              <App />
            </FeatureFlagProvider>
          </HeroUIProvider>
        </ThemeProvider>
      </UserProvider>
    </Router>
  </StrictMode>
);
