import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@heroui/react';

/**
 * OnboardingSuccess Component
 * 
 * Success celebration screens for completed onboarding actions
 * Follows exact wireframe specifications for success states
 */
const OnboardingSuccess = ({ 
  result, 
  onContinue, 
  onGoToDashboard 
}) => {
  const getSuccessConfig = () => {
    switch (result?.type) {
      case 'alliance_created':
        return {
          icon: '🎉',
          title: 'Alliance Created Successfully!',
          message: `Your "${result.alliance?.name}" alliance is ready for collaboration`,
          primaryAction: {
            label: 'Invite Members',
            action: () => onContinue('invite_members')
          },
          secondaryAction: {
            label: 'Go to Dashboard',
            action: onGoToDashboard
          }
        };
      
      case 'venture_created':
        return {
          icon: '🎉',
          title: 'Venture Created Successfully!',
          message: `Your "${result.venture?.name}" venture is ready for your first mission`,
          primaryAction: {
            label: 'Create Mission',
            action: () => onContinue('create_mission')
          },
          secondaryAction: {
            label: 'Go to Dashboard',
            action: onGoToDashboard
          }
        };
      
      case 'opportunities_found':
        return {
          icon: '🎯',
          title: 'Perfect Matches Found!',
          message: 'We found opportunities that match your skills and experience',
          primaryAction: {
            label: 'View Opportunities',
            action: () => onContinue('view_opportunities')
          },
          secondaryAction: {
            label: 'Go to Dashboard',
            action: onGoToDashboard
          }
        };
      
      case 'tutorial_complete':
        return {
          icon: '🎓',
          title: 'Tutorial Complete!',
          message: 'You now understand how Royaltea works',
          primaryAction: {
            label: 'Start Your Journey',
            action: () => onContinue('start_journey')
          },
          secondaryAction: {
            label: 'Go to Dashboard',
            action: onGoToDashboard
          }
        };
      
      default:
        return {
          icon: '✅',
          title: 'Welcome to Royaltea!',
          message: 'Your account is set up and ready to go',
          primaryAction: {
            label: 'Get Started',
            action: onGoToDashboard
          },
          secondaryAction: null
        };
    }
  };

  const config = getSuccessConfig();

  return (
    <div className="fixed inset-0 z-50 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="text-center max-w-2xl mx-auto px-8"
      >
        {/* Success Icon */}
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, duration: 0.5, type: "spring", stiffness: 200 }}
          className="text-8xl mb-6"
        >
          {config.icon}
        </motion.div>

        {/* Success Title */}
        <motion.h1
          className="text-3xl md:text-5xl font-bold text-white mb-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.5 }}
        >
          {config.title}
        </motion.h1>

        {/* Success Message */}
        <motion.p
          className="text-lg md:text-xl text-white text-opacity-80 mb-8 max-w-lg mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.5 }}
        >
          {config.message}
        </motion.p>

        {/* Action Buttons */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.5 }}
        >
          {/* Primary Action */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              onClick={config.primaryAction.action}
              size="lg"
              className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-8 py-4 text-lg font-medium min-w-[200px]"
            >
              {config.primaryAction.label}
            </Button>
          </motion.div>

          {/* Secondary Action */}
          {config.secondaryAction && (
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                onClick={config.secondaryAction.action}
                variant="ghost"
                className="text-white text-opacity-70 hover:text-opacity-100 bg-transparent hover:bg-white hover:bg-opacity-10 px-6 py-3"
                size="lg"
              >
                {config.secondaryAction.label}
              </Button>
            </motion.div>
          )}
        </motion.div>

        {/* Celebration Animation */}
        <motion.div
          className="absolute inset-0 pointer-events-none"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1, duration: 2 }}
        >
          {/* Floating particles effect */}
          {[...Array(12)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-white rounded-full opacity-60"
              style={{
                left: `${20 + Math.random() * 60}%`,
                top: `${20 + Math.random() * 60}%`,
              }}
              animate={{
                y: [-20, -40, -20],
                opacity: [0.6, 0.2, 0.6],
                scale: [1, 1.5, 1],
              }}
              transition={{
                duration: 2 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </motion.div>
      </motion.div>
    </div>
  );
};

export default OnboardingSuccess;
