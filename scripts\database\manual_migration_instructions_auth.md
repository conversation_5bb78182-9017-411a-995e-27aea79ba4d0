# Manual Migration Instructions for Auth Triggers

This migration will fix the issue with user profiles not being created during Google sign-up. Follow these steps to apply the migration manually:

## Step 1: Access the SQL Editor

1. Log in to your Supabase dashboard
2. Select your project
3. Click on "SQL Editor" in the left sidebar
4. Click "New Query" to create a new SQL query

## Step 2: Run the Migration SQL

Copy and paste the following SQL into the editor:

```sql
-- Improve authentication triggers to ensure user profiles are created
-- This migration adds or updates triggers that create user profiles when new users sign up

-- Create or replace the function that handles new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  display_name TEXT;
  user_exists BOOLEAN;
BEGIN
  -- Check if user already exists in the users table
  SELECT EXISTS (
    SELECT 1 FROM public.users WHERE id = NEW.id
  ) INTO user_exists;
  
  -- Only proceed if the user doesn't already exist
  IF NOT user_exists THEN
    -- Set display name from user metadata or email
    IF NEW.raw_user_meta_data->>'full_name' IS NOT NULL THEN
      display_name := NEW.raw_user_meta_data->>'full_name';
    ELSIF NEW.raw_user_meta_data->>'name' IS NOT NULL THEN
      display_name := NEW.raw_user_meta_data->>'name';
    ELSE
      -- Extract username from email (part before @)
      display_name := split_part(NEW.email, '@', 1);
    END IF;
    
    -- Insert the new user
    INSERT INTO public.users (id, email, display_name, created_at, updated_at)
    VALUES (
      NEW.id,
      NEW.email,
      display_name,
      NOW(),
      NOW()
    );
    
    RAISE NOTICE 'Created new user profile for %', NEW.email;
  ELSE
    RAISE NOTICE 'User % already exists in the users table', NEW.email;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop the trigger if it exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Create the trigger
CREATE TRIGGER on_auth_user_created
AFTER INSERT ON auth.users
FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create or replace the function that handles user updates
CREATE OR REPLACE FUNCTION public.handle_user_update()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the user in the users table if they exist
  UPDATE public.users
  SET 
    email = NEW.email,
    updated_at = NOW()
  WHERE id = NEW.id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop the trigger if it exists
DROP TRIGGER IF EXISTS on_auth_user_updated ON auth.users;

-- Create the trigger
CREATE TRIGGER on_auth_user_updated
AFTER UPDATE ON auth.users
FOR EACH ROW EXECUTE FUNCTION public.handle_user_update();
```

## Step 3: Execute the Query

Click the "Run" button to execute the SQL query. You should see a success message in the results panel.

## Step 4: Verify the Changes

### Check the Database Functions

1. Go to the "Database" section in the left sidebar
2. Click on "Functions" to view the database functions
3. Verify that the following functions have been created:
   - `handle_new_user`
   - `handle_user_update`

### Check the Triggers

1. Go to the "Database" section in the left sidebar
2. Click on "Triggers" to view the database triggers
3. Verify that the following triggers have been created:
   - `on_auth_user_created` (on the `auth.users` table)
   - `on_auth_user_updated` (on the `auth.users` table)

## Step 5: Test the Authentication

After successfully running the migration, test the authentication to ensure that:

1. New users who sign up with Google have their profiles automatically created in the `users` table
2. Existing users can still sign in with Google

## Troubleshooting

If you encounter any issues:

1. Check the error messages in the SQL Editor results panel
2. Make sure you have the necessary permissions to create triggers on the `auth.users` table
3. Verify that the `users` table exists and has the expected columns (`id`, `email`, `display_name`, `created_at`, `updated_at`)

For more help, refer to the Supabase documentation on [authentication hooks](https://supabase.com/docs/guides/auth/auth-hooks).
