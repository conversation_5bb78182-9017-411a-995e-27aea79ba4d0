# 🔑 API Keys Master Document
**⚠️ DEVELOPMENT ONLY - DO NOT COMMIT TO PUBLIC REPOS**

This document contains all API keys and secrets needed for Royaltea development. For production deployment, these should be set as environment variables in Netlify.

---

## 🚀 Quick Setup Instructions

1. **Copy this file** to `client/.env.local` (for frontend)
2. **Fill in your actual API keys** (replace placeholder values)
3. **Never commit** the actual `.env.local` file to version control
4. **For production**: Set these as environment variables in Netlify dashboard

---

## 📋 Current API Keys Status

### ✅ **CONFIGURED SERVICES**
- [x] Supabase (Database & Auth) - ✅ URL + Anon Key
- [x] Google OAuth (Authentication) - ✅ WORKING (secrets in Supabase dashboard)
- [x] GitHub OAuth (Authentication) - ✅ WORKING (secrets in Supabase dashboard)
- [x] Google Analytics (Tracking) - ✅ Tracking ID
- [x] Teller (Payment Processing) - ✅ Certificates Added (replacing Plaid)
- [ ] Email Service (Notifications) - ⚠️ Need SMTP Credentials

### 🔄 **SERVICES TO ADD**
- [ ] OpenAI/Claude (AI Features)
- [ ] Discord (Team Communication)
- [ ] Slack (Team Communication)
- [ ] AWS S3 (File Storage)
- [ ] Jira/Trello (Project Management)
- [ ] Twilio (SMS Notifications)
- [ ] Push Notifications
- [ ] Social Media APIs

### ✅ **MIGRATION COMPLETE**
- [x] Teller Integration (Replacing Plaid) - ✅ Certificates configured
- [x] Update payment functions to use Teller API - ✅ Complete
- [x] Update frontend components to use Teller - ✅ Complete
- [x] Update all documentation to reference Teller - ✅ Complete
- [ ] Test Teller integration end-to-end - ⚠️ Ready for testing

---

## 🔧 Environment Variables Template

```bash
# ==========================================
# CORE INFRASTRUCTURE
# ==========================================

# Supabase Configuration (REQUIRED)
VITE_SUPABASE_URL=https://hqqlrrqvjcetoxbdjgzx.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MjA1NTksImV4cCI6MjA1OTM5NjU1OX0.e0oaDNVbA563SZJHPm6UTFOXcLbWorKn42a_brChpKQ
SUPABASE_SERVICE_KEY=your_supabase_service_key_here
SUPABASE_URL=https://hqqlrrqvjcetoxbdjgzx.supabase.co

# Database Direct Access
supabase_database_password=your_database_password_here

# Site Configuration
SITE_URL=https://royalty.technology
NODE_ENV=development
VITE_DEBUG=false

# ==========================================
# AUTHENTICATION & OAUTH
# ==========================================

# Google OAuth (CONFIGURED - Working via Supabase)
# Note: OAuth secrets are stored in Supabase dashboard, not environment variables
GOOGLE_CLIENT_ID=807950544313-4gq4f6jrvpv8vjl28gblfclqbhdr5fcg.apps.googleusercontent.com
# GOOGLE_CLIENT_SECRET=managed_by_supabase_dashboard

# GitHub OAuth (CONFIGURED - Working via Supabase)
# Note: OAuth secrets are stored in Supabase dashboard, not environment variables
GITHUB_CLIENT_ID=Ov23li1gCLryRzy97ZgL
# GITHUB_CLIENT_SECRET=managed_by_supabase_dashboard

# Additional OAuth Providers (OPTIONAL)
DISCORD_CLIENT_ID=your_discord_client_id_here
DISCORD_CLIENT_SECRET=your_discord_client_secret_here
LINKEDIN_CLIENT_ID=your_linkedin_client_id_here
LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret_here

# ==========================================
# PAYMENT PROCESSING
# ==========================================

# Teller Configuration (PRIMARY - Replacing Plaid)
# Note: Teller uses certificate-based authentication
TELLER_APPLICATION_ID=app_pelk82mrrofp6upddo000
TELLER_ENVIRONMENT=sandbox
TELLER_WEBHOOK_URL=https://royalty.technology/.netlify/functions/teller-webhook
TELLER_CERTIFICATE_PATH=./teller/certificate.pem
TELLER_PRIVATE_KEY_PATH=./teller/private_key.pem

# Plaid Configuration (LEGACY - REMOVED - Replaced by Teller)
# PLAID_CLIENT_ID=DEPRECATED_USE_TELLER
# PLAID_SECRET=DEPRECATED_USE_TELLER
# PLAID_ENV=DEPRECATED_USE_TELLER
# PLAID_WEBHOOK_URL=DEPRECATED_USE_TELLER

# Payment Security
PAYMENT_WEBHOOK_SECRET=your_webhook_secret_here
PAYMENT_ENCRYPTION_KEY=your_encryption_key_here

# Stripe (OPTIONAL - Alternative Payment Processor)
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key_here
STRIPE_SECRET_KEY=your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here

# ==========================================
# AI & MACHINE LEARNING
# ==========================================

# OpenAI (For AI Features)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_ORG_ID=your_openai_org_id_here

# Anthropic Claude (Alternative AI)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Google AI/Gemini
GOOGLE_AI_API_KEY=your_google_ai_api_key_here

# ==========================================
# COMMUNICATION & NOTIFICATIONS
# ==========================================

# Email Service (CONFIGURED)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=your_email_username_here
EMAIL_PASS=your_email_password_here
EMAIL_FROM=<EMAIL>

# Twilio (SMS & WhatsApp)
TWILIO_ACCOUNT_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here
TWILIO_PHONE_NUMBER=your_twilio_phone_number_here

# Discord Bot (Team Communication)
DISCORD_BOT_TOKEN=your_discord_bot_token_here
DISCORD_GUILD_ID=your_discord_guild_id_here

# Slack Integration
SLACK_BOT_TOKEN=your_slack_bot_token_here
SLACK_SIGNING_SECRET=your_slack_signing_secret_here
SLACK_CLIENT_ID=your_slack_client_id_here
SLACK_CLIENT_SECRET=your_slack_client_secret_here

# Push Notifications
VAPID_PUBLIC_KEY=your_vapid_public_key_here
VAPID_PRIVATE_KEY=your_vapid_private_key_here
VAPID_SUBJECT=mailto:<EMAIL>

# ==========================================
# ANALYTICS & MONITORING
# ==========================================

# Google Analytics (CONFIGURED)
VITE_GA_TRACKING_ID=G-S7SFML469V
GA_MEASUREMENT_ID=G-S7SFML469V

# Additional Analytics
MIXPANEL_TOKEN=your_mixpanel_token_here
AMPLITUDE_API_KEY=your_amplitude_api_key_here
HOTJAR_ID=your_hotjar_id_here

# Error Monitoring
SENTRY_DSN=your_sentry_dsn_here
SENTRY_AUTH_TOKEN=your_sentry_auth_token_here

# ==========================================
# FILE STORAGE & CDN
# ==========================================

# AWS S3 (File Storage)
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_REGION=us-east-1
AWS_S3_BUCKET=royaltea-files

# Cloudinary (Image/Video Processing)
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name_here
CLOUDINARY_API_KEY=your_cloudinary_api_key_here
CLOUDINARY_API_SECRET=your_cloudinary_api_secret_here

# ==========================================
# PROJECT MANAGEMENT INTEGRATIONS
# ==========================================

# Jira Integration
JIRA_BASE_URL=your_jira_instance_url_here
JIRA_EMAIL=your_jira_email_here
JIRA_API_TOKEN=your_jira_api_token_here

# Trello Integration
TRELLO_API_KEY=your_trello_api_key_here
TRELLO_TOKEN=your_trello_token_here

# GitHub Integration (Beyond OAuth)
GITHUB_PERSONAL_ACCESS_TOKEN=your_github_pat_here

# ==========================================
# SOCIAL MEDIA & EXTERNAL APIs
# ==========================================

# Twitter/X API
TWITTER_API_KEY=your_twitter_api_key_here
TWITTER_API_SECRET=your_twitter_api_secret_here
TWITTER_ACCESS_TOKEN=your_twitter_access_token_here
TWITTER_ACCESS_TOKEN_SECRET=your_twitter_access_token_secret_here

# LinkedIn API
LINKEDIN_API_KEY=your_linkedin_api_key_here
LINKEDIN_API_SECRET=your_linkedin_api_secret_here

# ==========================================
# DEVELOPMENT & TESTING
# ==========================================

# Testing Keys (Use sandbox/test versions)
TEST_TELLER_APPLICATION_ID=your_test_teller_app_id_here
TEST_TELLER_CERTIFICATE_PATH=./teller/test_certificate.pem
TEST_STRIPE_KEY=your_test_stripe_key_here

# Local Development
VITE_LOCAL_API_URL=http://localhost:8888/.netlify/functions
LOCAL_SUPABASE_URL=http://localhost:54321
LOCAL_SUPABASE_ANON_KEY=your_local_supabase_anon_key_here
```

> **⚠️ IMPORTANT NOTES:**
> - **OAuth Authentication**: Google & GitHub OAuth are WORKING! Secrets are managed in Supabase dashboard, not env vars
> - **Supabase Anon Key**: This is your actual working key from the project
> - **OAuth Client IDs**: These are real and properly configured
> - **Teller Integration**: Certificates are configured! Teller offers better dev experience + 100 free API calls
> - **Payment Migration**: Moving from Plaid to Teller for better testing and free tier
> - **Local Development**: The "dummy-secret" values in supabase/config.toml are just for local dev

---

## 📚 Where to Get Each API Key

### **Core Infrastructure**
- **Supabase**: [Dashboard](https://supabase.com/dashboard/project/hqqlrrqvjcetoxbdjgzx/settings/api)
- **Netlify**: [Site Settings](https://app.netlify.com/sites/royalty-technology/settings/env)

### **Authentication**
- **Google OAuth**: [Google Cloud Console](https://console.cloud.google.com/apis/credentials)
- **GitHub OAuth**: [GitHub Developer Settings](https://github.com/settings/developers)
- **Discord OAuth**: [Discord Developer Portal](https://discord.com/developers/applications)

### **Payments**
- **Teller**: [Teller Console](https://teller.io/console) - Certificate-based auth (already configured)
- **Plaid**: [Plaid Dashboard](https://dashboard.plaid.com/team/keys) - (Legacy, being replaced)
- **Stripe**: [Stripe Dashboard](https://dashboard.stripe.com/apikeys)

### **AI Services**
- **OpenAI**: [OpenAI API Keys](https://platform.openai.com/api-keys)
- **Anthropic**: [Anthropic Console](https://console.anthropic.com/)
- **Google AI**: [Google AI Studio](https://makersuite.google.com/app/apikey)

### **Communication**
- **Twilio**: [Twilio Console](https://console.twilio.com/)
- **Discord Bot**: [Discord Developer Portal](https://discord.com/developers/applications)
- **Slack**: [Slack API](https://api.slack.com/apps)

### **Analytics & Monitoring**
- **Google Analytics**: [GA4 Admin](https://analytics.google.com/analytics/web/)
- **Sentry**: [Sentry Settings](https://sentry.io/settings/)
- **Mixpanel**: [Mixpanel Settings](https://mixpanel.com/settings/project)

### **File Storage**
- **AWS S3**: [AWS IAM Console](https://console.aws.amazon.com/iam/)
- **Cloudinary**: [Cloudinary Console](https://cloudinary.com/console)

### **Project Management**
- **Jira**: [Atlassian API Tokens](https://id.atlassian.com/manage-profile/security/api-tokens)
- **Trello**: [Trello Developer API Keys](https://trello.com/app-key)

---

## 🔒 Security Best Practices

1. **Never commit** actual API keys to version control
2. **Use environment variables** for all sensitive data
3. **Rotate keys regularly** (especially for production)
4. **Use least privilege** - only grant necessary permissions
5. **Monitor usage** - set up alerts for unusual API activity
6. **Use different keys** for development vs production
7. **Store backup copies** securely (password manager, encrypted files)

---

## 🚀 Production Deployment

For production deployment on Netlify, set these environment variables in:
**Netlify Dashboard → Site Settings → Environment Variables**

**Required for Production:**
- All Supabase keys
- Plaid production keys
- Email service credentials
- Google Analytics ID
- OAuth client secrets

---

## 📞 Support & Resources

- **Supabase Support**: [docs.supabase.com](https://docs.supabase.com)
- **Teller Support**: [teller.io/docs](https://teller.io/docs)
- **Netlify Support**: [docs.netlify.com](https://docs.netlify.com)
- **Project Issues**: [GitHub Issues](https://github.com/CityOfGamers/royaltea/issues)

---

**Last Updated**: December 2024  
**Maintained By**: Development Team  
**Status**: Active Development
