// <PERSON>ript to generate SQL commands for updating the roadmap
console.log('=== Generating SQL Commands for Roadmap Update ===\n');

// Latest feature update
const latestFeature = {
  title: "PDF Preview Improvements",
  description: "Enhanced PDF preview with left-justified text and fixed automatic download issues. PDFs now display properly and only download when explicitly requested by the user.",
  date: new Date().toISOString(),
  author: "Development Team",
  version: "1.0.1"
};

// Generate SQL for updating the latest feature
console.log('-- SQL to update the latest feature in the roadmap:');
console.log(`
-- First, get the current roadmap data
WITH current_roadmap AS (
  SELECT id, data
  FROM roadmap
  ORDER BY created_at DESC
  LIMIT 1
)

-- Then update the metadata item with the new latest_feature
UPDATE roadmap
SET 
  data = jsonb_set(
    data, 
    '{6,latest_feature}', 
    '${JSON.stringify(latestFeature)}'::jsonb
  ),
  last_updated = NOW()
FROM current_roadmap
WHERE roadmap.id = current_roadmap.id;
`);

// Generate SQL for marking tasks as completed
console.log('\n-- SQL to mark PDF preview tasks as completed:');
console.log(`
-- First, get the current roadmap data
WITH current_roadmap AS (
  SELECT id, data
  FROM roadmap
  ORDER BY created_at DESC
  LIMIT 1
)

-- Then update task 5.3.4 (Improve PDF preview formatting)
UPDATE roadmap
SET 
  data = jsonb_set(
    data, 
    '{4,sections,2,tasks,3,completed}', 
    'true'
  ),
  last_updated = NOW()
FROM current_roadmap
WHERE roadmap.id = current_roadmap.id;

-- Then update task 5.3.5 (Fix PDF download issues)
UPDATE roadmap
SET 
  data = jsonb_set(
    data, 
    '{4,sections,2,tasks,4,completed}', 
    'true'
  ),
  last_updated = NOW()
FROM current_roadmap
WHERE roadmap.id = current_roadmap.id;
`);

// Generate SQL for adding a new task
console.log('\n-- SQL to add a new task for agreement customization:');
console.log(`
-- First, get the current roadmap data
WITH current_roadmap AS (
  SELECT id, data
  FROM roadmap
  ORDER BY created_at DESC
  LIMIT 1
)

-- Then add the new task to section 5.3
UPDATE roadmap
SET 
  data = jsonb_set(
    data, 
    '{4,sections,2,tasks}', 
    (
      SELECT jsonb_agg(task)
      FROM (
        SELECT *
        FROM jsonb_array_elements((SELECT data#>'{4,sections,2,tasks}' FROM current_roadmap)) AS task
        UNION ALL
        SELECT '{"id":"5.3.6","text":"Enhance agreement customization for project-specific details","completed":false}'::jsonb
      ) AS tasks
    )
  ),
  last_updated = NOW()
FROM current_roadmap
WHERE roadmap.id = current_roadmap.id;
`);

console.log('\n=== SQL Generation Complete ===');
console.log('Copy these SQL commands and run them in the Supabase SQL Editor to update the roadmap.');
