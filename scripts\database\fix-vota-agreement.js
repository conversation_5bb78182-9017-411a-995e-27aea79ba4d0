// Script to fix the VotA agreement
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

// Initialize Supabase client with the production URL and service role key
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzgyMDU1OSwiZXhwIjoyMDU5Mzk2NTU5fQ.k5xvFBzBxNMCtPd0d8Ko9dAcT5Y3op3ZgFvlAf85LLs';
const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log('Fixing VotA agreement...\n');

    // Get the VotA project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('*')
      .eq('name', 'VotA')
      .single();

    if (projectError) {
      console.error('Error fetching VotA project:', projectError);
      return;
    }

    if (!project) {
      console.log('VotA project not found');
      return;
    }

    console.log(`Found project: ${project.name} (${project.id})`);

    // Get the agreement for VotA
    const { data: agreements, error: agreementsError } = await supabase
      .from('contributor_agreements')
      .select('*')
      .eq('project_id', project.id);

    if (agreementsError) {
      console.error('Error fetching agreements:', agreementsError);
      return;
    }

    if (!agreements || agreements.length === 0) {
      console.log('No agreements found for VotA');
      return;
    }

    const agreement = agreements[0];
    console.log(`\nFixing agreement ID: ${agreement.id}`);
    console.log(`Version: ${agreement.version || 1}`);
    console.log(`Status: ${agreement.status || 'Unknown'}`);

    // Create a backup of the original agreement
    const backupFilename = `backup-vota-agreement-${agreement.id}.md`;
    fs.writeFileSync(backupFilename, agreement.agreement_text);
    console.log(`Original agreement backed up to ${backupFilename}`);

    // Apply direct replacements for the VotA agreement
    let fixedText = agreement.agreement_text;

    // Direct replacements for the specific lines we found
    fixedText = fixedText.replace(/- Basic village layout and building system/g, '- Basic game layout and building system');
    fixedText = fixedText.replace(/- Initial AI for villagers/g, '- Initial AI for players');

    // Check if the fixes worked
    const villageReferences = checkForVillageReferences(fixedText);

    if (villageReferences.length > 0) {
      console.log(`\n⚠️ Still found ${villageReferences.length} Village references after fixes:`);
      villageReferences.forEach((ref, index) => {
        console.log(`\n${index + 1}. Line ${ref.lineNumber}: ${ref.line}`);
        console.log(`   Context: ${ref.context}`);
      });

      // Try more aggressive replacements
      console.log('\nTrying more aggressive replacements...');

      // Replace any remaining "village" with "game"
      fixedText = fixedText.replace(/\bvillage\b/gi, 'game');

      // Check again
      const remainingRefs = checkForVillageReferences(fixedText);

      if (remainingRefs.length > 0) {
        console.log(`\n⚠️ Still found ${remainingRefs.length} Village references after aggressive fixes:`);
        remainingRefs.forEach((ref, index) => {
          console.log(`\n${index + 1}. Line ${ref.lineNumber}: ${ref.line}`);
          console.log(`   Context: ${ref.context}`);
        });

        console.log('\nUnable to fix all Village references automatically.');
        return;
      }
    }

    // Update the agreement in the database
    console.log('\nUpdating agreement in the database...');

    const { error: updateError } = await supabase
      .from('contributor_agreements')
      .update({ agreement_text: fixedText })
      .eq('id', agreement.id);

    if (updateError) {
      console.error('Error updating agreement:', updateError);
    } else {
      console.log('✓ Agreement updated successfully!');
    }

    // Verify the update
    const { data: updatedAgreement, error: fetchError } = await supabase
      .from('contributor_agreements')
      .select('*')
      .eq('id', agreement.id)
      .single();

    if (fetchError) {
      console.error('Error fetching updated agreement:', fetchError);
      return;
    }

    const finalCheck = checkForVillageReferences(updatedAgreement.agreement_text);

    if (finalCheck.length > 0) {
      console.log(`\n⚠️ Found ${finalCheck.length} Village references in the updated agreement:`);
      finalCheck.forEach((ref, index) => {
        console.log(`\n${index + 1}. Line ${ref.lineNumber}: ${ref.line}`);
        console.log(`   Context: ${ref.context}`);
      });
    } else {
      console.log('\n✓ No Village references found in the updated agreement!');
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

// Helper function to check for Village references
function checkForVillageReferences(text) {
  if (!text) return [];

  const lines = text.split('\n');
  const references = [];

  const villagePatterns = [
    /village/i,
    /Village of The Ages/i,
    /Village of the Ages/i
    // Removed VOTA pattern as it's the project name
  ];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    for (const pattern of villagePatterns) {
      if (pattern.test(line)) {
        // Get context (1 line before and after)
        const start = Math.max(0, i - 1);
        const end = Math.min(lines.length - 1, i + 1);
        const context = lines.slice(start, end + 1).join('\n');

        references.push({
          lineNumber: i + 1,
          line: line,
          context: context
        });

        // Only add each line once, even if it matches multiple patterns
        break;
      }
    }
  }

  return references;
}

main();
