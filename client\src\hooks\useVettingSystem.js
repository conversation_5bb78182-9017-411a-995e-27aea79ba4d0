import { useState, useEffect, useCallback } from 'react';
import { useSupabase } from './useSupabase';

/**
 * Custom hook for vetting system operations
 * Provides functions for managing vetting applications, reviews, and workflow
 */
export const useVettingSystem = () => {
  const { supabase, user } = useSupabase();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Clear error when user changes
  useEffect(() => {
    setError(null);
  }, [user]);

  /**
   * Submit a new vetting application
   */
  const submitApplication = useCallback(async (applicationData) => {
    if (!user) throw new Error('User not authenticated');

    setLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase
        .from('vetting_applications')
        .insert({
          user_id: user.id,
          ...applicationData,
          status: 'pending',
          submitted_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      // Log the application submission
      await supabase
        .from('vetting_workflow_logs')
        .insert({
          application_id: data.id,
          user_id: user.id,
          action: 'application_submitted',
          to_status: 'pending',
          details: { application_type: applicationData.application_type }
        });

      return data;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [supabase, user]);

  /**
   * Get user's vetting applications
   */
  const getUserApplications = useCallback(async (userId = null) => {
    const targetUserId = userId || user?.id;
    if (!targetUserId) throw new Error('User ID required');

    setLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase
        .from('vetting_applications')
        .select(`
          *,
          reviews:vetting_reviews(
            *,
            reviewer:reviewer_id(full_name, avatar_url)
          )
        `)
        .eq('user_id', targetUserId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [supabase, user]);

  /**
   * Get applications assigned for review
   */
  const getAssignedApplications = useCallback(async (reviewerId = null) => {
    const targetReviewerId = reviewerId || user?.id;
    if (!targetReviewerId) throw new Error('Reviewer ID required');

    setLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase
        .from('reviewer_assignments')
        .select(`
          *,
          application:application_id(
            *,
            user:user_id(full_name, avatar_url, email),
            reviews:vetting_reviews(*)
          )
        `)
        .eq('reviewer_id', targetReviewerId)
        .is('completed_at', null)
        .order('assigned_at', { ascending: true });

      if (error) throw error;
      return data?.map(assignment => assignment.application) || [];
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [supabase, user]);

  /**
   * Submit a review for an application
   */
  const submitReview = useCallback(async (applicationId, reviewData) => {
    if (!user) throw new Error('User not authenticated');

    setLoading(true);
    setError(null);

    try {
      // Create review record
      const { data: review, error: reviewError } = await supabase
        .from('vetting_reviews')
        .insert({
          application_id: applicationId,
          reviewer_id: user.id,
          ...reviewData,
          review_completed_at: new Date().toISOString(),
          is_final_review: true
        })
        .select()
        .single();

      if (reviewError) throw reviewError;

      // Update reviewer assignment
      const { error: assignmentError } = await supabase
        .from('reviewer_assignments')
        .update({ completed_at: new Date().toISOString() })
        .eq('application_id', applicationId)
        .eq('reviewer_id', user.id);

      if (assignmentError) throw assignmentError;

      // Update application status based on decision
      let newStatus = 'under_review';
      if (reviewData.decision === 'approve') {
        newStatus = 'approved';
      } else if (reviewData.decision === 'reject') {
        newStatus = 'rejected';
      }

      const { error: statusError } = await supabase
        .from('vetting_applications')
        .update({ 
          status: newStatus,
          reviewed_at: new Date().toISOString(),
          ...(newStatus === 'approved' && { approved_at: new Date().toISOString() })
        })
        .eq('id', applicationId);

      if (statusError) throw statusError;

      // Log workflow change
      const { error: logError } = await supabase
        .from('vetting_workflow_logs')
        .insert({
          application_id: applicationId,
          user_id: user.id,
          action: 'review_completed',
          to_status: newStatus,
          details: { review_id: review.id, decision: reviewData.decision }
        });

      if (logError) throw logError;

      return review;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [supabase, user]);

  /**
   * Get vetting criteria for application type
   */
  const getVettingCriteria = useCallback(async (applicationType = null) => {
    setLoading(true);
    setError(null);

    try {
      let query = supabase
        .from('vetting_criteria')
        .select('*')
        .eq('is_active', true)
        .order('weight', { ascending: false });

      if (applicationType) {
        query = query.eq('application_type', applicationType);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data || [];
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [supabase]);

  /**
   * Get user's verification badges
   */
  const getUserBadges = useCallback(async (userId = null) => {
    const targetUserId = userId || user?.id;
    if (!targetUserId) throw new Error('User ID required');

    setLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase
        .from('verification_badges')
        .select('*')
        .eq('user_id', targetUserId)
        .eq('is_visible', true)
        .order('display_order', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [supabase, user]);

  /**
   * Award a verification badge to user
   */
  const awardBadge = useCallback(async (userId, badgeData) => {
    if (!user) throw new Error('User not authenticated');

    setLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase
        .from('verification_badges')
        .insert({
          user_id: userId,
          verified_by: user.id,
          verified_at: new Date().toISOString(),
          ...badgeData
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [supabase, user]);

  /**
   * Get skill assessments for user
   */
  const getSkillAssessments = useCallback(async (userId = null) => {
    const targetUserId = userId || user?.id;
    if (!targetUserId) throw new Error('User ID required');

    setLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase
        .from('skill_assessments')
        .select('*')
        .eq('user_id', targetUserId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [supabase, user]);

  /**
   * Submit skill assessment
   */
  const submitSkillAssessment = useCallback(async (assessmentData) => {
    if (!user) throw new Error('User not authenticated');

    setLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase
        .from('skill_assessments')
        .upsert({
          user_id: user.id,
          ...assessmentData
        }, { 
          onConflict: 'user_id,skill_name,assessment_type' 
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [supabase, user]);

  /**
   * Get vetting statistics
   */
  const getVettingStats = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // Get application counts by status
      const { data: statusCounts, error: statusError } = await supabase
        .from('vetting_applications')
        .select('status')
        .then(({ data, error }) => {
          if (error) throw error;
          const counts = {};
          data?.forEach(app => {
            counts[app.status] = (counts[app.status] || 0) + 1;
          });
          return { data: counts, error: null };
        });

      if (statusError) throw statusError;

      // Get average review time
      const { data: avgReviewTime, error: timeError } = await supabase
        .rpc('get_average_review_time');

      if (timeError) throw timeError;

      // Get reviewer workload
      const { data: reviewerWorkload, error: workloadError } = await supabase
        .from('reviewer_assignments')
        .select('reviewer_id')
        .is('completed_at', null)
        .then(({ data, error }) => {
          if (error) throw error;
          const workload = {};
          data?.forEach(assignment => {
            workload[assignment.reviewer_id] = (workload[assignment.reviewer_id] || 0) + 1;
          });
          return { data: workload, error: null };
        });

      if (workloadError) throw workloadError;

      return {
        statusCounts: statusCounts || {},
        averageReviewTime: avgReviewTime || 0,
        reviewerWorkload: reviewerWorkload || {}
      };
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [supabase]);

  /**
   * Check if user is verified
   */
  const isUserVerified = useCallback(async (userId = null) => {
    const targetUserId = userId || user?.id;
    if (!targetUserId) return false;

    try {
      const { data, error } = await supabase
        .from('vetting_applications')
        .select('status')
        .eq('user_id', targetUserId)
        .eq('status', 'approved')
        .limit(1);

      if (error) throw error;
      return data && data.length > 0;
    } catch (err) {
      console.error('Error checking verification status:', err);
      return false;
    }
  }, [supabase, user]);

  return {
    // State
    loading,
    error,

    // Application management
    submitApplication,
    getUserApplications,

    // Review management
    getAssignedApplications,
    submitReview,

    // Criteria and badges
    getVettingCriteria,
    getUserBadges,
    awardBadge,

    // Skill assessments
    getSkillAssessments,
    submitSkillAssessment,

    // Statistics and verification
    getVettingStats,
    isUserVerified
  };
};

export default useVettingSystem;
