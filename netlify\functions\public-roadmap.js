// Public roadmap API for external access
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

exports.handler = async function(event, context) {
  // Set CORS headers for public API
  const headers = {
    'Access-Control-Allow-Origin': '*', // Allow any origin
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, OPTIONS'
  };
  
  // Handle OPTIONS request (preflight)
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 204,
      headers,
      body: ''
    };
  }
  
  // Only allow GET requests
  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ message: "Method not allowed" })
    };
  }
  
  try {
    // Get roadmap data
    const { data, error } = await supabase.rpc('get_roadmap');
    
    if (error) {
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({ message: "Error getting roadmap data", error: error.message })
      };
    }
    
    // Calculate overall progress stats
    const stats = calculateStats(data || []);
    
    // Return roadmap data and stats
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        roadmap: data || [],
        stats
      })
    };
  } catch (error) {
    console.error('Error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ message: "Server error", error: error.message })
    };
  }
};

// Calculate overall progress stats
function calculateStats(phases) {
  let totalTasks = 0;
  let completedTasks = 0;
  let phaseStats = [];

  phases.forEach(phase => {
    let phaseTotalTasks = 0;
    let phaseCompletedTasks = 0;
    
    phase.sections.forEach(section => {
      phaseTotalTasks += section.tasks.length;
      phaseCompletedTasks += section.tasks.filter(task => task.completed).length;
    });
    
    totalTasks += phaseTotalTasks;
    completedTasks += phaseCompletedTasks;
    
    phaseStats.push({
      id: phase.id,
      title: phase.title,
      timeframe: phase.timeframe,
      progress: phaseTotalTasks > 0 ? Math.round((phaseCompletedTasks / phaseTotalTasks) * 100) : 0
    });
  });

  return {
    totalTasks,
    completedTasks,
    progressPercentage: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
    phases: phaseStats
  };
}
