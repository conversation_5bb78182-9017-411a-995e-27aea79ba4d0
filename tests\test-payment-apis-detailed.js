// Test Payment APIs with detailed error analysis
const fetch = require('node-fetch');

const SITE_URL = 'https://royalty.technology';

async function testPaymentAPIsDetailed() {
  const endpoints = [
    '/.netlify/functions/companies',
    '/.netlify/functions/financial-transactions',
    '/.netlify/functions/commission-payments',
    '/.netlify/functions/recurring-fees'
  ];
  
  console.log('🔍 Testing Payment Processing APIs with detailed analysis...\n');
  
  for (const endpoint of endpoints) {
    try {
      console.log(`Testing ${endpoint}...`);
      const response = await fetch(`${SITE_URL}${endpoint}`);
      
      console.log(`Status: ${response.status}`);
      console.log(`Content-Type: ${response.headers.get('content-type')}`);
      
      const text = await response.text();
      
      // Try to parse as JSON
      try {
        const json = JSON.parse(text);
        console.log(`✅ Valid JSON response`);
        
        if (response.status === 401) {
          console.log(`🔐 Authentication required (expected)`);
          console.log(`Error: ${json.error}`);
        } else if (response.status === 500) {
          console.log(`❌ Server error`);
          console.log(`Error: ${json.error}`);
          console.log(`Timestamp: ${json.timestamp}`);
        } else if (response.status === 200) {
          console.log(`✅ Success`);
          console.log(`Data:`, json);
        } else {
          console.log(`⚠️ Unexpected status: ${response.status}`);
          console.log(`Response:`, json);
        }
      } catch (e) {
        console.log(`❌ Not valid JSON`);
        console.log(`Response: ${text}`);
      }
      
      console.log('---\n');
      
    } catch (error) {
      console.log(`❌ Network Error: ${error.message}\n`);
    }
  }
  
  // Test with mock authentication header
  console.log('🔐 Testing with mock authentication...\n');
  
  const testEndpoint = '/.netlify/functions/companies';
  try {
    const response = await fetch(`${SITE_URL}${testEndpoint}`, {
      headers: {
        'Authorization': 'Bearer mock-token-for-testing'
      }
    });
    
    console.log(`Mock auth test - Status: ${response.status}`);
    const text = await response.text();
    
    try {
      const json = JSON.parse(text);
      console.log(`Mock auth response:`, json);
    } catch (e) {
      console.log(`Mock auth response (text):`, text.substring(0, 200));
    }
  } catch (error) {
    console.log(`Mock auth test error: ${error.message}`);
  }
}

testPaymentAPIsDetailed();
