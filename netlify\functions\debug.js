// Debug function to help diagnose issues
exports.handler = async function(event, context) {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
  };
  
  // Handle OPTIONS request (preflight)
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 204,
      headers,
      body: ''
    };
  }
  
  // Collect debug information
  const debugInfo = {
    timestamp: new Date().toISOString(),
    event: {
      httpMethod: event.httpMethod,
      path: event.path,
      headers: event.headers,
      queryStringParameters: event.queryStringParameters,
      body: event.body ? '(body exists)' : '(no body)',
      isBase64Encoded: event.isBase64Encoded
    },
    environment: {
      NODE_VERSION: process.version,
      SUPABASE_URL_EXISTS: !!process.env.SUPABASE_URL,
      SUPABASE_SERVICE_KEY_EXISTS: !!process.env.SUPABASE_SERVICE_KEY,
      SITE_URL_EXISTS: !!process.env.SITE_URL,
      NETLIFY_ENV: process.env.NETLIFY_ENV || 'not set'
    },
    context: {
      functionName: context.functionName,
      functionVersion: context.functionVersion,
      awsRequestId: context.awsRequestId,
      memoryLimitInMB: context.memoryLimitInMB
    }
  };
  
  // Return debug information
  return {
    statusCode: 200,
    headers,
    body: JSON.stringify(debugInfo, null, 2)
  };
};
