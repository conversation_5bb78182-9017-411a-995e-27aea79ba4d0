import React, { useState, useContext } from 'react';
import { motion } from 'framer-motion';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button, Input } from '@heroui/react';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { toast } from 'react-hot-toast';

/**
 * ImmersiveLogin Component
 * 
 * Full-screen immersive login experience following wireframe specifications
 * Minimal UI with large touch targets and smooth animations
 * Connects to onboarding flow for new users
 */
const ImmersiveLogin = ({ 
  onSwitchToSignup, 
  onSwitchToReset, 
  onCancel,
  redirectTo = '/dashboard' 
}) => {
  const { login, loginWithGoogle, loginWithGithub, isLoading } = useContext(UserContext);
  const navigate = useNavigate();
  const location = useLocation();
  
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Handle form input changes
  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) setError(''); // Clear error when user starts typing
  };

  // Handle email/password login
  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      await login(formData.email, formData.password);
      
      // Redirect to intended destination
      const from = location.state?.from || redirectTo;
      navigate(from, { replace: true });
      
      toast.success('Welcome back!');
    } catch (err) {
      console.error('Login error:', err);
      setError(err.message || 'Failed to sign in. Please check your credentials.');
    } finally {
      setLoading(false);
    }
  };

  // Handle social authentication
  const handleSocialAuth = async (provider) => {
    setError('');
    setLoading(true);
    
    try {
      if (provider === 'google') {
        await loginWithGoogle();
      } else if (provider === 'github') {
        await loginWithGithub();
      }
      
      // Redirect will be handled by auth state change
      toast.success(`Welcome! Signed in with ${provider}.`);
    } catch (err) {
      console.error('Social auth error:', err);
      setError(err.message || `Failed to sign in with ${provider}`);
    } finally {
      setLoading(false);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  return (
    <motion.div
      className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-primary-50 to-secondary-50"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Exit button */}
      {onCancel && (
        <motion.div
          className="absolute top-6 right-6 z-10"
          variants={itemVariants}
        >
          <Button
            variant="light"
            size="lg"
            onPress={onCancel}
            isIconOnly
            className="text-foreground hover:bg-default-100"
          >
            <i className="bi bi-x-lg text-2xl"></i>
          </Button>
        </motion.div>
      )}

      <div className="max-w-md mx-auto w-full">
        {/* Welcome Title */}
        <motion.div variants={itemVariants} className="text-center mb-12">
          <h1 className="text-5xl font-bold text-foreground mb-4">
            Welcome Back
          </h1>
          <p className="text-lg text-default-600">
            Sign in to continue your journey
          </p>
        </motion.div>

        {/* Login Form */}
        <motion.div variants={itemVariants}>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Email Input */}
            <div>
              <Input
                type="email"
                label="Email Address"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                size="lg"
                variant="bordered"
                isRequired
                isDisabled={loading || isLoading}
                classNames={{
                  input: "text-lg",
                  inputWrapper: "h-14"
                }}
                startContent={
                  <i className="bi bi-envelope text-default-400"></i>
                }
              />
            </div>

            {/* Password Input */}
            <div>
              <Input
                type={showPassword ? "text" : "password"}
                label="Password"
                placeholder="Enter your password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                size="lg"
                variant="bordered"
                isRequired
                isDisabled={loading || isLoading}
                classNames={{
                  input: "text-lg",
                  inputWrapper: "h-14"
                }}
                startContent={
                  <i className="bi bi-lock text-default-400"></i>
                }
                endContent={
                  <Button
                    variant="light"
                    size="sm"
                    isIconOnly
                    onPress={() => setShowPassword(!showPassword)}
                    className="text-default-400 hover:text-default-600"
                  >
                    <i className={`bi ${showPassword ? 'bi-eye-slash' : 'bi-eye'}`}></i>
                  </Button>
                }
              />
            </div>

            {/* Error Message */}
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-danger-50 border border-danger-200 rounded-lg p-3"
              >
                <p className="text-danger text-sm">{error}</p>
              </motion.div>
            )}

            {/* Sign In Button */}
            <Button
              type="submit"
              size="lg"
              className="w-full bg-primary text-white font-semibold py-4 text-lg"
              isLoading={loading || isLoading}
              isDisabled={!formData.email || !formData.password}
            >
              {loading || isLoading ? 'Signing In...' : 'Sign In'}
            </Button>
          </form>
        </motion.div>

        {/* Social Login */}
        <motion.div variants={itemVariants} className="mt-8">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-default-200"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-4 bg-background text-default-500">Or continue with</span>
            </div>
          </div>

          <div className="mt-6 grid grid-cols-2 gap-3">
            <Button
              variant="bordered"
              size="lg"
              onPress={() => handleSocialAuth('google')}
              isDisabled={loading || isLoading}
              className="h-12"
            >
              <i className="bi bi-google text-lg mr-2"></i>
              Google
            </Button>
            <Button
              variant="bordered"
              size="lg"
              onPress={() => handleSocialAuth('github')}
              isDisabled={loading || isLoading}
              className="h-12"
            >
              <i className="bi bi-github text-lg mr-2"></i>
              GitHub
            </Button>
          </div>
        </motion.div>

        {/* Footer Links */}
        <motion.div variants={itemVariants} className="mt-8 text-center space-y-4">
          {/* Forgot Password */}
          {onSwitchToReset && (
            <div>
              <Button
                variant="light"
                onPress={onSwitchToReset}
                className="text-primary hover:text-primary-600"
              >
                Forgot your password?
              </Button>
            </div>
          )}

          {/* Switch to Signup */}
          {onSwitchToSignup && (
            <div className="flex items-center justify-center space-x-2">
              <span className="text-default-600">Don't have an account?</span>
              <Button
                variant="light"
                onPress={onSwitchToSignup}
                className="text-primary hover:text-primary-600 font-semibold"
              >
                Join →
              </Button>
            </div>
          )}
        </motion.div>
      </div>
    </motion.div>
  );
};

export default ImmersiveLogin;
