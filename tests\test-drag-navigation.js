/**
 * Test Script for Drag Navigation Functionality
 * 
 * This script tests the experimental navigation system's drag functionality
 * to ensure there are no dead zones and that dragging is responsive.
 */

const { chromium } = require('playwright');

async function testDragNavigation() {
  console.log('🧪 Starting Drag Navigation Tests...\n');

  const browser = await chromium.launch({ 
    headless: false, // Show browser for visual verification
    slowMo: 100 // Slow down for better observation
  });
  
  const context = await browser.newContext({
    viewport: { width: 1920, height: 1080 }
  });
  
  const page = await context.newPage();

  try {
    // Navigate to the application with test mode
    console.log('📍 Navigating to application...');
    await page.goto('http://localhost:3000?test_mode=true&skip_onboarding=true');
    
    // Wait for the experimental navigation to load
    await page.waitForSelector('[data-testid="experimental-navigation"]', { timeout: 10000 });
    console.log('✅ Experimental navigation loaded');

    // Test 1: Switch to overworld view
    console.log('\n🔍 Test 1: Switching to overworld view...');
    await page.keyboard.press('Escape'); // Go to navigation
    await page.waitForTimeout(500);
    await page.keyboard.press('ArrowUp'); // Go to overworld
    await page.waitForTimeout(1000);
    console.log('✅ Switched to overworld view');

    // Test 2: Test drag from center of screen
    console.log('\n🖱️ Test 2: Testing drag from center of screen...');
    const viewport = page.viewportSize();
    const centerX = viewport.width / 2;
    const centerY = viewport.height / 2;
    
    await testDragFromPoint(page, centerX, centerY, 'center');

    // Test 3: Test drag from corners (potential dead zones)
    console.log('\n🔍 Test 3: Testing drag from corners...');
    const corners = [
      { x: 50, y: 50, name: 'top-left' },
      { x: viewport.width - 50, y: 50, name: 'top-right' },
      { x: 50, y: viewport.height - 50, name: 'bottom-left' },
      { x: viewport.width - 50, y: viewport.height - 50, name: 'bottom-right' }
    ];

    for (const corner of corners) {
      await testDragFromPoint(page, corner.x, corner.y, corner.name);
    }

    // Test 4: Test drag from edges
    console.log('\n🔍 Test 4: Testing drag from edges...');
    const edges = [
      { x: centerX, y: 10, name: 'top-edge' },
      { x: centerX, y: viewport.height - 10, name: 'bottom-edge' },
      { x: 10, y: centerY, name: 'left-edge' },
      { x: viewport.width - 10, y: centerY, name: 'right-edge' }
    ];

    for (const edge of edges) {
      await testDragFromPoint(page, edge.x, edge.y, edge.name);
    }

    // Test 5: Test rapid drag movements
    console.log('\n⚡ Test 5: Testing rapid drag movements...');
    await testRapidDrag(page, centerX, centerY);

    // Test 6: Test drag responsiveness
    console.log('\n📏 Test 6: Testing drag responsiveness...');
    await testDragResponsiveness(page, centerX, centerY);

    // Test 7: Test that canvas cards don't interfere with dragging
    console.log('\n🎯 Test 7: Testing canvas card interaction...');
    await testCanvasCardInteraction(page);

    console.log('\n✅ All drag navigation tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    throw error;
  } finally {
    await browser.close();
  }
}

async function testDragFromPoint(page, startX, startY, location) {
  console.log(`  Testing drag from ${location} (${startX}, ${startY})...`);
  
  // Get initial view position
  const initialPosition = await page.evaluate(() => {
    const debugInfo = document.querySelector('[data-testid="debug-info"]');
    if (debugInfo) {
      const viewPosText = debugInfo.textContent.match(/View Position: \(([^,]+), ([^)]+)\)/);
      if (viewPosText) {
        return { x: parseFloat(viewPosText[1]), y: parseFloat(viewPosText[2]) };
      }
    }
    return { x: 0, y: 0 };
  });

  // Perform drag
  const dragDistance = 100;
  await page.mouse.move(startX, startY);
  await page.mouse.down();
  await page.waitForTimeout(50); // Brief pause to ensure drag starts
  await page.mouse.move(startX + dragDistance, startY + dragDistance, { steps: 10 });
  await page.waitForTimeout(100);
  await page.mouse.up();
  await page.waitForTimeout(200); // Wait for position to update

  // Check if position changed
  const finalPosition = await page.evaluate(() => {
    const debugInfo = document.querySelector('[data-testid="debug-info"]');
    if (debugInfo) {
      const viewPosText = debugInfo.textContent.match(/View Position: \(([^,]+), ([^)]+)\)/);
      if (viewPosText) {
        return { x: parseFloat(viewPosText[1]), y: parseFloat(viewPosText[2]) };
      }
    }
    return { x: 0, y: 0 };
  });

  const deltaX = Math.abs(finalPosition.x - initialPosition.x);
  const deltaY = Math.abs(finalPosition.y - initialPosition.y);
  const moved = deltaX > 5 || deltaY > 5; // Allow for small rounding errors

  if (moved) {
    console.log(`    ✅ Drag from ${location} successful (moved ${deltaX.toFixed(1)}, ${deltaY.toFixed(1)})`);
  } else {
    console.log(`    ❌ Drag from ${location} failed - no movement detected`);
    throw new Error(`Dead zone detected at ${location}`);
  }
}

async function testRapidDrag(page, centerX, centerY) {
  console.log('  Testing rapid drag movements...');
  
  // Perform multiple rapid drags in different directions
  const directions = [
    { dx: 50, dy: 0, name: 'right' },
    { dx: 0, dy: 50, name: 'down' },
    { dx: -50, dy: 0, name: 'left' },
    { dx: 0, dy: -50, name: 'up' }
  ];

  for (const dir of directions) {
    await page.mouse.move(centerX, centerY);
    await page.mouse.down();
    await page.mouse.move(centerX + dir.dx, centerY + dir.dy, { steps: 3 });
    await page.mouse.up();
    await page.waitForTimeout(50); // Brief pause between drags
  }

  console.log('    ✅ Rapid drag movements completed');
}

async function testDragResponsiveness(page, centerX, centerY) {
  console.log('  Testing drag responsiveness...');
  
  // Test smooth drag with many steps
  await page.mouse.move(centerX, centerY);
  await page.mouse.down();
  
  // Move in a circle to test smooth following
  const radius = 80;
  const steps = 20;
  for (let i = 0; i <= steps; i++) {
    const angle = (i / steps) * 2 * Math.PI;
    const x = centerX + Math.cos(angle) * radius;
    const y = centerY + Math.sin(angle) * radius;
    await page.mouse.move(x, y);
    await page.waitForTimeout(20); // Small delay to test responsiveness
  }
  
  await page.mouse.up();
  console.log('    ✅ Drag responsiveness test completed');
}

async function testCanvasCardInteraction(page) {
  console.log('  Testing canvas card interaction...');
  
  // Try to find a canvas card
  const canvasCard = await page.locator('[data-canvas-card]').first();
  
  if (await canvasCard.count() > 0) {
    const cardBox = await canvasCard.boundingBox();
    if (cardBox) {
      // Click on card (should navigate, not drag)
      await page.mouse.click(cardBox.x + cardBox.width / 2, cardBox.y + cardBox.height / 2);
      await page.waitForTimeout(500);
      
      // Check if we navigated to content
      const isInContent = await page.evaluate(() => {
        return document.body.textContent.includes('content') || 
               document.querySelector('[data-testid="content-view"]') !== null;
      });
      
      if (isInContent) {
        console.log('    ✅ Canvas card click navigation works');
        // Go back to navigation
        await page.keyboard.press('Escape');
        await page.waitForTimeout(500);
      } else {
        console.log('    ⚠️ Canvas card click may not have navigated (this might be expected)');
      }
    }
  } else {
    console.log('    ⚠️ No canvas cards found for interaction test');
  }
}

// Run the tests
if (require.main === module) {
  testDragNavigation()
    .then(() => {
      console.log('\n🎉 All tests passed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Tests failed:', error);
      process.exit(1);
    });
}

module.exports = { testDragNavigation };
