// Simple Alliance Migration Application
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_KEY);

async function applyAllianceMigration() {
  console.log('🚀 Applying Alliance System Migration (Simple)...');
  
  try {
    // Step 1: Add alliance columns to teams table
    console.log('\n1️⃣ Adding alliance columns to teams table...');
    
    const allianceColumns = [
      "ALTER TABLE public.teams ADD COLUMN IF NOT EXISTS industry VARCHAR(100);",
      "ALTER TABLE public.teams ADD COLUMN IF NOT EXISTS business_model JSONB DEFAULT '{}'::jsonb;",
      "ALTER TABLE public.teams ADD COLUMN IF NOT EXISTS max_members INTEGER DEFAULT 10;",
      "ALTER TABLE public.teams ADD COLUMN IF NOT EXISTS is_public BOOLEAN DEFAULT true;",
      "ALTER TABLE public.teams ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active';"
    ];
    
    for (const sql of allianceColumns) {
      try {
        await supabase.rpc('exec', { sql });
        console.log('   ✅ Added column');
      } catch (err) {
        console.log('   ⚠️ Column may already exist');
      }
    }
    
    // Step 2: Enhance team_members table
    console.log('\n2️⃣ Enhancing team_members table...');
    
    const memberColumns = [
      "ALTER TABLE public.team_members ADD COLUMN IF NOT EXISTS role TEXT DEFAULT 'member';",
      "ALTER TABLE public.team_members ADD COLUMN IF NOT EXISTS permissions JSONB DEFAULT '{}'::jsonb;",
      "ALTER TABLE public.team_members ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active';",
      "ALTER TABLE public.team_members ADD COLUMN IF NOT EXISTS joined_at TIMESTAMP WITH TIME ZONE DEFAULT now();",
      "ALTER TABLE public.team_members ADD COLUMN IF NOT EXISTS is_admin BOOLEAN DEFAULT false;"
    ];
    
    for (const sql of memberColumns) {
      try {
        await supabase.rpc('exec', { sql });
        console.log('   ✅ Added column');
      } catch (err) {
        console.log('   ⚠️ Column may already exist');
      }
    }
    
    // Step 3: Create alliance_invitations table
    console.log('\n3️⃣ Creating alliance_invitations table...');
    
    const createInvitationsTable = `
      CREATE TABLE IF NOT EXISTS public.alliance_invitations (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        alliance_id UUID NOT NULL REFERENCES public.teams(id) ON DELETE CASCADE,
        email VARCHAR(255) NOT NULL,
        role TEXT DEFAULT 'member',
        invited_by UUID NOT NULL REFERENCES auth.users(id),
        message TEXT,
        status TEXT DEFAULT 'pending',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
        expires_at TIMESTAMP WITH TIME ZONE DEFAULT (now() + INTERVAL '7 days')
      );
    `;
    
    try {
      await supabase.rpc('exec', { sql: createInvitationsTable });
      console.log('   ✅ alliance_invitations table created');
    } catch (err) {
      console.log('   ⚠️ Table may already exist:', err.message);
    }
    
    // Step 4: Add venture columns to projects table
    console.log('\n4️⃣ Adding venture columns to projects table...');
    
    const ventureColumns = [
      "ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS venture_type TEXT DEFAULT 'software';",
      "ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS revenue_model JSONB DEFAULT '{}'::jsonb;",
      "ALTER TABLE public.projects ADD COLUMN IF NOT EXISTS alliance_id UUID REFERENCES public.teams(id);"
    ];
    
    for (const sql of ventureColumns) {
      try {
        await supabase.rpc('exec', { sql });
        console.log('   ✅ Added column');
      } catch (err) {
        console.log('   ⚠️ Column may already exist');
      }
    }
    
    // Step 5: Add mission/bounty columns to tasks table
    console.log('\n5️⃣ Adding mission/bounty columns to tasks table...');
    
    const missionColumns = [
      "ALTER TABLE public.tasks ADD COLUMN IF NOT EXISTS task_category TEXT DEFAULT 'mission';",
      "ALTER TABLE public.tasks ADD COLUMN IF NOT EXISTS bounty_amount DECIMAL(10,2) DEFAULT 0;",
      "ALTER TABLE public.tasks ADD COLUMN IF NOT EXISTS is_public BOOLEAN DEFAULT false;",
      "ALTER TABLE public.tasks ADD COLUMN IF NOT EXISTS priority TEXT DEFAULT 'medium';",
      "ALTER TABLE public.tasks ADD COLUMN IF NOT EXISTS deadline TIMESTAMP WITH TIME ZONE;"
    ];
    
    for (const sql of missionColumns) {
      try {
        await supabase.rpc('exec', { sql });
        console.log('   ✅ Added column');
      } catch (err) {
        console.log('   ⚠️ Column may already exist');
      }
    }
    
    console.log('\n🎯 Alliance System Migration Completed!');
    
    // Verify the migration
    await verifyMigration();
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
  }
}

async function verifyMigration() {
  console.log('\n🔍 Verifying migration...');
  
  try {
    // Test teams table
    const { data: testTeam, error: teamError } = await supabase
      .from('teams')
      .insert([{
        name: 'Test Alliance Verification',
        description: 'Testing alliance system',
        alliance_type: 'emerging',
        industry: 'software',
        business_model: { type: 'revenue_share', rate: 0.1 },
        created_by: '2a033231-d173-4292-aa36-90f4d735bcf3'
      }])
      .select()
      .single();
    
    if (teamError) {
      console.log('❌ Teams table test failed:', teamError.message);
    } else {
      console.log('✅ Teams table working with alliance columns');
      
      // Clean up test data
      await supabase.from('teams').delete().eq('id', testTeam.id);
    }
    
    // Test alliance_invitations table
    const { data: invitations, error: invError } = await supabase
      .from('alliance_invitations')
      .select('*')
      .limit(0);
    
    if (invError) {
      console.log('❌ Alliance invitations table not accessible:', invError.message);
    } else {
      console.log('✅ Alliance invitations table accessible');
    }
    
    console.log('\n🎉 Migration verification completed!');
    
  } catch (verifyError) {
    console.log('❌ Verification failed:', verifyError.message);
  }
}

applyAllianceMigration();
