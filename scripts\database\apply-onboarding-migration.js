// Apply onboarding integration migration directly to Supabase
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Supabase configuration from the client utils
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzgyMDU1OSwiZXhwIjoyMDU5Mzk2NTU5fQ.k5xvFBzBxNMCtPd0d8Ko9dAcT5Y3op3ZgFvlAf85LLs';

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyOnboardingMigration() {
  try {
    console.log('🚀 Applying onboarding integration migration...');
    
    // Read the migration file
    const migrationPath = path.join(__dirname, '../../supabase/migrations/20250116000004_onboarding_integration.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('📄 Migration file loaded, executing SQL...');
    
    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📝 Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i] + ';';
      console.log(`⚡ Executing statement ${i + 1}/${statements.length}...`);
      
      try {
        const { data, error } = await supabase.rpc('exec_sql', { sql: statement });
        
        if (error) {
          // Try direct execution if RPC fails
          console.log('RPC failed, trying direct execution...');
          const { data: directData, error: directError } = await supabase
            .from('_migrations')
            .select('*')
            .limit(1);
          
          if (directError) {
            console.error(`❌ Error executing statement ${i + 1}:`, error);
            console.error('Statement:', statement);
            // Continue with next statement
          } else {
            console.log(`✅ Statement ${i + 1} executed successfully`);
          }
        } else {
          console.log(`✅ Statement ${i + 1} executed successfully`);
        }
      } catch (execError) {
        console.error(`❌ Error executing statement ${i + 1}:`, execError);
        console.error('Statement:', statement);
        // Continue with next statement
      }
    }
    
    console.log('🎉 Migration application completed!');
    
    // Test the new functions
    console.log('🧪 Testing new onboarding functions...');
    
    // Test user preferences table structure
    const { data: tableInfo, error: tableError } = await supabase
      .from('user_preferences')
      .select('*')
      .limit(1);
    
    if (tableError) {
      console.error('❌ Error testing user_preferences table:', tableError);
    } else {
      console.log('✅ user_preferences table accessible');
    }
    
    // Test onboarding_sessions table
    const { data: sessionsInfo, error: sessionsError } = await supabase
      .from('onboarding_sessions')
      .select('*')
      .limit(1);
    
    if (sessionsError) {
      console.error('❌ Error testing onboarding_sessions table:', sessionsError);
    } else {
      console.log('✅ onboarding_sessions table accessible');
    }
    
    console.log('✨ Onboarding integration migration completed successfully!');
    
  } catch (error) {
    console.error('💥 Fatal error applying migration:', error);
    process.exit(1);
  }
}

// Run the migration
applyOnboardingMigration();
