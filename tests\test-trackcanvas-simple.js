// Simple TrackCanvas Test
const { chromium } = require('playwright');

const SITE_URL = 'https://royalty.technology';

async function testTrackCanvas() {
  console.log('🎯 Testing TrackCanvas specifically...\n');

  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Authenticate first
    console.log('🔐 Authenticating...');
    await page.goto(SITE_URL);
    await page.waitForLoadState('networkidle');

    const emailInput = page.locator('input[type="email"]').first();
    const needsAuth = await emailInput.isVisible();

    if (needsAuth) {
      await emailInput.fill('<EMAIL>');
      await page.fill('input[type="password"]', 'TestPassword123!');
      await page.click('button[type="submit"]');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);
    }

    console.log('✅ Authentication completed');

    // Test multiple routes to see if this is a general issue
    const routesToTest = [
      '/teams',
      '/contributions',
      '/projects'
    ];

    for (const route of routesToTest) {
      console.log(`\n📍 Testing ${route} route...`);
      await page.goto(`${SITE_URL}${route}`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);

      const routeContent = await page.textContent('body');
      console.log(`  Content length: ${routeContent.length}`);
      console.log(`  Is empty: ${routeContent.trim().length < 50}`);

      if (routeContent.length > 100) {
        console.log(`  ✅ ${route} renders properly`);
        break; // Found a working route, focus on contributions
      } else {
        console.log(`  ❌ ${route} appears empty`);
      }
    }

    // Focus back on contributions route for detailed analysis
    console.log('\n📍 Detailed analysis of /contributions route...');
    await page.goto(`${SITE_URL}/contributions`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000); // Give extra time for React components

    const content = await page.textContent('body');
    console.log(`Content length: ${content.length}`);
    console.log(`Content preview: "${content.substring(0, 500)}"`);

    // Check for TrackCanvas specific content
    const hasTrackContent = content.includes('Track Your Work') ||
                           content.includes('Time Tracker') ||
                           content.includes('Submit Work') ||
                           content.includes('⏱️');

    console.log(`Has track content: ${hasTrackContent}`);

    // Check for specific TrackCanvas elements
    const trackElements = [
      'Track Your Work',
      'Time Tracker',
      'Submit Work',
      'Progress',
      'Analytics',
      'Start Tracking',
      'contribution',
      'tracking'
    ];

    console.log('\n🔍 Checking for TrackCanvas elements:');
    for (const element of trackElements) {
      const found = content.includes(element);
      console.log(`  "${element}": ${found ? '✅' : '❌'}`);
    }

    // Check for any error messages in the page
    const hasError = content.includes('Error') ||
                    content.includes('error') ||
                    content.includes('Failed') ||
                    content.includes('failed');

    console.log(`\nHas error messages: ${hasError}`);

    // Check for loading states and skeleton content
    const hasLoading = content.includes('Loading') ||
                      content.includes('loading');

    const hasSkeleton = content.includes('skeleton') ||
                       content.includes('animate') ||
                       content.includes('opacity');

    console.log(`Has loading indicators: ${hasLoading}`);
    console.log(`Has skeleton content: ${hasSkeleton}`);

    // Check if the page is completely empty (which suggests a rendering failure)
    const isEmpty = content.trim().length < 50;
    console.log(`Page appears empty: ${isEmpty}`);

    // Check console for JavaScript errors
    const logs = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        logs.push(`ERROR: ${msg.text()}`);
      }
    });

    await page.waitForTimeout(2000);

    if (logs.length > 0) {
      console.log('\n❌ JavaScript errors found:');
      logs.forEach(log => console.log(`  ${log}`));
    } else {
      console.log('\n✅ No JavaScript errors detected');
    }

    // Check if the page is actually showing the experimental navigation
    const hasExperimentalNav = content.includes('experimental') ||
                              content.includes('canvas') ||
                              content.includes('navigation');

    console.log(`Has experimental navigation: ${hasExperimentalNav}`);

    // Check for any React components or HeroUI elements
    const hasReactComponents = content.includes('Card') ||
                              content.includes('Button') ||
                              content.includes('Tab') ||
                              content.includes('heroui');

    console.log(`Has React/HeroUI components: ${hasReactComponents}`);

    // Check if we can find any data-* attributes (common in React apps)
    const hasDataAttributes = content.includes('data-') ||
                              content.includes('class=') ||
                              content.includes('className=');

    console.log(`Has data attributes: ${hasDataAttributes}`);

    // Check current URL
    const currentUrl = page.url();
    console.log(`\nCurrent URL: ${currentUrl}`);

  } catch (error) {
    console.error('❌ Error during testing:', error.message);
  } finally {
    await browser.close();
  }
}

testTrackCanvas();
