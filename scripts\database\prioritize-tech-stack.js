// Script to prioritize tech stack updates in the roadmap
require('dotenv').config({ path: './client/.env.local' });
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseKey) {
  console.error('Error: SUPABASE_SERVICE_KEY environment variable is not set');
  process.exit(1);
}

console.log('Using Supabase URL:', supabaseUrl);

const supabase = createClient(supabaseUrl, supabaseKey);

// Helper function to find a phase by ID
function findPhase(roadmapData, phaseId) {
  return roadmapData.find(phase => phase.id === phaseId);
}

// Helper function to find a section by ID within a phase
function findSection(roadmapData, phaseId, sectionId) {
  const phase = findPhase(roadmapData, phaseId);
  if (!phase || !phase.sections) return null;
  return phase.sections.find(section => section.id === sectionId);
}

// Function to update the roadmap
async function updateRoadmap(roadmapId, roadmapData) {
  try {
    console.log(`\n=== Updating roadmap with ID: ${roadmapId} ===`);

    const { data: updatedData, error: updateError } = await supabase
      .from('roadmap')
      .update({
        data: roadmapData,
        last_updated: new Date().toISOString()
      })
      .eq('id', roadmapId)
      .select();

    if (updateError) {
      console.error('Error updating roadmap:', updateError);
      return false;
    }

    console.log('Updated roadmap successfully');
    return true;
  } catch (error) {
    console.error('Error in updateRoadmap:', error);
    return false;
  }
}

// Function to update the latest feature in the roadmap metadata
function updateLatestFeature(roadmapData) {
  // Find the metadata phase (usually has type: 'metadata')
  const metadataPhase = roadmapData.find(phase => phase.type === 'metadata');
  
  if (!metadataPhase) {
    // If no metadata phase exists, check if the first phase has a stats property
    if (roadmapData[0] && roadmapData[0].stats) {
      if (!roadmapData[0].stats.latest_update) {
        roadmapData[0].stats.latest_update = {};
      }
      
      roadmapData[0].stats.latest_update = {
        date: new Date().toISOString(),
        title: "Tech Stack Modernization Initiative",
        author: "Development Team",
        version: "1.0.0",
        description: "Prioritized the tech stack modernization initiative to improve performance, maintainability, and developer experience."
      };
      
      console.log('Updated latest feature in first phase stats');
      return true;
    }
    
    console.log('No metadata phase or stats found in roadmap');
    return false;
  }
  
  // Update the metadata
  if (!metadataPhase.stats) {
    metadataPhase.stats = {};
  }
  
  if (!metadataPhase.stats.latest_update) {
    metadataPhase.stats.latest_update = {};
  }
  
  metadataPhase.stats.latest_update = {
    date: new Date().toISOString(),
    title: "Tech Stack Modernization Initiative",
    author: "Development Team",
    version: "1.0.0",
    description: "Prioritized the tech stack modernization initiative to improve performance, maintainability, and developer experience."
  };
  
  console.log('Updated latest feature in metadata phase');
  return true;
}

// Main function to prioritize tech stack updates
async function prioritizeTechStackUpdates() {
  try {
    console.log('=== Fetching current roadmap data ===');
    
    // Get the latest roadmap entry
    const { data: roadmapData, error: roadmapError } = await supabase
      .from('roadmap')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (roadmapError) {
      console.error('Error fetching roadmap from Supabase:', roadmapError);
      return;
    }
    
    if (!roadmapData || roadmapData.length === 0) {
      console.log('No roadmap data found in the database');
      return;
    }
    
    const roadmap = roadmapData[0];
    const phases = roadmap.data;
    
    console.log(`Found roadmap with ID: ${roadmap.id}`);
    
    // Find the tech stack section
    let techPhase = findPhase(phases, 5);
    let techSection = null;
    
    if (techPhase && techPhase.sections) {
      techSection = techPhase.sections.find(s => s.title.includes("Tech Stack Evaluation"));
    }
    
    if (!techSection) {
      console.log('Tech Stack Evaluation section not found');
      return;
    }
    
    console.log(`Found Tech Stack section: ${techSection.id}`);
    
    // Find or create Phase 2 (or use the earliest available phase after Phase 1)
    let targetPhase = findPhase(phases, 2);
    
    if (!targetPhase) {
      // If Phase 2 doesn't exist, create it
      targetPhase = {
        id: 2,
        title: "Tech Stack Modernization",
        timeframe: "In Progress - Priority",
        expanded: true,
        sections: []
      };
      
      // Insert after Phase 1
      const insertIndex = phases.findIndex(p => p.id === 1) + 1;
      if (insertIndex > 0) {
        phases.splice(insertIndex, 0, targetPhase);
      } else {
        phases.push(targetPhase);
      }
      
      console.log('Created new Phase 2 for Tech Stack Modernization');
    } else {
      // Update the existing Phase 2 title and timeframe
      targetPhase.title = "Tech Stack Modernization";
      targetPhase.timeframe = "In Progress - Priority";
      console.log('Updated existing Phase 2 for Tech Stack Modernization');
    }
    
    // Create a new section ID for the tech stack section in Phase 2
    const newSectionId = "2.1";
    
    // Create a copy of the tech stack section with the new ID
    const newSection = {
      ...techSection,
      id: newSectionId
    };
    
    // Update task IDs to match the new section ID
    if (newSection.tasks) {
      newSection.tasks.forEach((task, index) => {
        task.id = `${newSectionId}.${index + 1}`;
      });
    }
    
    // Add the section to Phase 2
    targetPhase.sections.push(newSection);
    
    // Remove the original tech stack section from Phase 5
    if (techPhase && techSection) {
      techPhase.sections = techPhase.sections.filter(s => s.id !== techSection.id);
    }
    
    // Add implementation section to Phase 2
    const implementationSection = {
      id: "2.2",
      title: "Implementation & Migration",
      tasks: [
        { id: "2.2.1", text: "Set up new development environment with updated tech stack", completed: false },
        { id: "2.2.2", text: "Implement core functionality in new tech stack", completed: false },
        { id: "2.2.3", text: "Migrate database to improved structure", completed: false },
        { id: "2.2.4", text: "Develop automated tests for new implementation", completed: false },
        { id: "2.2.5", text: "Create CI/CD pipeline for new tech stack", completed: false }
      ]
    };
    
    targetPhase.sections.push(implementationSection);
    
    // Update the latest feature metadata
    updateLatestFeature(phases);
    
    // Update the roadmap
    const success = await updateRoadmap(roadmap.id, phases);
    if (success) {
      console.log('\n=== Roadmap updated successfully with prioritized tech stack updates ===');
    } else {
      console.error('\n=== Failed to update roadmap ===');
    }
    
  } catch (error) {
    console.error('Error prioritizing tech stack updates:', error);
  }
}

// Run the function
prioritizeTechStackUpdates();
