import React, { useState, useEffect } from 'react';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import {
  calculateRoyaltyDistribution,
  formatCurrency,
  formatPercentage,
  applyManualAdjustments
} from '../../utils/royalty/royalty-calculator';

const RoyaltyDistributionPreview = ({
  projectId,
  revenueId,
  revenueAmount,
  revenueCurrency = 'USD',
  onDistributionSave
}) => {
  const [loading, setLoading] = useState(true);
  const [contributors, setContributors] = useState([]);
  const [contributions, setContributions] = useState([]);
  const [project, setProject] = useState(null);
  const [distribution, setDistribution] = useState([]);
  const [calculationModel, setCalculationModel] = useState('equal_split');
  const [manualAdjustments, setManualAdjustments] = useState({});
  const [showDetails, setShowDetails] = useState(false);
  const [cogWeights, setCogWeights] = useState({ tasks: 1, time: 1, difficulty: 1 });
  const [saving, setSaving] = useState(false);

  // Fetch project data, contributors, and contributions
  useEffect(() => {
    const fetchData = async () => {
      if (!projectId) return;

      try {
        setLoading(true);

        // Fetch project data
        const { data: projectData, error: projectError } = await supabase
          .from('projects')
          .select('*')
          .eq('id', projectId)
          .single();

        if (projectError) throw projectError;

        setProject(projectData);

        // Fetch royalty model data separately
        const { data: royaltyModelData, error: royaltyModelError } = await supabase
          .from('royalty_models')
          .select('*')
          .eq('project_id', projectId)
          .single();

        if (royaltyModelError && royaltyModelError.code !== 'PGRST116') {
          console.error('Error fetching royalty model:', royaltyModelError);
        }

        // Add royalty model to project data and set calculation model
        if (royaltyModelData) {
          projectData.royalty_model = royaltyModelData;

          // Set initial calculation model based on project settings
          if (royaltyModelData.model_type) {
            setCalculationModel(royaltyModelData.model_type);

            // Set CoG weights if available
            if (royaltyModelData.model_type === 'cog_model' && royaltyModelData.configuration) {
              const { tasks_weight, hours_weight, difficulty_weight } = royaltyModelData.configuration;
              setCogWeights({
                tasks: tasks_weight || 1,
                time: hours_weight || 1,
                difficulty: difficulty_weight || 1
              });
            }
          }
        }

        // Fetch contributors
        const { data: contributorsData, error: contributorsError } = await supabase
          .from('project_contributors')
          .select('*')
          .eq('project_id', projectId)
          .eq('status', 'active');

        if (contributorsError) throw contributorsError;

        // Fetch user data for all contributors
        const userIds = contributorsData.map(c => c.user_id).filter(Boolean);

        if (userIds.length > 0) {
          const { data: usersData, error: usersError } = await supabase
            .from('users')
            .select('id, display_name, email')
            .in('id', userIds);

          if (usersError) throw usersError;

          // Add user data to contributors
          const contributorsWithUsers = contributorsData.map(contributor => {
            const user = usersData.find(u => u.id === contributor.user_id);
            return {
              ...contributor,
              user: user || null
            };
          });

          setContributors(contributorsWithUsers || []);
        } else {
          setContributors(contributorsData || []);
        }

        // Fetch contributions
        const { data: contributionsData, error: contributionsError } = await supabase
          .from('contributions')
          .select('*')
          .eq('project_id', projectId);

        if (contributionsError) throw contributionsError;

        setContributions(contributionsData || []);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error('Failed to load project data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [projectId]);

  // Calculate distribution when data changes
  useEffect(() => {
    if (!revenueAmount || contributors.length === 0) return;

    const amount = parseFloat(revenueAmount);
    if (isNaN(amount) || amount <= 0) return;

    // Calculate distribution based on selected model
    const calculatedDistribution = calculateRoyaltyDistribution(
      calculationModel,
      amount,
      contributors,
      contributions,
      { weights: cogWeights }
    );

    // Apply any manual adjustments
    const adjustmentsArray = Object.entries(manualAdjustments).map(([contributorId, adjustment]) => ({
      contributor_id: contributorId,
      adjustment_percentage: adjustment
    }));

    const finalDistribution = adjustmentsArray.length > 0
      ? applyManualAdjustments(calculatedDistribution, adjustmentsArray, amount)
      : calculatedDistribution;

    setDistribution(finalDistribution);
  }, [revenueAmount, contributors, contributions, calculationModel, cogWeights, manualAdjustments]);

  // Handle calculation model change
  const handleModelChange = (e) => {
    setCalculationModel(e.target.value);
    // Reset manual adjustments when changing models
    setManualAdjustments({});
  };

  // Handle CoG weight change
  const handleWeightChange = (weightType, value) => {
    setCogWeights(prev => ({
      ...prev,
      [weightType]: parseFloat(value) || 0
    }));
  };

  // Handle manual adjustment change
  const handleAdjustmentChange = (contributorId, value) => {
    const adjustment = parseFloat(value) || 0;

    setManualAdjustments(prev => ({
      ...prev,
      [contributorId]: adjustment
    }));
  };

  // Get contributor name
  const getContributorName = (contributor) => {
    if (!contributor || !contributor.user) return 'Unknown';
    return contributor.user.display_name || contributor.user.email || 'Unknown';
  };

  // Save distribution to database
  const saveDistribution = async () => {
    if (!revenueId || !projectId || distribution.length === 0) {
      toast.error('Cannot save distribution: missing data');
      return;
    }

    try {
      setSaving(true);

      // Calculate total amount
      const totalAmount = distribution.reduce((sum, item) => sum + parseFloat(item.amount), 0);

      // Create royalty distribution record
      const { data: distributionData, error: distributionError } = await supabase
        .from('royalty_distributions')
        .insert({
          project_id: projectId,
          revenue_id: revenueId,
          distribution_date: new Date().toISOString().split('T')[0],
          total_amount: totalAmount,
          currency: revenueCurrency,
          status: 'calculated',
          calculation_method: calculationModel,
          calculation_data: {
            distribution_details: distribution.map(item => ({
              contributor_id: item.contributor_id,
              user_id: item.user_id,
              amount: item.amount,
              percentage: item.percentage,
              calculation_details: item.calculation_details,
              manual_adjustment: manualAdjustments[item.contributor_id] || 0
            })),
            model_options: calculationModel === 'cog_model' ? cogWeights : {}
          },
          created_by: supabase.auth.user()?.id
        })
        .select()
        .single();

      if (distributionError) throw distributionError;

      // Create royalty payment records for each contributor
      const paymentPromises = distribution.map(item => {
        return supabase
          .from('royalty_payments')
          .insert({
            distribution_id: distributionData.id,
            recipient_id: item.user_id,
            amount: item.amount,
            currency: revenueCurrency,
            status: 'pending'
          });
      });

      await Promise.all(paymentPromises);

      // Update revenue status
      const { error: updateError } = await supabase
        .from('revenue_entries')
        .update({ status: 'distributed' })
        .eq('id', revenueId);

      if (updateError) throw updateError;

      toast.success('Distribution saved successfully');

      // Call callback if provided
      if (onDistributionSave) {
        onDistributionSave(distributionData);
      }
    } catch (error) {
      console.error('Error saving distribution:', error);
      toast.error('Failed to save distribution');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return <div className="loading-spinner">Loading...</div>;
  }

  return (
    <div className="royalty-distribution-preview">
      <div className="preview-header">
        <h3 className="preview-title">Royalty Distribution Preview</h3>
        <div className="total-amount">
          Total: {formatCurrency(revenueAmount, revenueCurrency)}
        </div>
      </div>

      <div className="calculation-controls">
        <div className="model-selector">
          <label htmlFor="calculation-model">Calculation Model:</label>
          <select
            id="calculation-model"
            value={calculationModel}
            onChange={handleModelChange}
            className="model-select"
          >
            <option value="equal_split">Equal Split</option>
            <option value="task_based">Task-Based</option>
            <option value="time_based">Time-Based</option>
            <option value="role_based">Role-Based</option>
            <option value="cog_model">CoG Model (Tasks-Time-Difficulty)</option>
          </select>
        </div>

        {calculationModel === 'cog_model' && (
          <div className="cog-weights">
            <h4>CoG Model Weights:</h4>
            <div className="weights-controls">
              <div className="weight-control">
                <label htmlFor="tasks-weight">Tasks:</label>
                <input
                  type="number"
                  id="tasks-weight"
                  value={cogWeights.tasks}
                  onChange={(e) => handleWeightChange('tasks', e.target.value)}
                  min="0"
                  step="0.1"
                  className="weight-input"
                />
              </div>

              <div className="weight-control">
                <label htmlFor="time-weight">Time:</label>
                <input
                  type="number"
                  id="time-weight"
                  value={cogWeights.time}
                  onChange={(e) => handleWeightChange('time', e.target.value)}
                  min="0"
                  step="0.1"
                  className="weight-input"
                />
              </div>

              <div className="weight-control">
                <label htmlFor="difficulty-weight">Difficulty:</label>
                <input
                  type="number"
                  id="difficulty-weight"
                  value={cogWeights.difficulty}
                  onChange={(e) => handleWeightChange('difficulty', e.target.value)}
                  min="0"
                  step="0.1"
                  className="weight-input"
                />
              </div>
            </div>
          </div>
        )}

        <button
          className="toggle-details-button"
          onClick={() => setShowDetails(!showDetails)}
        >
          {showDetails ? 'Hide Details' : 'Show Details'}
        </button>
      </div>

      <div className="distribution-table-container">
        <table className="distribution-table">
          <thead>
            <tr>
              <th>Contributor</th>
              <th>Amount</th>
              <th>Percentage</th>
              {showDetails && (
                <>
                  <th>Calculation Details</th>
                  <th>Manual Adjustment (%)</th>
                </>
              )}
            </tr>
          </thead>
          <tbody>
            {distribution.map((item) => {
              const contributor = contributors.find(c => c.id === item.contributor_id);
              return (
                <tr key={item.contributor_id}>
                  <td className="contributor-name">{getContributorName(contributor)}</td>
                  <td className="amount">{formatCurrency(item.amount, revenueCurrency)}</td>
                  <td className="percentage">{formatPercentage(item.percentage)}</td>

                  {showDetails && (
                    <>
                      <td className="calculation-details">
                        {calculationModel === 'equal_split' && (
                          <span>Equal share of total amount</span>
                        )}

                        {calculationModel === 'task_based' && (
                          <span>
                            {item.calculation_details.contributor_tasks} tasks out of {item.calculation_details.total_tasks} total
                          </span>
                        )}

                        {calculationModel === 'time_based' && (
                          <span>
                            {item.calculation_details.contributor_hours.toFixed(1)} hours out of {item.calculation_details.total_hours.toFixed(1)} total
                          </span>
                        )}

                        {calculationModel === 'role_based' && (
                          <span>
                            Weight: {item.calculation_details.contributor_weight} out of {item.calculation_details.total_weight} total
                          </span>
                        )}

                        {calculationModel === 'cog_model' && (
                          <div className="cog-details">
                            <div>Task Points: {item.calculation_details.task_points.toFixed(1)}</div>
                            <div>Time Points: {item.calculation_details.time_points.toFixed(1)}</div>
                            <div>Difficulty Points: {item.calculation_details.difficulty_points.toFixed(1)}</div>
                            <div>Total Points: {item.calculation_details.contributor_points.toFixed(1)} / {item.calculation_details.total_points.toFixed(1)}</div>
                          </div>
                        )}
                      </td>

                      <td className="manual-adjustment">
                        <input
                          type="number"
                          value={manualAdjustments[item.contributor_id] || 0}
                          onChange={(e) => handleAdjustmentChange(item.contributor_id, e.target.value)}
                          min="-100"
                          max="100"
                          step="0.1"
                          className="adjustment-input"
                        />
                      </td>
                    </>
                  )}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      <div className="distribution-actions">
        <button
          className="save-distribution-button"
          onClick={saveDistribution}
          disabled={saving || distribution.length === 0}
        >
          {saving ? (
            <>
              <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
              <span className="ms-2">Saving...</span>
            </>
          ) : (
            'Save Distribution'
          )}
        </button>
      </div>
    </div>
  );
};

export default RoyaltyDistributionPreview;
