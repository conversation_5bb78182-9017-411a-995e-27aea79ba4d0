import React, { useState } from 'react';
import { Card, CardBody, Button, Tabs, Tab } from '@heroui/react';
import { motion, AnimatePresence } from 'framer-motion';
import SectionRenderer from './SectionRenderer';

/**
 * Section Navigator Component
 *
 * Provides navigation between different sections within a canvas.
 * Displays section content with smooth transitions and tab-based navigation.
 */
const SectionNavigator = ({
  canvasId,
  sections,
  defaultSection = 'overview',
  className = ''
}) => {
  const [activeSection, setActiveSection] = useState(defaultSection);

  // Get section configuration
  const getSectionConfig = (sectionId) => {
    const configs = {
      // Home canvas sections
      overview: {
        title: 'Overview',
        icon: '📊',
        description: 'Dashboard overview and key metrics'
      },
      'recent-activity': {
        title: 'Activity',
        icon: '📈',
        description: 'Recent project activity and updates'
      },
      'quick-actions': {
        title: 'Actions',
        icon: '⚡',
        description: 'Quick access to common workflows'
      },
      notifications: {
        title: 'Notifications',
        icon: '🔔',
        description: 'Alerts and system messages'
      },

      // Projects canvas sections
      'project-list': {
        title: 'Projects',
        icon: '📁',
        description: 'All your projects'
      },
      'project-detail': {
        title: 'Details',
        icon: '📋',
        description: 'Project information and settings'
      },
      'team-management': {
        title: 'Team',
        icon: '👥',
        description: 'Manage team members and roles'
      },
      'project-settings': {
        title: 'Settings',
        icon: '⚙️',
        description: 'Project configuration'
      },

      // Contributions canvas sections
      'time-tracker': {
        title: 'Time Tracker',
        icon: '⏱️',
        description: 'Track your work time'
      },
      'contribution-log': {
        title: 'Log',
        icon: '📝',
        description: 'View contribution history'
      },
      'difficulty-assessment': {
        title: 'Assessment',
        icon: '🎯',
        description: 'Evaluate work complexity'
      },
      submission: {
        title: 'Submit',
        icon: '📤',
        description: 'Submit contributions for review'
      },

      // Default fallback
      default: {
        title: 'Section',
        icon: '📄',
        description: 'Canvas section'
      }
    };

    return configs[sectionId] || configs.default;
  };

  // Render section content
  const renderSectionContent = () => {
    const sectionData = sections[activeSection];
    if (!sectionData) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <span className="text-6xl mb-4 block">🚧</span>
            <h3 className="text-white text-lg font-medium mb-2">Section Under Construction</h3>
            <p className="text-white/60">This section is being built. Check back soon!</p>
          </div>
        </div>
      );
    }

    return (
      <SectionRenderer
        canvasId={canvasId}
        sectionId={activeSection}
        componentName={sectionData.component}
        props={sectionData.props || {}}
      />
    );
  };

  return (
    <div className={`h-full flex flex-col ${className}`}>
      {/* Section Navigation Tabs - REMOVED per user request */}

      {/* Section Content - Show default section only */}
      <div className="flex-1 overflow-hidden">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeSection}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="h-full overflow-y-auto"
          >
            {renderSectionContent()}
          </motion.div>
        </AnimatePresence>
      </div>


    </div>
  );
};

export default SectionNavigator;
