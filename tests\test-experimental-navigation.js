// Comprehensive Experimental Navigation System Test
const { chromium } = require('playwright');

const SITE_URL = 'https://royalty.technology';

async function testExperimentalNavigation() {
  console.log('🎮 COMPREHENSIVE EXPERIMENTAL NAVIGATION TEST\n');
  console.log('Testing the village sim-like overworld navigation system...\n');
  
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 1000 // Slow down for better observation
  });
  const context = await browser.newContext({
    viewport: { width: 1920, height: 1080 }
  });
  const page = await context.newPage();
  
  try {
    // === AUTHENTICATION ===
    console.log('🔐 Step 1: Authentication...');
    await page.goto(SITE_URL);
    await page.waitForLoadState('networkidle');
    
    const emailInput = page.locator('input[type="email"]').first();
    const needsAuth = await emailInput.isVisible();
    
    if (needsAuth) {
      await emailInput.fill('<EMAIL>');
      await page.fill('input[type="password"]', 'TestPassword123!');
      await page.click('button[type="submit"]');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);
    }
    
    console.log('✅ Authentication completed\n');
    
    // === NAVIGATION SYSTEM DETECTION ===
    console.log('🗺️ Step 2: Detecting Navigation System...');
    
    // Check for experimental navigation elements
    const hasExperimentalNav = await page.locator('[data-testid*="experimental"], [class*="experimental"], [class*="canvas"], [class*="overworld"]').count() > 0;
    const hasGridView = await page.locator('[class*="grid"], [class*="bento"]').count() > 0;
    const hasOverworldView = await page.locator('[class*="overworld"], [class*="world"], [class*="village"]').count() > 0;
    
    console.log(`Experimental Navigation Elements: ${hasExperimentalNav ? '✅' : '❌'}`);
    console.log(`Grid View Elements: ${hasGridView ? '✅' : '❌'}`);
    console.log(`Overworld View Elements: ${hasOverworldView ? '✅' : '❌'}\n`);
    
    // === VIEW MODE TESTING ===
    console.log('🔍 Step 3: Testing View Modes...');
    
    // Test for different view modes
    const viewModes = ['grid', 'overworld', 'content'];
    let currentViewMode = 'unknown';
    
    for (const mode of viewModes) {
      const hasMode = await page.locator(`[data-view="${mode}"], [class*="${mode}"]`).count() > 0;
      if (hasMode) {
        currentViewMode = mode;
        break;
      }
    }
    
    console.log(`Current View Mode: ${currentViewMode}`);
    
    // === CANVAS DETECTION ===
    console.log('\n🎯 Step 4: Detecting Canvas Elements...');
    
    const canvasElements = [
      'home', 'start', 'track', 'earn', 'projects', 'teams', 
      'contributions', 'kanban', 'analytics', 'validation'
    ];
    
    const foundCanvases = [];
    for (const canvas of canvasElements) {
      const hasCanvas = await page.locator(`[data-canvas="${canvas}"], [class*="${canvas}"], [id*="${canvas}"]`).count() > 0;
      if (hasCanvas) {
        foundCanvases.push(canvas);
      }
    }
    
    console.log(`Found Canvases: ${foundCanvases.join(', ')}`);
    console.log(`Total Canvas Count: ${foundCanvases.length}\n`);
    
    // === DRAG FUNCTIONALITY TEST ===
    console.log('🖱️ Step 5: Testing Drag Functionality...');
    
    // Test if the map/view is draggable
    const viewport = page.locator('body');
    const boundingBox = await viewport.boundingBox();
    
    if (boundingBox) {
      const startX = boundingBox.width / 2;
      const startY = boundingBox.height / 2;
      const endX = startX + 100;
      const endY = startY + 100;
      
      console.log(`Attempting drag from (${startX}, ${startY}) to (${endX}, ${endY})`);
      
      try {
        await page.mouse.move(startX, startY);
        await page.mouse.down();
        await page.waitForTimeout(500);
        await page.mouse.move(endX, endY, { steps: 10 });
        await page.waitForTimeout(500);
        await page.mouse.up();
        
        console.log('✅ Drag operation completed');
      } catch (error) {
        console.log(`❌ Drag operation failed: ${error.message}`);
      }
    }
    
    await page.waitForTimeout(2000);
    
    // === ZOOM FUNCTIONALITY TEST ===
    console.log('\n🔍 Step 6: Testing Zoom Functionality...');
    
    // Test zoom in
    try {
      await page.keyboard.down('Control');
      await page.mouse.wheel(0, -100); // Zoom in
      await page.keyboard.up('Control');
      await page.waitForTimeout(1000);
      console.log('✅ Zoom in attempted');
      
      await page.keyboard.down('Control');
      await page.mouse.wheel(0, 100); // Zoom out
      await page.keyboard.up('Control');
      await page.waitForTimeout(1000);
      console.log('✅ Zoom out attempted');
    } catch (error) {
      console.log(`❌ Zoom operations failed: ${error.message}`);
    }
    
    // === CARD INTERACTION TEST ===
    console.log('\n🃏 Step 7: Testing Card Interactions...');
    
    // Look for clickable cards or canvas elements
    const cards = await page.locator('[class*="card"], [class*="canvas"], [data-canvas], [role="button"]').all();
    console.log(`Found ${cards.length} potential interactive elements`);
    
    if (cards.length > 0) {
      try {
        // Test clicking the first card
        await cards[0].click();
        await page.waitForTimeout(2000);
        console.log('✅ Card click interaction completed');
        
        // Check if navigation occurred
        const currentUrl = page.url();
        console.log(`Current URL after card click: ${currentUrl}`);
      } catch (error) {
        console.log(`❌ Card interaction failed: ${error.message}`);
      }
    }
    
    // === ROUTE TESTING ===
    console.log('\n🛣️ Step 8: Testing Canvas Routes...');
    
    const routesToTest = ['/track', '/earn', '/projects', '/teams'];
    
    for (const route of routesToTest) {
      try {
        console.log(`Testing route: ${route}`);
        await page.goto(`${SITE_URL}${route}`);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        const content = await page.textContent('body');
        const hasContent = content.length > 100;
        const hasCanvas = content.includes('canvas') || content.includes('Canvas');
        
        console.log(`  Route ${route}: ${hasContent ? '✅' : '❌'} Content (${content.length} chars)`);
        console.log(`  Canvas Elements: ${hasCanvas ? '✅' : '❌'}`);
      } catch (error) {
        console.log(`  Route ${route}: ❌ Failed - ${error.message}`);
      }
    }
    
    // === VILLAGE SIM ASSESSMENT ===
    console.log('\n🏘️ Step 9: Village Sim Assessment...');
    
    // Go back to main navigation
    await page.goto(SITE_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    const villageLikeFeatures = {
      'Draggable Map': false,
      'Zoomable Interface': false,
      'Connected Nodes': false,
      'Spatial Layout': false,
      'Interactive Cards': false,
      'Multiple View Modes': false
    };
    
    // Check for village sim characteristics
    const hasDraggableElements = await page.locator('[draggable="true"], [class*="draggable"]').count() > 0;
    const hasConnections = await page.locator('[class*="connection"], [class*="line"], [class*="edge"]').count() > 0;
    const hasSpatialLayout = await page.locator('[style*="position"], [class*="absolute"], [class*="relative"]').count() > 5;
    
    villageLikeFeatures['Draggable Map'] = hasDraggableElements;
    villageLikeFeatures['Connected Nodes'] = hasConnections;
    villageLikeFeatures['Spatial Layout'] = hasSpatialLayout;
    villageLikeFeatures['Interactive Cards'] = foundCanvases.length > 0;
    villageLikeFeatures['Multiple View Modes'] = currentViewMode !== 'unknown';
    
    console.log('\n📊 VILLAGE SIM FEATURES ASSESSMENT:');
    for (const [feature, present] of Object.entries(villageLikeFeatures)) {
      console.log(`  ${feature}: ${present ? '✅' : '❌'}`);
    }
    
    // === FINAL ASSESSMENT ===
    console.log('\n🎯 FINAL ASSESSMENT:');
    const totalFeatures = Object.keys(villageLikeFeatures).length;
    const presentFeatures = Object.values(villageLikeFeatures).filter(Boolean).length;
    const completionPercentage = Math.round((presentFeatures / totalFeatures) * 100);
    
    console.log(`Village Sim Completion: ${completionPercentage}% (${presentFeatures}/${totalFeatures})`);
    
    if (completionPercentage >= 80) {
      console.log('🏆 EXCELLENT: Navigation system is highly village sim-like!');
    } else if (completionPercentage >= 60) {
      console.log('🎯 GOOD: Navigation system has strong village sim elements');
    } else if (completionPercentage >= 40) {
      console.log('⚠️ MODERATE: Some village sim features present');
    } else {
      console.log('🚧 NEEDS WORK: Limited village sim functionality');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    console.log('\n🏁 Test completed. Browser will remain open for manual inspection...');
    // Keep browser open for manual inspection
    // await browser.close();
  }
}

testExperimentalNavigation();
