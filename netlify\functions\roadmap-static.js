// Import the roadmap data
let getUpdatedRoadmapData;

try {
  // Try to import from the client directory
  const roadmapModule = require('../../client/src/components/admin/updated-roadmap-data');
  getUpdatedRoadmapData = roadmapModule.getUpdatedRoadmapData;
} catch (importError) {
  console.warn('Failed to import roadmap data from client directory:', importError.message);

  // Fallback: Define a simple roadmap data function
  getUpdatedRoadmapData = () => [
    {
      id: 1,
      title: "Foundation & User Management",
      timeframe: "Completed",
      expanded: true,
      sections: [
        {
          id: "1.1",
          title: "Project Setup & Configuration",
          tasks: [
            { id: "1.1.1", text: "Finalize tech stack (React, Supabase)", completed: true },
            { id: "1.1.2", text: "Set up development environment", completed: true },
            { id: "1.1.3", text: "Configure Netlify deployment", completed: true }
          ]
        }
      ]
    },
    {
      id: 2,
      title: "Project Creation & Management",
      timeframe: "Phase 1",
      expanded: false,
      sections: [
        {
          id: "2.1",
          title: "Project Wizard",
          tasks: [
            { id: "2.1.1", text: "Design project creation flow", completed: true },
            { id: "2.1.2", text: "Implement project basics form", completed: true },
            { id: "2.1.3", text: "Add team & contributors section", completed: true }
          ]
        }
      ]
    }
  ];
}

// Function to calculate stats
function calculateStats(phases) {
  let totalTasks = 0;
  let completedTasks = 0;
  let phaseStats = [];

  phases.forEach(phase => {
    let phaseTotalTasks = 0;
    let phaseCompletedTasks = 0;

    phase.sections.forEach(section => {
      phaseTotalTasks += section.tasks.length;
      phaseCompletedTasks += section.tasks.filter(task => task.completed).length;
    });

    totalTasks += phaseTotalTasks;
    completedTasks += phaseCompletedTasks;

    phaseStats.push({
      id: phase.id,
      title: phase.title,
      timeframe: phase.timeframe,
      progress: phaseTotalTasks > 0 ? Math.round((phaseCompletedTasks / phaseTotalTasks) * 100) : 0
    });
  });

  return {
    totalTasks,
    completedTasks,
    progressPercentage: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
    phases: phaseStats
  };
}

exports.handler = async function(event, context) {
  // Handle OPTIONS request for CORS
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, OPTIONS'
      },
      body: ''
    };
  }

  try {
    // Get the roadmap data
    const roadmapData = getUpdatedRoadmapData();

    // Calculate stats
    const stats = calculateStats(roadmapData);

    // Return the data
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, OPTIONS'
      },
      body: JSON.stringify({
        success: true,
        data: roadmapData,
        stats: stats
      })
    };
  } catch (error) {
    console.error('Error in roadmap-static function:', error);

    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, OPTIONS'
      },
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    };
  }
};
