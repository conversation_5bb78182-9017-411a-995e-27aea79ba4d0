import { useState, useContext, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { UserContext } from "../../../contexts/supabase-auth.context";
import { Card, CardBody, CardHeader, Button, Input } from "../../components/ui/heroui";

// Import shadcn components that are being used
import { CardTitle, CardDescription, CardContent } from "../../components/ui/shadcn/card";
import { Alert, AlertDescription } from "../../components/ui/shadcn/alert";
import { Label } from "../../components/ui/shadcn/label";

// Import the new immersive auth components
import AuthLandingPage from "../../components/auth/AuthLandingPage";

const SimpleLogin = () => {
  const { currentUser, isLoading, login, signup, loginWithGoogle, loginWithGithub } = useContext(UserContext);
  const navigate = useNavigate();
  const location = useLocation();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [displayName, setDisplayName] = useState("");
  const [isSignUp, setIsSignUp] = useState(false);
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [immersiveMode, setImmersiveMode] = useState(true); // Default to immersive mode

  // Redirect if user is already logged in
  useEffect(() => {
    if (!isLoading && currentUser) {
      console.log('User already logged in, redirecting');
      // Redirect to the page they were trying to access, or to dashboard
      const from = location.state?.from || '/dashboard';
      console.log('Redirecting to:', from);
      navigate(from, { replace: true });
    }
  }, [currentUser, isLoading, navigate, location]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");
    setLoading(true);

    try {
      if (isSignUp) {
        // Include display name in user metadata when signing up
        await signup(email, password, { full_name: displayName });
        console.log('Signup successful');
      } else {
        await login(email, password);
        console.log('Login successful');
      }

      // After successful login/signup, redirect to the page they were trying to access
      const from = location.state?.from || '/dashboard';
      console.log('Redirecting after auth to:', from);
      navigate(from, { replace: true });
    } catch (err) {
      console.error('Auth error:', err);
      setError(err.message || "An error occurred during authentication");
    } finally {
      setLoading(false);
    }
  };

  // Use immersive mode by default, traditional mode as fallback
  if (immersiveMode) {
    return (
      <AuthLandingPage
        redirectTo={location.state?.from || '/dashboard'}
        onCancel={() => {
          // Allow users to switch to traditional mode if needed
          setImmersiveMode(false);
        }}
      />
    );
  }

  // Traditional mode (fallback)
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Mode toggle */}
        <div className="text-center mb-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setImmersiveMode(true)}
            className="text-primary border-primary hover:bg-primary hover:text-white"
          >
            🚀 Try Immersive Mode
          </Button>
        </div>

        <Card className="shadow-lg">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl font-bold">Welcome to Royaltea</CardTitle>
            <CardDescription className="text-lg">
              {isSignUp ? "Create an Account" : "Log In to Your Account"}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email address</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>

              {isSignUp && (
                <div className="space-y-2">
                  <Label htmlFor="displayName">Display Name</Label>
                  <Input
                    id="displayName"
                    type="text"
                    placeholder="Enter your display name"
                    value={displayName}
                    onChange={(e) => setDisplayName(e.target.value)}
                    required={isSignUp}
                  />
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </div>

              <Button
                type="submit"
                className="w-full"
                size="lg"
                disabled={loading}
              >
                {loading ? "Processing..." : isSignUp ? "Sign Up" : "Log In"}
              </Button>
            </form>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">OR</span>
              </div>
            </div>

            <div className="space-y-3">
              <Button
                variant="outline"
                className="w-full"
                onClick={() => {
                  setError("");
                  console.log('Initiating Google login...');
                  loginWithGoogle()
                    .then(() => {
                      console.log('Google login successful');
                      // After successful login, redirect to the page they were trying to access
                      const from = location.state?.from || '/dashboard';
                      console.log('Redirecting after Google auth to:', from);
                      navigate(from, { replace: true });
                    })
                    .catch(err => {
                      console.error('Google login error:', err);
                      setError(err.message || "Error signing in with Google");
                    });
                }}
              >
                <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                  <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                Continue with Google
              </Button>

              <Button
                variant="outline"
                className="w-full"
                onClick={() => {
                  setError("");
                  console.log('Initiating GitHub login...');
                  loginWithGithub()
                    .then(() => {
                      console.log('GitHub login successful');
                      // After successful login, redirect to the page they were trying to access
                      const from = location.state?.from || '/dashboard';
                      console.log('Redirecting after GitHub auth to:', from);
                      navigate(from, { replace: true });
                    })
                    .catch(err => {
                      console.error('GitHub login error:', err);
                      setError(err.message || "Error signing in with GitHub");
                    });
                }}
              >
                <svg className="mr-2 h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                </svg>
                Continue with GitHub
              </Button>
            </div>

            <div className="text-center">
              <p className="text-sm text-muted-foreground">
                {isSignUp ? "Already have an account?" : "Don't have an account?"}{" "}
                <Button
                  variant="link"
                  onClick={() => setIsSignUp(!isSignUp)}
                  className="p-0 h-auto font-normal text-primary underline-offset-4 hover:underline"
                >
                  {isSignUp ? "Log In" : "Sign Up"}
                </Button>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SimpleLogin;
