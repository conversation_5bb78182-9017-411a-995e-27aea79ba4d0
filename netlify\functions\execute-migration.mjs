// Execute Migration API function using ES modules
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const initSupabase = () => {
  const supabaseUrl = Netlify.env.get("SUPABASE_URL");
  const supabaseKey = Netlify.env.get("SUPABASE_SERVICE_KEY") || Netlify.env.get("SUPABASE_ANON_KEY");
  
  if (!supabaseUrl || !supabaseKey) {
    throw new Error('Missing Supabase credentials');
  }
  
  return createClient(supabaseUrl, supabaseKey);
};

// Main function handler
export default async (req, context) => {
  // Set CORS headers for cross-origin access
  const headers = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'POST, OPTIONS'
  };
  
  // Handle OPTIONS request for CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers
    });
  }
  
  // Only allow POST requests
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Method not allowed'
      }),
      { 
        status: 405,
        headers
      }
    );
  }
  
  try {
    // Get user from context
    const { user } = context.clientContext;
    
    if (!user) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Unauthorized'
        }),
        { 
          status: 401,
          headers
        }
      );
    }
    
    // Check if user is an admin
    const supabase = initSupabase();
    
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.sub)
      .single();
    
    if (userError) {
      console.error('Error fetching user:', userError);
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Unauthorized'
        }),
        { 
          status: 401,
          headers
        }
      );
    }
    
    if (!userData.is_admin) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Only admins can execute migrations'
        }),
        { 
          status: 403,
          headers
        }
      );
    }
    
    // Parse the request body
    const body = await req.json();
    
    if (!body.sql) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Missing SQL query'
        }),
        { 
          status: 400,
          headers
        }
      );
    }
    
    // Execute the SQL query
    const { data, error } = await supabase.rpc('execute_sql', {
      sql_query: body.sql
    });
    
    if (error) {
      console.error('Error executing SQL:', error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message
        }),
        { 
          status: 500,
          headers
        }
      );
    }
    
    return new Response(
      JSON.stringify({
        success: true,
        message: 'Migration executed successfully',
        data
      }),
      { headers }
    );
    
  } catch (error) {
    console.error('Error in execute-migration function:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      { 
        status: 500,
        headers
      }
    );
  }
};

// Configure the function path
export const config = {
  path: "/api/execute-migration"
};
