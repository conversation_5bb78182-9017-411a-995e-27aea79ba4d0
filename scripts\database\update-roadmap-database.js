// Script to update the roadmap with PDF preview improvements
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const fs = require('fs');
const path = require('path');

// Read the key from .env.local file
const envPath = path.join(__dirname, 'client', '.env.local');
let supabaseKey;

try {
  const envContent = fs.readFileSync(envPath, 'utf8');
  const keyMatch = envContent.match(/SUPABASE_SERVICE_KEY=(.+)/);
  if (keyMatch && keyMatch[1]) {
    supabaseKey = keyMatch[1].trim();
  } else {
    throw new Error('SUPABASE_SERVICE_KEY not found in .env.local');
  }
} catch (error) {
  console.error(`Error reading key from ${envPath}:`, error.message);
  console.log('Please provide the Supabase service key as an environment variable');
  process.exit(1);
}

const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';

const supabase = createClient(supabaseUrl, supabaseKey);

console.log(`Using Supabase URL: ${supabaseUrl}`);
console.log('=== Roadmap Update for PDF Preview Improvements ===\n');

// Function to get the current roadmap data
async function getCurrentRoadmap() {
  try {
    console.log('=== Getting current roadmap data ===');

    // Get the latest roadmap entry
    const { data: roadmapData, error: roadmapError } = await supabase
      .from('roadmap')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1);

    if (roadmapError) {
      console.error('Error fetching roadmap from Supabase:', roadmapError);
      return null;
    }

    if (!roadmapData || roadmapData.length === 0) {
      console.log('No roadmap data found in the database');
      return null;
    }

    console.log('Found roadmap data with ID:', roadmapData[0].id);
    return roadmapData[0];
  } catch (error) {
    console.error('Error in getCurrentRoadmap:', error);
    return null;
  }
}

// Function to update the roadmap
async function updateRoadmap(roadmapId, roadmapData) {
  try {
    console.log(`\n=== Updating roadmap with ID: ${roadmapId} ===`);

    const { data: updatedData, error: updateError } = await supabase
      .from('roadmap')
      .update({
        data: roadmapData,
        last_updated: new Date().toISOString()
      })
      .eq('id', roadmapId)
      .select();

    if (updateError) {
      console.error('Error updating roadmap:', updateError);
      return false;
    }

    console.log('Updated roadmap successfully');
    return true;
  } catch (error) {
    console.error('Error in updateRoadmap:', error);
    return false;
  }
}

// Function to find a task by ID
function findTask(roadmapData, taskId) {
  for (const phase of roadmapData) {
    if (phase.type === 'metadata' || !phase.sections) continue;

    for (const section of phase.sections) {
      const task = section.tasks.find(t => t.id === taskId);
      if (task) {
        return task;
      }
    }
  }
  return null;
}

// Function to find a section by IDs
function findSection(roadmapData, phaseId, sectionId) {
  const phase = roadmapData.find(p => p.id === phaseId);
  if (!phase || !phase.sections) return null;

  return phase.sections.find(s => s.id === sectionId);
}

// Function to add a new task
function addNewTask(roadmapData, phaseId, sectionId, taskText, completed = false) {
  const section = findSection(roadmapData, phaseId, sectionId);
  if (!section) {
    console.log(`Section ${sectionId} not found in phase ${phaseId}`);
    return false;
  }

  // Generate a new task ID
  const lastTaskId = section.tasks.length > 0
    ? section.tasks[section.tasks.length - 1].id
    : `${sectionId}.0`;

  const lastIdParts = lastTaskId.split('.');
  const lastIdNumber = parseInt(lastIdParts[lastIdParts.length - 1]);
  const newIdNumber = lastIdNumber + 1;
  const newTaskId = `${sectionId}.${newIdNumber}`;

  // Add the new task
  section.tasks.push({
    id: newTaskId,
    text: taskText,
    completed: completed
  });

  console.log(`Added new task with ID ${newTaskId}: "${taskText}"`);
  return true;
}

// Function to update the latest feature in metadata
function updateLatestFeature(roadmapData) {
  // Find the metadata item
  const metadataIndex = roadmapData.findIndex(item => item.type === 'metadata');

  if (metadataIndex !== -1) {
    // Update the latest_feature object in the metadata
    roadmapData[metadataIndex].latest_feature = {
      title: "PDF Preview Improvements",
      description: "Enhanced PDF preview with left-justified text and fixed automatic download issues. PDFs now display properly and only download when explicitly requested by the user.",
      date: new Date().toISOString(),
      author: "Development Team",
      version: "1.0.1"
    };
    console.log('Updated latest_feature in metadata');
    return true;
  } else {
    console.log('Metadata item not found in roadmap data');
    return false;
  }
}

// Main function
async function main() {
  try {
    // Get current roadmap
    const roadmap = await getCurrentRoadmap();
    if (!roadmap) {
      console.error('Failed to get current roadmap');
      return;
    }

    const roadmapData = roadmap.data;
    let updated = false;

    // 1. Mark PDF preview tasks as completed
    const task1 = findTask(roadmapData, "5.3.4");
    if (task1) {
      task1.completed = true;
      console.log('Marked task 5.3.4 "Improve PDF preview formatting" as completed');
      updated = true;
    } else {
      console.log('Task 5.3.4 not found');
    }

    const task2 = findTask(roadmapData, "5.3.5");
    if (task2) {
      task2.completed = true;
      console.log('Marked task 5.3.5 "Fix PDF download issues" as completed');
      updated = true;
    } else {
      console.log('Task 5.3.5 not found');
    }

    // 2. Add new task for agreement customization
    const addedTask = addNewTask(
      roadmapData,
      5,
      "5.3",
      "Enhance agreement customization for project-specific details",
      false
    );
    if (addedTask) {
      updated = true;
    }

    // 3. Update the latest feature in metadata
    const updatedFeature = updateLatestFeature(roadmapData);
    if (updatedFeature) {
      updated = true;
    }

    // 4. Update the roadmap if changes were made
    if (updated) {
      const success = await updateRoadmap(roadmap.id, roadmapData);
      if (success) {
        console.log('\n=== Roadmap updated successfully ===');
      } else {
        console.error('\n=== Failed to update roadmap ===');
      }
    } else {
      console.log('\n=== No changes were made to the roadmap ===');
    }
  } catch (error) {
    console.error('Error in main function:', error);
  }
}

// Run the main function
main();
