// Authentication Setup for Playwright Tests
// Proper authentication bypass and session management

import { test as base, expect } from '@playwright/test';

const SITE_URL = 'https://royalty.technology';

// Test user credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

// Extend base test with authentication
export const test = base.extend({
  // Authenticated page fixture
  authenticatedPage: async ({ page }, use) => {
    // Navigate to site
    await page.goto(SITE_URL);
    await page.waitForLoadState('networkidle');
    
    // Check if login is needed
    const needsLogin = await page.locator('input[type="email"]').isVisible();
    
    if (needsLogin) {
      console.log('🔑 Performing authentication...');
      
      // Fill login form
      await page.fill('input[type="email"]', TEST_USER.email);
      await page.fill('input[type="password"]', TEST_USER.password);
      
      // Submit login
      await page.click('button[type="submit"]');
      
      // Wait for authentication to complete
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);
      
      // Verify authentication succeeded
      const isAuthenticated = !(await page.locator('input[type="email"]').isVisible());
      if (!isAuthenticated) {
        throw new Error('Authentication failed');
      }
      
      console.log('✅ Authentication successful');
    }
    
    await use(page);
  }
});

// Helper function to bypass authentication using localStorage/sessionStorage
export async function bypassAuth(page) {
  // Method 1: Set authentication tokens directly
  await page.addInitScript(() => {
    // Mock Supabase session
    const mockSession = {
      access_token: 'mock-access-token',
      refresh_token: 'mock-refresh-token',
      expires_in: 3600,
      token_type: 'bearer',
      user: {
        id: 'test-user-id',
        email: '<EMAIL>',
        user_metadata: {
          full_name: 'Test User'
        }
      }
    };
    
    // Set in localStorage (common pattern for Supabase)
    localStorage.setItem('supabase.auth.token', JSON.stringify(mockSession));
    localStorage.setItem('sb-hqqlrrqvjcetoxbdjgzx-auth-token', JSON.stringify(mockSession));
    
    // Set user context
    window.__TEST_USER__ = mockSession.user;
  });
}

// Helper function to create test team/alliance
export async function createTestAlliance(page) {
  // Navigate to teams/alliances
  await page.goto(`${SITE_URL}/teams`);
  await page.waitForLoadState('networkidle');
  
  // Look for create button
  const createButton = page.locator('text=Create').first();
  if (await createButton.isVisible()) {
    await createButton.click();
    await page.waitForLoadState('networkidle');
    
    // Fill in alliance details
    await page.fill('input[name="name"]', 'Test Alliance');
    await page.fill('textarea[name="description"]', 'Test alliance for automated testing');
    
    // Submit
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    
    return true;
  }
  
  return false;
}

// Helper function to test navigation flow
export async function testNavigationFlow(page) {
  console.log('🧭 Testing navigation flow...');
  
  // Start at home
  await page.goto(SITE_URL);
  await page.waitForLoadState('networkidle');
  
  // Check for experimental navigation
  const hasCanvas = await page.locator('canvas').isVisible();
  console.log('Has experimental navigation:', hasCanvas);
  
  if (hasCanvas) {
    // Test keyboard navigation
    console.log('Testing keyboard navigation...');
    
    // Go to overworld view
    await page.keyboard.press('ArrowDown');
    await page.waitForTimeout(1000);
    
    // Go to grid view
    await page.keyboard.press('ArrowDown');
    await page.waitForTimeout(1000);
    
    // Look for teams card
    const teamsCard = page.locator('text=Teams').first();
    if (await teamsCard.isVisible()) {
      console.log('✅ Teams card found in grid view');
      await teamsCard.click();
      await page.waitForLoadState('networkidle');
      
      const currentUrl = page.url();
      console.log('Navigated to:', currentUrl);
      
      return currentUrl.includes('/teams');
    }
  } else {
    // Test regular navigation
    console.log('Testing regular navigation...');
    
    const teamsLink = page.locator('a[href*="teams"]').first();
    if (await teamsLink.isVisible()) {
      await teamsLink.click();
      await page.waitForLoadState('networkidle');
      return true;
    }
  }
  
  return false;
}

export { expect };
