import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Chip, Progress, Avatar } from '@heroui/react';

/**
 * MissionCard Component
 * 
 * Enhanced mission card following wireframe specifications
 * Game-like design with clear rewards, difficulty, and skill matching
 */
const MissionCard = ({ 
  mission, 
  index, 
  onClaim, 
  onView, 
  onAskQuestion,
  onSave,
  currentUser,
  skillMatchScore = null 
}) => {
  // Get difficulty color and stars
  const getDifficultyDisplay = (difficulty) => {
    const difficultyMap = {
      'easy': { stars: '⭐⭐⭐☆☆☆☆☆☆☆', color: 'success', rating: '3/10' },
      'medium': { stars: '⭐⭐⭐⭐⭐☆☆☆☆☆', color: 'warning', rating: '5/10' },
      'hard': { stars: '⭐⭐⭐⭐⭐⭐⭐☆☆☆', color: 'danger', rating: '7/10' },
      'expert': { stars: '⭐⭐⭐⭐⭐⭐⭐⭐⭐☆', color: 'secondary', rating: '9/10' }
    };
    return difficultyMap[difficulty] || difficultyMap['medium'];
  };

  // Get mission type icon
  const getMissionTypeIcon = (type) => {
    const iconMap = {
      'bug': '🔥',
      'feature': '⚡',
      'design': '🎨',
      'documentation': '📋',
      'testing': '🧪',
      'general': '⚔️'
    };
    return iconMap[type] || '⚔️';
  };

  // Get reward display
  const getRewardDisplay = () => {
    if (mission.budget_usd) {
      return `$${mission.budget_usd}`;
    }
    return `${mission.rewardOrbs || 100} ORBs`;
  };

  // Get timeline display
  const getTimelineDisplay = () => {
    if (mission.estimatedDuration) {
      if (mission.estimatedDuration <= 8) return `${mission.estimatedDuration}h`;
      const days = Math.ceil(mission.estimatedDuration / 8);
      if (days === 1) return '1 day';
      if (days <= 7) return `${days} days`;
      const weeks = Math.ceil(days / 7);
      return `${weeks} week${weeks > 1 ? 's' : ''}`;
    }
    return 'Flexible';
  };

  // Get project display
  const getProjectDisplay = () => {
    if (mission.project?.name) {
      return mission.project.name;
    }
    return 'General Mission';
  };

  // Get assignee display
  const getAssigneeDisplay = () => {
    if (mission.assignee) {
      return {
        name: mission.assignee.display_name || mission.assignee.full_name || 'Unknown',
        rating: '4.8★' // TODO: Get actual rating from user profile
      };
    }
    return null;
  };

  const difficultyInfo = getDifficultyDisplay(mission.difficultyRating);
  const assignee = getAssigneeDisplay();
  const isMyMission = mission.assignee_id === currentUser?.id;
  const canClaim = mission.claimable && !isMyMission;
  const isAvailable = mission.missionStatus === 'available';
  const isInProgress = mission.missionStatus === 'active';

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3, delay: index * 0.05 }}
      whileHover={{ scale: 1.02 }}
      className="h-full"
    >
      <Card className="h-full hover:shadow-lg transition-all duration-200 border border-default-200">
        <CardHeader className="pb-3">
          <div className="w-full">
            {/* Mission Title and Type */}
            <div className="flex items-start justify-between mb-2">
              <h3 className="text-lg font-semibold line-clamp-2 flex-1">
                {getMissionTypeIcon(mission.missionType)} {mission.title}
              </h3>
              {skillMatchScore && (
                <Chip size="sm" color="success" variant="flat" className="ml-2">
                  {skillMatchScore}% Match
                </Chip>
              )}
            </div>

            {/* Reward, Difficulty, Timeline, Project */}
            <div className="flex flex-wrap items-center gap-2 text-sm">
              <span className="font-semibold text-success">
                💰 {getRewardDisplay()}
              </span>
              <span className="text-default-600">•</span>
              <span title={`Difficulty: ${difficultyInfo.rating}`}>
                {difficultyInfo.stars.slice(0, 10)}
              </span>
              <span className="text-default-600">•</span>
              <span className="text-default-600">
                ⏰ {getTimelineDisplay()}
              </span>
              <span className="text-default-600">•</span>
              <span className="text-primary text-xs">
                🏗️ {getProjectDisplay()}
              </span>
            </div>
          </div>
        </CardHeader>

        <CardBody className="pt-0">
          {/* Skills Required */}
          {mission.skillsRequired && mission.skillsRequired.length > 0 && (
            <div className="mb-3">
              <div className="text-xs text-default-500 mb-1">🎯 Skills:</div>
              <div className="flex flex-wrap gap-1">
                {mission.skillsRequired.slice(0, 3).map((skill, idx) => (
                  <Chip key={idx} size="sm" variant="flat" color="primary">
                    {skill}
                  </Chip>
                ))}
                {mission.skillsRequired.length > 3 && (
                  <Chip size="sm" variant="flat" color="default">
                    +{mission.skillsRequired.length - 3}
                  </Chip>
                )}
              </div>
            </div>
          )}

          {/* Assignee or Status */}
          {assignee ? (
            <div className="mb-3">
              <div className="text-xs text-default-500 mb-1">👤 Assigned to:</div>
              <div className="flex items-center space-x-2">
                <Avatar size="sm" name={assignee.name} className="w-6 h-6" />
                <span className="text-sm font-medium">{assignee.name}</span>
                <span className="text-xs text-warning">({assignee.rating})</span>
              </div>
            </div>
          ) : isAvailable ? (
            <div className="mb-3">
              <div className="text-xs text-success mb-1">👤 Status:</div>
              <div className="text-sm font-medium text-success">Open for Claims</div>
            </div>
          ) : null}

          {/* Progress Bar (for in-progress missions) */}
          {isInProgress && mission.progress > 0 && (
            <div className="mb-3">
              <div className="flex justify-between text-xs text-default-500 mb-1">
                <span>Progress</span>
                <span>{mission.progress}%</span>
              </div>
              <Progress 
                value={mission.progress} 
                color="primary" 
                size="sm"
                className="w-full"
              />
            </div>
          )}

          {/* Mission Description */}
          <div className="mb-4">
            <div className="text-xs text-default-500 mb-1">📋 Description:</div>
            <p className="text-sm text-default-600 line-clamp-3">
              {mission.description || 'No description provided'}
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-2 mt-auto">
            {canClaim && (
              <Button
                size="sm"
                color="primary"
                onPress={() => onClaim(mission.id)}
                className="flex-1"
              >
                Claim Mission
              </Button>
            )}
            
            {isMyMission && (
              <Button
                size="sm"
                color="success"
                variant="flat"
                onPress={() => onView(mission)}
                className="flex-1"
              >
                Continue Work
              </Button>
            )}

            {!isMyMission && !canClaim && (
              <Button
                size="sm"
                variant="flat"
                onPress={() => onView(mission)}
                className="flex-1"
              >
                View Details
              </Button>
            )}

            <Button
              size="sm"
              variant="light"
              isIconOnly
              onPress={() => onSave && onSave(mission.id)}
              title="Save Mission"
            >
              <i className="bi bi-bookmark text-default-500"></i>
            </Button>

            <Button
              size="sm"
              variant="light"
              isIconOnly
              onPress={() => onAskQuestion && onAskQuestion(mission)}
              title="Ask Question"
            >
              <i className="bi bi-chat-dots text-default-500"></i>
            </Button>
          </div>
        </CardBody>
      </Card>
    </motion.div>
  );
};

export default MissionCard;
