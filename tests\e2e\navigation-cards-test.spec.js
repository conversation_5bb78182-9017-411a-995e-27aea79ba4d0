// Navigation Cards Testing
// Day 3 - Comprehensive testing of navigation between different cards and views

import { test, expect } from '@playwright/test';

const SITE_URL = 'https://royalty.technology';

// Test credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

// Helper function to authenticate
async function authenticate(page) {
  console.log(`🔑 Authenticating as ${TEST_USER.email}`);

  await page.goto(SITE_URL);
  await page.waitForLoadState('networkidle');

  const emailInput = page.locator('input[type="email"]').first();
  const needsAuth = await emailInput.isVisible();

  if (needsAuth) {
    await emailInput.fill(TEST_USER.email);
    await page.fill('input[type="password"]', TEST_USER.password);
    await page.click('button[type="submit"]');

    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    const stillNeedsAuth = await page.locator('input[type="email"]').isVisible();
    if (stillNeedsAuth) {
      throw new Error(`Authentication failed for ${TEST_USER.email}`);
    }

    console.log(`✅ Authentication successful`);
  }

  return true;
}

test.describe('Navigation Cards and Views Testing', () => {
  test('should test experimental navigation system comprehensively', async ({ page }) => {
    console.log('🎮 Testing experimental navigation system...');

    await authenticate(page);

    // Start at home page with test mode parameter
    await page.goto(`${SITE_URL}?test_mode=true&skip_onboarding=true`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000); // Give more time for React to load

    // Check for experimental navigation elements
    console.log('🔍 Checking for experimental navigation elements...');

    const canvas = page.locator('canvas').first();
    const hasCanvas = await canvas.isVisible();
    console.log(`Canvas element present: ${hasCanvas}`);

    if (hasCanvas) {
      // Get canvas properties
      const canvasBox = await canvas.boundingBox();
      console.log(`Canvas dimensions: ${canvasBox?.width}x${canvasBox?.height}`);

      // Test mouse interactions
      console.log('🖱️ Testing mouse interactions...');
      await canvas.click({ position: { x: canvasBox.width / 2, y: canvasBox.height / 2 } });
      await page.waitForTimeout(1000);

      // Test keyboard navigation through different views
      console.log('⌨️ Testing keyboard navigation...');

      const navigationSteps = [
        { key: 'ArrowDown', description: 'Move to overworld view' },
        { key: 'ArrowDown', description: 'Move to grid view' },
        { key: 'ArrowUp', description: 'Move back to overworld' },
        { key: 'ArrowUp', description: 'Move back to dashboard' },
        { key: 'ArrowLeft', description: 'Navigate left' },
        { key: 'ArrowRight', description: 'Navigate right' }
      ];

      for (const step of navigationSteps) {
        console.log(`  ${step.description} (${step.key})`);
        await page.keyboard.press(step.key);
        await page.waitForTimeout(1500);

        // Check current view state
        const currentUrl = page.url();
        const currentHash = await page.evaluate(() => window.location.hash);
        console.log(`    Current URL: ${currentUrl}`);
        console.log(`    Current hash: ${currentHash}`);

        // Look for visible cards
        const visibleCards = await page.locator('[class*="card"], [class*="grid"], [class*="item"]').count();
        console.log(`    Visible cards/items: ${visibleCards}`);
      }

      // Test specific card interactions
      console.log('🃏 Testing card interactions...');

      // Go to grid view
      await page.keyboard.press('ArrowDown');
      await page.keyboard.press('ArrowDown');
      await page.waitForTimeout(2000);

      // Look for specific cards
      const cardSelectors = [
        'text=Teams',
        'text=Projects',
        'text=Track',
        'text=Earn',
        'text=Learn',
        'text=Social',
        'text=Settings'
      ];

      for (const selector of cardSelectors) {
        const card = page.locator(selector).first();
        const isVisible = await card.isVisible();
        console.log(`  ${selector}: ${isVisible ? '✅ Visible' : '❌ Not visible'}`);

        if (isVisible) {
          // Test card click
          console.log(`    Testing click on ${selector}...`);
          await card.click();
          await page.waitForLoadState('networkidle');
          await page.waitForTimeout(2000);

          const newUrl = page.url();
          console.log(`    Navigated to: ${newUrl}`);

          // Check page content
          const pageContent = await page.textContent('body');
          console.log(`    Page content length: ${pageContent.length}`);

          // Go back to navigation
          await page.goto(SITE_URL);
          await page.waitForLoadState('networkidle');
          await page.waitForTimeout(1000);

          // Return to grid view
          await page.keyboard.press('ArrowDown');
          await page.keyboard.press('ArrowDown');
          await page.waitForTimeout(1000);
        }
      }

    } else {
      console.log('❌ No experimental navigation canvas found');

      // Test regular navigation
      console.log('🔗 Testing regular navigation links...');

      const navLinks = await page.locator('nav a, .nav a, [role="navigation"] a').count();
      console.log(`Regular navigation links found: ${navLinks}`);

      // Test direct navigation to different pages
      const pages = [
        { url: '/teams', name: 'Teams' },
        { url: '/projects', name: 'Projects' },
        { url: '/track', name: 'Track' },
        { url: '/earn', name: 'Earn' },
        { url: '/learn', name: 'Learn' },
        { url: '/social', name: 'Social' },
        { url: '/settings', name: 'Settings' }
      ];

      for (const pageInfo of pages) {
        console.log(`Testing ${pageInfo.name} page...`);

        await page.goto(`${SITE_URL}${pageInfo.url}`);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);

        const content = await page.textContent('body');
        console.log(`  ${pageInfo.name} content length: ${content.length}`);

        // Check for page-specific content
        const hasPageContent = content.toLowerCase().includes(pageInfo.name.toLowerCase());
        console.log(`  Has ${pageInfo.name} content: ${hasPageContent}`);
      }
    }
  });

  test('should test teams page navigation and alliance features', async ({ page }) => {
    console.log('🏰 Testing teams page navigation and alliance features...');

    await authenticate(page);

    // Navigate to teams page with test mode
    await page.goto(`${SITE_URL}/teams?test_mode=true&skip_onboarding=true`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000); // Give more time for React to load

    console.log('📊 Analyzing teams page content...');

    const content = await page.textContent('body');
    console.log(`Teams page content length: ${content.length}`);
    console.log(`Content preview: ${content.substring(0, 300)}...`);

    // Check for team-related elements
    const teamElements = [
      'team', 'Team', 'alliance', 'Alliance', 'create', 'Create',
      'manage', 'Manage', 'join', 'Join', 'member', 'Member'
    ];

    console.log('🔍 Checking for team-related content...');
    for (const element of teamElements) {
      const found = content.includes(element);
      console.log(`  "${element}": ${found}`);
    }

    // Look for interactive elements
    const buttons = await page.locator('button').count();
    const links = await page.locator('a').count();
    const forms = await page.locator('form').count();

    console.log(`Interactive elements:`);
    console.log(`  Buttons: ${buttons}`);
    console.log(`  Links: ${links}`);
    console.log(`  Forms: ${forms}`);

    // Test alliance management routes
    console.log('⚔️ Testing alliance management routes...');

    const allianceRoutes = [
      '/teams/create',
      '/teams/test-id/manage',
      '/teams/test-id/members',
      '/teams/test-id/settings'
    ];

    for (const route of allianceRoutes) {
      console.log(`Testing route: ${route}`);

      await page.goto(`${SITE_URL}${route}`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);

      const routeContent = await page.textContent('body');
      console.log(`  Content length: ${routeContent.length}`);

      // Check for alliance management content
      const allianceIndicators = [
        'Alliance Information',
        'Business Entity',
        'Alliance Permissions',
        'Register Business',
        'Manage Alliance',
        'Team Members',
        'Alliance Settings'
      ];

      let foundIndicators = 0;
      for (const indicator of allianceIndicators) {
        if (routeContent.includes(indicator)) {
          foundIndicators++;
          console.log(`    ✅ Found: ${indicator}`);
        }
      }

      console.log(`  Alliance indicators found: ${foundIndicators}/${allianceIndicators.length}`);
    }
  });

  test('should test all major page routes and content loading', async ({ page }) => {
    console.log('🌐 Testing all major page routes...');

    await authenticate(page);

    // Test all major routes
    const routes = [
      { path: '/', name: 'Home', expectedContent: ['royaltea', 'dashboard'] },
      { path: '/teams', name: 'Teams', expectedContent: ['team', 'alliance'] },
      { path: '/projects', name: 'Projects', expectedContent: ['project', 'create'] },
      { path: '/track', name: 'Track', expectedContent: ['track', 'contribution'] },
      { path: '/earn', name: 'Earn', expectedContent: ['earn', 'revenue'] },
      { path: '/learn', name: 'Learn', expectedContent: ['learn', 'skill'] },
      { path: '/social', name: 'Social', expectedContent: ['social', 'friend'] },
      { path: '/settings', name: 'Settings', expectedContent: ['setting', 'profile'] },
      { path: '/help', name: 'Help', expectedContent: ['help', 'support'] }
    ];

    for (const route of routes) {
      console.log(`\n📄 Testing ${route.name} page (${route.path})...`);

      await page.goto(`${SITE_URL}${route.path}`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);

      const content = await page.textContent('body');
      console.log(`  Content length: ${content.length}`);

      // Check for expected content
      let foundContent = 0;
      for (const expected of route.expectedContent) {
        if (content.toLowerCase().includes(expected.toLowerCase())) {
          foundContent++;
          console.log(`    ✅ Found: ${expected}`);
        } else {
          console.log(`    ❌ Missing: ${expected}`);
        }
      }

      console.log(`  Expected content found: ${foundContent}/${route.expectedContent.length}`);

      // Check for React components
      const hasReactContent = content.length > 500; // Reasonable threshold
      console.log(`  Has substantial content: ${hasReactContent}`);

      // Check for loading states
      const hasLoadingIndicators = content.includes('Loading') || content.includes('loading');
      console.log(`  Has loading indicators: ${hasLoadingIndicators}`);

      // Check for error states
      const hasErrors = content.includes('Error') || content.includes('error') || content.includes('404');
      console.log(`  Has error indicators: ${hasErrors}`);
    }
  });
});
