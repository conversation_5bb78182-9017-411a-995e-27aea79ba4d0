import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { format, subDays, startOfMonth, endOfMonth } from 'date-fns';
import { toast } from 'react-hot-toast';
import { Card, CardBody, CardHeader, Button } from '../ui/heroui';

/**
 * ValidationMetricsDashboard Component
 *
 * Displays analytics and metrics for contribution validations
 * @param {Object} props - Component props
 * @param {string} props.projectId - Optional project ID to filter metrics by project
 */
const ValidationMetricsDashboard = ({ projectId }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dateRange, setDateRange] = useState('30days');
  const [metrics, setMetrics] = useState({
    totalValidations: 0,
    approvalRate: 0,
    rejectionRate: 0,
    changesRequestedRate: 0,
    averageValidationTime: 0,
    validationsByStatus: {},
    validationsByValidator: {},
    validationTrend: {},
    pendingValidations: 0
  });

  // Calculate date range based on selection
  const getDateRange = () => {
    const now = new Date();

    switch (dateRange) {
      case '7days':
        return { start: subDays(now, 7), end: now };
      case '30days':
        return { start: subDays(now, 30), end: now };
      case '90days':
        return { start: subDays(now, 90), end: now };
      case 'thisMonth':
        return { start: startOfMonth(now), end: endOfMonth(now) };
      default:
        return { start: subDays(now, 30), end: now };
    }
  };

  // Fetch validation metrics
  useEffect(() => {
    const fetchValidationMetrics = async () => {
      if (!currentUser) return;

      setLoading(true);
      try {
        const { start, end } = getDateRange();

        // Format dates for Supabase query
        const startDate = format(start, 'yyyy-MM-dd');
        const endDate = format(end, 'yyyy-MM-dd');

        // Base query for validations
        let query = supabase
          .from('contribution_validations')
          .select(`
            id,
            contribution_id,
            validator_id,
            status,
            created_at,
            updated_at,
            contributions(
              id,
              project_id,
              user_id,
              validation_status,
              created_at
            )
          `)
          .gte('created_at', startDate)
          .lte('created_at', endDate);

        // Add project filter if provided
        if (projectId) {
          query = query.eq('contributions.project_id', projectId);
        }

        // Execute query
        const { data: validations, error: validationsError } = await query;

        if (validationsError) {
          throw validationsError;
        }

        // Get pending contributions
        let pendingQuery = supabase
          .from('contributions')
          .select('id, validation_status')
          .eq('validation_status', 'pending');

        if (projectId) {
          pendingQuery = pendingQuery.eq('project_id', projectId);
        }

        const { data: pendingContributions, error: pendingError } = await pendingQuery;

        if (pendingError) {
          console.warn('Error fetching pending contributions:', pendingError);
        }

        // Calculate metrics
        const totalValidations = validations.length;

        // Count validations by status
        const validationsByStatus = validations.reduce((acc, v) => {
          acc[v.status] = (acc[v.status] || 0) + 1;
          return acc;
        }, {});

        // Calculate rates
        const approvalRate = totalValidations > 0
          ? ((validationsByStatus['approved'] || 0) / totalValidations) * 100
          : 0;

        const rejectionRate = totalValidations > 0
          ? ((validationsByStatus['rejected'] || 0) / totalValidations) * 100
          : 0;

        const changesRequestedRate = totalValidations > 0
          ? ((validationsByStatus['pending_changes'] || 0) / totalValidations) * 100
          : 0;

        // Group validations by validator
        const validationsByValidator = {};
        for (const validation of validations) {
          if (!validationsByValidator[validation.validator_id]) {
            validationsByValidator[validation.validator_id] = {
              count: 0,
              approved: 0,
              rejected: 0,
              pending_changes: 0,
              validator_id: validation.validator_id
            };
          }

          validationsByValidator[validation.validator_id].count++;
          validationsByValidator[validation.validator_id][validation.status] =
            (validationsByValidator[validation.validator_id][validation.status] || 0) + 1;
        }

        // Calculate validation time (time between contribution creation and validation)
        let totalValidationTime = 0;
        let validationTimeCount = 0;

        for (const validation of validations) {
          if (validation.contributions && validation.contributions.created_at) {
            const contributionDate = new Date(validation.contributions.created_at);
            const validationDate = new Date(validation.created_at);
            const timeDiff = validationDate - contributionDate;

            // Only count if the difference is positive and less than 30 days (to filter out outliers)
            if (timeDiff > 0 && timeDiff < 30 * 24 * 60 * 60 * 1000) {
              totalValidationTime += timeDiff;
              validationTimeCount++;
            }
          }
        }

        const averageValidationTime = validationTimeCount > 0
          ? totalValidationTime / validationTimeCount
          : 0;

        // Group validations by date for trend analysis
        const validationTrend = {};
        for (const validation of validations) {
          const date = format(new Date(validation.created_at), 'yyyy-MM-dd');

          if (!validationTrend[date]) {
            validationTrend[date] = {
              total: 0,
              approved: 0,
              rejected: 0,
              pending_changes: 0
            };
          }

          validationTrend[date].total++;
          validationTrend[date][validation.status] =
            (validationTrend[date][validation.status] || 0) + 1;
        }

        // Get validator details
        const validatorIds = Object.keys(validationsByValidator);
        if (validatorIds.length > 0) {
          const { data: validators, error: validatorsError } = await supabase
            .from('users')
            .select('id, display_name, email')
            .in('id', validatorIds);

          if (!validatorsError && validators) {
            validators.forEach(validator => {
              if (validationsByValidator[validator.id]) {
                validationsByValidator[validator.id].name = validator.display_name || validator.email;
              }
            });
          }
        }

        // Set metrics
        setMetrics({
          totalValidations,
          approvalRate,
          rejectionRate,
          changesRequestedRate,
          averageValidationTime,
          validationsByStatus,
          validationsByValidator,
          validationTrend,
          pendingValidations: pendingContributions ? pendingContributions.length : 0
        });

      } catch (err) {
        console.error('Error fetching validation metrics:', err);
        setError('Failed to load validation metrics');
        toast.error('Failed to load validation metrics');
      } finally {
        setLoading(false);
      }
    };

    fetchValidationMetrics();
  }, [currentUser, projectId, dateRange]);

  // Format time in hours, minutes
  const formatTime = (milliseconds) => {
    if (!milliseconds) return '0 min';

    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}d ${hours % 24}h`;
    } else if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else {
      return `${minutes}m`;
    }
  };

  // Format percentage
  const formatPercentage = (value) => {
    return `${Math.round(value)}%`;
  };

  // Get trend data for chart
  const getTrendData = () => {
    const dates = Object.keys(metrics.validationTrend).sort();
    return dates.map(date => ({
      date,
      ...metrics.validationTrend[date]
    }));
  };

  // Handle date range change
  const handleDateRangeChange = (range) => {
    setDateRange(range);
  };

  if (loading) {
    return (
      <div className="validation-metrics-dashboard loading">
        <div className="loading-spinner">
          <i className="bi bi-arrow-repeat spinning"></i>
          <span>Loading validation metrics...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="validation-metrics-dashboard error">
        <div className="error-message">
          <i className="bi bi-exclamation-triangle"></i>
          <span>{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="validation-metrics-dashboard">
      <div className="dashboard-header">
        <h2>Validation Metrics</h2>
        <div className="date-range-selector">
          <button
            className={`date-range-btn ${dateRange === '7days' ? 'active' : ''}`}
            onClick={() => handleDateRangeChange('7days')}
          >
            7 Days
          </button>
          <button
            className={`date-range-btn ${dateRange === '30days' ? 'active' : ''}`}
            onClick={() => handleDateRangeChange('30days')}
          >
            30 Days
          </button>
          <button
            className={`date-range-btn ${dateRange === '90days' ? 'active' : ''}`}
            onClick={() => handleDateRangeChange('90days')}
          >
            90 Days
          </button>
          <button
            className={`date-range-btn ${dateRange === 'thisMonth' ? 'active' : ''}`}
            onClick={() => handleDateRangeChange('thisMonth')}
          >
            This Month
          </button>
        </div>
      </div>

      <div className="metrics-grid">
        {/* Total Validations Metric */}
        <div className="metric-card">
          <div className="metric-icon">
            <i className="bi bi-check2-all"></i>
          </div>
          <div className="metric-content">
            <div className="metric-value">{metrics.totalValidations}</div>
            <div className="metric-label">Total Validations</div>
          </div>
        </div>

        {/* Pending Validations Metric */}
        <div className="metric-card">
          <div className="metric-icon">
            <i className="bi bi-hourglass-split"></i>
          </div>
          <div className="metric-content">
            <div className="metric-value">{metrics.pendingValidations}</div>
            <div className="metric-label">Pending Validations</div>
          </div>
        </div>

        {/* Approval Rate Metric */}
        <div className="metric-card">
          <div className="metric-icon">
            <i className="bi bi-check-circle"></i>
          </div>
          <div className="metric-content">
            <div className="metric-value">{formatPercentage(metrics.approvalRate)}</div>
            <div className="metric-label">Approval Rate</div>
            <div className="metric-progress">
              <div
                className="metric-progress-bar approval"
                style={{ width: formatPercentage(metrics.approvalRate) }}
              ></div>
            </div>
          </div>
        </div>

        {/* Rejection Rate Metric */}
        <div className="metric-card">
          <div className="metric-icon">
            <i className="bi bi-x-circle"></i>
          </div>
          <div className="metric-content">
            <div className="metric-value">{formatPercentage(metrics.rejectionRate)}</div>
            <div className="metric-label">Rejection Rate</div>
            <div className="metric-progress">
              <div
                className="metric-progress-bar rejection"
                style={{ width: formatPercentage(metrics.rejectionRate) }}
              ></div>
            </div>
          </div>
        </div>

        {/* Changes Requested Rate Metric */}
        <div className="metric-card">
          <div className="metric-icon">
            <i className="bi bi-pencil-square"></i>
          </div>
          <div className="metric-content">
            <div className="metric-value">{formatPercentage(metrics.changesRequestedRate)}</div>
            <div className="metric-label">Changes Requested Rate</div>
            <div className="metric-progress">
              <div
                className="metric-progress-bar changes"
                style={{ width: formatPercentage(metrics.changesRequestedRate) }}
              ></div>
            </div>
          </div>
        </div>

        {/* Average Validation Time Metric */}
        <div className="metric-card">
          <div className="metric-icon">
            <i className="bi bi-clock-history"></i>
          </div>
          <div className="metric-content">
            <div className="metric-value">{formatTime(metrics.averageValidationTime)}</div>
            <div className="metric-label">Avg. Validation Time</div>
          </div>
        </div>
      </div>

      {/* Validation Status Distribution */}
      {metrics.totalValidations > 0 && (
        <div className="metrics-section">
          <h3 className="section-title">Validation Status Distribution</h3>
          <div className="status-distribution">
            <div className="distribution-chart">
              {Object.entries(metrics.validationsByStatus).map(([status, count]) => {
                const percentage = (count / metrics.totalValidations) * 100;
                const statusColors = {
                  approved: 'var(--success-color)',
                  rejected: 'var(--danger-color)',
                  pending_changes: 'var(--info-color)',
                  pending: 'var(--warning-color)'
                };

                const statusLabels = {
                  approved: 'Approved',
                  rejected: 'Rejected',
                  pending_changes: 'Changes Requested',
                  pending: 'Pending'
                };

                return (
                  <div key={status} className="distribution-bar-container">
                    <div className="distribution-label">
                      {statusLabels[status] || status}
                    </div>
                    <div className="distribution-bar">
                      <div
                        className="distribution-bar-fill"
                        style={{
                          width: `${percentage}%`,
                          backgroundColor: statusColors[status] || 'var(--secondary-color)'
                        }}
                      ></div>
                    </div>
                    <div className="distribution-value">
                      {count} ({Math.round(percentage)}%)
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}

      {/* Validation Trend */}
      {Object.keys(metrics.validationTrend).length > 0 && (
        <div className="metrics-section">
          <h3 className="section-title">Validation Trend</h3>
          <div className="trend-chart">
            <div className="trend-chart-container">
              {getTrendData().map((day, index) => (
                <div key={day.date} className="trend-day">
                  <div className="trend-bars">
                    {day.approved > 0 && (
                      <div
                        className="trend-bar approved"
                        style={{ height: `${(day.approved / Math.max(...getTrendData().map(d => d.total))) * 100}%` }}
                        title={`${day.approved} approved on ${day.date}`}
                      ></div>
                    )}
                    {day.rejected > 0 && (
                      <div
                        className="trend-bar rejected"
                        style={{ height: `${(day.rejected / Math.max(...getTrendData().map(d => d.total))) * 100}%` }}
                        title={`${day.rejected} rejected on ${day.date}`}
                      ></div>
                    )}
                    {day.pending_changes > 0 && (
                      <div
                        className="trend-bar changes"
                        style={{ height: `${(day.pending_changes / Math.max(...getTrendData().map(d => d.total))) * 100}%` }}
                        title={`${day.pending_changes} changes requested on ${day.date}`}
                      ></div>
                    )}
                  </div>
                  <div className="trend-date">
                    {format(new Date(day.date), 'MM/dd')}
                  </div>
                </div>
              ))}
            </div>
            <div className="trend-legend">
              <div className="legend-item">
                <div className="legend-color approved"></div>
                <div className="legend-label">Approved</div>
              </div>
              <div className="legend-item">
                <div className="legend-color rejected"></div>
                <div className="legend-label">Rejected</div>
              </div>
              <div className="legend-item">
                <div className="legend-color changes"></div>
                <div className="legend-label">Changes Requested</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Validator Performance */}
      {Object.keys(metrics.validationsByValidator).length > 0 && (
        <div className="metrics-section">
          <h3 className="section-title">Validator Performance</h3>
          <div className="validator-table-container">
            <table className="validator-table">
              <thead>
                <tr>
                  <th>Validator</th>
                  <th>Total</th>
                  <th>Approved</th>
                  <th>Rejected</th>
                  <th>Changes</th>
                  <th>Approval Rate</th>
                </tr>
              </thead>
              <tbody>
                {Object.values(metrics.validationsByValidator)
                  .sort((a, b) => b.count - a.count)
                  .map(validator => {
                    const approvalRate = validator.count > 0
                      ? (validator.approved / validator.count) * 100
                      : 0;

                    return (
                      <tr key={validator.validator_id}>
                        <td>{validator.name || 'Unknown'}</td>
                        <td>{validator.count}</td>
                        <td>{validator.approved || 0}</td>
                        <td>{validator.rejected || 0}</td>
                        <td>{validator.pending_changes || 0}</td>
                        <td>
                          <div className="table-progress">
                            <div
                              className="table-progress-bar"
                              style={{ width: `${approvalRate}%` }}
                            ></div>
                            <span>{Math.round(approvalRate)}%</span>
                          </div>
                        </td>
                      </tr>
                    );
                  })
                }
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default ValidationMetricsDashboard;
