// Deep Dive Navigation Test - Focus on Dragging and Card Interactions
const { chromium } = require('playwright');

const SITE_URL = 'https://royalty.technology';

async function testNavigationDeepDive() {
  console.log('🔬 DEEP DIVE NAVIGATION TEST\n');
  console.log('Focusing on dragging, card interactions, and view transitions...\n');
  
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 500
  });
  const context = await browser.newContext({
    viewport: { width: 1920, height: 1080 }
  });
  const page = await context.newPage();
  
  try {
    // === AUTHENTICATION ===
    console.log('🔐 Authenticating...');
    await page.goto(SITE_URL);
    await page.waitForLoadState('networkidle');
    
    const emailInput = page.locator('input[type="email"]').first();
    const needsAuth = await emailInput.isVisible();
    
    if (needsAuth) {
      await emailInput.fill('<EMAIL>');
      await page.fill('input[type="password"]', 'TestPassword123!');
      await page.click('button[type="submit"]');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);
    }
    
    console.log('✅ Authentication completed\n');
    
    // === DETAILED ELEMENT INSPECTION ===
    console.log('🔍 Detailed Element Inspection...');
    
    // Get all elements with various selectors
    const allElements = await page.$$('*');
    console.log(`Total DOM elements: ${allElements.length}`);
    
    // Check for specific navigation-related classes and attributes
    const navigationSelectors = [
      '[class*="canvas"]',
      '[class*="card"]', 
      '[class*="grid"]',
      '[class*="overworld"]',
      '[class*="navigation"]',
      '[class*="experimental"]',
      '[data-canvas]',
      '[data-card]',
      '[data-view]',
      '[draggable]',
      '[role="button"]',
      'button',
      '.cursor-pointer',
      '[class*="clickable"]',
      '[class*="interactive"]'
    ];
    
    for (const selector of navigationSelectors) {
      const elements = await page.locator(selector).all();
      if (elements.length > 0) {
        console.log(`${selector}: ${elements.length} elements found`);
        
        // Get details of first few elements
        for (let i = 0; i < Math.min(3, elements.length); i++) {
          try {
            const element = elements[i];
            const tagName = await element.evaluate(el => el.tagName);
            const className = await element.evaluate(el => el.className);
            const id = await element.evaluate(el => el.id);
            const textContent = await element.evaluate(el => el.textContent?.substring(0, 50));
            
            console.log(`  [${i}] ${tagName} - class:"${className}" id:"${id}" text:"${textContent}"`);
          } catch (e) {
            console.log(`  [${i}] Error getting element details: ${e.message}`);
          }
        }
      }
    }
    
    // === CANVAS SYSTEM INVESTIGATION ===
    console.log('\n🎯 Canvas System Investigation...');
    
    // Look for ExperimentalNavigation component
    const hasExperimentalNav = await page.locator('[class*="ExperimentalNavigation"], [data-component="ExperimentalNavigation"]').count();
    console.log(`ExperimentalNavigation component: ${hasExperimentalNav > 0 ? '✅' : '❌'}`);
    
    // Check for canvas definitions
    const canvasNames = ['home', 'start', 'track', 'earn', 'projects', 'teams', 'contributions', 'kanban'];
    const foundCanvasElements = {};
    
    for (const canvasName of canvasNames) {
      const selectors = [
        `[data-canvas="${canvasName}"]`,
        `[class*="${canvasName}"]`,
        `[id*="${canvasName}"]`,
        `[aria-label*="${canvasName}"]`
      ];
      
      let found = false;
      for (const selector of selectors) {
        const count = await page.locator(selector).count();
        if (count > 0) {
          found = true;
          foundCanvasElements[canvasName] = { selector, count };
          break;
        }
      }
      
      console.log(`Canvas "${canvasName}": ${found ? '✅' : '❌'}`);
    }
    
    // === DRAG TESTING WITH SPECIFIC ELEMENTS ===
    console.log('\n🖱️ Advanced Drag Testing...');
    
    // Test dragging on different elements
    const dragTargets = [
      'body',
      '[class*="canvas"]',
      '[class*="grid"]',
      '[class*="navigation"]',
      'main',
      '[role="main"]'
    ];
    
    for (const target of dragTargets) {
      try {
        const element = page.locator(target).first();
        const isVisible = await element.isVisible();
        
        if (isVisible) {
          console.log(`Testing drag on: ${target}`);
          
          const box = await element.boundingBox();
          if (box) {
            const startX = box.x + box.width / 2;
            const startY = box.y + box.height / 2;
            const endX = startX + 100;
            const endY = startY + 50;
            
            // Test drag
            await page.mouse.move(startX, startY);
            await page.mouse.down();
            await page.waitForTimeout(200);
            await page.mouse.move(endX, endY, { steps: 5 });
            await page.waitForTimeout(200);
            await page.mouse.up();
            
            console.log(`  ✅ Drag completed on ${target}`);
            await page.waitForTimeout(1000);
          }
        }
      } catch (error) {
        console.log(`  ❌ Drag failed on ${target}: ${error.message}`);
      }
    }
    
    // === VIEW MODE SWITCHING TEST ===
    console.log('\n🔄 View Mode Switching Test...');
    
    // Look for view mode controls
    const viewControls = [
      '[data-view]',
      '[class*="zoom"]',
      '[class*="view"]',
      '[class*="mode"]',
      'button[class*="grid"]',
      'button[class*="overworld"]',
      'button[class*="content"]'
    ];
    
    for (const control of viewControls) {
      const elements = await page.locator(control).all();
      if (elements.length > 0) {
        console.log(`Found ${elements.length} view controls: ${control}`);
        
        // Try clicking the first one
        try {
          await elements[0].click();
          await page.waitForTimeout(1000);
          console.log(`  ✅ Clicked view control`);
        } catch (error) {
          console.log(`  ❌ Failed to click view control: ${error.message}`);
        }
      }
    }
    
    // === CARD RESIZING TEST ===
    console.log('\n📏 Card Resizing Test...');
    
    // Look for resizable elements
    const resizableSelectors = [
      '[class*="resize"]',
      '[style*="resize"]',
      '[class*="card"]',
      '[data-canvas]'
    ];
    
    for (const selector of resizableSelectors) {
      const elements = await page.locator(selector).all();
      if (elements.length > 0) {
        console.log(`Found ${elements.length} potentially resizable elements: ${selector}`);
        
        // Test if any have resize handles
        for (let i = 0; i < Math.min(2, elements.length); i++) {
          try {
            const element = elements[i];
            const box = await element.boundingBox();
            
            if (box) {
              // Try dragging from bottom-right corner (typical resize handle location)
              const resizeX = box.x + box.width - 5;
              const resizeY = box.y + box.height - 5;
              
              await page.mouse.move(resizeX, resizeY);
              const cursor = await page.evaluate(() => document.elementFromPoint(arguments[0], arguments[1])?.style.cursor, resizeX, resizeY);
              
              if (cursor && (cursor.includes('resize') || cursor.includes('nw') || cursor.includes('se'))) {
                console.log(`  ✅ Resize cursor detected on element ${i}`);
                
                // Attempt resize
                await page.mouse.down();
                await page.mouse.move(resizeX + 50, resizeY + 50, { steps: 3 });
                await page.mouse.up();
                console.log(`  ✅ Resize operation attempted`);
              } else {
                console.log(`  ❌ No resize cursor on element ${i}`);
              }
            }
          } catch (error) {
            console.log(`  ❌ Resize test failed on element ${i}: ${error.message}`);
          }
        }
      }
    }
    
    // === FULL-SCREEN DRAG TEST ===
    console.log('\n🖥️ Full-Screen Drag Test...');
    
    // Test dragging cards to full screen
    const cardElements = await page.locator('[class*="card"], [data-canvas]').all();
    
    if (cardElements.length > 0) {
      console.log(`Found ${cardElements.length} card elements for full-screen test`);
      
      try {
        const card = cardElements[0];
        const cardBox = await card.boundingBox();
        
        if (cardBox) {
          const startX = cardBox.x + cardBox.width / 2;
          const startY = cardBox.y + cardBox.height / 2;
          
          // Drag to center of screen (simulating full-screen drag)
          const centerX = 960;
          const centerY = 540;
          
          console.log(`Dragging card from (${startX}, ${startY}) to center (${centerX}, ${centerY})`);
          
          await page.mouse.move(startX, startY);
          await page.mouse.down();
          await page.waitForTimeout(300);
          await page.mouse.move(centerX, centerY, { steps: 10 });
          await page.waitForTimeout(300);
          await page.mouse.up();
          
          await page.waitForTimeout(2000);
          
          // Check if navigation occurred
          const currentUrl = page.url();
          console.log(`URL after full-screen drag: ${currentUrl}`);
          
          // Check if content changed
          const content = await page.textContent('body');
          const hasCanvasContent = content.includes('Canvas') || content.includes('canvas');
          console.log(`Canvas content after drag: ${hasCanvasContent ? '✅' : '❌'}`);
        }
      } catch (error) {
        console.log(`❌ Full-screen drag test failed: ${error.message}`);
      }
    } else {
      console.log('❌ No card elements found for full-screen test');
    }
    
    // === SUMMARY ===
    console.log('\n📊 DEEP DIVE SUMMARY:');
    console.log(`- Canvas elements found: ${Object.keys(foundCanvasElements).length}`);
    console.log(`- Draggable functionality: Limited`);
    console.log(`- Card interactions: Needs investigation`);
    console.log(`- View mode switching: Needs implementation`);
    console.log(`- Resize functionality: Not detected`);
    
  } catch (error) {
    console.error('❌ Deep dive test failed:', error.message);
  } finally {
    console.log('\n🏁 Deep dive completed. Browser remains open for manual inspection...');
    // Keep browser open for manual inspection
    // await browser.close();
  }
}

testNavigationDeepDive();
