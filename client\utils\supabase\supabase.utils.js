import { createClient } from '@supabase/supabase-js';
// Simple visibility handler functions
const isPageVisible = () => {
  if (typeof document !== 'undefined') {
    return document.visibilityState === 'visible';
  }
  return true; // Default to visible in non-browser environments
};

const queueOperation = (operation) => {
  // Simple implementation - just run the operation if visible
  if (isPageVisible()) {
    operation();
  } else {
    console.log('Page not visible, operation would be queued');
    // In a real implementation, we would queue this for later
    // For now, just run it anyway
    operation();
  }
};

// Initialize Supabase client
// Use import.meta.env for Vite compatibility
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Debug helper
const logSupabaseInfo = (message, data) => {
  console.log(`[Supabase] ${message}`, data);
};

// Log current URL and environment
logSupabaseInfo('Current URL from env', supabaseUrl);
logSupabaseInfo('Environment mode', import.meta.env.MODE);

// Check for missing environment variables
if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables');
  console.log('VITE_SUPABASE_URL:', supabaseUrl);
  console.log('VITE_SUPABASE_ANON_KEY:', supabaseAnonKey ? 'Set' : 'Missing');
  console.log('Please set VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY in your .env file');

  // Throw error in production to prevent silent failures
  if (import.meta.env.MODE === 'production') {
    throw new Error('Supabase environment variables are required in production');
  }
}

// Determine the correct URL to use
let finalSupabaseUrl;

// Always use the direct Supabase project URL for API access
finalSupabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
logSupabaseInfo('Using Supabase project URL directly', finalSupabaseUrl);

// For auth domain, we'll still use the custom domain in the auth config below

// Create the Supabase client
export const supabase = createClient(
  finalSupabaseUrl,
  supabaseAnonKey || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MjA1NTksImV4cCI6MjA1OTM5NjU1OX0.e0oaDNVbA563SZJHPm6UTFOXcLbWorKn42a_brChpKQ',
  {
    auth: {
      flowType: 'pkce',
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true
      // Removed custom auth domain - using default Supabase auth domain
      // Custom auth domains require additional DNS and SSL configuration
    }
  }
);

// Log the final Supabase configuration
logSupabaseInfo('Supabase client initialized with URL', supabase.supabaseUrl);

// User-related functions
export const getCurrentUser = async () => {
  const { data: { user } } = await supabase.auth.getUser();
  return user;
};

// Subscribe to realtime changes for a table
export const subscribeToTable = (tableName, callback) => {
  return supabase
    .channel(`public:${tableName}`)
    .on('postgres_changes',
        { event: '*', schema: 'public', table: tableName },
        (payload) => {
          console.log(`Change received for ${tableName}:`, payload);
          callback(payload);
        }
    )
    .subscribe();
};

// Projects functions
export const getProjects = async () => {
  // Check if page is visible
  if (!isPageVisible()) {
    console.log('Page not visible, queueing getProjects operation');
    return new Promise((resolve) => {
      queueOperation(async () => {
        const projects = await getProjects();
        resolve(projects);
      });
    });
  }

  const { data, error } = await supabase
    .from('projects')
    .select('*');

  if (error) {
    console.error('Error fetching projects:', error);
    throw error;
  }

  return data;
};

export const getProject = async (id) => {
  // Check if page is visible
  if (!isPageVisible()) {
    console.log(`Page not visible, queueing getProject operation for ID ${id}`);
    return new Promise((resolve, reject) => {
      queueOperation(async () => {
        try {
          const project = await getProject(id);
          resolve(project);
        } catch (error) {
          reject(error);
        }
      });
    });
  }

  const { data, error } = await supabase
    .from('projects')
    .select(`
      *,
      contributions(*),
      contributions.user:users(*)
    `)
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching project:', error);
    throw error;
  }

  return data;
};

export const createProject = async (projectData) => {
  const { data, error } = await supabase
    .from('projects')
    .insert([projectData])
    .select();

  if (error) {
    console.error('Error creating project:', error);
    throw error;
  }

  return data[0];
};

export const updateProject = async (id, projectData) => {
  const { data, error } = await supabase
    .from('projects')
    .update(projectData)
    .eq('id', id)
    .select();

  if (error) {
    console.error('Error updating project:', error);
    throw error;
  }

  return data[0];
};

export const deleteProject = async (id) => {
  const { error } = await supabase
    .from('projects')
    .delete()
    .eq('id', id);

  if (error) {
    console.error('Error deleting project:', error);
    throw error;
  }

  return true;
};

// Contributions functions
export const getContributions = async () => {
  const { data, error } = await supabase
    .from('contributions')
    .select(`
      *,
      project:projects(*),
      user:users(*)
    `);

  if (error) {
    console.error('Error fetching contributions:', error);
    throw error;
  }

  return data;
};

export const createContribution = async (contributionData) => {
  const { data, error } = await supabase
    .from('contributions')
    .insert([contributionData])
    .select();

  if (error) {
    console.error('Error creating contribution:', error);
    throw error;
  }

  return data[0];
};

// Users functions
export const getUsers = async () => {
  const { data, error } = await supabase
    .from('users')
    .select('*');

  if (error) {
    console.error('Error fetching users:', error);
    throw error;
  }

  return data;
};

export const getUser = async (id) => {
  const { data, error } = await supabase
    .from('users')
    .select(`
      *,
      contributions(*),
      projects:projects(*)
    `)
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching user:', error);
    throw error;
  }

  return data;
};
