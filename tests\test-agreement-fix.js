// Test script to verify the agreement generation fix
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

// Initialize Supabase client with the production URL and service role key
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzgyMDU1OSwiZXhwIjoyMDU5Mzk2NTU5fQ.k5xvFBzBxNMCtPd0d8Ko9dAcT5Y3op3ZgFvlAf85LLs';
const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log('Testing agreement generation fix...\n');

    // Get the Dream Project from Supabase
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('*')
      .eq('name', 'Dream Project')
      .single();

    if (projectError) {
      console.error('Error fetching Dream Project:', projectError);
      return;
    }

    if (!project) {
      console.log('Dream Project not found');
      return;
    }

    console.log(`Testing with project: ${project.name} (${project.id})`);
    console.log(`Project type: ${project.project_type}`);
    console.log(`Description: ${project.description || 'No description'}`);

    // Get the existing agreement for the Dream Project
    const { data: agreements, error: agreementsError } = await supabase
      .from('contributor_agreements')
      .select('*')
      .eq('project_id', project.id);

    if (agreementsError) {
      console.error('Error fetching agreements:', agreementsError);
      return;
    }

    if (!agreements || agreements.length === 0) {
      console.log('No agreements found for Dream Project');
      return;
    }

    console.log(`\nFound ${agreements.length} agreements for Dream Project`);

    // Check the first agreement for Village references
    const agreement = agreements[0];
    console.log(`\nChecking agreement ID: ${agreement.id}`);
    console.log(`Version: ${agreement.version || 1}`);
    console.log(`Status: ${agreement.status || 'Unknown'}`);

    // Check for Village references
    console.log('\nChecking for Village references in the agreement...');
    const villageReferences = checkForVillageReferences(agreement.agreement_text);

    if (villageReferences.length > 0) {
      console.log(`⚠️ Found ${villageReferences.length} Village references:`);
      villageReferences.forEach((ref, index) => {
        if (index < 5) { // Only show the first 5 references
          console.log(`\n${index + 1}. Line ${ref.lineNumber}: ${ref.line}`);
          console.log(`   Context: ${ref.context}`);
        }
      });

      if (villageReferences.length > 5) {
        console.log(`\n... and ${villageReferences.length - 5} more references`);
      }

      // Save the agreement text to a file for inspection
      fs.writeFileSync('agreement-with-village-refs.md', agreement.agreement_text);
      console.log('\nAgreement text saved to agreement-with-village-refs.md for inspection');

      // Create a simple test to verify our replacement logic
      console.log('\nTesting replacement logic...');
      const testText = `
This is a test agreement for Village of The Ages, a village simulation game.
It includes references to village building, villagers, and neighboring villages.
The Village of the Ages (VOTA) is a game about managing a village.
`;

      // Apply our replacement logic
      let cleanedText = testText;

      // Project name and description for testing
      const projectName = 'Dream Project';
      const projectDescription = 'A collaborative game development project';
      const projectType = 'game';

      // Replace Village of The Ages references
      const villagePatterns = [
        /Village of The Ages/gi,
        /Village of the Ages/gi,
        /\bVOTA\b/gi,
        /\bvillage simulation game\b/gi,
        /\bvillage building\b/gi,
        /\bvillagers\b/gi,
        /\bneighboring villages\b/gi,
        /\bvillage\b/gi
      ];

      // Replace all village patterns with appropriate project-specific content
      villagePatterns.forEach(pattern => {
        if (pattern.toString().includes('village simulation game')) {
          cleanedText = cleanedText.replace(pattern, projectDescription);
        } else if (pattern.toString().includes('villagers')) {
          cleanedText = cleanedText.replace(pattern, 'players');
        } else if (pattern.toString().includes('neighboring villages')) {
          cleanedText = cleanedText.replace(pattern, 'external partners');
        } else if (pattern.toString().includes('village building')) {
          cleanedText = cleanedText.replace(pattern, 'game building');
        } else if (pattern.toString().includes('Village of The Ages') ||
                   pattern.toString().includes('Village of the Ages') ||
                   pattern.toString().includes('VOTA')) {
          cleanedText = cleanedText.replace(pattern, projectName);
        } else if (pattern.toString().includes('village')) {
          cleanedText = cleanedText.replace(pattern, 'game');
        }
      });

      console.log('Original test text:');
      console.log(testText);
      console.log('\nCleaned test text:');
      console.log(cleanedText);

      // Check if any village references remain
      const remainingRefs = checkForVillageReferences(cleanedText);
      if (remainingRefs.length > 0) {
        console.log(`\n⚠️ ${remainingRefs.length} Village references still remain after cleaning`);
      } else {
        console.log('\n✓ All Village references successfully removed!');
      }
    } else {
      console.log('✓ No Village references found in the agreement!');
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

// Helper function to check for Village references
function checkForVillageReferences(text) {
  const lines = text.split('\n');
  const references = [];

  const villagePatterns = [
    /village/i,
    /Village of The Ages/i,
    /Village of the Ages/i,
    /VOTA/i
  ];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    for (const pattern of villagePatterns) {
      if (pattern.test(line)) {
        // Get context (1 line before and after)
        const start = Math.max(0, i - 1);
        const end = Math.min(lines.length - 1, i + 1);
        const context = lines.slice(start, end + 1).join('\n');

        references.push({
          lineNumber: i + 1,
          line: line,
          context: context
        });

        // Only add each line once, even if it matches multiple patterns
        break;
      }
    }
  }

  return references;
}

main();
