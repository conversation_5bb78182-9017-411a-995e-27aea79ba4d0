const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Supabase configuration
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || process.argv[2];

if (!supabaseKey) {
  console.error('Error: Supabase key is required. Please provide it as an environment variable or command line argument.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkProjectActivitiesTable() {
  try {
    // Try to query the project_activities table
    const { data, error } = await supabase
      .from('project_activities')
      .select('id')
      .limit(1);

    if (error) {
      if (error.code === '42P01') {
        console.error('The project_activities table does not exist.');
        return false;
      } else {
        console.error('Error checking project_activities table:', error);
        return false;
      }
    }

    console.log('The project_activities table exists.');
    return true;
  } catch (error) {
    console.error('Unexpected error:', error);
    return false;
  }
}

async function applyMigration() {
  try {
    // Read the migration SQL
    const migrationPath = path.join(__dirname, 'supabase/migrations/20240601000002_create_project_activities_table.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('Migration SQL loaded successfully.');
    console.log('To apply this migration, please follow these steps:');
    console.log('1. Log in to the Supabase dashboard at https://app.supabase.com');
    console.log('2. Select your project (hqqlrrqvjcetoxbdjgzx)');
    console.log('3. Go to the SQL Editor');
    console.log('4. Create a new query');
    console.log('5. Paste the following SQL:');
    console.log('\n' + migrationSQL + '\n');
    console.log('6. Run the query');
    console.log('7. Verify that the project_activities table was created successfully');

    return true;
  } catch (error) {
    console.error('Unexpected error preparing migration:', error);
    return false;
  }
}

async function main() {
  const tableExists = await checkProjectActivitiesTable();

  if (!tableExists) {
    console.log('Preparing migration instructions...');
    await applyMigration();
  }

  process.exit(0);
}

main();
