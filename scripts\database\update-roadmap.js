// Script to update roadmap items in the database
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function updateRoadmapItems() {
  try {
    // Update bulk validation item
    const { data: bulkValidation, error: bulkValidationError } = await supabase
      .from('roadmap_items')
      .update({ completed: true })
      .eq('item_id', '3.2.6')
      .select();

    if (bulkValidationError) {
      console.error('Error updating bulk validation item:', bulkValidationError);
    } else {
      console.log('Bulk validation item updated:', bulkValidation);
    }

    // Update validation notifications item
    const { data: validationNotifications, error: validationNotificationsError } = await supabase
      .from('roadmap_items')
      .update({ completed: true })
      .eq('item_id', '3.2.8')
      .select();

    if (validationNotificationsError) {
      console.error('Error updating validation notifications item:', validationNotificationsError);
    } else {
      console.log('Validation notifications item updated:', validationNotifications);
    }

    // Update analytics dashboard item
    const { data: analyticsDashboard, error: analyticsDashboardError } = await supabase
      .from('roadmap_items')
      .update({ completed: true })
      .eq('item_id', '2.8.8')
      .select();

    if (analyticsDashboardError) {
      console.error('Error updating analytics dashboard item:', analyticsDashboardError);
    } else {
      console.log('Analytics dashboard item updated:', analyticsDashboard);
    }

    console.log('Roadmap updates completed successfully');
  } catch (error) {
    console.error('Error updating roadmap items:', error);
  }
}

// Run the update function
updateRoadmapItems();
