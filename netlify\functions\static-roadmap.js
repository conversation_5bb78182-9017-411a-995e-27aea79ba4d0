// Static Roadmap Function
const fs = require('fs');
const path = require('path');

exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Content-Type': 'application/json'
  };
  
  try {
    // Path to the static roadmap data file
    const staticRoadmapPath = path.join(__dirname, '../../static-roadmap.json');
    
    // Check if the file exists
    if (!fs.existsSync(staticRoadmapPath)) {
      throw new Error('Static roadmap data file not found');
    }
    
    // Read the file
    const staticRoadmapData = fs.readFileSync(staticRoadmapPath, 'utf8');
    
    // Parse the JSON data
    const roadmapData = JSON.parse(staticRoadmapData);
    
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        success: true,
        data: roadmapData.data,
        latest_feature: roadmapData.latest_feature,
        updated_at: roadmapData.updated_at,
        source: 'static-file'
      })
    };
  } catch (error) {
    console.error('Error serving static roadmap data:', error);
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    };
  }
};
