import React, { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { supabase } from '../../../utils/supabase/supabase.utils';
import ContributionValidation from '../../components/contribution/ContributionValidation';
import { toast } from 'react-hot-toast';

const ContributionValidatePage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { currentUser } = useContext(UserContext);
  const [contribution, setContribution] = useState(null);
  const [project, setProject] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch contribution data
  useEffect(() => {
    const fetchContribution = async () => {
      if (!id) {
        setError('Contribution ID is required');
        setLoading(false);
        return;
      }

      try {
        // First, get the contribution data
        const { data: contributionData, error: contributionError } = await supabase
          .from('contributions')
          .select('*')
          .eq('id', id)
          .single();

        if (contributionError) throw contributionError;

        // Then fetch the user data separately
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('id, email, display_name')
          .eq('id', contributionData.user_id)
          .single();

        // And fetch the project data separately
        const { data: projectData, error: projectError } = await supabase
          .from('projects')
          .select('id, name, description, created_by')
          .eq('id', contributionData.project_id)
          .single();

        // Combine the data
        const data = {
          ...contributionData,
          user: userData || null,
          project: projectData || null
        };

        // Handle any errors from the user or project queries
        if (userError) {
          console.error('Error fetching user data:', userError);
          // Continue anyway, we'll just show "Unknown" for the user
        }

        if (projectError) {
          console.error('Error fetching project data:', projectError);
          setError('Failed to load project data');
          setLoading(false);
          return;
        }

        if (!data) {
          setError('Contribution not found');
          setLoading(false);
          return;
        }

        setContribution(data);
        setProject(data.project);
        setLoading(false);
      } catch (error) {
        console.error('Error in fetchContribution:', error);
        setError('An unexpected error occurred');
        setLoading(false);
      }
    };

    fetchContribution();
  }, [id]);

  // Handle validation complete
  const handleValidationComplete = (status) => {
    // Update the local state
    setContribution(prev => ({
      ...prev,
      validation_status: status
    }));

    // Show success message
    toast.success(`Contribution ${status.replace('_', ' ')}`);
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="validate-page loading">
        <div className="loading-spinner"></div>
        <p>Loading contribution data...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="validate-page error">
        <div className="error-container">
          <h2>Error</h2>
          <p>{error}</p>
          <button
            className="btn btn-primary"
            onClick={() => navigate(-1)}
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="validate-page">
      <div className="validate-container">
        <div className="validate-header">
          <h1>Contribution Validation</h1>
          <div className="validate-actions">
            <button
              className="btn btn-outline-secondary"
              onClick={() => navigate(-1)}
            >
              <i className="bi bi-arrow-left"></i> Back
            </button>
            <Link
              to={`/project/${project.id}`}
              className="btn btn-outline-primary"
            >
              <i className="bi bi-folder"></i> Project Page
            </Link>
          </div>
        </div>

        <div className="contribution-details-card">
          <div className="card-header">
            <h2>{contribution.task_name}</h2>
            <div className="contribution-meta">
              <div className="meta-item">
                <span className="meta-label">Contributor:</span>
                <span className="meta-value">{contribution.user?.display_name || contribution.user?.email || 'Unknown'}</span>
              </div>
              <div className="meta-item">
                <span className="meta-label">Date:</span>
                <span className="meta-value">{formatDate(contribution.date_performed || contribution.created_at)}</span>
              </div>
            </div>
          </div>

          <div className="card-body">
            <div className="contribution-stats">
              <div className="stat-item">
                <span className="stat-label">Type:</span>
                <span className="stat-value">{contribution.task_type}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Category:</span>
                <span className="stat-value">{contribution.category}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Difficulty:</span>
                <span className="stat-value">{contribution.difficulty}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">Hours:</span>
                <span className="stat-value">{contribution.hours_spent}</span>
              </div>
            </div>

            {contribution.description && (
              <div className="contribution-description">
                <h3>Description</h3>
                <p>{contribution.description}</p>
              </div>
            )}

            {contribution.has_attachments && contribution.attachments_data && contribution.attachments_data.length > 0 && (
              <div className="contribution-attachments">
                <h3>Attachments</h3>
                <div className="attachments-list">
                  {contribution.attachments_data.map((attachment, index) => {
                    // Determine file type icon
                    let fileIcon = "bi-file-earmark";
                    const fileExt = attachment.name ? attachment.name.split('.').pop().toLowerCase() : '';

                    if (['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'].includes(fileExt)) {
                      fileIcon = "bi-file-earmark-image";
                    } else if (['pdf'].includes(fileExt)) {
                      fileIcon = "bi-file-earmark-pdf";
                    } else if (['doc', 'docx'].includes(fileExt)) {
                      fileIcon = "bi-file-earmark-word";
                    } else if (['xls', 'xlsx'].includes(fileExt)) {
                      fileIcon = "bi-file-earmark-excel";
                    } else if (['ppt', 'pptx'].includes(fileExt)) {
                      fileIcon = "bi-file-earmark-slides";
                    } else if (['zip', 'rar', '7z'].includes(fileExt)) {
                      fileIcon = "bi-file-earmark-zip";
                    } else if (['txt', 'md'].includes(fileExt)) {
                      fileIcon = "bi-file-earmark-text";
                    } else if (['js', 'jsx', 'ts', 'tsx', 'html', 'css', 'json', 'py', 'java', 'c', 'cpp'].includes(fileExt)) {
                      fileIcon = "bi-file-earmark-code";
                    }

                    return (
                      <div key={index} className="attachment-item">
                        <i className={`bi ${fileIcon}`}></i>
                        <span>{attachment.name || `File ${index + 1}`}</span>
                        {attachment.url && (
                          <div className="attachment-actions">
                            <a
                              href={attachment.url}
                              download={attachment.name}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="btn-download"
                              title="Download file"
                            >
                              <i className="bi bi-download"></i>
                            </a>
                            {['jpg', 'jpeg', 'png', 'gif', 'pdf'].includes(fileExt) && (
                              <a
                                href={attachment.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="btn-preview"
                                title="Preview file"
                              >
                                <i className="bi bi-eye"></i>
                              </a>
                            )}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="validation-section">
          <ContributionValidation
            contributionId={contribution.id}
            projectId={contribution.project_id}
            currentStatus={contribution.validation_status || 'pending'}
            onValidationComplete={handleValidationComplete}
          />
        </div>
      </div>
    </div>
  );
};

export default ContributionValidatePage;
