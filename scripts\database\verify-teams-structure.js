// Verify teams table structure for alliance migration
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_KEY);

async function verifyTeamsStructure() {
  console.log('🔍 Verifying Teams Table Structure for Alliance Migration...');
  
  try {
    // Try to create a test team to see the full structure
    const testTeam = {
      name: 'Test Alliance Structure Check',
      description: 'Temporary team to verify table structure',
      alliance_type: 'emerging',
      is_business_entity: false,
      created_by: '2a033231-d173-4292-aa36-90f4d735bcf3' // Using existing user ID
    };

    console.log('\n📝 Attempting to create test team to verify structure...');
    const { data: createdTeam, error: createError } = await supabase
      .from('teams')
      .insert([testTeam])
      .select()
      .single();

    if (createError) {
      console.log('❌ Create error:', createError.message);
      console.log('Error details:', createError);
      
      // Try to get structure another way
      console.log('\n🔍 Attempting alternative structure check...');
      const { data, error } = await supabase
        .from('teams')
        .select('*')
        .limit(0); // Get no rows but should show structure
      
      if (error) {
        console.log('❌ Alternative check failed:', error.message);
      } else {
        console.log('✅ Teams table accessible');
      }
    } else {
      console.log('✅ Test team created successfully!');
      console.log('📋 Full teams table structure:');
      console.log('Columns:', Object.keys(createdTeam));
      console.log('Sample data:', createdTeam);

      // Clean up test team
      console.log('\n🧹 Cleaning up test team...');
      const { error: deleteError } = await supabase
        .from('teams')
        .delete()
        .eq('id', createdTeam.id);

      if (deleteError) {
        console.log('⚠️ Could not delete test team:', deleteError.message);
      } else {
        console.log('✅ Test team cleaned up');
      }
    }

    // Check if we need to add any missing columns
    console.log('\n🔧 Checking for required alliance columns...');
    
    // Check team_members structure too
    console.log('\n📋 Checking team_members structure...');
    const { data: memberData, error: memberError } = await supabase
      .from('team_members')
      .select('*')
      .limit(0);
    
    if (memberError) {
      console.log('❌ Team_members check failed:', memberError.message);
    } else {
      console.log('✅ Team_members table accessible');
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

verifyTeamsStructure();
