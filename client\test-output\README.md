# Agreement Generator Testing

This directory contains the output of the agreement generator tests. The tests generate agreements for different project types and configurations, and verify that they contain the expected content.

## Directory Structure

- `agreements/`: Contains the generated agreement files
  - `game-project-agreement.md`: Agreement for a game project with custom royalty model
  - `music-project-agreement.md`: Agreement for a music project with equal split royalty model
  - `software-project-agreement.md`: Agreement for a software project with task-based royalty model
  - `verification-results.json`: Results of the verification process

## Running the Tests

From the client directory, you can run the following scripts:

### Windows Command Prompt

```
test-agreements.bat         # Generate test agreements
verify-agreements.bat       # Verify generated agreements
test-and-verify-agreements.bat  # Run both in sequence
```

### PowerShell

```
.\test-agreements.ps1         # Generate test agreements
.\verify-agreements.ps1       # Verify generated agreements
.\test-and-verify-agreements.ps1  # Run both in sequence
```

### Node.js

```
node scripts/test-agreement-generator.js  # Generate test agreements
node scripts/verify-agreements.js         # Verify generated agreements
```

## Verification Criteria

The verification script checks for:

1. **Expected Phrases**: Project-specific content that should be present
2. **Unexpected Phrases**: Template content that should be replaced
3. **Placeholders**: Any remaining unreplaced placeholders
4. **Project-Specific Terminology**: Correct terminology based on project type
5. **Exhibits**: Proper formatting of exhibits

## Adding New Test Cases

To add new test cases, edit the `scripts/test-agreement-generator.js` file and add a new entry to the `testCases` array. Then update the verification criteria in `scripts/verify-agreements.js` to match your new test case.

## Troubleshooting

If the verification fails, check the error messages in the console output or the `verification-results.json` file for details on what went wrong. Common issues include:

- Unreplaced placeholders in the agreement
- Missing project-specific content
- Incorrect terminology for the project type
- Missing or improperly formatted exhibits
