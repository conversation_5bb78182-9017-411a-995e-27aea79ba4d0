# Manual Migration Instructions for Projects Table

This migration will fix the issue with the missing `created_at` column in the projects table. Follow these steps to apply the migration manually:

## Step 1: Access the SQL Editor

1. Log in to your Supabase dashboard
2. Select your project
3. Click on "SQL Editor" in the left sidebar
4. Click "New Query" to create a new SQL query

## Step 2: Run the Migration SQL

Copy and paste the following SQL into the editor:

```sql
-- Add created_at and updated_at columns to projects table if they don't exist
DO $$
DECLARE
  created_at_exists BOOLEAN;
  updated_at_exists BOOLEAN;
  title_exists BOOLEAN;
  name_exists BOOLEAN;
BEGIN
  -- Check if created_at column exists
  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'projects'
    AND column_name = 'created_at'
  ) INTO created_at_exists;

  -- Check if updated_at column exists
  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'projects'
    AND column_name = 'updated_at'
  ) INTO updated_at_exists;

  -- Check if title column exists
  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'projects'
    AND column_name = 'title'
  ) INTO title_exists;

  -- Check if name column exists
  SELECT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = 'projects'
    AND column_name = 'name'
  ) INTO name_exists;

  -- Add created_at column if it doesn't exist
  IF NOT created_at_exists THEN
    ALTER TABLE public.projects ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT now();
    RAISE NOTICE 'Added created_at column to projects table';
  ELSE
    RAISE NOTICE 'created_at column already exists in projects table';
  END IF;

  -- Add updated_at column if it doesn't exist
  IF NOT updated_at_exists THEN
    ALTER TABLE public.projects ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT now();
    RAISE NOTICE 'Added updated_at column to projects table';
  ELSE
    RAISE NOTICE 'updated_at column already exists in projects table';
  END IF;

  -- Handle title/name inconsistency
  IF title_exists AND NOT name_exists THEN
    -- Add name column and copy data from title
    ALTER TABLE public.projects ADD COLUMN name TEXT;
    UPDATE public.projects SET name = title;
    ALTER TABLE public.projects ALTER COLUMN name SET NOT NULL;
    RAISE NOTICE 'Added name column and copied data from title column';
  ELSIF name_exists AND NOT title_exists THEN
    -- Add title column and copy data from name
    ALTER TABLE public.projects ADD COLUMN title TEXT;
    UPDATE public.projects SET title = name;
    ALTER TABLE public.projects ALTER COLUMN title SET NOT NULL;
    RAISE NOTICE 'Added title column and copied data from name column';
  END IF;
END $$;

-- Create or replace the trigger function
CREATE OR REPLACE FUNCTION update_projects_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop the trigger if it exists
DROP TRIGGER IF EXISTS update_projects_updated_at ON public.projects;

-- Create the trigger
CREATE TRIGGER update_projects_updated_at
BEFORE UPDATE ON public.projects
FOR EACH ROW
EXECUTE FUNCTION update_projects_updated_at();
```

## Step 3: Execute the Query

Click the "Run" button to execute the SQL query. You should see a success message in the results panel.

## Step 4: Verify the Changes

### Check the Projects Table

1. Go to the "Table Editor" in the left sidebar
2. Select the "projects" table
3. Click on "Edit" to view the table structure
4. Verify that the following columns have been added:
   - `created_at` (timestamp with time zone)
   - `updated_at` (timestamp with time zone)

### Check for Title/Name Consistency

The migration also handles a potential inconsistency where some code might be using `title` and other code might be using `name` for the project title. The migration ensures both columns exist and contain the same data.

## Step 5: Test the Application

After successfully running the migration, test the application to ensure that:

1. The projects list loads correctly
2. Project creation works
3. Project details can be viewed

## Troubleshooting

If you encounter any issues:

1. Check the error messages in the SQL Editor results panel
2. Make sure you have the necessary permissions to modify the projects table
3. If a specific part of the migration fails, you can run that part separately

For more help, refer to the Supabase documentation on [migrations](https://supabase.com/docs/guides/database/migrations).
