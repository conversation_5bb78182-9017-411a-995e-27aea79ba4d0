-- Fix Foreign Key Relationships in Supabase
-- This script fixes the foreign key relationships for the Retro Profile system
-- Run this in the Supabase SQL Editor

-- Step 1: Drop existing views and functions (they're just workarounds)
DROP VIEW IF EXISTS public.profile_comments_with_authors;
DROP VIEW IF EXISTS public.top_collaborators_with_users;
DROP FUNCTION IF EXISTS get_profile_comments;
DROP FUNCTION IF EXISTS get_top_collaborators;

-- Step 2: Recreate the tables with explicit foreign key constraints
-- First, backup existing data
CREATE TEMP TABLE profile_comments_backup AS SELECT * FROM public.profile_comments;
CREATE TEMP TABLE top_collaborators_backup AS SELECT * FROM public.top_collaborators;

-- Drop existing tables
DROP TABLE IF EXISTS public.profile_comments CASCADE;
DROP TABLE IF EXISTS public.top_collaborators CASCADE;

-- Recreate profile_comments table with explicit foreign key constraints
CREATE TABLE public.profile_comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    profile_id UUID NOT NULL,
    author_id UUID NOT NULL,
    content TEXT NOT NULL,
    is_approved BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    CONSTRAINT profile_comments_profile_id_fkey FOREIGN KEY (profile_id)
        REFERENCES auth.users(id) ON DELETE CASCADE,
    CONSTRAINT profile_comments_author_id_fkey FOREIGN KEY (author_id)
        REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Add explicit comments to help Supabase recognize the relationships
COMMENT ON CONSTRAINT profile_comments_profile_id_fkey ON public.profile_comments IS 'Links to the profile being commented on';
COMMENT ON CONSTRAINT profile_comments_author_id_fkey ON public.profile_comments IS 'Links to the author of the comment';

-- Create index for faster comment lookups
CREATE INDEX idx_profile_comments_profile ON public.profile_comments(profile_id);
CREATE INDEX idx_profile_comments_author ON public.profile_comments(author_id);

-- Recreate top_collaborators table with explicit foreign key constraints
CREATE TABLE public.top_collaborators (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    collaborator_id UUID NOT NULL,
    display_order INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(user_id, collaborator_id),
    CONSTRAINT top_collaborators_user_id_fkey FOREIGN KEY (user_id)
        REFERENCES auth.users(id) ON DELETE CASCADE,
    CONSTRAINT top_collaborators_collaborator_id_fkey FOREIGN KEY (collaborator_id)
        REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Add explicit comments to help Supabase recognize the relationships
COMMENT ON CONSTRAINT top_collaborators_user_id_fkey ON public.top_collaborators IS 'Links to the user who owns this list';
COMMENT ON CONSTRAINT top_collaborators_collaborator_id_fkey ON public.top_collaborators IS 'Links to the collaborator user';

-- Create index for faster collaborator lookups
CREATE INDEX idx_top_collaborators_user ON public.top_collaborators(user_id);
CREATE INDEX idx_top_collaborators_collaborator ON public.top_collaborators(collaborator_id);

-- Step 3: Restore the data
INSERT INTO public.profile_comments (id, profile_id, author_id, content, is_approved, created_at, updated_at)
SELECT id, profile_id, author_id, content, is_approved, created_at, updated_at FROM profile_comments_backup;

INSERT INTO public.top_collaborators (id, user_id, collaborator_id, display_order, created_at, updated_at)
SELECT id, user_id, collaborator_id, display_order, created_at, updated_at FROM top_collaborators_backup;

-- Step 4: Add RLS policies for profile_comments
ALTER TABLE public.profile_comments ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Anyone can view approved comments" ON public.profile_comments;
DROP POLICY IF EXISTS "Users can add comments" ON public.profile_comments;
DROP POLICY IF EXISTS "Users can update their own comments" ON public.profile_comments;
DROP POLICY IF EXISTS "Users can delete comments" ON public.profile_comments;

-- Anyone can view approved comments
CREATE POLICY "Anyone can view approved comments"
ON public.profile_comments
FOR SELECT
TO authenticated
USING (is_approved = true OR profile_id = auth.uid() OR author_id = auth.uid());

-- Users can add comments to profiles
CREATE POLICY "Users can add comments"
ON public.profile_comments
FOR INSERT
TO authenticated
WITH CHECK (true);

-- Users can update their own comments
CREATE POLICY "Users can update their own comments"
ON public.profile_comments
FOR UPDATE
TO authenticated
USING (author_id = auth.uid());

-- Users can delete their own comments or comments on their profile
CREATE POLICY "Users can delete comments"
ON public.profile_comments
FOR DELETE
TO authenticated
USING (author_id = auth.uid() OR profile_id = auth.uid());

-- Step 5: Add RLS policies for top_collaborators
ALTER TABLE public.top_collaborators ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Anyone can view top collaborators" ON public.top_collaborators;
DROP POLICY IF EXISTS "Users can manage their top collaborators" ON public.top_collaborators;

-- Anyone can view top collaborators
CREATE POLICY "Anyone can view top collaborators"
ON public.top_collaborators
FOR SELECT
TO authenticated
USING (true);

-- Users can manage their own top collaborators
CREATE POLICY "Users can manage their top collaborators"
ON public.top_collaborators
FOR ALL
TO authenticated
USING (user_id = auth.uid());

-- Step 6: Refresh the PostgREST schema cache
-- This is a workaround to force PostgREST to recognize the new relationships
-- It creates a dummy table and then drops it
CREATE TABLE IF NOT EXISTS public.refresh_schema_cache (id SERIAL PRIMARY KEY);
DROP TABLE IF EXISTS public.refresh_schema_cache;

-- Done! The foreign key relationships should now be properly recognized by Supabase's PostgREST API.
