// Script to update roadmap data in Supabase
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: './client/.env.local' });

// Initialize Supabase client with service key for admin access
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Make sure VITE_SUPABASE_URL and SUPABASE_SERVICE_KEY are set in client/.env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function updateRoadmapData() {
  console.log('=== Updating Roadmap Data ===');

  try {
    // First, get the current roadmap data
    const { data: roadmapData, error: roadmapError } = await supabase
      .from('roadmap')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1);

    if (roadmapError) {
      console.error('Error fetching roadmap data:', roadmapError);
      return;
    }

    if (!roadmapData || roadmapData.length === 0) {
      console.error('No roadmap data found');
      return;
    }

    const currentRoadmap = roadmapData[0];
    let phases = currentRoadmap.data;

    if (!Array.isArray(phases)) {
      console.error('Roadmap data is not an array');
      return;
    }

    console.log(`Current roadmap has ${phases.length} phases`);

    // Filter out Phase 0 and any undefined phases
    phases = phases.filter(phase => phase.id !== 0 && phase.id !== undefined && phase.title !== undefined);
    console.log(`After filtering invalid phases, roadmap has ${phases.length} phases`);

    // Update progress for each phase based on current status
    // Phase 1: Foundation & User Management - 100% complete
    const phase1 = phases.find(phase => phase.id === 1);
    if (phase1) {
      phase1.timeframe = "Completed";
      // Mark all tasks as completed
      phase1.sections.forEach(section => {
        section.tasks.forEach(task => {
          task.completed = true;
        });
      });
    }

    // Phase 2: Project Management - 100% complete
    const phase2 = phases.find(phase => phase.id === 2);
    if (phase2) {
      phase2.timeframe = "In Progress";
      // Update specific tasks
      phase2.sections.forEach(section => {
        if (section.id === "2.1") { // Project Creation Wizard
          section.tasks.forEach(task => {
            // Mark steps 1-3 as completed
            if (["2.1.1", "2.1.2", "2.1.3"].includes(task.id)) {
              task.completed = true;
            }
            // Mark steps 4-5 as in progress
            if (["2.1.4", "2.1.5"].includes(task.id)) {
              task.completed = true;
            }
            // Steps 6-8 remain incomplete
          });
        }
        if (section.id === "2.2") { // Basic Task Management
          section.tasks.forEach(task => {
            if (["2.2.1", "2.2.2"].includes(task.id)) {
              task.completed = true;
            }
          });
        }
      });
    }

    // Phase 3: Contribution Tracking - 56% complete
    const phase3 = phases.find(phase => phase.id === 3);
    if (phase3) {
      phase3.timeframe = "Phase 2";
      // Update specific tasks
      phase3.sections.forEach(section => {
        if (section.id === "3.1") { // Manual Contribution System
          section.tasks.forEach(task => {
            if (["3.1.1", "3.1.2", "3.1.3", "3.1.4"].includes(task.id)) {
              task.completed = true;
            }
          });
        }
        if (section.id === "3.2") { // Royalty Calculation Engine
          section.tasks.forEach(task => {
            if (["3.2.1"].includes(task.id)) {
              task.completed = true;
            }
          });
        }
      });
    }

    // Phase 4: Revenue & Royalty Distribution - 6% complete
    const phase4 = phases.find(phase => phase.id === 4);
    if (phase4) {
      phase4.timeframe = "Phase 2";
      // Most tasks remain incomplete
      // Mark specific tasks as completed
      phase4.sections.forEach(section => {
        if (section.id === "4.1") { // Revenue Tracking
          section.tasks.forEach(task => {
            if (["4.1.1"].includes(task.id)) {
              task.completed = true;
            }
          });
        }
      });
    }

    // Phase 5: Platform Enhancements - 6% complete
    const phase5 = phases.find(phase => phase.id === 5);
    if (phase5) {
      phase5.timeframe = "Phase 3";
      // Most tasks remain incomplete
      // Mark specific tasks as completed
      phase5.sections.forEach(section => {
        if (section.id === "5.1") { // User Experience Improvements
          section.tasks.forEach(task => {
            if (["5.1.1"].includes(task.id)) {
              task.completed = true;
            }
          });
        }
      });
    }

    // Phase 6: Advanced Features & Scaling - 0% complete
    const phase6 = phases.find(phase => phase.id === 6);
    if (phase6) {
      phase6.timeframe = "Phase 3";
      // All tasks remain incomplete
    }

    // Update the roadmap in the database
    const { data: updateData, error: updateError } = await supabase
      .from('roadmap')
      .update({
        data: phases,
        updated_at: new Date()
      })
      .eq('id', currentRoadmap.id);

    if (updateError) {
      console.error('Error updating roadmap data:', updateError);
    } else {
      console.log('Roadmap data updated successfully');
    }

    // Since the roadmap_phases and roadmap_sections tables don't exist,
    // we'll focus on updating the main roadmap table only
    console.log('Skipping roadmap_phases and roadmap_sections tables as they do not exist');

  } catch (error) {
    console.error('Error updating roadmap data:', error);
  }
}

// Run the function
updateRoadmapData();
