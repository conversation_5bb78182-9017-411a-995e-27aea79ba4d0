import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { supabase } from '../../../utils/supabase/supabase.utils';
import LoadingAnimation from '../layout/LoadingAnimation';
import { Card, CardBody, CardHeader, Button } from '../ui/heroui';

/**
 * ProjectAnalyticsDashboard Component
 *
 * Displays analytics and metrics for a project
 */
const ProjectAnalyticsDashboard = ({ projectId }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [metrics, setMetrics] = useState({
    contributorCount: 0,
    totalContributions: 0,
    totalHours: 0,
    completedMilestones: 0,
    totalMilestones: 0,
    completedTasks: 0,
    totalTasks: 0,
    totalRevenue: 0,
    contributionsByType: {},
    contributionsByUser: {},
    revenueByMonth: {},
    tasksByStatus: {}
  });

  // Fetch project analytics data
  useEffect(() => {
    const fetchAnalytics = async () => {
      if (!projectId || !currentUser) return;

      try {
        setLoading(true);

        // Fetch contributors
        const { data: contributors, error: contributorsError } = await supabase
          .from('project_contributors')
          .select('*')
          .eq('project_id', projectId)
          .eq('status', 'active');

        if (contributorsError) throw contributorsError;

        // Fetch contributions (only approved ones)
        const { data: contributions, error: contributionsError } = await supabase
          .from('contributions')
          .select('*')
          .eq('project_id', projectId)
          .eq('validation_status', 'approved'); // Only count approved contributions

        if (contributionsError) throw contributionsError;

        // Fetch milestones
        const { data: milestones, error: milestonesError } = await supabase
          .from('milestones')
          .select('*')
          .eq('project_id', projectId);

        if (milestonesError) throw milestonesError;

        // Fetch tasks (if table exists)
        let tasks = [];
        try {
          const { data: tasksData, error: tasksError } = await supabase
            .from('tasks')
            .select('*')
            .eq('project_id', projectId);

          if (!tasksError) {
            tasks = tasksData || [];
          }
        } catch (err) {
          console.log('Tasks table may not exist yet');
        }

        // Fetch revenue
        const { data: revenue, error: revenueError } = await supabase
          .from('revenue')
          .select('*')
          .eq('project_id', projectId);

        if (revenueError) throw revenueError;

        // Calculate metrics
        const totalHours = contributions.reduce((sum, c) => sum + (parseFloat(c.hours_spent) || 0), 0);
        const completedMilestones = milestones.filter(m => m.status === 'completed').length;
        const completedTasks = tasks.filter(t => t.status === 'done' || t.status === 'completed').length;
        const totalRevenue = revenue.reduce((sum, r) => sum + (parseFloat(r.amount) || 0), 0);

        // Group contributions by type
        const contributionsByType = contributions.reduce((acc, c) => {
          const type = c.task_type || 'Other';
          acc[type] = (acc[type] || 0) + (parseFloat(c.hours_spent) || 0);
          return acc;
        }, {});

        // Group contributions by user
        const contributionsByUser = contributions.reduce((acc, c) => {
          const userId = c.user_id;
          acc[userId] = (acc[userId] || 0) + (parseFloat(c.hours_spent) || 0);
          return acc;
        }, {});

        // Group revenue by month
        const revenueByMonth = revenue.reduce((acc, r) => {
          const date = new Date(r.date_received);
          const month = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
          acc[month] = (acc[month] || 0) + (parseFloat(r.amount) || 0);
          return acc;
        }, {});

        // Group tasks by status
        const tasksByStatus = tasks.reduce((acc, t) => {
          const status = t.status || 'unknown';
          acc[status] = (acc[status] || 0) + 1;
          return acc;
        }, {});

        // Set metrics
        setMetrics({
          contributorCount: contributors.length,
          totalContributions: contributions.length,
          totalHours,
          completedMilestones,
          totalMilestones: milestones.length,
          completedTasks,
          totalTasks: tasks.length,
          totalRevenue,
          contributionsByType,
          contributionsByUser,
          revenueByMonth,
          tasksByStatus
        });

      } catch (err) {
        console.error('Error fetching project analytics:', err);
        setError('Failed to load project analytics');
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, [projectId, currentUser]);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Format percentage
  const formatPercentage = (value, total) => {
    if (total === 0) return '0%';
    return `${Math.round((value / total) * 100)}%`;
  };

  // Format hours
  const formatHours = (hours) => {
    return `${hours.toFixed(1)} hrs`;
  };

  if (loading) {
    return <LoadingAnimation />;
  }

  if (error) {
    return (
      <div className="analytics-error">
        <i className="bi bi-exclamation-triangle"></i>
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div className="project-analytics-dashboard">
      <h3 className="analytics-title">Project Analytics</h3>
      <div className="analytics-note">
        <i className="bi bi-info-circle"></i> Only approved contributions are counted in metrics
      </div>

      <div className="analytics-grid">
        {/* Contributors Metric */}
        <div className="metric-card">
          <div className="metric-icon">
            <i className="bi bi-people"></i>
          </div>
          <div className="metric-content">
            <div className="metric-value">{metrics.contributorCount}</div>
            <div className="metric-label">Contributors</div>
          </div>
        </div>

        {/* Hours Metric */}
        <div className="metric-card">
          <div className="metric-icon">
            <i className="bi bi-clock"></i>
          </div>
          <div className="metric-content">
            <div className="metric-value">{formatHours(metrics.totalHours)}</div>
            <div className="metric-label">Total Hours</div>
          </div>
        </div>

        {/* Milestones Metric */}
        <div className="metric-card">
          <div className="metric-icon">
            <i className="bi bi-flag"></i>
          </div>
          <div className="metric-content">
            <div className="metric-value">
              {metrics.completedMilestones}/{metrics.totalMilestones}
            </div>
            <div className="metric-label">Milestones Completed</div>
            <div className="metric-progress">
              <div
                className="metric-progress-bar"
                style={{
                  width: formatPercentage(metrics.completedMilestones, metrics.totalMilestones)
                }}
              ></div>
            </div>
          </div>
        </div>

        {/* Tasks Metric */}
        <div className="metric-card">
          <div className="metric-icon">
            <i className="bi bi-check2-square"></i>
          </div>
          <div className="metric-content">
            <div className="metric-value">
              {metrics.completedTasks}/{metrics.totalTasks}
            </div>
            <div className="metric-label">Tasks Completed</div>
            <div className="metric-progress">
              <div
                className="metric-progress-bar"
                style={{
                  width: formatPercentage(metrics.completedTasks, metrics.totalTasks)
                }}
              ></div>
            </div>
          </div>
        </div>

        {/* Revenue Metric */}
        <div className="metric-card">
          <div className="metric-icon">
            <i className="bi bi-cash-coin"></i>
          </div>
          <div className="metric-content">
            <div className="metric-value">{formatCurrency(metrics.totalRevenue)}</div>
            <div className="metric-label">Total Revenue</div>
          </div>
        </div>

        {/* Contribution Rate Metric */}
        <div className="metric-card">
          <div className="metric-icon">
            <i className="bi bi-graph-up"></i>
          </div>
          <div className="metric-content">
            <div className="metric-value">
              {metrics.totalHours > 0 && metrics.totalContributions > 0
                ? formatHours(metrics.totalHours / metrics.totalContributions)
                : '0 hrs'}
            </div>
            <div className="metric-label">Avg. Hours per Contribution</div>
          </div>
        </div>
      </div>

      {/* Contributions by Type */}
      <div className="analytics-section">
        <h4 className="section-title">Contributions by Type</h4>
        {Object.keys(metrics.contributionsByType).length === 0 ? (
          <div className="no-data">No contribution data available</div>
        ) : (
          <div className="chart-container">
            <div className="bar-chart">
              {Object.entries(metrics.contributionsByType)
                .sort((a, b) => b[1] - a[1])
                .map(([type, hours]) => (
                  <div className="chart-item" key={type}>
                    <div className="chart-label">{type}</div>
                    <div className="chart-bar-container">
                      <div
                        className="chart-bar"
                        style={{
                          width: `${Math.min(100, (hours / metrics.totalHours) * 100)}%`
                        }}
                      ></div>
                      <div className="chart-value">{formatHours(hours)}</div>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        )}
      </div>

      {/* Tasks by Status */}
      {metrics.totalTasks > 0 && (
        <div className="analytics-section">
          <h4 className="section-title">Tasks by Status</h4>
          <div className="chart-container">
            <div className="pie-chart-container">
              <div className="pie-chart">
                {Object.entries(metrics.tasksByStatus).map(([status, count], index) => {
                  const percentage = (count / metrics.totalTasks) * 100;
                  const colors = ['#4CAF50', '#2196F3', '#FFC107', '#FF5722', '#9C27B0', '#607D8B'];
                  const color = colors[index % colors.length];

                  return (
                    <div
                      key={status}
                      className="pie-segment"
                      style={{
                        backgroundColor: color,
                        clipPath: `polygon(50% 50%, 50% 0%, ${percentage > 25 ? '100% 0%, ' : ''}${percentage > 50 ? '100% 100%, ' : ''}${percentage > 75 ? '0% 100%, 0% 0%' : ''})`
                      }}
                    ></div>
                  );
                })}
              </div>
              <div className="pie-legend">
                {Object.entries(metrics.tasksByStatus).map(([status, count], index) => {
                  const colors = ['#4CAF50', '#2196F3', '#FFC107', '#FF5722', '#9C27B0', '#607D8B'];
                  const color = colors[index % colors.length];

                  return (
                    <div className="legend-item" key={status}>
                      <div
                        className="legend-color"
                        style={{ backgroundColor: color }}
                      ></div>
                      <div className="legend-label">{status}</div>
                      <div className="legend-value">{count}</div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Revenue by Month */}
      {Object.keys(metrics.revenueByMonth).length > 0 && (
        <div className="analytics-section">
          <h4 className="section-title">Revenue by Month</h4>
          <div className="chart-container">
            <div className="bar-chart">
              {Object.entries(metrics.revenueByMonth)
                .sort((a, b) => a[0].localeCompare(b[0]))
                .map(([month, amount]) => {
                  const [year, monthNum] = month.split('-');
                  const date = new Date(parseInt(year), parseInt(monthNum) - 1);
                  const monthName = date.toLocaleDateString('en-US', { month: 'short' });

                  return (
                    <div className="chart-item" key={month}>
                      <div className="chart-label">{`${monthName} ${year}`}</div>
                      <div className="chart-bar-container">
                        <div
                          className="chart-bar revenue-bar"
                          style={{
                            width: `${Math.min(100, (amount / metrics.totalRevenue) * 100)}%`
                          }}
                        ></div>
                        <div className="chart-value">{formatCurrency(amount)}</div>
                      </div>
                    </div>
                  );
                })}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProjectAnalyticsDashboard;
