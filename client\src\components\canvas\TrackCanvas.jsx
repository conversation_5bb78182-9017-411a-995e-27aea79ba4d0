import React, { useState, useEffect, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardBody, Button, Tabs, Tab } from '@heroui/react';
import SimpleTimeTracker from '../tracking/SimpleTimeTracker';
import QuickContributionForm from '../tracking/QuickContributionForm';
import ContributionProgress from '../tracking/ContributionProgress';
import NavigationProgress from '../navigation/NavigationProgress';
import AdvancedFilters from '../common/AdvancedFilters';
import InteractiveCharts from '../analytics/InteractiveCharts';
import ContextualHelp from '../help/ContextualHelp';
import useKeyboardShortcuts from '../../hooks/useKeyboardShortcuts';
import { useContributionsQuery } from '../../hooks/usePaginatedQuery';
import { useCachedData } from '../../hooks/useDataCache';
import { ContributionCardSkeleton, CanvasSkeleton } from '../common/SkeletonScreens';
import { useDataSync } from '../../contexts/DataSyncContext';
import { UserContext } from '../../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../../utils/supabase/supabase.utils.js';

/**
 * Track Canvas Component
 *
 * The main interface for the Track journey in the experimental navigation system.
 * Provides comprehensive contribution tracking, time management, and progress visualization.
 * Follows the spatial-first design philosophy with smooth transitions and intuitive layout.
 */
const TrackCanvas = ({ currentUser, className = "" }) => {
  const [activeTab, setActiveTab] = useState('tracker');
  const [showActiveIndicator, setShowActiveIndicator] = useState(false);
  const { syncTriggers, triggerContributionSync } = useDataSync();
  const { currentUser: contextUser } = useContext(UserContext);

  // Advanced features state
  const [contributionData, setContributionData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [filters, setFilters] = useState({});
  const [loading, setLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(false);

  // Use currentUser from props or context
  const user = currentUser || contextUser;

  // Keyboard shortcuts
  useKeyboardShortcuts({
    onNavigateToTab: (tabIndex) => {
      const tabKeys = ['tracker', 'submit', 'progress', 'analytics'];
      if (tabKeys[tabIndex]) {
        setActiveTab(tabKeys[tabIndex]);
      }
    },
    onToggleFilters: () => setShowFilters(!showFilters),
    onCreateNew: () => setActiveTab('submit'),
    currentCanvas: 'contributions'
  });

  // Handle contribution submission with real-time updates
  const handleContributionSubmit = (contribution) => {
    console.log('New contribution submitted:', contribution);

    // Trigger sync across Track and Earn journeys
    triggerContributionSync();

    // Show success feedback
    setShowActiveIndicator(true);
    setTimeout(() => setShowActiveIndicator(false), 3000);
  };

  // Handle time tracking completion
  const handleTimeTrackingComplete = (contribution) => {
    console.log('Time tracking completed:', contribution);

    // Trigger sync across Track and Earn journeys
    triggerContributionSync();
  };

  // Determine current step based on active tab
  const getCurrentStep = () => {
    switch (activeTab) {
      case 'tracker': return 1;
      case 'submit': return 2;
      case 'progress': return 3;
      default: return 1;
    }
  };

  const getNextSteps = () => {
    switch (activeTab) {
      case 'tracker':
        return [
          'Start time tracking for your current task',
          'Submit contributions when work is complete',
          'Review progress and analytics'
        ];
      case 'submit':
        return [
          'Fill out contribution details',
          'Set difficulty rating and hours',
          'Submit for validation'
        ];
      case 'progress':
        return [
          'Review your contribution metrics',
          'Check validation status',
          'Plan next tasks'
        ];
      case 'analytics':
        return [
          'Analyze contribution trends',
          'Review performance metrics',
          'Identify improvement opportunities'
        ];
      default:
        return [];
    }
  };

  // Use optimized contributions query with caching
  const {
    data: contributionsQueryData,
    loading: contributionsLoading,
    error: contributionsError,
    refresh: refreshContributions
  } = useContributionsQuery(user?.id, {
    pageSize: 50, // Load more items for analytics
    enableRealtime: true,
    filters: filters
  });

  // Update local state when query data changes
  useEffect(() => {
    if (contributionsQueryData?.data) {
      setContributionData(contributionsQueryData.data);
      applyFilters(contributionsQueryData.data, filters);
    }
  }, [contributionsQueryData, filters]);

  // Apply filters to contribution data
  const applyFilters = (data, currentFilters) => {
    let filtered = [...data];

    // Text search
    if (currentFilters.search) {
      const searchTerm = currentFilters.search.toLowerCase();
      filtered = filtered.filter(item =>
        item.title?.toLowerCase().includes(searchTerm) ||
        item.description?.toLowerCase().includes(searchTerm) ||
        item.projects?.name?.toLowerCase().includes(searchTerm)
      );
    }

    // Status filter
    if (currentFilters.status && currentFilters.status !== 'all') {
      filtered = filtered.filter(item => item.status === currentFilters.status);
    }

    // Difficulty range
    if (currentFilters.difficulty) {
      const [min, max] = currentFilters.difficulty;
      filtered = filtered.filter(item =>
        item.difficulty_rating >= min && item.difficulty_rating <= max
      );
    }

    // Date range
    if (currentFilters.dateRange?.start || currentFilters.dateRange?.end) {
      filtered = filtered.filter(item => {
        const itemDate = new Date(item.created_at);
        const start = currentFilters.dateRange.start ? new Date(currentFilters.dateRange.start) : null;
        const end = currentFilters.dateRange.end ? new Date(currentFilters.dateRange.end) : null;

        if (start && itemDate < start) return false;
        if (end && itemDate > end) return false;
        return true;
      });
    }

    // Show only mine filter
    if (currentFilters.showOnlyMine) {
      filtered = filtered.filter(item => item.user_id === user.id);
    }

    // Sort
    if (currentFilters.sortBy) {
      filtered.sort((a, b) => {
        const aVal = a[currentFilters.sortBy];
        const bVal = b[currentFilters.sortBy];

        if (currentFilters.sortOrder === 'desc') {
          return bVal > aVal ? 1 : -1;
        } else {
          return aVal > bVal ? 1 : -1;
        }
      });
    }

    setFilteredData(filtered);
  };

  // Handle filter changes
  const handleFiltersChange = (newFilters) => {
    setFilters(newFilters);
    applyFilters(contributionData, newFilters);
  };

  // Update loading state from optimized query
  useEffect(() => {
    setLoading(contributionsLoading);
  }, [contributionsLoading]);

  // Tab configuration
  const tabs = [
    {
      key: 'tracker',
      title: '⏱️ Time Tracker',
      description: 'Track time as you work'
    },
    {
      key: 'submit',
      title: '📤 Submit Work',
      description: 'Log completed contributions'
    },
    {
      key: 'progress',
      title: '📊 Progress',
      description: 'View your contribution stats'
    },
    {
      key: 'analytics',
      title: '📈 Analytics',
      description: 'Analyze trends and performance'
    }
  ];

  // Show skeleton loading for initial load
  if (loading && !contributionData.length) {
    return <CanvasSkeleton type="track" className={className} />;
  }

  return (
    <div className={`min-h-screen bg-gradient-to-br from-slate-900 via-orange-900 to-red-900 ${className}`}>
      {/* Header */}
      <motion.div
        className="relative z-10 pt-8 pb-6"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="container mx-auto px-6">
          <div className="text-center mb-8">
            <motion.div
              className="text-6xl mb-4"
              animate={{
                scale: [1, 1.1, 1],
                rotate: [0, 5, -5, 0]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatType: "reverse"
              }}
            >
              ⏱️
            </motion.div>
            <h1 className="text-4xl font-bold text-white mb-2">
              Track Your Work
            </h1>
            <p className="text-white/80 text-lg max-w-2xl mx-auto">
              Log your contributions, track your time, and monitor your progress.
              Every moment of work counts toward your royalty share.
            </p>
          </div>

          {/* Active Tracking Indicator */}
          <AnimatePresence>
            {showActiveIndicator && (
              <motion.div
                className="fixed top-4 right-4 z-50"
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0 }}
              >
                <Card className="bg-green-500/20 border border-green-500/50">
                  <CardBody className="p-3">
                    <div className="flex items-center gap-2 text-green-400">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="text-sm font-medium">Tracking Active</span>
                    </div>
                  </CardBody>
                </Card>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="container mx-auto px-6 pb-12">
        {/* Navigation Progress */}
        <NavigationProgress
          currentJourney="track"
          currentStep={getCurrentStep()}
          totalSteps={3}
          nextSteps={getNextSteps()}
          className="mb-6"
        />

        <motion.div
          className="max-w-6xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {/* Navigation Tabs */}
          <Card className="mb-8 bg-white/5 border border-white/10">
            <CardBody className="p-6">
              <Tabs
                selectedKey={activeTab}
                onSelectionChange={setActiveTab}
                variant="underlined"
                classNames={{
                  tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
                  cursor: "w-full bg-gradient-to-r from-orange-500 to-red-500",
                  tab: "max-w-fit px-0 h-12",
                  tabContent: "group-data-[selected=true]:text-white text-white/70"
                }}
              >
                {tabs.map((tab) => (
                  <Tab
                    key={tab.key}
                    title={
                      <div className="flex items-center space-x-2">
                        <span>{tab.title}</span>
                      </div>
                    }
                  />
                ))}
              </Tabs>

              {/* Tab Description */}
              <motion.div
                key={activeTab}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-4"
              >
                <p className="text-white/70 text-sm">
                  {tabs.find(tab => tab.key === activeTab)?.description}
                </p>
              </motion.div>
            </CardBody>
          </Card>

          {/* Tab Content */}
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {activeTab === 'tracker' && (
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  <div className="lg:col-span-2" data-tour="time-tracker">
                    <SimpleTimeTracker
                      onTrackingStart={() => setShowActiveIndicator(true)}
                      onTrackingStop={() => setShowActiveIndicator(false)}
                      onContributionComplete={handleTimeTrackingComplete}
                      refreshTrigger={syncTriggers.contributions}
                    />
                  </div>
                  <div data-tour="progress-view">
                    <ContributionProgress refreshTrigger={syncTriggers.contributions} />
                  </div>
                </div>
              )}

              {activeTab === 'submit' && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <div data-tour="submit-form">
                    <QuickContributionForm
                      onSubmit={handleContributionSubmit}
                      refreshTrigger={syncTriggers.contributions}
                    />
                  </div>
                  <ContributionProgress refreshTrigger={syncTriggers.contributions} />
                </div>
              )}

              {activeTab === 'progress' && (
                <div className="grid grid-cols-1 gap-8">
                  {/* Advanced Filters */}
                  <AdvancedFilters
                    filterType="contributions"
                    onFiltersChange={handleFiltersChange}
                  />

                  <ContributionProgress refreshTrigger={syncTriggers.contributions} />

                  {/* Additional Progress Insights */}
                  <Card className="bg-gradient-to-br from-blue-500/10 to-purple-500/10 border border-white/10">
                    <CardBody className="p-6">
                      <h3 className="text-xl font-semibold text-white mb-4">Insights & Tips</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-3">
                          <h4 className="text-lg font-medium text-white">💡 Productivity Tips</h4>
                          <ul className="space-y-2 text-white/80 text-sm">
                            <li>• Break large tasks into smaller, trackable chunks</li>
                            <li>• Use the time tracker for focused work sessions</li>
                            <li>• Log contributions immediately after completion</li>
                            <li>• Set realistic daily and weekly goals</li>
                          </ul>
                        </div>
                        <div className="space-y-3">
                          <h4 className="text-lg font-medium text-white">🎯 Royalty Impact</h4>
                          <ul className="space-y-2 text-white/80 text-sm">
                            <li>• Higher difficulty tasks increase your share</li>
                            <li>• Consistent contributions build long-term value</li>
                            <li>• Quality over quantity - focus on meaningful work</li>
                            <li>• Approved contributions count toward royalties</li>
                          </ul>
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                </div>
              )}

              {activeTab === 'analytics' && (
                <div className="grid grid-cols-1 gap-8" data-tour="analytics">
                  {/* Advanced Filters */}
                  <AdvancedFilters
                    filterType="contributions"
                    onFiltersChange={handleFiltersChange}
                  />

                  {/* Interactive Charts */}
                  <InteractiveCharts
                    contributionData={filteredData}
                    earningsData={[]}
                  />

                  {/* Detailed Analytics */}
                  <Card className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-white/10">
                    <CardBody className="p-6">
                      <h3 className="text-xl font-semibold text-white mb-4">Performance Analytics</h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="text-center">
                          <p className="text-3xl font-bold text-purple-400">
                            {filteredData.length}
                          </p>
                          <p className="text-white/80 text-sm">Filtered Results</p>
                        </div>
                        <div className="text-center">
                          <p className="text-3xl font-bold text-pink-400">
                            {filteredData.reduce((sum, c) => sum + (c.hours_tracked || 0), 0).toFixed(1)}
                          </p>
                          <p className="text-white/80 text-sm">Total Hours</p>
                        </div>
                        <div className="text-center">
                          <p className="text-3xl font-bold text-blue-400">
                            {filteredData.length > 0
                              ? (filteredData.reduce((sum, c) => sum + (c.difficulty_rating || 3), 0) / filteredData.length).toFixed(1)
                              : '0.0'
                            }
                          </p>
                          <p className="text-white/80 text-sm">Avg Difficulty</p>
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                </div>
              )}
            </motion.div>
          </AnimatePresence>

          {/* Quick Actions */}
          <motion.div
            className="mt-8 flex justify-center gap-4 flex-wrap"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8 }}
          >
            <Button
              onClick={() => setActiveTab('tracker')}
              className={`${activeTab === 'tracker' ? 'bg-orange-500' : 'bg-white/10'} text-white`}
              variant={activeTab === 'tracker' ? 'solid' : 'ghost'}
            >
              Start Tracking
            </Button>
            <Button
              onClick={() => setActiveTab('submit')}
              className={`${activeTab === 'submit' ? 'bg-orange-500' : 'bg-white/10'} text-white`}
              variant={activeTab === 'submit' ? 'solid' : 'ghost'}
            >
              Submit Work
            </Button>
            <Button
              onClick={() => setActiveTab('progress')}
              className={`${activeTab === 'progress' ? 'bg-orange-500' : 'bg-white/10'} text-white`}
              variant={activeTab === 'progress' ? 'solid' : 'ghost'}
            >
              View Progress
            </Button>
            <Button
              onClick={() => setActiveTab('analytics')}
              className={`${activeTab === 'analytics' ? 'bg-orange-500' : 'bg-white/10'} text-white`}
              variant={activeTab === 'analytics' ? 'solid' : 'ghost'}
            >
              Analytics
            </Button>
          </motion.div>
        </motion.div>
      </div>

      {/* Contextual Help */}
      <ContextualHelp context="track" />
    </div>
  );
};

export default TrackCanvas;
