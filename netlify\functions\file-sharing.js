// File Sharing API
// Backend Specialist: Secure file sharing and collaboration system
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Upload File
const uploadFile = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const data = JSON.parse(event.body);
    
    if (!data.file_name || !data.file_url || !data.file_size || !data.file_type) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          error: 'file_name, file_url, file_size, and file_type are required' 
        })
      };
    }

    // Validate file size (max 100MB)
    const maxFileSize = 100 * 1024 * 1024; // 100MB in bytes
    if (data.file_size > maxFileSize) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'File size exceeds 100MB limit' })
      };
    }

    // Validate file type (basic security check)
    const allowedTypes = [
      'image/', 'video/', 'audio/', 'text/', 'application/pdf',
      'application/msword', 'application/vnd.openxmlformats-officedocument',
      'application/zip', 'application/json', 'application/javascript'
    ];
    
    const isAllowedType = allowedTypes.some(type => data.file_type.startsWith(type));
    if (!isAllowedType) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'File type not allowed' })
      };
    }

    // Verify context permissions if provided
    if (data.conversation_id) {
      const { data: participant, error: participantError } = await supabase
        .from('conversation_participants')
        .select('id')
        .eq('conversation_id', data.conversation_id)
        .eq('user_id', userId)
        .is('left_at', null)
        .single();

      if (participantError) {
        return {
          statusCode: 403,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ error: 'Cannot share files to this conversation' })
        };
      }
    }

    if (data.project_id) {
      const { data: contributor, error: contributorError } = await supabase
        .from('project_contributors')
        .select('id')
        .eq('project_id', data.project_id)
        .eq('user_id', userId)
        .single();

      if (contributorError) {
        return {
          statusCode: 403,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ error: 'Cannot share files to this project' })
        };
      }
    }

    if (data.alliance_id) {
      const { data: member, error: memberError } = await supabase
        .from('team_members')
        .select('id')
        .eq('team_id', data.alliance_id)
        .eq('user_id', userId)
        .single();

      if (memberError) {
        return {
          statusCode: 403,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ error: 'Cannot share files to this alliance' })
        };
      }
    }

    // Create file record
    const fileData = {
      file_name: data.file_name,
      file_size: data.file_size,
      file_type: data.file_type,
      file_url: data.file_url,
      file_hash: data.file_hash || null,
      uploaded_by: userId,
      upload_source: data.upload_source || 'direct',
      conversation_id: data.conversation_id || null,
      project_id: data.project_id || null,
      alliance_id: data.alliance_id || null,
      description: data.description || null,
      tags: data.tags || [],
      metadata: data.metadata || {},
      visibility: data.visibility || 'private'
    };

    const { data: file, error: fileError } = await supabase
      .from('shared_files')
      .insert([fileData])
      .select(`
        id,
        file_name,
        file_size,
        file_type,
        file_url,
        description,
        tags,
        visibility,
        created_at,
        uploader:users!shared_files_uploaded_by_fkey(
          id,
          display_name,
          avatar_url
        )
      `)
      .single();

    if (fileError) {
      throw new Error(`Failed to create file record: ${fileError.message}`);
    }

    // Create activity feed entry
    await createFileActivity(file, 'file_shared');

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ file })
    };

  } catch (error) {
    console.error('Upload file error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to upload file' })
    };
  }
};

// Get Shared Files
const getSharedFiles = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const conversationId = queryParams.get('conversation_id');
    const projectId = queryParams.get('project_id');
    const allianceId = queryParams.get('alliance_id');
    const fileType = queryParams.get('file_type');
    const limit = parseInt(queryParams.get('limit') || '20');

    let query = supabase
      .from('shared_files')
      .select(`
        id,
        file_name,
        file_size,
        file_type,
        file_url,
        description,
        tags,
        visibility,
        download_count,
        created_at,
        uploader:users!shared_files_uploaded_by_fkey(
          id,
          display_name,
          avatar_url
        ),
        conversation:conversations(
          id,
          title,
          conversation_type
        ),
        project:projects(
          id,
          name,
          title
        ),
        alliance:teams(
          id,
          name
        )
      `)
      .eq('is_deleted', false)
      .order('created_at', { ascending: false })
      .limit(limit);

    // Apply filters
    if (conversationId) {
      query = query.eq('conversation_id', conversationId);
    }
    if (projectId) {
      query = query.eq('project_id', projectId);
    }
    if (allianceId) {
      query = query.eq('alliance_id', allianceId);
    }
    if (fileType) {
      query = query.like('file_type', `${fileType}%`);
    }

    const { data: files, error: filesError } = await query;

    if (filesError) {
      throw new Error(`Failed to fetch files: ${filesError.message}`);
    }

    // Filter files based on access permissions
    const accessibleFiles = await Promise.all(
      files.map(async (file) => {
        const hasAccess = await checkFileAccess(file, userId);
        return hasAccess ? file : null;
      })
    );

    const filteredFiles = accessibleFiles.filter(file => file !== null);

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        files: filteredFiles,
        total: filteredFiles.length
      })
    };

  } catch (error) {
    console.error('Get shared files error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch files' })
    };
  }
};

// Download File
const downloadFile = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const fileId = event.path.split('/').pop();

    // Get file details
    const { data: file, error: fileError } = await supabase
      .from('shared_files')
      .select('*')
      .eq('id', fileId)
      .eq('is_deleted', false)
      .single();

    if (fileError) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'File not found' })
      };
    }

    // Check access permissions
    const hasAccess = await checkFileAccess(file, userId);
    if (!hasAccess) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Access denied to this file' })
      };
    }

    // Increment download count
    await supabase
      .from('shared_files')
      .update({ download_count: file.download_count + 1 })
      .eq('id', fileId);

    // Return file URL for download
    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        file: {
          id: file.id,
          file_name: file.file_name,
          file_size: file.file_size,
          file_type: file.file_type,
          file_url: file.file_url,
          download_count: file.download_count + 1
        }
      })
    };

  } catch (error) {
    console.error('Download file error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to download file' })
    };
  }
};

// Share File with Users/Teams
const shareFile = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const fileId = event.path.split('/')[1];
    const data = JSON.parse(event.body);

    // Verify user owns the file or has share permissions
    const { data: file, error: fileError } = await supabase
      .from('shared_files')
      .select('uploaded_by, visibility')
      .eq('id', fileId)
      .single();

    if (fileError) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'File not found' })
      };
    }

    if (file.uploaded_by !== userId) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Only file owner can share files' })
      };
    }

    // Create file permissions
    const permissions = [];
    
    if (data.user_ids && data.user_ids.length > 0) {
      data.user_ids.forEach(targetUserId => {
        permissions.push({
          file_id: fileId,
          user_id: targetUserId,
          permission_type: data.permission_type || 'view',
          granted_by: userId,
          expires_at: data.expires_at || null
        });
      });
    }

    if (data.team_ids && data.team_ids.length > 0) {
      data.team_ids.forEach(teamId => {
        permissions.push({
          file_id: fileId,
          team_id: teamId,
          permission_type: data.permission_type || 'view',
          granted_by: userId,
          expires_at: data.expires_at || null
        });
      });
    }

    if (permissions.length === 0) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'No users or teams specified for sharing' })
      };
    }

    const { data: createdPermissions, error: permissionsError } = await supabase
      .from('file_permissions')
      .upsert(permissions, { onConflict: 'file_id,user_id,team_id,permission_type' })
      .select();

    if (permissionsError) {
      throw new Error(`Failed to create file permissions: ${permissionsError.message}`);
    }

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: 'File shared successfully',
        permissions: createdPermissions.length
      })
    };

  } catch (error) {
    console.error('Share file error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to share file' })
    };
  }
};

// Delete File
const deleteFile = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const fileId = event.path.split('/').pop();

    // Verify user owns the file
    const { data: file, error: fileError } = await supabase
      .from('shared_files')
      .select('uploaded_by, file_name')
      .eq('id', fileId)
      .eq('is_deleted', false)
      .single();

    if (fileError) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'File not found' })
      };
    }

    if (file.uploaded_by !== userId) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Only file owner can delete files' })
      };
    }

    // Soft delete the file
    const { error: deleteError } = await supabase
      .from('shared_files')
      .update({ 
        is_deleted: true,
        deleted_at: new Date().toISOString()
      })
      .eq('id', fileId);

    if (deleteError) {
      throw new Error(`Failed to delete file: ${deleteError.message}`);
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: 'File deleted successfully',
        file_id: fileId
      })
    };

  } catch (error) {
    console.error('Delete file error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to delete file' })
    };
  }
};

// Helper function to check file access permissions
const checkFileAccess = async (file, userId) => {
  try {
    // File owner always has access
    if (file.uploaded_by === userId) {
      return true;
    }

    // Public files are accessible to all
    if (file.visibility === 'public') {
      return true;
    }

    // Check explicit permissions
    const { data: permission } = await supabase
      .from('file_permissions')
      .select('id')
      .eq('file_id', file.id)
      .eq('user_id', userId)
      .single();

    if (permission) {
      return true;
    }

    // Check team permissions
    const { data: teamPermissions } = await supabase
      .from('file_permissions')
      .select('team_id')
      .eq('file_id', file.id)
      .not('team_id', 'is', null);

    if (teamPermissions && teamPermissions.length > 0) {
      const teamIds = teamPermissions.map(p => p.team_id);
      const { data: membership } = await supabase
        .from('team_members')
        .select('id')
        .in('team_id', teamIds)
        .eq('user_id', userId)
        .single();

      if (membership) {
        return true;
      }
    }

    // Check context-based access
    if (file.conversation_id) {
      const { data: participant } = await supabase
        .from('conversation_participants')
        .select('id')
        .eq('conversation_id', file.conversation_id)
        .eq('user_id', userId)
        .is('left_at', null)
        .single();

      if (participant) {
        return true;
      }
    }

    if (file.project_id) {
      const { data: contributor } = await supabase
        .from('project_contributors')
        .select('id')
        .eq('project_id', file.project_id)
        .eq('user_id', userId)
        .single();

      if (contributor) {
        return true;
      }
    }

    if (file.alliance_id) {
      const { data: member } = await supabase
        .from('team_members')
        .select('id')
        .eq('team_id', file.alliance_id)
        .eq('user_id', userId)
        .single();

      if (member) {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error('Check file access error:', error);
    return false;
  }
};

// Helper function to create file activity
const createFileActivity = async (file, activityType) => {
  try {
    const activityData = {
      activity_type: activityType,
      activity_title: `File ${activityType === 'file_shared' ? 'shared' : 'updated'}`,
      activity_description: `${file.file_name} (${(file.file_size / 1024 / 1024).toFixed(2)} MB)`,
      actor_id: file.uploader.id,
      file_id: file.id,
      conversation_id: file.conversation_id,
      project_id: file.project_id,
      alliance_id: file.alliance_id,
      visibility: file.visibility,
      metadata: {
        file_type: file.file_type,
        file_size: file.file_size
      }
    };

    await supabase
      .from('activity_feeds')
      .insert([activityData]);

  } catch (error) {
    console.error('Create file activity error:', error);
  }
};

// Get File Statistics
const getFileStatistics = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    // Get file statistics
    const { data: stats } = await supabase
      .from('shared_files')
      .select('file_type, file_size, visibility, created_at')
      .eq('uploaded_by', userId)
      .eq('is_deleted', false);

    const statistics = {
      total_files: stats?.length || 0,
      total_size: stats?.reduce((sum, file) => sum + file.file_size, 0) || 0,
      file_types: {},
      visibility_breakdown: {},
      recent_uploads: 0
    };

    // Calculate file type breakdown
    stats?.forEach(file => {
      const type = file.file_type.split('/')[0];
      statistics.file_types[type] = (statistics.file_types[type] || 0) + 1;

      statistics.visibility_breakdown[file.visibility] =
        (statistics.visibility_breakdown[file.visibility] || 0) + 1;

      // Count recent uploads (last 7 days)
      const uploadDate = new Date(file.created_at);
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      if (uploadDate > weekAgo) {
        statistics.recent_uploads++;
      }
    });

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ statistics })
    };

  } catch (error) {
    console.error('Get file statistics error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to get file statistics' })
    };
  }
};

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/file-sharing', '');

  try {
    let response;

    if (event.httpMethod === 'POST') {
      if (path === '' || path === '/') {
        response = await uploadFile(event);
      } else if (path.includes('/share')) {
        response = await shareFile(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'GET') {
      if (path === '' || path === '/') {
        response = await getSharedFiles(event);
      } else if (path.includes('/download/')) {
        response = await downloadFile(event);
      } else if (path === '/statistics') {
        response = await getFileStatistics(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'DELETE') {
      response = await deleteFile(event);
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('File Sharing API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
