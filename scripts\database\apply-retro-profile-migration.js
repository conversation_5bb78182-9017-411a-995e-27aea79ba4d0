// <PERSON><PERSON>t to apply the Retro Profile migration directly
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please set SUPABASE_URL and SUPABASE_SERVICE_KEY environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);
console.log('Supabase client initialized');

// Function to execute a single SQL statement
async function executeStatement(statement) {
  try {
    // Check if the statement is for creating a table
    if (statement.trim().toUpperCase().startsWith('CREATE TABLE')) {
      const tableName = statement.match(/CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?(?:public\.)?(\w+)/i);
      if (tableName && tableName[1]) {
        console.log(`Creating table: ${tableName[1]}`);
      }
    }

    // Check if the statement is for altering a table
    if (statement.trim().toUpperCase().startsWith('ALTER TABLE')) {
      const match = statement.match(/ALTER\s+TABLE\s+(?:public\.)?(\w+)\s+ADD\s+COLUMN\s+(?:IF\s+NOT\s+EXISTS\s+)?(\w+)/i);
      if (match && match[1] && match[2]) {
        console.log(`Adding column ${match[2]} to table ${match[1]}`);
      }
    }

    // Execute the statement using RPC
    const { data, error } = await supabase.rpc('execute_sql', {
      sql_query: statement
    });

    if (error) {
      // Check if the error is because the column already exists
      if (error.message.includes('already exists')) {
        console.log(`Column already exists, skipping: ${statement.substring(0, 50)}...`);
        return { success: true, skipped: true };
      }

      // Check if the error is because the table already exists
      if (error.message.includes('relation') && error.message.includes('already exists')) {
        console.log(`Table already exists, skipping: ${statement.substring(0, 50)}...`);
        return { success: true, skipped: true };
      }

      // Check if the error is because the policy already exists
      if (error.message.includes('policy') && error.message.includes('already exists')) {
        console.log(`Policy already exists, skipping: ${statement.substring(0, 50)}...`);
        return { success: true, skipped: true };
      }

      // Check if the error is because the function already exists
      if (error.message.includes('function') && error.message.includes('already exists')) {
        console.log(`Function already exists, skipping: ${statement.substring(0, 50)}...`);
        return { success: true, skipped: true };
      }

      // Check if the error is because the index already exists
      if (error.message.includes('index') && error.message.includes('already exists')) {
        console.log(`Index already exists, skipping: ${statement.substring(0, 50)}...`);
        return { success: true, skipped: true };
      }

      // If it's another error, return it
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Function to split SQL into individual statements
function splitSQLStatements(sql) {
  // Split on semicolons but ignore those inside quotes or comments
  const statements = [];
  let currentStatement = '';
  let inSingleQuote = false;
  let inDoubleQuote = false;
  let inComment = false;

  for (let i = 0; i < sql.length; i++) {
    const char = sql[i];
    const nextChar = sql[i + 1] || '';

    // Handle comments
    if (!inSingleQuote && !inDoubleQuote) {
      if (char === '-' && nextChar === '-') {
        inComment = true;
      } else if (inComment && char === '\n') {
        inComment = false;
      }
    }

    // Handle quotes
    if (!inComment) {
      if (char === "'" && sql[i - 1] !== '\\') {
        inSingleQuote = !inSingleQuote;
      } else if (char === '"' && sql[i - 1] !== '\\') {
        inDoubleQuote = !inDoubleQuote;
      }
    }

    // Add character to current statement
    currentStatement += char;

    // Check for statement end
    if (char === ';' && !inSingleQuote && !inDoubleQuote && !inComment) {
      statements.push(currentStatement.trim());
      currentStatement = '';
    }
  }

  // Add the last statement if it doesn't end with a semicolon
  if (currentStatement.trim()) {
    statements.push(currentStatement.trim());
  }

  return statements.filter(stmt => stmt.trim() !== '' && !stmt.trim().startsWith('--'));
}

// Main function to apply the migration
async function applyMigration() {
  try {
    console.log('Starting Retro Profile migration process...');

    // Read the migration SQL file
    const migrationPath = path.join(__dirname, 'supabase/migrations/20240701000003_create_retro_profile_system.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    console.log(`Read migration file: ${migrationPath}`);

    // Split into individual statements
    const statements = splitSQLStatements(migrationSQL);
    console.log(`Found ${statements.length} SQL statements to execute`);

    // Execute each statement
    let successCount = 0;
    let skipCount = 0;
    let failCount = 0;
    const failedStatements = [];

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`\nExecuting statement ${i + 1}/${statements.length}...`);

      const result = await executeStatement(statement);

      if (result.success) {
        if (result.skipped) {
          skipCount++;
        } else {
          successCount++;
          console.log('Success!');
        }
      } else {
        failCount++;
        console.error(`Error: ${result.error}`);
        failedStatements.push({
          index: i + 1,
          statement: statement.substring(0, 100) + (statement.length > 100 ? '...' : ''),
          error: result.error
        });
      }
    }

    // Print summary
    console.log('\n=== Migration Summary ===');
    console.log(`Total statements: ${statements.length}`);
    console.log(`Successfully executed: ${successCount}`);
    console.log(`Skipped (already exists): ${skipCount}`);
    console.log(`Failed: ${failCount}`);

    if (failCount > 0) {
      console.log('\nFailed Statements:');
      failedStatements.forEach(failure => {
        console.log(`\n${failure.index}. ${failure.statement}`);
        console.log(`   Error: ${failure.error}`);
      });
    }

    // Check if the migration was successful
    if (failCount === 0) {
      console.log('\nMigration completed successfully!');

      // Verify that the columns were added
      console.log('\nVerifying migration...');
      const { data, error } = await supabase
        .from('users')
        .select('headline, location, website, cover_image_url')
        .limit(1);

      if (error) {
        console.error('Verification failed:', error.message);
      } else {
        console.log('Verification successful! The new columns are available.');
      }
    } else {
      console.log('\nMigration completed with errors. Please check the failed statements.');
    }
  } catch (error) {
    console.error('Migration failed:', error);
  }
}

// Run the migration
applyMigration();
