// React Rendering Test
// Day 2 - Testing what React is actually rendering

import { test, expect } from '@playwright/test';

const SITE_URL = 'https://royalty.technology';

test.describe('React Rendering Analysis', () => {
  test('should analyze what React is actually rendering', async ({ page }) => {
    console.log('🔍 Analyzing React rendering...');
    
    await page.goto(`${SITE_URL}/teams`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Get the actual rendered content
    const rootContent = await page.evaluate(() => {
      const root = document.getElementById('root');
      return {
        innerHTML: root ? root.innerHTML : 'NO ROOT',
        textContent: root ? root.textContent : 'NO ROOT',
        childElementCount: root ? root.childElementCount : 0,
        children: root ? Array.from(root.children).map(child => ({
          tagName: child.tagName,
          className: child.className,
          textContent: child.textContent.substring(0, 100)
        })) : []
      };
    });
    
    console.log('Root element analysis:');
    console.log('- Child elements:', rootContent.childElementCount);
    console.log('- Text content length:', rootContent.textContent.length);
    console.log('- Text content preview:', rootContent.textContent.substring(0, 200));
    
    console.log('\nChild elements:');
    rootContent.children.forEach((child, index) => {
      console.log(`${index + 1}. <${child.tagName}> class="${child.className}" text="${child.textContent}"`);
    });
    
    // Check if React Router is working
    const routerInfo = await page.evaluate(() => {
      // Check for React Router indicators
      const hasRouter = window.location.pathname !== '/';
      const currentPath = window.location.pathname;
      
      // Check for any route-specific content
      const bodyText = document.body.textContent;
      const hasRouteContent = bodyText.includes('teams') || bodyText.includes('Teams') || 
                             bodyText.includes('alliance') || bodyText.includes('Alliance');
      
      return {
        currentPath,
        hasRouter,
        hasRouteContent,
        bodyTextLength: bodyText.length,
        bodyTextPreview: bodyText.substring(0, 300)
      };
    });
    
    console.log('\nRouter analysis:');
    console.log('- Current path:', routerInfo.currentPath);
    console.log('- Has router:', routerInfo.hasRouter);
    console.log('- Has route content:', routerInfo.hasRouteContent);
    console.log('- Body text length:', routerInfo.bodyTextLength);
    console.log('- Body text preview:', routerInfo.bodyTextPreview);
    
    // Check for loading states
    const loadingStates = await page.evaluate(() => {
      const loadingIndicators = [
        'loading',
        'Loading',
        'spinner',
        'Spinner',
        'skeleton',
        'Skeleton'
      ];
      
      const bodyText = document.body.textContent;
      const foundIndicators = loadingIndicators.filter(indicator => 
        bodyText.includes(indicator)
      );
      
      return {
        foundIndicators,
        hasLoadingClass: document.querySelector('.loading, .spinner, .skeleton') !== null
      };
    });
    
    console.log('\nLoading state analysis:');
    console.log('- Loading indicators found:', loadingStates.foundIndicators);
    console.log('- Has loading class:', loadingStates.hasLoadingClass);
    
    // Check for authentication state
    const authState = await page.evaluate(() => {
      const bodyText = document.body.textContent;
      const authIndicators = {
        hasLogin: bodyText.includes('Login') || bodyText.includes('login'),
        hasLogout: bodyText.includes('Logout') || bodyText.includes('logout'),
        hasProfile: bodyText.includes('Profile') || bodyText.includes('profile'),
        hasUser: bodyText.includes('User') || bodyText.includes('user')
      };
      
      return authIndicators;
    });
    
    console.log('\nAuthentication state:');
    console.log('- Has login:', authState.hasLogin);
    console.log('- Has logout:', authState.hasLogout);
    console.log('- Has profile:', authState.hasProfile);
    console.log('- Has user:', authState.hasUser);
    
    // Check for specific component indicators
    const componentCheck = await page.evaluate(() => {
      const bodyText = document.body.textContent;
      const innerHTML = document.body.innerHTML;
      
      return {
        // Team-related
        hasTeamText: bodyText.includes('team') || bodyText.includes('Team'),
        hasTeamClass: innerHTML.includes('team') || innerHTML.includes('Team'),
        
        // Alliance-related
        hasAllianceText: bodyText.includes('alliance') || bodyText.includes('Alliance'),
        hasAllianceClass: innerHTML.includes('alliance') || innerHTML.includes('Alliance'),
        
        // Component-related
        hasComponentClass: innerHTML.includes('component') || innerHTML.includes('Component'),
        
        // React-related
        hasReactClass: innerHTML.includes('react') || innerHTML.includes('React'),
        
        // Error states
        hasError: bodyText.includes('error') || bodyText.includes('Error'),
        has404: bodyText.includes('404') || bodyText.includes('Not Found')
      };
    });
    
    console.log('\nComponent analysis:');
    console.log('- Has team text:', componentCheck.hasTeamText);
    console.log('- Has team class:', componentCheck.hasTeamClass);
    console.log('- Has alliance text:', componentCheck.hasAllianceText);
    console.log('- Has alliance class:', componentCheck.hasAllianceClass);
    console.log('- Has component class:', componentCheck.hasComponentClass);
    console.log('- Has React class:', componentCheck.hasReactClass);
    console.log('- Has error:', componentCheck.hasError);
    console.log('- Has 404:', componentCheck.has404);
    
    // Get the actual HTML structure
    const htmlStructure = await page.evaluate(() => {
      const root = document.getElementById('root');
      if (!root) return 'NO ROOT';
      
      function getStructure(element, depth = 0) {
        if (depth > 3) return '...'; // Limit depth
        
        const tag = element.tagName.toLowerCase();
        const className = element.className ? ` class="${element.className}"` : '';
        const children = Array.from(element.children);
        
        if (children.length === 0) {
          const text = element.textContent.trim();
          return `<${tag}${className}>${text.substring(0, 50)}${text.length > 50 ? '...' : ''}</${tag}>`;
        }
        
        const childStructure = children.map(child => 
          '  '.repeat(depth + 1) + getStructure(child, depth + 1)
        ).join('\n');
        
        return `<${tag}${className}>\n${childStructure}\n${'  '.repeat(depth)}</${tag}>`;
      }
      
      return getStructure(root);
    });
    
    console.log('\nHTML structure:');
    console.log(htmlStructure);
  });
});
