import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader } from '@heroui/react';
import { Chip } from '@heroui/react';
import { Progress } from '@heroui/react';
import { Button } from '@heroui/react';
import { 
  DollarSign, 
  TrendingUp, 
  Clock, 
  CreditCard,
  ArrowUpRight,
  ArrowDownRight,
  Wallet,
  PiggyBank
} from 'lucide-react';
import { useSupabase } from '../../hooks/useSupabase';
import { formatCurrency, formatDate } from '../../utils/formatters';

const RevenueDashboard = () => {
  const { supabase, user } = useSupabase();
  const [revenueData, setRevenueData] = useState({
    totalEarnings: 0,
    pendingPayments: 0,
    thisMonthEarnings: 0,
    escrowBalance: 0,
    recentTransactions: [],
    paymentMethods: [],
    loading: true
  });

  useEffect(() => {
    if (user) {
      fetchRevenueData();
    }
  }, [user]);

  const fetchRevenueData = async () => {
    try {
      // Fetch payment transactions
      const { data: transactions, error: transError } = await supabase
        .from('payment_transactions')
        .select(`
          *,
          from_user:from_user_id(full_name, avatar_url),
          to_user:to_user_id(full_name, avatar_url)
        `)
        .or(`from_user_id.eq.${user.id},to_user_id.eq.${user.id}`)
        .order('created_at', { ascending: false })
        .limit(10);

      if (transError) throw transError;

      // Fetch escrow accounts
      const { data: escrowAccounts, error: escrowError } = await supabase
        .from('escrow_accounts')
        .select('*')
        .eq('project_id', user.id); // Assuming user projects

      if (escrowError) throw escrowError;

      // Fetch linked payment methods
      const { data: paymentMethods, error: methodsError } = await supabase
        .from('teller_accounts')
        .select('*')
        .eq('user_id', user.id)
        .eq('is_active', true);

      if (methodsError) throw methodsError;

      // Calculate revenue metrics
      const inboundTransactions = transactions?.filter(t => 
        t.to_user_id === user.id && t.status === 'completed'
      ) || [];
      
      const totalEarnings = inboundTransactions.reduce((sum, t) => sum + parseFloat(t.amount), 0);
      
      const thisMonth = new Date();
      thisMonth.setDate(1);
      const thisMonthEarnings = inboundTransactions
        .filter(t => new Date(t.created_at) >= thisMonth)
        .reduce((sum, t) => sum + parseFloat(t.amount), 0);

      const pendingTransactions = transactions?.filter(t => 
        t.to_user_id === user.id && ['pending', 'processing'].includes(t.status)
      ) || [];
      const pendingPayments = pendingTransactions.reduce((sum, t) => sum + parseFloat(t.amount), 0);

      const escrowBalance = escrowAccounts?.reduce((sum, e) => sum + parseFloat(e.current_balance), 0) || 0;

      setRevenueData({
        totalEarnings,
        pendingPayments,
        thisMonthEarnings,
        escrowBalance,
        recentTransactions: transactions || [],
        paymentMethods: paymentMethods || [],
        loading: false
      });

    } catch (error) {
      console.error('Error fetching revenue data:', error);
      setRevenueData(prev => ({ ...prev, loading: false }));
    }
  };

  const getTransactionIcon = (transaction) => {
    if (transaction.to_user_id === user.id) {
      return <ArrowDownRight className="w-4 h-4 text-green-500" />;
    }
    return <ArrowUpRight className="w-4 h-4 text-red-500" />;
  };

  const getTransactionAmount = (transaction) => {
    const amount = parseFloat(transaction.amount);
    const isInbound = transaction.to_user_id === user.id;
    return isInbound ? `+${formatCurrency(amount)}` : `-${formatCurrency(amount)}`;
  };

  const getStatusColor = (status) => {
    const colors = {
      completed: 'success',
      pending: 'warning',
      processing: 'primary',
      failed: 'danger',
      cancelled: 'default'
    };
    return colors[status] || 'default';
  };

  if (revenueData.loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardBody className="p-6">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </CardBody>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Revenue Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="border-l-4 border-l-green-500">
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 mb-1">Total Earnings</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(revenueData.totalEarnings)}
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-green-500" />
            </div>
          </CardBody>
        </Card>

        <Card className="border-l-4 border-l-blue-500">
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 mb-1">This Month</p>
                <p className="text-2xl font-bold text-blue-600">
                  {formatCurrency(revenueData.thisMonthEarnings)}
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-blue-500" />
            </div>
          </CardBody>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 mb-1">Pending Payments</p>
                <p className="text-2xl font-bold text-orange-600">
                  {formatCurrency(revenueData.pendingPayments)}
                </p>
              </div>
              <Clock className="w-8 h-8 text-orange-500" />
            </div>
          </CardBody>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 mb-1">Escrow Balance</p>
                <p className="text-2xl font-bold text-purple-600">
                  {formatCurrency(revenueData.escrowBalance)}
                </p>
              </div>
              <PiggyBank className="w-8 h-8 text-purple-500" />
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Payment Methods */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Wallet className="w-5 h-5" />
              Payment Methods
            </h3>
            <Button size="sm" color="primary" variant="flat">
              Add Account
            </Button>
          </div>
        </CardHeader>
        <CardBody>
          {revenueData.paymentMethods.length === 0 ? (
            <div className="text-center py-8">
              <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 mb-4">No payment methods connected</p>
              <Button color="primary">Connect Bank Account</Button>
            </div>
          ) : (
            <div className="space-y-3">
              {revenueData.paymentMethods.map((method) => (
                <div key={method.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <CreditCard className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium">{method.account_name}</p>
                      <p className="text-sm text-gray-600">
                        {method.institution_name} • {method.account_type}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Chip 
                      size="sm" 
                      color={method.is_verified ? 'success' : 'warning'}
                      variant="flat"
                    >
                      {method.is_verified ? 'Verified' : 'Pending'}
                    </Chip>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardBody>
      </Card>

      {/* Recent Transactions */}
      <Card>
        <CardHeader className="pb-3">
          <h3 className="text-lg font-semibold">Recent Transactions</h3>
        </CardHeader>
        <CardBody>
          {revenueData.recentTransactions.length === 0 ? (
            <div className="text-center py-8">
              <DollarSign className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No transactions yet</p>
            </div>
          ) : (
            <div className="space-y-3">
              {revenueData.recentTransactions.map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex items-center gap-3">
                    {getTransactionIcon(transaction)}
                    <div>
                      <p className="font-medium">
                        {transaction.description || `${transaction.payment_method} Transfer`}
                      </p>
                      <p className="text-sm text-gray-600">
                        {formatDate(transaction.created_at)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`font-semibold ${
                      transaction.to_user_id === user.id ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {getTransactionAmount(transaction)}
                    </p>
                    <Chip 
                      size="sm" 
                      color={getStatusColor(transaction.status)}
                      variant="flat"
                    >
                      {transaction.status}
                    </Chip>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
};

export default RevenueDashboard;
