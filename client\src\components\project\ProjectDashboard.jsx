import React, { useState, useEffect, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardBody, CardHeader, Progress, Chip, Button, Tabs, Tab } from '@heroui/react';
import { UserContext } from '../../../contexts/supabase-auth.context.jsx';
import { useDataSync } from '../../contexts/DataSyncContext';
import { supabase } from '../../../utils/supabase/supabase.utils.js';
import { toast } from 'react-hot-toast';

/**
 * Enhanced Project Dashboard Component
 *
 * Provides real-time metrics, contribution analytics, and comprehensive project overview.
 * Integrates with DataSyncContext for live updates and maintains consistency with
 * the experimental navigation system.
 */

const ProjectDashboard = ({ projectId, className = "" }) => {
  const { currentUser } = useContext(UserContext);
  const { syncTriggers } = useDataSync();

  const [project, setProject] = useState(null);
  const [metrics, setMetrics] = useState({
    totalContributions: 0,
    totalHours: 0,
    activeContributors: 0,
    completionPercentage: 0,
    recentActivity: [],
    contributionTrends: [],
    earningsProjection: 0
  });
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  // Load project data and metrics
  const loadProjectData = async () => {
    if (!projectId || !currentUser) return;

    try {
      setLoading(true);

      // Load project details
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .select(`
          *,
          project_contributors (
            id,
            user_id,
            role,
            status,
            joined_at
          ),
          milestones (
            id,
            name,
            deadline,
            status,
            completion_percentage
          )
        `)
        .eq('id', projectId)
        .single();

      if (projectError) throw projectError;
      setProject(projectData);

      // Load contribution metrics
      const { data: contributions, error: contributionsError } = await supabase
        .from('contributions')
        .select('*')
        .eq('project_id', projectId)
        .eq('status', 'approved');

      if (contributionsError) throw contributionsError;

      // Load recent activity (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const { data: recentActivity, error: activityError } = await supabase
        .from('contributions')
        .select(`
          *,
          users (
            id,
            email,
            user_metadata
          )
        `)
        .eq('project_id', projectId)
        .gte('created_at', thirtyDaysAgo.toISOString())
        .order('created_at', { ascending: false })
        .limit(10);

      if (activityError) throw activityError;

      // Calculate metrics
      const totalHours = contributions.reduce((sum, c) => sum + (c.hours_tracked || 0), 0);
      const activeContributors = new Set(contributions.map(c => c.user_id)).size;

      // Calculate completion percentage based on milestones
      const milestones = projectData.milestones || [];
      const completedMilestones = milestones.filter(m => m.status === 'completed').length;
      const completionPercentage = milestones.length > 0 ? (completedMilestones / milestones.length) * 100 : 0;

      // Generate contribution trends (last 7 days)
      const trends = generateContributionTrends(contributions);

      // Calculate earnings projection
      const avgDifficulty = contributions.length > 0
        ? contributions.reduce((sum, c) => sum + (c.difficulty_rating || 3), 0) / contributions.length
        : 3;
      const earningsProjection = totalHours * 25 * (avgDifficulty / 3);

      setMetrics({
        totalContributions: contributions.length,
        totalHours,
        activeContributors,
        completionPercentage,
        recentActivity: recentActivity || [],
        contributionTrends: trends,
        earningsProjection
      });

    } catch (error) {
      console.error('Error loading project data:', error);
      toast.error('Failed to load project data');
    } finally {
      setLoading(false);
    }
  };

  // Generate contribution trends for the last 7 days
  const generateContributionTrends = (contributions) => {
    const trends = [];
    const today = new Date();

    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];

      const dayContributions = contributions.filter(c =>
        c.created_at.startsWith(dateStr)
      );

      trends.push({
        date: dateStr,
        count: dayContributions.length,
        hours: dayContributions.reduce((sum, c) => sum + (c.hours_tracked || 0), 0)
      });
    }

    return trends;
  };

  // Load data on mount and when sync triggers change
  useEffect(() => {
    loadProjectData();
  }, [projectId, currentUser, syncTriggers.projects, syncTriggers.contributions]);

  if (loading) {
    return (
      <div className={`p-6 ${className}`}>
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-default-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-default-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className={`p-6 text-center ${className}`}>
        <p className="text-muted-foreground">Project not found</p>
      </div>
    );
  }

  return (
    <div className={`p-6 space-y-6 ${className}`}>
      {/* Project Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-foreground">{project.name}</h1>
          <p className="text-muted-foreground mt-1">{project.description}</p>
        </div>
        <div className="flex items-center gap-3">
          <Chip color="primary" variant="flat">
            {project.project_type}
          </Chip>
          <Chip
            color={project.status === 'active' ? 'success' : 'default'}
            variant="flat"
          >
            {project.status}
          </Chip>
        </div>
      </motion.div>

      {/* Key Metrics Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-4 gap-6"
      >
        <Card>
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-3 bg-primary/10 rounded-lg">
                <span className="text-2xl">📊</span>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Contributions</p>
                <p className="text-2xl font-bold text-foreground">{metrics.totalContributions}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-3 bg-secondary/10 rounded-lg">
                <span className="text-2xl">⏱️</span>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Hours</p>
                <p className="text-2xl font-bold text-foreground">{metrics.totalHours.toFixed(1)}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-3 bg-success/10 rounded-lg">
                <span className="text-2xl">👥</span>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Active Contributors</p>
                <p className="text-2xl font-bold text-foreground">{metrics.activeContributors}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-3 bg-warning/10 rounded-lg">
                <span className="text-2xl">🎯</span>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Completion</p>
                <p className="text-2xl font-bold text-foreground">{metrics.completionPercentage.toFixed(0)}%</p>
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Progress Overview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Project Progress</h3>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Overall Completion</span>
                  <span>{metrics.completionPercentage.toFixed(1)}%</span>
                </div>
                <Progress
                  value={metrics.completionPercentage}
                  color="primary"
                  className="mb-4"
                />
              </div>

              {project.milestones && project.milestones.length > 0 && (
                <div>
                  <h4 className="font-medium mb-3">Milestones</h4>
                  <div className="space-y-2">
                    {project.milestones.slice(0, 3).map((milestone) => (
                      <div key={milestone.id} className="flex items-center justify-between">
                        <span className="text-sm">{milestone.name}</span>
                        <Chip
                          size="sm"
                          color={milestone.status === 'completed' ? 'success' : 'default'}
                          variant="flat"
                        >
                          {milestone.status}
                        </Chip>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardBody>
        </Card>
      </motion.div>
    </div>
  );
};

export default ProjectDashboard;
