// Quick test of all APIs
const fetch = require('node-fetch');

const SITE_URL = 'https://royalty.technology';
const AUTH_TOKEN = 'eyJhbGciOiJIUzI1NiIsImtpZCI6IjF1bjdLRUJ1NU9lVEgwWUwiLCJ0eXAiOiJKV1QifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.nu1g3DOQW331jqw-6ki28tJe7yJTYtoH4IVJ3kycgZs';

async function testAllAPIs() {
  console.log('🚀 Quick test of all payment APIs...\n');
  
  const endpoints = [
    '/.netlify/functions/companies',
    '/.netlify/functions/financial-transactions',
    '/.netlify/functions/commission-payments',
    '/.netlify/functions/recurring-fees'
  ];
  
  for (const endpoint of endpoints) {
    try {
      console.log(`Testing ${endpoint}...`);
      
      const response = await fetch(`${SITE_URL}${endpoint}`, {
        headers: {
          'Authorization': `Bearer ${AUTH_TOKEN}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log(`Status: ${response.status}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ Success - ${data.companies?.length || data.transactions?.length || data.commissions?.length || data.recurringFees?.length || 0} records`);
      } else {
        const text = await response.text();
        if (text.includes('<!DOCTYPE')) {
          console.log(`❌ Returns HTML (function not found)`);
        } else {
          console.log(`⚠️ Error: ${text.substring(0, 100)}`);
        }
      }
      
      console.log('---');
      
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      console.log('---');
    }
  }
}

testAllAPIs();
