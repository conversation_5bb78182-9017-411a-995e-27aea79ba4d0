import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import DatePicker from 'react-datepicker';

const RevenueTranches = ({ projectData, setProjectData, onNext, onBack }) => {
  const [showTrancheForm, setShowTrancheForm] = useState(false);
  const [editingIndex, setEditingIndex] = useState(null);
  const [newTranche, setNewTranche] = useState({
    name: '',
    description: '',
    start_date: null,
    end_date: null,
    revenue_sources: [],
    distribution_thresholds: {
      minimum_revenue: 0,
      maximum_payout: null,
      per_contributor_minimum: 0,
      per_contributor_maximum: null
    },
    rollover_config: 'none'
  });
  const [newSource, setNewSource] = useState('');

  // Initialize with existing tranches if available
  useEffect(() => {
    if (!projectData.revenue_tranches || projectData.revenue_tranches.length === 0) {
      // Set default tranche if none exists
      setProjectData({
        ...projectData,
        revenue_tranches: [{
          name: 'Initial Release',
          description: '',
          start_date: null,
          end_date: null,
          revenue_sources: [],
          distribution_thresholds: {
            minimum_revenue: 0,
            maximum_payout: null,
            per_contributor_minimum: 0,
            per_contributor_maximum: null
          },
          rollover_config: 'none'
        }]
      });
    }
  }, []);

  // Revenue source options
  const revenueSourceOptions = [
    { value: 'app_store', label: 'App Store' },
    { value: 'google_play', label: 'Google Play' },
    { value: 'steam', label: 'Steam' },
    { value: 'epic_games', label: 'Epic Games Store' },
    { value: 'direct_sales', label: 'Direct Sales' },
    { value: 'subscriptions', label: 'Subscriptions' },
    { value: 'in_app_purchases', label: 'In-App Purchases' },
    { value: 'advertising', label: 'Advertising' },
    { value: 'licensing', label: 'Licensing' },
    { value: 'merchandise', label: 'Merchandise' },
    { value: 'crowdfunding', label: 'Crowdfunding' },
    { value: 'donations', label: 'Donations' },
    { value: 'other', label: 'Other' }
  ];

  // Rollover options
  const rolloverOptions = [
    { value: 'none', label: 'No rollover' },
    { value: 'next_tranche', label: 'Roll over to next tranche' },
    { value: 'proportional', label: 'Proportional distribution' },
    { value: 'equal', label: 'Equal distribution' }
  ];

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewTranche({ ...newTranche, [name]: value });
  };

  // Handle distribution thresholds changes
  const handleThresholdChange = (e) => {
    const { name, value } = e.target;
    const parsedValue = value === '' ? null : parseCurrencyInput(value);

    setNewTranche({
      ...newTranche,
      distribution_thresholds: {
        ...newTranche.distribution_thresholds,
        [name]: parsedValue
      }
    });
  };

  // Save tranche
  const saveTrancheHandler = () => {
    if (!newTranche.name) {
      toast.error('Tranche name is required');
      return;
    }

    const updatedTranches = [...projectData.revenue_tranches];

    if (editingIndex !== null) {
      // Update existing tranche
      updatedTranches[editingIndex] = newTranche;
    } else {
      // Add new tranche
      updatedTranches.push(newTranche);
    }

    setProjectData({
      ...projectData,
      revenue_tranches: updatedTranches
    });

    setShowTrancheForm(false);
    setEditingIndex(null);
    setNewTranche({
      name: '',
      description: '',
      start_date: null,
      end_date: null,
      revenue_sources: [],
      platform_fee_config: {
        apply_before: true,
        percentage: 5
      },
      distribution_thresholds: {
        minimum_revenue: 0,
        maximum_payout: null,
        per_contributor_minimum: 0,
        per_contributor_maximum: null
      },
      rollover_config: 'none'
    });

    toast.success(
      editingIndex !== null
        ? 'Tranche updated successfully'
        : 'Tranche added successfully'
    );
  };

  // Edit tranche
  const editTrancheHandler = (index) => {
    setNewTranche(projectData.revenue_tranches[index]);
    setEditingIndex(index);
    setShowTrancheForm(true);
  };

  // Delete tranche
  const deleteTrancheHandler = (index) => {
    const updatedTranches = [...projectData.revenue_tranches];
    updatedTranches.splice(index, 1);

    setProjectData({
      ...projectData,
      revenue_tranches: updatedTranches
    });

    toast.success('Tranche deleted successfully');
  };

  // Add revenue source
  const addRevenueSource = () => {
    if (!newSource) return;

    if (newTranche.revenue_sources.includes(newSource)) {
      toast.error('This revenue source is already added');
      return;
    }

    setNewTranche({
      ...newTranche,
      revenue_sources: [...newTranche.revenue_sources, newSource]
    });

    setNewSource('');
  };

  // Remove revenue source
  const removeRevenueSource = (source) => {
    setNewTranche({
      ...newTranche,
      revenue_sources: newTranche.revenue_sources.filter((s) => s !== source)
    });
  };

  // Format currency
  const formatCurrency = (value) => {
    if (value === null || value === undefined) return '';
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  // Parse currency input
  const parseCurrencyInput = (value) => {
    if (!value) return 0;
    return parseFloat(value.replace(/,/g, ''));
  };

  return (
    <div className="wizard-step-content">
      <h2 className="step-title">Revenue Tranches Setup</h2>
      <p className="step-description">
        Configure how revenue will be distributed over time. Tranches allow you to define different revenue periods with specific rules.
      </p>

      {!showTrancheForm ? (
        <>
          <div className="d-flex justify-content-between align-items-center mb-4">
            <h3 className="h5 mb-0">
              Revenue Tranches ({projectData.revenue_tranches.length})
            </h3>
            <button
              type="button"
              className="btn btn-primary"
              onClick={() => setShowTrancheForm(true)}
            >
              <i className="bi bi-plus-lg me-1"></i> Add Tranche
            </button>
          </div>

          {projectData.revenue_tranches.length === 0 ? (
            <div className="alert alert-info">
              No revenue tranches added yet. Add at least one tranche to configure how
              revenue will be distributed.
            </div>
          ) : (
            <div className="list-group mb-4">
              {projectData.revenue_tranches.map((tranche, index) => (
                <div key={index} className="list-group-item list-group-item-action">
                  <div className="d-flex justify-content-between align-items-start">
                    <div>
                      <h5 className="mb-1">{tranche.name}</h5>
                      {tranche.description && (
                        <p className="mb-1 text-muted small">{tranche.description}</p>
                      )}
                      <div className="d-flex flex-wrap gap-3 small mt-2">
                        <span className="badge bg-light text-dark">
                          <i className="bi bi-calendar me-1"></i>
                          {tranche.start_date
                            ? new Date(tranche.start_date).toLocaleDateString()
                            : 'No start date'}
                          {tranche.end_date
                            ? ` to ${new Date(tranche.end_date).toLocaleDateString()}`
                            : ''}
                        </span>
                        <span className="badge bg-light text-dark">
                          <i className="bi bi-tag me-1"></i>
                          {tranche.revenue_sources.length > 0
                            ? `${tranche.revenue_sources.length} sources`
                            : 'No sources'}
                        </span>

                        <span className="badge bg-light text-dark">
                          <i className="bi bi-arrow-repeat me-1"></i>
                          {rolloverOptions.find((option) => option.value === tranche.rollover_config)?.label || 'No rollover'}
                        </span>
                      </div>
                    </div>
                    <div>
                      <button
                        type="button"
                        className="btn btn-sm btn-outline-primary me-2"
                        onClick={() => editTrancheHandler(index)}
                      >
                        <i className="bi bi-pencil"></i> Edit
                      </button>
                      <button
                        type="button"
                        className="btn btn-sm btn-outline-danger"
                        onClick={() => {
                          if (window.confirm('Are you sure you want to delete this tranche?')) {
                            deleteTrancheHandler(index);
                          }
                        }}
                      >
                        <i className="bi bi-trash"></i>
                      </button>
                    </div>
                  </div>

                  <div className="mt-3 small">
                    <div className="row g-3">
                      <div className="col-md-6">
                        <div className="card h-100">
                          <div className="card-header py-2 bg-light">Revenue Sources</div>
                          <div className="card-body py-2">
                            {tranche.revenue_sources.length > 0 ? (
                              <ul className="list-unstyled mb-0">
                                {tranche.revenue_sources.map((source, i) => (
                                  <li key={i}><i className="bi bi-dot me-1"></i>{source}</li>
                                ))}
                              </ul>
                            ) : (
                              <p className="text-muted mb-0">No revenue sources defined</p>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="col-md-6">
                        <div className="card h-100">
                          <div className="card-header py-2 bg-light">Distribution Thresholds</div>
                          <div className="card-body py-2">
                            <ul className="list-unstyled mb-0">
                              <li><strong>Min Revenue:</strong> ${formatCurrency(tranche.distribution_thresholds.minimum_revenue)}</li>
                              <li><strong>Max Payout:</strong> {tranche.distribution_thresholds.maximum_payout
                                ? `$${formatCurrency(tranche.distribution_thresholds.maximum_payout)}`
                                : 'No limit'}</li>
                              <li><strong>Per Contributor Min:</strong> ${formatCurrency(tranche.distribution_thresholds.per_contributor_minimum)}</li>
                              <li><strong>Per Contributor Max:</strong> {tranche.distribution_thresholds.per_contributor_maximum
                                ? `$${formatCurrency(tranche.distribution_thresholds.per_contributor_maximum)}`
                                : 'No limit'}</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          <div className="wizard-navigation">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onBack}
            >
              Back
            </button>
            <button
              type="button"
              className="btn btn-primary"
              onClick={onNext}
            >
              Next
            </button>
          </div>
        </>
      ) : (
        <div className="tranche-form">
          <h3 className="h5 mb-3">
            {editingIndex !== null ? 'Edit Tranche' : 'Add New Tranche'}
          </h3>

          <div className="mb-3">
            <label htmlFor="name" className="form-label">
              Tranche Name <span className="text-danger">*</span>
            </label>
            <input
              type="text"
              className="form-control"
              id="name"
              name="name"
              value={newTranche.name}
              onChange={handleInputChange}
              required
            />
          </div>

          <div className="mb-3">
            <label htmlFor="description" className="form-label">
              Description
            </label>
            <textarea
              className="form-control"
              id="description"
              name="description"
              value={newTranche.description}
              onChange={handleInputChange}
              rows="2"
            />
          </div>

          <div className="row mb-3">
            <div className="col-md-6">
              <label htmlFor="start_date" className="form-label">
                Start Date
              </label>
              <DatePicker
                selected={newTranche.start_date ? new Date(newTranche.start_date) : null}
                onChange={(date) =>
                  setNewTranche({ ...newTranche, start_date: date })
                }
                className="form-control"
                placeholderText="Select start date"
                dateFormat="MM/dd/yyyy"
              />
            </div>
            <div className="col-md-6">
              <label htmlFor="end_date" className="form-label">
                End Date
              </label>
              <DatePicker
                selected={newTranche.end_date ? new Date(newTranche.end_date) : null}
                onChange={(date) =>
                  setNewTranche({ ...newTranche, end_date: date })
                }
                className="form-control"
                placeholderText="Select end date"
                dateFormat="MM/dd/yyyy"
                minDate={newTranche.start_date ? new Date(newTranche.start_date) : null}
              />
            </div>
          </div>

          <div className="mb-3">
            <label className="form-label">Revenue Sources</label>
            <div className="input-group mb-2">
              <select
                className="form-select"
                value={newSource}
                onChange={(e) => setNewSource(e.target.value)}
              >
                <option value="">Select a revenue source</option>
                {revenueSourceOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              <button
                type="button"
                className="btn btn-outline-primary"
                onClick={addRevenueSource}
              >
                Add
              </button>
            </div>

            {newTranche.revenue_sources.length > 0 ? (
              <div className="revenue-sources-list">
                {newTranche.revenue_sources.map((source, index) => (
                  <div key={index} className="revenue-source-item">
                    <span>
                      {revenueSourceOptions.find((option) => option.value === source)?.label || source}
                    </span>
                    <button
                      type="button"
                      className="btn btn-sm btn-outline-danger"
                      onClick={() => removeRevenueSource(source)}
                    >
                      <i className="bi bi-x"></i>
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-muted small">No revenue sources added</div>
            )}
          </div>

          <div className="card mb-3">
            <div className="card-header">Distribution Thresholds</div>
            <div className="card-body">
              <div className="row mb-3">
                <div className="col-md-6">
                  <label htmlFor="minimum_revenue" className="form-label">
                    Minimum Revenue
                  </label>
                  <div className="input-group">
                    <span className="input-group-text">$</span>
                    <input
                      type="text"
                      className="form-control"
                      id="minimum_revenue"
                      name="minimum_revenue"
                      value={formatCurrency(newTranche.distribution_thresholds.minimum_revenue)}
                      onChange={handleThresholdChange}
                    />
                  </div>
                  <small className="text-muted">
                    Minimum revenue before distribution begins
                  </small>
                </div>
                <div className="col-md-6">
                  <label htmlFor="maximum_payout" className="form-label">
                    Maximum Payout
                  </label>
                  <div className="input-group">
                    <span className="input-group-text">$</span>
                    <input
                      type="text"
                      className="form-control"
                      id="maximum_payout"
                      name="maximum_payout"
                      value={formatCurrency(newTranche.distribution_thresholds.maximum_payout)}
                      onChange={handleThresholdChange}
                      placeholder="No limit"
                    />
                  </div>
                  <small className="text-muted">
                    Maximum total payout (leave empty for no limit)
                  </small>
                </div>
              </div>

              <div className="row">
                <div className="col-md-6">
                  <label htmlFor="per_contributor_minimum" className="form-label">
                    Per Contributor Minimum
                  </label>
                  <div className="input-group">
                    <span className="input-group-text">$</span>
                    <input
                      type="text"
                      className="form-control"
                      id="per_contributor_minimum"
                      name="per_contributor_minimum"
                      value={formatCurrency(newTranche.distribution_thresholds.per_contributor_minimum)}
                      onChange={handleThresholdChange}
                    />
                  </div>
                  <small className="text-muted">
                    Minimum payout per contributor
                  </small>
                </div>
                <div className="col-md-6">
                  <label htmlFor="per_contributor_maximum" className="form-label">
                    Per Contributor Maximum
                  </label>
                  <div className="input-group">
                    <span className="input-group-text">$</span>
                    <input
                      type="text"
                      className="form-control"
                      id="per_contributor_maximum"
                      name="per_contributor_maximum"
                      value={formatCurrency(newTranche.distribution_thresholds.per_contributor_maximum)}
                      onChange={handleThresholdChange}
                      placeholder="No limit"
                    />
                  </div>
                  <small className="text-muted">
                    Maximum payout per contributor (leave empty for no limit)
                  </small>
                </div>
              </div>
            </div>
          </div>

          <div className="mb-3">
            <label htmlFor="rollover_config" className="form-label">
              Rollover Configuration
            </label>
            <select
              className="form-select"
              id="rollover_config"
              name="rollover_config"
              value={newTranche.rollover_config}
              onChange={handleInputChange}
            >
              {rolloverOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <small className="text-muted">
              How to handle excess revenue after maximum payouts
            </small>
          </div>

          <div className="d-flex justify-content-between mt-4">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={() => {
                setShowTrancheForm(false);
                setEditingIndex(null);
                setNewTranche({
                  name: '',
                  description: '',
                  start_date: null,
                  end_date: null,
                  revenue_sources: [],
                  distribution_thresholds: {
                    minimum_revenue: 0,
                    maximum_payout: null,
                    per_contributor_minimum: 0,
                    per_contributor_maximum: null
                  },
                  rollover_config: 'none'
                });
              }}
            >
              Cancel
            </button>
            <button
              type="button"
              className="btn btn-primary"
              onClick={saveTrancheHandler}
            >
              {editingIndex !== null ? 'Update Tranche' : 'Add Tranche'}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default RevenueTranches;
