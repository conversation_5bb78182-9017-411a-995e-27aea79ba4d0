// Authenticated Alliance Test
// Day 2 - Testing alliance components with authentication

import { test, expect } from '@playwright/test';

const SITE_URL = 'https://royalty.technology';

// Test user credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

test.describe('Authenticated Alliance Testing', () => {
  test('should test alliance components with authentication', async ({ page }) => {
    console.log('🔍 Starting authenticated alliance test...');
    
    // Navigate to the site
    await page.goto(SITE_URL);
    await page.waitForLoadState('networkidle');
    
    // Check if we need to login
    console.log('🔐 Checking authentication status...');
    
    // Look for login button or form
    const loginButton = page.locator('text=Login').first();
    const loginForm = page.locator('form').first();
    const emailInput = page.locator('input[type="email"]').first();
    
    const needsLogin = await loginButton.isVisible() || await emailInput.isVisible();
    console.log('Needs login:', needsLogin);
    
    if (needsLogin) {
      console.log('🔑 Attempting to login...');
      
      // Try to click login button if it exists
      if (await loginButton.isVisible()) {
        await loginButton.click();
        await page.waitForLoadState('networkidle');
      }
      
      // Fill in credentials
      const emailField = page.locator('input[type="email"]').first();
      const passwordField = page.locator('input[type="password"]').first();
      const submitButton = page.locator('button[type="submit"]').first();
      
      if (await emailField.isVisible()) {
        await emailField.fill(TEST_USER.email);
        console.log('✅ Email filled');
      }
      
      if (await passwordField.isVisible()) {
        await passwordField.fill(TEST_USER.password);
        console.log('✅ Password filled');
      }
      
      if (await submitButton.isVisible()) {
        await submitButton.click();
        console.log('✅ Login submitted');
        
        // Wait for login to complete
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
      }
    }
    
    // Check if we're now authenticated
    const isAuthenticated = !(await page.locator('text=Login').first().isVisible());
    console.log('🎯 Authentication successful:', isAuthenticated);
    
    // Now test alliance routes
    console.log('\n🏰 Testing alliance routes...');
    
    // Test /teams route
    await page.goto(`${SITE_URL}/teams`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    const teamsPageContent = await page.textContent('body');
    console.log('Teams page content length:', teamsPageContent.length);
    
    // Look for team-related content
    const hasTeamContent = teamsPageContent.includes('team') || teamsPageContent.includes('Team');
    console.log('Has team content:', hasTeamContent);
    
    // Look for create/manage buttons
    const createButton = page.locator('text=Create').first();
    const manageButton = page.locator('text=Manage').first();
    
    const hasCreateButton = await createButton.isVisible();
    const hasManageButton = await manageButton.isVisible();
    
    console.log('Has create button:', hasCreateButton);
    console.log('Has manage button:', hasManageButton);
    
    // Test /alliances route
    await page.goto(`${SITE_URL}/alliances`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    const alliancesPageContent = await page.textContent('body');
    console.log('Alliances page content length:', alliancesPageContent.length);
    
    // Look for alliance-related content
    const hasAllianceContent = alliancesPageContent.includes('alliance') || alliancesPageContent.includes('Alliance');
    console.log('Has alliance content:', hasAllianceContent);
    
    // Test direct alliance management route
    console.log('\n⚔️ Testing alliance management...');
    
    await page.goto(`${SITE_URL}/teams/test-id/manage`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    const managePageContent = await page.textContent('body');
    console.log('Manage page content length:', managePageContent.length);
    
    // Look for our specific alliance management content
    const allianceManagementIndicators = [
      'Manage Alliance',
      'Alliance Information',
      'Business Entity',
      'Alliance Permissions',
      'Register Business',
      'Company Registration'
    ];
    
    console.log('\n🔍 Checking for alliance management indicators...');
    for (const indicator of allianceManagementIndicators) {
      const found = managePageContent.includes(indicator);
      console.log(`"${indicator}": ${found}`);
    }
    
    // Check for our CSS classes
    const cssClasses = [
      'alliance-manage-container',
      'alliance-info-section',
      'business-entity-section',
      'company-registration'
    ];
    
    console.log('\n🎨 Checking for CSS classes...');
    for (const className of cssClasses) {
      const hasClass = await page.locator(`.${className}`).count() > 0;
      console.log(`"${className}": ${hasClass}`);
    }
    
    // Check if AllianceManage component was loaded
    const componentLoaded = await page.evaluate(() => {
      // Check if our component is in the DOM
      return document.querySelector('.alliance-manage-container') !== null ||
             document.querySelector('[class*="alliance"]') !== null ||
             document.body.innerHTML.includes('AllianceManage');
    });
    
    console.log('\n🎯 AllianceManage component loaded:', componentLoaded);
    
    // Check for any JavaScript errors during navigation
    const errors = [];
    page.on('pageerror', error => {
      errors.push(error.message);
    });
    
    await page.waitForTimeout(2000);
    
    if (errors.length > 0) {
      console.log('\n❌ JavaScript errors during testing:');
      errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    } else {
      console.log('\n✅ No JavaScript errors during testing');
    }
  });

  test('should test experimental navigation with authentication', async ({ page }) => {
    console.log('🔍 Testing experimental navigation...');
    
    await page.goto(SITE_URL);
    await page.waitForLoadState('networkidle');
    
    // Check for canvas (experimental navigation)
    const canvas = await page.locator('canvas').first();
    const hasCanvas = await canvas.isVisible();
    console.log('Has experimental navigation canvas:', hasCanvas);
    
    if (hasCanvas) {
      console.log('🎮 Testing experimental navigation...');
      
      // Try keyboard navigation
      await page.keyboard.press('ArrowDown');
      await page.waitForTimeout(1000);
      
      await page.keyboard.press('ArrowDown');
      await page.waitForTimeout(1000);
      
      // Look for teams card
      const teamsCard = page.locator('text=Teams').first();
      const teamsVisible = await teamsCard.isVisible();
      console.log('Teams card visible:', teamsVisible);
      
      if (teamsVisible) {
        await teamsCard.click();
        await page.waitForLoadState('networkidle');
        
        const currentUrl = page.url();
        console.log('Navigated to:', currentUrl);
        
        // Check if we're on teams page
        const isTeamsPage = currentUrl.includes('/teams');
        console.log('Successfully navigated to teams:', isTeamsPage);
        
        if (isTeamsPage) {
          // Look for team management options
          const manageOptions = await page.locator('text=Manage').count();
          console.log('Manage options found:', manageOptions);
        }
      }
    } else {
      console.log('📱 No experimental navigation, checking regular navigation...');
      
      // Look for regular navigation links
      const navLinks = await page.locator('a[href*="teams"], a[href*="alliances"]').count();
      console.log('Navigation links found:', navLinks);
      
      if (navLinks > 0) {
        const teamsLink = page.locator('a[href*="teams"]').first();
        if (await teamsLink.isVisible()) {
          await teamsLink.click();
          await page.waitForLoadState('networkidle');
          
          const currentUrl = page.url();
          console.log('Navigated via regular nav to:', currentUrl);
        }
      }
    }
  });
});
