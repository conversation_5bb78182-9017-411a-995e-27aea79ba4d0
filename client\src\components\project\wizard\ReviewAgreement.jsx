import React, { useState, useEffect, useContext, useRef } from 'react';
import { UserContext } from '../../../../contexts/supabase-auth.context';
import { supabase } from '../../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import PDFPreview from '../../agreement/PDFPreview';
import SignatureCanvas from '../../agreement/SignatureCanvas';
import TemplateSelector from '../../agreement/TemplateSelector';
import AgreementValidator from '../../agreement/AgreementValidator';
import { markdownToHTML } from '../../../utils/pdf/pdfGenerator';
import { generateAgreement, regenerateAgreement as regenerateAgreementUtil } from '../../../utils/agreement';
import { TEMPLATE_TYPES } from '../../../utils/agreement/templateManager';
import AgreementTester from './AgreementTester';

const ReviewAgreement = ({ projectData, setProjectData, projectId }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(false);
  const [generatingAgreement, setGeneratingAgreement] = useState(false);
  const [agreementText, setAgreementText] = useState('');
  const [signatureData, setSignatureData] = useState(null);
  const [fullName, setFullName] = useState(currentUser?.user_metadata?.full_name || '');
  const [agreementTemplate, setAgreementTemplate] = useState('');
  const [templateType, setTemplateType] = useState(TEMPLATE_TYPES.STANDARD);
  const [showPdfPreview, setShowPdfPreview] = useState(false);
  const [showAgreementTester, setShowAgreementTester] = useState(false);
  const [showValidator, setShowValidator] = useState(false);
  const [validationResults, setValidationResults] = useState(null);
  const agreementTemplateLoaded = useRef(false);

  // Load agreement template
  useEffect(() => {
    const fetchTemplate = async () => {
      try {
        // Use the new template manager to load the template
        const { NewAgreementGenerator } = await import('../../../utils/agreement/newAgreementGenerator');
        const generator = new NewAgreementGenerator();
        const template = await generator.loadTemplate(templateType);

        setAgreementTemplate(template);
        agreementTemplateLoaded.current = true;
      } catch (error) {
        console.error('Error loading agreement template:', error);
        toast.error('Failed to load agreement template');
      }
    };

    fetchTemplate();
  }, [templateType]);

  // Function to regenerate agreement
  const regenerateAgreement = async () => {
    if (!projectId) return;

    setGeneratingAgreement(true);

    try {
      // Fetch project details
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select(`
          *,
          milestones:project_milestones(*)
        `)
        .eq('id', projectId)
        .single();

      if (projectError) throw projectError;

      // Fetch contributors
      const { data: contributors, error: contributorsError } = await supabase
        .from('project_contributors')
        .select(`
          *,
          users(id, email, display_name, address, state, county, title)
        `)
        .eq('project_id', projectId);

      if (contributorsError) throw contributorsError;

      // Fetch royalty model
      const { data: royaltyModel, error: royaltyModelError } = await supabase
        .from('royalty_models')
        .select('*')
        .eq('project_id', projectId)
        .single();

      if (royaltyModelError && royaltyModelError.code !== 'PGRST116') throw royaltyModelError;

      // Fetch the agreement template if not already loaded
      if (!agreementTemplate) {
        const response = await fetch('/example-cog-contributor-agreement.md');
        if (!response.ok) {
          throw new Error('Failed to load agreement template');
        }
        const template = await response.text();
        setAgreementTemplate(template);
      }

      // Prepare project data with all necessary information
      const projectData = {
        ...project,
        royalty_model: royaltyModel || { model_type: 'equal' },
        revenue_tranches: project.revenue_tranches || [],
        contributors: contributors || []
      };

      // Generate agreement text (now async)
      const agreement = await generateContributorAgreement(
        projectData,
        contributors || [],
        royaltyModel
      );

      setAgreementText(agreement);

      // Get contributor ID for the current user
      const { data: contributor, error: contributorError } = await supabase
        .from('project_contributors')
        .select('id')
        .eq('project_id', projectId)
        .eq('user_id', currentUser.id)
        .single();

      if (contributorError) {
        console.error('Error finding contributor:', contributorError);
        toast.error('Failed to find contributor record');
        setGeneratingAgreement(false);
        return;
      }

      // Check if agreement already exists
      const { data: existingAgreements, error: checkError } = await supabase
        .from('contributor_agreements')
        .select('*')
        .eq('project_id', projectId)
        .eq('contributor_id', contributor.id);

      if (checkError) {
        console.error('Error checking for existing agreement:', checkError);
        toast.error('Failed to check for existing agreement');
        setGeneratingAgreement(false);
        return;
      }

      const currentDate = new Date().toISOString();

      if (existingAgreements && existingAgreements.length > 0) {
        // Update existing agreement with new version
        const existingAgreement = existingAgreements[0];
        const currentVersion = existingAgreement.version || 1;
        const newVersion = currentVersion + 1;

        // Prepare previous versions array
        const previousVersions = existingAgreement.previous_versions || [];

        // Add current version to previous versions
        previousVersions.push({
          version: currentVersion,
          agreement_text: existingAgreement.agreement_text,
          updated_at: existingAgreement.updated_at,
          created_at: existingAgreement.created_at,
          updated_by: currentUser.id,
          status: existingAgreement.status,
          signature_data: existingAgreement.signature_data,
          signed_at: existingAgreement.signed_at
        });

        // Update the agreement with new text and increment version
        const { error: updateError } = await supabase
          .from('contributor_agreements')
          .update({
            agreement_text: agreement,
            version: newVersion,
            status: 'pending', // Reset to pending since it's a new version
            signature_data: null, // Clear signature data
            signed_at: null,
            updated_at: currentDate,
            created_at: currentDate, // Update creation date for the new version
            previous_versions: previousVersions
          })
          .eq('id', existingAgreement.id);

        if (updateError) {
          console.error('Error updating agreement:', updateError);
          toast.error('Failed to update agreement in database');
          setGeneratingAgreement(false);
          return;
        }

        toast.success(`Agreement updated to version ${newVersion}`);
      } else {
        // Create new agreement
        const { error: createError } = await supabase
          .from('contributor_agreements')
          .insert([
            {
              project_id: projectId,
              contributor_id: contributor.id,
              agreement_text: agreement,
              status: 'pending',
              version: 1,
              created_at: currentDate,
              updated_at: currentDate
            }
          ]);

        if (createError) {
          console.error('Error creating agreement:', createError);
          toast.error('Failed to create agreement in database');
          setGeneratingAgreement(false);
          return;
        }

        toast.success('New agreement created successfully');
      }
    } catch (error) {
      console.error('Error generating agreement:', error);
      toast.error('Failed to generate agreement');
    } finally {
      setGeneratingAgreement(false);
    }
  };

  // Generate agreement text on initial load
  useEffect(() => {
    regenerateAgreement();
  }, [projectId]);

  // Generate contributor agreement
  const generateContributorAgreement = async (project, contributors, royaltyModel) => {
    if (!agreementTemplate) {
      // Fallback to basic template if the example template couldn't be loaded
      return generateBasicAgreement(project, contributors, royaltyModel);
    }

    try {
      // Prepare options for the agreement generator
      const options = {
        contributors: contributors || [],
        currentUser: currentUser,
        royaltyModel: royaltyModel,
        milestones: project.milestones || [],
        fullName: fullName,
        templateType: templateType // Add the selected template type
      };

      // Use the new agreement generator
      const agreement = await generateAgreement(project, options);
      return agreement;
    } catch (error) {
      console.error('Error generating agreement:', error);

      // Fallback to basic template if the generator fails
      console.log('Falling back to basic agreement generation');
      return generateBasicAgreement(project, contributors, royaltyModel);
    }
  };

  // Generate basic agreement (fallback)
  const generateBasicAgreement = (project, contributors, royaltyModel) => {
    const today = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const ownerName = contributors.find(
      (contributor) => contributor.permission_level === 'Owner'
    )?.display_name || 'Project Owner';

    const royaltyModelText = getRoyaltyModelText(royaltyModel);

    return `
CONTRIBUTOR AGREEMENT

This Contributor Agreement (the "Agreement") is entered into as of ${today} by and between:

${ownerName} ("Project Owner"), and
The undersigned contributors (each a "Contributor" and collectively, the "Contributors").

WHEREAS, Project Owner is the creator and owner of the project known as "${project.title || project.name}" (the "Project");

WHEREAS, Contributors wish to contribute to the development of the Project and share in any revenue generated by the Project;

NOW, THEREFORE, in consideration of the mutual covenants contained herein, the parties agree as follows:

1. PROJECT DESCRIPTION

   The Project is described as follows:
   ${project.description || 'A collaborative project.'}

2. CONTRIBUTIONS

   2.1 Contributors agree to contribute to the Project by providing services, content, code, designs, or other materials as agreed upon by the parties.

   2.2 All contributions must be original work created by the Contributor or work for which the Contributor has obtained all necessary rights and permissions.

   2.3 Contributors retain ownership of their pre-existing intellectual property but grant a perpetual, worldwide, non-exclusive, royalty-free license to use, reproduce, modify, and distribute such pre-existing intellectual property as part of the Project.

3. ROYALTY DISTRIBUTION

   ${royaltyModelText}

4. TERM AND TERMINATION

   4.1 This Agreement shall commence on the date first written above and shall continue until terminated as provided herein.

   4.2 Either party may terminate this Agreement with respect to future contributions by providing written notice to the other party.

   4.3 Termination shall not affect any rights or obligations accrued prior to the effective date of termination.

5. CONFIDENTIALITY

   5.1 Contributors acknowledge that they may have access to confidential information related to the Project.

   5.2 Contributors agree to maintain the confidentiality of such information and not to disclose it to any third party without the prior written consent of the Project Owner.

6. REPRESENTATIONS AND WARRANTIES

   6.1 Each Contributor represents and warrants that their contributions do not infringe upon the intellectual property rights of any third party.

   6.2 Each Contributor represents and warrants that they have the full right and authority to enter into this Agreement and to perform their obligations hereunder.

7. MISCELLANEOUS

   7.1 This Agreement constitutes the entire agreement between the parties with respect to the subject matter hereof and supersedes all prior and contemporaneous agreements and understandings, whether oral or written.

   7.2 This Agreement may be amended only by a written instrument signed by all parties.

   7.3 This Agreement shall be governed by and construed in accordance with the laws of the jurisdiction in which the Project Owner resides, without giving effect to any choice of law or conflict of law provisions.

IN WITNESS WHEREOF, the parties have executed this Agreement as of the date first written above.

Project Owner:
${ownerName}

Contributor:
${fullName || '[Contributor Name]'}
${currentUser?.email || '[Contributor Email]'}
[Digital Signature]
[Date]
`;
  };

  // Generate Schedule A text (Services description)
  const generateScheduleAText = (project) => {
    const projectType = project.project_type || 'Software';
    const projectTitle = project.title || project.name || 'Untitled Project';
    const projectDescription = project.description || 'A collaborative project';

    return `This project involves development work on "${projectTitle}", ${projectDescription}.

1. **Services**

   a. **General.** Pursuant to the terms and conditions of this Agreement and subject to Company's acceptance, Contributor shall:
      i. develop the Work Product following the requirements and technical specifications set forth in Exhibit I and in accordance with the roadmap set forth in Exhibit II and Good Industry Practice ("Developing Services"); and
      ii. provide the Support Services in accordance with Good Industry Practice.

   b. **Performance.** Contributor understands and agrees that Contributor is solely responsible for the control and supervision of the means by which the Services are provided consistent with the goal of successfully completing the Services on time. Contributor shall allocate sufficient resources to ensure that it performs its Services under this Agreement in accordance with the roadmap in Exhibit II and in accordance with Good Industry Practice.

   c. **Co-operation.**
      i. During the period from the Effective Date until Launch, unless directed otherwise by Company, Contributor shall attend at least weekly calls with Company, with frequency increasing during crunch periods as needed, and provide builds (application versions) every two (2) weeks with Company.
      ii. The Parties shall share responsibility for Work Product Management as agreed between the Parties from time to time and as outlined in Exhibit II. If the Parties are unable to reach agreement in respect of a decision in relation to Work Product Management, Company's decision shall prevail.
      iii. If there is additional need for development that is not in the agreed roadmap the Parties will negotiate in good faith the timeline for Contributor to deliver such further development. The Parties will also negotiate in good faith the costs to deliver such further development.`;
  };

  // Generate Schedule B text (Consideration/Royalty model)
  const generateScheduleBText = (royaltyModel, project) => {
    if (!royaltyModel) {
      return `1. **General**

   1.1 This Schedule B sets out (i) how the fees, contribution points, and revenue tranch participation payable to Contributor under this Agreement are calculated, and (ii) when they become due to Contributor. It also sets out how the Company will report the revenue tranch due to Contributor.

2. **Definitions**

   Unless otherwise specified, all capitalized terms in this Schedule B shall have the meanings set forth below:

   **"Contribution Points"** means the numerical value assigned to Contributor's Contributions based on factors including time committed, task complexity, and overall impact on the Work Product, as calculated according to Section 3 of this Schedule.

   **"Revenue"** means all income received by Company from the commercialization of the Work Product, less the deductions specified in Section 4.1.

3. **Revenue Share Percentage**
   - Contributor shall be entitled to receive a share of Revenue as determined by the Project Owner.
   - Revenue shall be distributed on a quarterly basis, provided that the amount due to a Contributor exceeds $100.`;
    }

    let modelText = '';
    const modelType = royaltyModel.model_type || 'custom';

    // Common header for all models
    const headerText = `1. **General**

   1.1 This Schedule B sets out (i) how the fees, contribution points, and revenue tranch participation payable to Contributor under this Agreement are calculated, and (ii) when they become due to Contributor. It also sets out how the Company will report the revenue tranch due to Contributor.

2. **Definitions**

   Unless otherwise specified, all capitalized terms in this Schedule B shall have the meanings set forth below:

   **"Contribution Points"** means the numerical value assigned to Contributor's Contributions based on factors including time committed, task complexity, and overall impact on the Work Product, as calculated according to Section 3 of this Schedule.

   **"Contribution Point System"** means the system used to assign points to Contributor's work as defined in Section 3 of this Schedule.

   **"Revenue"** means all income received by Company from the commercialization of the Work Product, including but not limited to sales, licensing, subscriptions, in-game purchases, downloadable content (DLC), and merchandise, less the deductions specified in Section 4.1.

   **"Revenue Tranch"** means a designated portion of Revenue that is allocated for distribution to Contributors based on predefined criteria, including time periods and revenue thresholds, as defined in Section 4 of this Schedule.`;

    // Model-specific contribution point system
    let contributionPointSystem = '';

    switch (modelType) {
      case 'equal':
        contributionPointSystem = `3. **Contribution Point System**

   Contribution Points shall be calculated based on equal distribution among all Contributors.

   3.1. Each Contributor receives an equal share of the Revenue Tranch.

   3.2. **Example Calculation**
      For a project with 4 contributors and a Revenue Tranch of $10,000:
      - Each Contributor's share = $10,000 ÷ 4 = $2,500`;
        break;
      case 'task':
        contributionPointSystem = `3. **Contribution Point System**

   Contribution Points shall be calculated based on the number of tasks completed.

   3.1. **Core Metrics**
      - **Tasks Completed**: Count of tasks completed, multiplied by the point value assigned to each task type and difficulty

   3.2. **Calculation Process**
      Step 1: Count total tasks completed by each Contributor
      Step 2: Calculate percentage of total tasks
      Step 3: Apply percentage to Revenue Tranch

   3.3. **Example Calculation**
      For a contributor who completed 40 out of 100 total project tasks:
      - Contributor's percentage = 40%
      - For a Revenue Tranch of $10,000, this contributor would receive $4,000`;
        break;
      case 'time':
        contributionPointSystem = `3. **Contribution Point System**

   Contribution Points shall be calculated based on time contributed to the project.

   3.1. **Core Metrics**
      - **Hours Worked**: Actual logged hours contributed to the project

   3.2. **Calculation Process**
      Step 1: Sum total hours contributed by each Contributor
      Step 2: Calculate percentage of total hours
      Step 3: Apply percentage to Revenue Tranch

   3.3. **Example Calculation**
      For a contributor who logged 80 hours out of 200 total project hours:
      - Contributor's percentage = 40%
      - For a Revenue Tranch of $10,000, this contributor would receive $4,000`;
        break;
      case 'custom':
      default:
        contributionPointSystem = `3. **Contribution Point System**

   Contribution Points shall be calculated based on the following metrics and formula:

   3.1. **Core Metrics**
      - **Tasks Completed**: Count of tasks completed, multiplied by the point value assigned to each task type and difficulty
      - **Hours Worked**: Actual logged hours contributed to the project
      - **Task Difficulty**: Sum of difficulty points for all completed tasks

   3.2. **Default Weights**
      - Tasks Completed: ${royaltyModel?.configuration?.tasks_weight || 30}%
      - Hours Worked: ${royaltyModel?.configuration?.hours_weight || 30}%
      - Task Difficulty: ${royaltyModel?.configuration?.difficulty_weight || 40}%

   3.3. **Default Task Types and Points**
      | Task Type | Difficulty Level | Points |
      |-----------|-----------------|--------|
      | Programming | Medium | 5 |
      | Art | Hard | 10 |
      | QA | Easy | 2 |
      | Writing | Medium | 5 |
      | Engineering | Hard | 10 |
      | Admin | Medium | 5 |
      | Design | Medium | 5 |

   3.4. **Calculation Process**
      Step 1: Assign Contribution Values
      - Tasks Completed: Count of tasks × points per task type
      - Hours Worked: Total logged hours
      - Task Difficulty: Sum of difficulty points for completed tasks

      Step 2: Apply Weights
      - Weighted Score for Tasks = Raw Tasks Score × Tasks Weight
      - Weighted Score for Hours = Raw Hours Score × Hours Weight
      - Weighted Score for Difficulty = Raw Difficulty Score × Difficulty Weight
      - Total Contribution Score = Sum of all weighted scores

      Step 3: Calculate Royalty Percentage
      - Contributor's Royalty % = (Contributor's Total Score ÷ Team Total Score) × 100

   3.5. **Example Calculation**
      For a contributor who completed 8 Programming tasks (Medium difficulty) and logged 20 hours:
      - Tasks Completed: 8 tasks × 5 points = 40 points
      - Hours Worked: 20 hours = 20 points
      - Task Difficulty: 8 × 5 = 40 difficulty points

      Applying default weights:
      - Weighted Tasks Score: 40 × ${(royaltyModel?.configuration?.tasks_weight || 30)/100} = ${Math.round(40 * (royaltyModel?.configuration?.tasks_weight || 30)/100)}
      - Weighted Hours Score: 20 × ${(royaltyModel?.configuration?.hours_weight || 30)/100} = ${Math.round(20 * (royaltyModel?.configuration?.hours_weight || 30)/100)}
      - Weighted Difficulty Score: 40 × ${(royaltyModel?.configuration?.difficulty_weight || 40)/100} = ${Math.round(40 * (royaltyModel?.configuration?.difficulty_weight || 40)/100)}
      - Total Contribution Score = ${Math.round(40 * (royaltyModel?.configuration?.tasks_weight || 30)/100)} + ${Math.round(20 * (royaltyModel?.configuration?.hours_weight || 30)/100)} + ${Math.round(40 * (royaltyModel?.configuration?.difficulty_weight || 40)/100)} = ${Math.round(40 * (royaltyModel?.configuration?.tasks_weight || 30)/100) + Math.round(20 * (royaltyModel?.configuration?.hours_weight || 30)/100) + Math.round(40 * (royaltyModel?.configuration?.difficulty_weight || 40)/100)}

      If the total team score is 110, the contributor's royalty percentage would be:
      - (${Math.round(40 * (royaltyModel?.configuration?.tasks_weight || 30)/100) + Math.round(20 * (royaltyModel?.configuration?.hours_weight || 30)/100) + Math.round(40 * (royaltyModel?.configuration?.difficulty_weight || 40)/100)} ÷ 110) × 100 = ${Math.round((Math.round(40 * (royaltyModel?.configuration?.tasks_weight || 30)/100) + Math.round(20 * (royaltyModel?.configuration?.hours_weight || 30)/100) + Math.round(40 * (royaltyModel?.configuration?.difficulty_weight || 40)/100))/110*100*100)/100}%

      For a royalty pool of $10,000, this contributor would receive:
      - ${Math.round((Math.round(40 * (royaltyModel?.configuration?.tasks_weight || 30)/100) + Math.round(20 * (royaltyModel?.configuration?.hours_weight || 30)/100) + Math.round(40 * (royaltyModel?.configuration?.difficulty_weight || 40)/100))/110*100*100)/100}% of $10,000 = $${Math.round((Math.round(40 * (royaltyModel?.configuration?.tasks_weight || 30)/100) + Math.round(20 * (royaltyModel?.configuration?.hours_weight || 30)/100) + Math.round(40 * (royaltyModel?.configuration?.difficulty_weight || 40)/100))/110*10000)}`;
    }

    // Revenue tranch parameters (common for all models)
    const revenueTranchText = `
4. **Revenue Tranch Parameters**

   4.1. **Revenue Definition and Calculation**
      - **Revenue** shall be calculated as gross receipts from the Work Product, less the following deductions:
        - Payment processing fees
        - Taxes
        - Marketing expenses
        - Third-party licensing fees
        - Other direct costs associated with the sale and distribution of the Work Product

   4.2. **Revenue Share Percentage**
      - Contributor shall be entitled to receive a share of ${royaltyModel?.contributor_percentage || 33}% of post-expense Revenue allocated to the applicable Revenue Tranch, calculated based on the proportion of Contributor's Contribution Points to the total Contribution Points for the Work Product.

   4.3. **Initial Revenue Tranch Parameters**
      - **Duration/Scope:** Covers the base product at launch and the first two (2) content releases
      - **Minimum Threshold for Payout:** $${royaltyModel?.min_payout || 100} in post-expense Revenue
      - **Maximum Individual Payment:** $${royaltyModel?.max_payout || 1000000} per Contributor
      - **Rollover:** Any unused Contribution Points (due to the Maximum Individual Payment limit or other reasons) will be rolled over into the next Revenue Tranch

   4.4. **Payment Schedule**
      - Payments shall be made quarterly within 45 days after the end of each calendar quarter, provided that the Minimum Threshold for Payout has been reached.

   4.5. **Reporting**
      - Company shall provide Contributor with quarterly reports detailing the calculation of Revenue, the Revenue Tranch status, and Contributor's share.`;

    // No platform fee information needed as it's automatically calculated per platform
    const platformFeeText = '';

    return headerText + '\n\n' + contributionPointSystem + revenueTranchText + platformFeeText;
  };

  // Generate Exhibit I text (Specifications)
  const generateSpecificationsText = (project) => {
    const projectTitle = project.title || project.name || 'Untitled Project';
    const projectDescription = project.description || 'A collaborative project';
    const projectType = project.project_type || 'Software';

    let features = '';
    if (project.features && project.features.length > 0) {
      features = project.features.map((feature, index) => `${index + 1}. **${feature.title || 'Feature ' + (index + 1)}**\n   ${feature.description || 'No description provided.'}`).join('\n\n');
    } else {
      // Default features based on project type
      switch (projectType.toLowerCase()) {
        case 'game':
          features = `1. **Core Gameplay**\n   Main gameplay mechanics and systems\n\n2. **User Interface**\n   Game UI and player interaction\n\n3. **Content**\n   Levels, characters, and assets\n\n4. **Audio**\n   Sound effects and music`;
          break;
        case 'web':
          features = `1. **Frontend**\n   User interface and client-side functionality\n\n2. **Backend**\n   Server-side logic and data management\n\n3. **Database**\n   Data storage and retrieval\n\n4. **Authentication**\n   User login and security`;
          break;
        case 'mobile':
          features = `1. **User Interface**\n   Mobile app UI and user experience\n\n2. **Core Functionality**\n   Main app features and capabilities\n\n3. **Data Management**\n   Local and remote data handling\n\n4. **Notifications**\n   Push notifications and alerts`;
          break;
        default:
          features = `1. **Core Features**\n   Main functionality and capabilities\n\n2. **User Interface**\n   User interaction and experience\n\n3. **Data Management**\n   Data handling and storage\n\n4. **Integration**\n   Third-party service integration`;
      }
    }

    // Technical requirements based on project type
    let technicalRequirements = '';
    switch (projectType.toLowerCase()) {
      case 'game':
        technicalRequirements = `- Platform: PC, Mobile, or Console\n- Engine: Unity or Unreal Engine\n- Minimum Specs: To be determined based on final implementation\n- Art Style: To be determined based on project requirements\n- Audio: Professional quality sound effects and music`;
        break;
      case 'web':
        technicalRequirements = `- Frontend: HTML5, CSS3, JavaScript\n- Backend: Node.js, Python, or similar\n- Database: SQL or NoSQL based on requirements\n- Hosting: Cloud-based hosting\n- Security: Industry standard security practices`;
        break;
      case 'mobile':
        technicalRequirements = `- Platforms: iOS and Android\n- Development: Native or cross-platform framework\n- Minimum OS Version: iOS 13+, Android 8+\n- UI Design: Following platform design guidelines\n- Performance: Optimized for mobile devices`;
        break;
      default:
        technicalRequirements = `- Platform: To be determined based on project requirements\n- Development: Using appropriate technologies for the project\n- Performance: Optimized for target platforms\n- Security: Following industry best practices\n- Scalability: Designed to handle expected user load`;
    }

    return `**${projectTitle} - Specifications**

**Project Overview:**
${projectDescription}

**Core Features:**

${features}

**Technical Requirements:**
${technicalRequirements}`;
  };

  // Generate Exhibit II text (Milestones/Roadmap)
  const generateMilestonesText = (milestones, project) => {
    const projectTitle = project.title || project.name || 'Untitled Project';

    // If we have actual milestones, use them
    if (milestones && milestones.length > 0) {
      const sortedMilestones = [...milestones].sort((a, b) => {
        const dateA = new Date(a.target_date || 0);
        const dateB = new Date(b.target_date || 0);
        return dateA - dateB;
      });

      const milestonesText = sortedMilestones.map((milestone, index) => {
        const date = milestone.target_date ? new Date(milestone.target_date).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }) : `Month ${index + 1}`;

        return `${index + 1}. **Milestone ${index + 1}: ${milestone.title || 'Untitled Milestone'} (${date})**\n   ${milestone.description || 'No description provided.'}`;
      }).join('\n\n');

      return `**${projectTitle} - Development Roadmap**

${milestonesText}`;
    }

    // Default roadmap if no milestones are provided
    return `**${projectTitle} - Development Roadmap**

**Phase 1: Planning and Design (Month 1)**
- Requirements gathering
- Design documentation
- Technical architecture
- Project setup

**Phase 2: Core Development (Months 2-3)**
- Core functionality implementation
- Basic features development
- Initial testing

**Phase 3: Feature Completion (Month 4)**
- Complete all planned features
- Integration testing
- Performance optimization

**Phase 4: Testing and Launch (Month 5)**
- Quality assurance
- Bug fixing
- Final polish
- Launch preparation

**Milestones:**

1. **Milestone 1: Project Kickoff (End of Month 1)**
   - Requirements and design documents completed
   - Development environment set up

2. **Milestone 2: Core Functionality (End of Month 2)**
   - Basic features implemented
   - Initial testing completed

3. **Milestone 3: Feature Complete (End of Month 4)**
   - All planned features implemented
   - Integration testing completed

4. **Milestone 4: Launch (End of Month 5)**
   - Final testing completed
   - Product ready for release`;
  };

  // Get royalty model text (legacy function for backward compatibility)
  const getRoyaltyModelText = (royaltyModel) => {
    if (!royaltyModel) {
      return `
   3.1 Contributors shall receive a share of revenue generated by the Project as determined by the Project Owner.

   3.2 Revenue shall be distributed on a quarterly basis, provided that the amount due to a Contributor exceeds $100.`;
    }

    let modelText = '';

    switch (royaltyModel.model_type) {
      case 'equal':
        modelText = `
   3.1 Contributors shall receive an equal share of revenue generated by the Project.

   3.2 Each Contributor's share shall be calculated by dividing the total distributable revenue by the number of Contributors.`;
        break;
      case 'task':
        modelText = `
   3.1 Contributors shall receive a share of revenue generated by the Project based on the number of tasks completed by each Contributor.

   3.2 Each Contributor's share shall be calculated by dividing the total distributable revenue by the total number of tasks, and then multiplying by the number of tasks completed by the Contributor.`;
        break;
      case 'time':
        modelText = `
   3.1 Contributors shall receive a share of revenue generated by the Project based on the hours tracked by each Contributor.

   3.2 Each Contributor's share shall be calculated by dividing the total distributable revenue by the total number of hours tracked, and then multiplying by the number of hours tracked by the Contributor.`;
        break;
      case 'role':
        modelText = `
   3.1 Contributors shall receive a share of revenue generated by the Project based on their role in the Project.

   3.2 Each Contributor's share shall be determined by the percentage allocated to their role as specified in the Project documentation.`;
        break;
      case 'custom':
        modelText = `
   3.1 Contributors shall receive a share of revenue generated by the Project based on a weighted combination of factors.

   3.2 Each Contributor's share shall be calculated using the following weights:
      - Tasks completed: ${royaltyModel.configuration.tasks_weight}%
      - Hours tracked: ${royaltyModel.configuration.hours_weight}%
      - Task difficulty: ${royaltyModel.configuration.difficulty_weight}%`;
        break;
      default:
        modelText = `
   3.1 Contributors shall receive a share of revenue generated by the Project as determined by the Project Owner.

   3.2 Revenue shall be distributed on a quarterly basis, provided that the amount due to a Contributor exceeds $100.`;
    }

    // Add platform fee information
    const platformFeeText = royaltyModel.is_pre_expense
      ? `
   3.3 Platform fees shall be deducted from the total revenue before calculating Contributor shares.`
      : `
   3.3 Platform fees shall be deducted from each Contributor's share after distribution.`;

    // Add distribution frequency
    const distributionText = `
   3.4 Revenue shall be distributed on a quarterly basis, provided that the amount due to a Contributor exceeds $100.

   3.5 If the amount due to a Contributor is less than $100, it shall be carried forward to the next distribution period.`;

    return modelText + platformFeeText + distributionText;
  };

  // Save agreement
  const saveAgreement = async () => {
    if (!projectId || !currentUser) {
      toast.error('Project ID or user not found');
      return;
    }

    setLoading(true);

    try {
      // Get contributor ID
      const { data: contributor, error: contributorError } = await supabase
        .from('project_contributors')
        .select('id')
        .eq('project_id', projectId)
        .eq('user_id', currentUser.id)
        .single();

      if (contributorError) throw contributorError;

      // Check if agreement already exists
      const { data: existingAgreements, error: checkError } = await supabase
        .from('contributor_agreements')
        .select('*')
        .eq('project_id', projectId)
        .eq('contributor_id', contributor.id);

      if (checkError) throw checkError;

      const currentDate = new Date().toISOString();

      if (existingAgreements && existingAgreements.length > 0) {
        const existingAgreement = existingAgreements[0];
        const currentVersion = existingAgreement.version || 1;

        // Update existing agreement without changing version
        const { error: updateError } = await supabase
          .from('contributor_agreements')
          .update({
            agreement_text: agreementText,
            updated_at: currentDate
          })
          .eq('id', existingAgreement.id);

        if (updateError) throw updateError;

        toast.success(`Agreement template saved (version ${currentVersion})`);
      } else {
        // Create new agreement
        const { error: createError } = await supabase
          .from('contributor_agreements')
          .insert([
            {
              project_id: projectId,
              contributor_id: contributor.id,
              agreement_text: agreementText,
              status: 'pending',
              version: 1,
              created_at: currentDate,
              updated_at: currentDate
            }
          ]);

        if (createError) throw createError;

        toast.success('New agreement template saved (version 1)');
      }
    } catch (error) {
      console.error('Error saving agreement:', error);
      toast.error('Failed to save agreement');
    } finally {
      setLoading(false);
    }
  };

  // Sign agreement
  const signAgreement = async () => {
    if (!projectId || !currentUser || !signatureData) {
      toast.error('Project ID, user, or signature not found');
      return;
    }

    if (!fullName.trim()) {
      toast.error('Full name is required');
      return;
    }

    setLoading(true);

    try {
      // Get contributor ID
      const { data: contributor, error: contributorError } = await supabase
        .from('project_contributors')
        .select('id')
        .eq('project_id', projectId)
        .eq('user_id', currentUser.id)
        .single();

      if (contributorError) throw contributorError;

      // Check if agreement already exists
      const { data: existingAgreements, error: checkError } = await supabase
        .from('contributor_agreements')
        .select('*')
        .eq('project_id', projectId)
        .eq('contributor_id', contributor.id);

      if (checkError) throw checkError;

      const currentDate = new Date().toISOString();
      let agreementId;
      let version = 1;

      // If no agreement exists, create one first
      if (!existingAgreements || existingAgreements.length === 0) {
        console.log('No existing agreement found, creating a new one');
        const { data: newAgreement, error: createError } = await supabase
          .from('contributor_agreements')
          .insert([
            {
              project_id: projectId,
              contributor_id: contributor.id,
              agreement_text: agreementText,
              status: 'pending',
              created_at: currentDate,
              updated_at: currentDate,
              version: version
            }
          ])
          .select('id')
          .single();

        if (createError) throw createError;
        agreementId = newAgreement.id;
      } else {
        const existingAgreement = existingAgreements[0];
        agreementId = existingAgreement.id;
        version = existingAgreement.version || 1;
      }

      // Update agreement with signature
      const { error: updateError } = await supabase
        .from('contributor_agreements')
        .update({
          signature_data: {
            signature: signatureData,
            signed_by: currentUser.id,
            signed_at: currentDate,
            full_name: fullName
          },
          status: 'signed',
          signed_at: currentDate,
          updated_at: currentDate
        })
        .eq('id', agreementId);

      if (updateError) throw updateError;

      toast.success(`Agreement version ${version} signed successfully`);
    } catch (error) {
      console.error('Error signing agreement:', error);
      toast.error('Failed to sign agreement');
    } finally {
      setLoading(false);
    }
  };

  // Export agreement as PDF
  const exportAsPDF = () => {
    if (!agreementText) {
      toast.error('No agreement text to export');
      return;
    }

    // Show PDF preview
    setShowPdfPreview(true);
  };

  // Extract key highlights from the agreement
  const extractHighlights = () => {
    const highlights = [
      {
        title: "Project Details",
        icon: "bi-info-circle",
        content: `${projectData.name} (${projectData.project_type || 'Project'})`,
        description: projectData.description || 'No description provided.'
      },
      {
        title: "Royalty Model",
        icon: "bi-cash-coin",
        content: projectData.royalty_model?.model_type === 'custom' ? 'Custom (CoG)' :
                (projectData.royalty_model?.model_type === 'equal' ? 'Equal Split' :
                (projectData.royalty_model?.model_type === 'task' ? 'Task-Based' : 'Time-Based')),
        description: projectData.royalty_model?.model_type === 'custom' ?
                    `Tasks: ${projectData.royalty_model?.configuration?.tasks_weight || 33}%, ` +
                    `Hours: ${projectData.royalty_model?.configuration?.hours_weight || 33}%, ` +
                    `Difficulty: ${projectData.royalty_model?.configuration?.difficulty_weight || 34}%` :
                    'Standard royalty distribution based on selected model.'
      },
      {
        title: "Contributors",
        icon: "bi-people",
        content: `${projectData.contributors?.length || 1} contributor(s)`,
        description: "All contributors will share in revenue based on the royalty model."
      },
      {
        title: "Revenue Tranches",
        icon: "bi-graph-up",
        content: `${projectData.revenue_tranches?.length || 1} tranche(s)`,
        description: projectData.revenue_tranches?.length > 0 ?
                    `Primary tranche: ${projectData.revenue_tranches[0].name}` :
                    'Standard revenue distribution.'
      },
      {
        title: "Milestones",
        icon: "bi-flag",
        content: `${projectData.milestones?.length || 0} milestone(s)`,
        description: projectData.milestones?.length > 0 ?
                    `First milestone: ${projectData.milestones[0].name}` :
                    'No milestones defined yet.'
      }
    ];

    return highlights;
  };

  const highlights = extractHighlights();

  return (
    <div className="wizard-step-content">
      {/* PDF Preview */}
      {showPdfPreview && (
        <PDFPreview
          agreementText={agreementText}
          metadata={{
            title: 'Contributor Agreement',
            projectName: projectData?.title || projectData?.name || 'Untitled Project',
            date: new Date().toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            }),
            filename: `${projectData?.title || projectData?.name || 'project'}-agreement.pdf`,
            signature: signatureData ? {
              name: fullName,
              date: new Date().toLocaleDateString(),
              image: signatureData
            } : null
          }}
          onClose={() => setShowPdfPreview(false)}
        />
      )}
      <h2 className="step-title">Review & Agreement</h2>
      <p className="step-description">
        Review your project configuration and generate a contributor agreement.
      </p>

      {/* Agreement Highlights Section */}
      <div className="card mb-4 highlight-card">
        <div className="card-header bg-primary text-white">
          <h5 className="mb-0">
            <i className="bi bi-stars me-2"></i>
            Agreement Highlights
          </h5>
        </div>
        <div className="card-body">
          <div className="row">
            {highlights.map((highlight, index) => (
              <div className="col-md-4 mb-3" key={index}>
                <div className="highlight-item">
                  <div className="highlight-icon">
                    <i className={`bi ${highlight.icon}`}></i>
                  </div>
                  <div className="highlight-content">
                    <h6>{highlight.title}</h6>
                    <div className="highlight-value">{highlight.content}</div>
                    <div className="highlight-description">{highlight.description}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="text-center mt-3">
            <p className="text-muted">
              <i className="bi bi-info-circle me-1"></i>
              These highlights summarize key aspects of your agreement. Review the full details below.
            </p>
          </div>
        </div>
      </div>

      <div className="summary-section">
        <div className="summary-header">
          <h3 className="summary-title">
            <i className="bi bi-info-circle me-2"></i>
            Project Summary
          </h3>
          <button
            type="button"
            className="btn-edit-summary"
            onClick={() => setCurrentStep(1)}
            title="Edit Project Basics"
          >
            <i className="bi bi-pencil me-1"></i>
            Edit
          </button>
        </div>

        <div className="summary-content">
          <div className="summary-item">
            <div className="summary-label">
              <span>Project Name</span>
              <div className="tooltip-container">
                <i className="bi bi-question-circle"></i>
                <div className="tooltip-text">The name of your project as it will appear in the agreement</div>
              </div>
            </div>
            <div className="summary-value">{projectData.name}</div>
          </div>

          <div className="summary-item">
            <div className="summary-label">
              <span>Description</span>
              <div className="tooltip-container">
                <i className="bi bi-question-circle"></i>
                <div className="tooltip-text">A brief description of what your project is about</div>
              </div>
            </div>
            <div className="summary-value">
              {projectData.description || 'No description provided'}
            </div>
          </div>

          <div className="summary-item">
            <div className="summary-label">
              <span>Project Type</span>
              <div className="tooltip-container">
                <i className="bi bi-question-circle"></i>
                <div className="tooltip-text">The category that best describes your project</div>
              </div>
            </div>
            <div className="summary-value">
              {projectData.project_type || 'Not specified'}
            </div>
          </div>

          <div className="summary-item">
            <div className="summary-label">
              <span>Launch Date</span>
              <div className="tooltip-container">
                <i className="bi bi-question-circle"></i>
                <div className="tooltip-text">The expected date when your project will be released</div>
              </div>
            </div>
            <div className="summary-value">
              {projectData.launch_date
                ? new Date(projectData.launch_date).toLocaleDateString()
                : 'Not specified'}
            </div>
          </div>

          <div className="summary-item">
            <div className="summary-label">
              <span>Privacy</span>
              <div className="tooltip-container">
                <i className="bi bi-question-circle"></i>
                <div className="tooltip-text">Whether your project is visible to all users or only to contributors</div>
              </div>
            </div>
            <div className="summary-value">
              <span className={`privacy-badge ${projectData.is_public ? 'public' : 'private'}`}>
                <i className={`bi ${projectData.is_public ? 'bi-globe' : 'bi-lock'} me-1`}></i>
                {projectData.is_public ? 'Public' : 'Private'}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="summary-section">
        <h3 className="summary-title">Team & Contributors</h3>

        <div className="summary-item">
          <div className="summary-label">Number of Contributors</div>
          <div className="summary-value">{projectData.contributors ? projectData.contributors.length : 0}</div>
        </div>

        {projectData.contributors && projectData.contributors.length > 0 && (
          <div className="table-responsive mt-2">
            <table className="table table-sm">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Email</th>
                  <th>Role</th>
                  <th>Permission</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                {projectData.contributors.map((contributor, index) => (
                  <tr key={index}>
                    <td>{contributor.display_name || 'Unnamed'}</td>
                    <td>{contributor.email || 'No email'}</td>
                    <td>{contributor.role || 'Not specified'}</td>
                    <td>{contributor.permission_level || 'Not specified'}</td>
                    <td>
                      <span
                        className={`badge ${
                          contributor.status === 'active'
                            ? 'bg-success'
                            : contributor.status === 'pending'
                            ? 'bg-warning'
                            : 'bg-danger'
                        }`}
                      >
                        {contributor.status || 'Unknown'}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      <div className="summary-section">
        <h3 className="summary-title">Royalty Model</h3>

        <div className="summary-item">
          <div className="summary-label">Model Type</div>
          <div className="summary-value">
            {projectData.royalty_model.model_type === 'equal'
              ? 'Equal Split'
              : projectData.royalty_model.model_type === 'task'
              ? 'Task-based'
              : projectData.royalty_model.model_type === 'time'
              ? 'Time-based'
              : projectData.royalty_model.model_type === 'role'
              ? 'Role-based'
              : 'Custom Model'}
          </div>
        </div>

        {projectData.royalty_model.model_type === 'custom' && (
          <>
            <div className="summary-item">
              <div className="summary-label">Tasks Weight</div>
              <div className="summary-value">
                {projectData.royalty_model.configuration.tasks_weight}%
              </div>
            </div>

            <div className="summary-item">
              <div className="summary-label">Hours Weight</div>
              <div className="summary-value">
                {projectData.royalty_model.configuration.hours_weight}%
              </div>
            </div>

            <div className="summary-item">
              <div className="summary-label">Difficulty Weight</div>
              <div className="summary-value">
                {projectData.royalty_model.configuration.difficulty_weight}%
              </div>
            </div>
          </>
        )}

        <div className="summary-item">
          <div className="summary-label">Platform Fee</div>
          <div className="summary-value">
            {projectData.royalty_model.is_pre_expense
              ? 'Applied before distribution'
              : 'Applied after distribution'}
          </div>
        </div>
      </div>

      <div className="summary-section">
        <h3 className="summary-title">Revenue Tranches</h3>

        <div className="summary-item">
          <div className="summary-label">Number of Tranches</div>
          <div className="summary-value">{projectData.revenue_tranches ? projectData.revenue_tranches.length : 0}</div>
        </div>

        {projectData.revenue_tranches && projectData.revenue_tranches.length > 0 && (
          <div className="table-responsive mt-2">
            <table className="table table-sm">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Date Range</th>
                  <th>Platform Fee</th>
                  <th>Min Revenue</th>
                  <th>Rollover</th>
                </tr>
              </thead>
              <tbody>
                {projectData.revenue_tranches.map((tranche, index) => (
                  <tr key={index}>
                    <td>{tranche.name || 'Unnamed Tranche'}</td>
                    <td>
                      {tranche.start_date
                        ? new Date(tranche.start_date).toLocaleDateString()
                        : 'N/A'}{' '}
                      to{' '}
                      {tranche.end_date
                        ? new Date(tranche.end_date).toLocaleDateString()
                        : 'N/A'}
                    </td>
                    <td>
                      {tranche.platform_fee_config ? tranche.platform_fee_config.percentage : 0}%{' '}
                      {tranche.platform_fee_config && tranche.platform_fee_config.apply_before
                        ? '(before)'
                        : '(after)'}
                    </td>
                    <td>
                      $
                      {tranche.distribution_thresholds && tranche.distribution_thresholds.minimum_revenue
                        ? tranche.distribution_thresholds.minimum_revenue.toLocaleString()
                        : '0'}
                    </td>
                    <td>
                      {tranche.rollover_config === 'none'
                        ? 'None'
                        : tranche.rollover_config === 'next'
                        ? 'Next Tranche'
                        : tranche.rollover_config === 'proportional'
                        ? 'Proportional'
                        : 'Equal'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Template Selector */}
      <TemplateSelector
        selectedTemplate={templateType}
        onTemplateChange={(newTemplate) => {
          setTemplateType(newTemplate);
          // Reset the agreement template loaded flag to trigger a reload
          agreementTemplateLoaded.current = false;
        }}
        showPreview={false}
      />

      {/* Validator */}
      {showValidator && agreementText && (
        <AgreementValidator
          agreementText={agreementText}
          projectType={projectData.project_type}
          onValidationComplete={setValidationResults}
        />
      )}

      <div className="agreement-section">
        <div className="agreement-header">
          <div className="agreement-title">
            <i className="bi bi-file-earmark-text-fill me-2"></i>
            <h3>Contributor Agreement</h3>
          </div>
          <div className="agreement-actions">
            <button
              type="button"
              className="btn btn-action btn-regenerate"
              onClick={regenerateAgreement}
              disabled={loading || generatingAgreement}
              title="Generate a new agreement with current project data"
            >
              <i className="bi bi-arrow-repeat me-1"></i> Regenerate
            </button>
            <button
              type="button"
              className="btn btn-action btn-export"
              onClick={exportAsPDF}
              disabled={loading || generatingAgreement}
              title="Export agreement as PDF document"
            >
              <i className="bi bi-file-earmark-pdf me-1"></i> Export PDF
            </button>
            <button
              type="button"
              className="btn btn-action btn-validate"
              onClick={() => setShowValidator(!showValidator)}
              disabled={loading || generatingAgreement || !agreementText}
              title="Validate agreement for legal completeness"
            >
              <i className="bi bi-check-circle me-1"></i> {showValidator ? 'Hide Validation' : 'Validate'}
            </button>
            <button
              type="button"
              className="btn btn-action btn-save"
              onClick={saveAgreement}
              disabled={loading || generatingAgreement}
              title="Save this agreement as a template"
            >
              <i className="bi bi-save me-1"></i> Save Template
            </button>
            <button
              type="button"
              className="btn btn-action btn-test"
              onClick={() => setShowAgreementTester(!showAgreementTester)}
              title="Test agreement generation with different configurations"
            >
              <i className="bi bi-gear me-1"></i> Test Generator
            </button>
          </div>
        </div>

        <div className="agreement-content-container">
          {generatingAgreement ? (
            <div className="agreement-loading">
              <div className="spinner">
                <div className="spinner-border text-primary" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
              </div>
              <div className="loading-message">
                <p>Generating your agreement...</p>
                <p className="loading-subtext">This may take a few moments</p>
              </div>
            </div>
          ) : (
            <div className="agreement-document">
              <div className="document-toolbar">
                <div className="toolbar-actions">
                  <button
                    className="toolbar-btn"
                    title="Zoom In"
                    onClick={() => {
                      const content = document.querySelector('.agreement-content');
                      if (content) {
                        const currentSize = parseFloat(window.getComputedStyle(content).fontSize);
                        content.style.fontSize = `${Math.min(currentSize + 1, 16)}px`;
                      }
                    }}
                  >
                    <i className="bi bi-zoom-in"></i>
                  </button>
                  <button
                    className="toolbar-btn"
                    title="Zoom Out"
                    onClick={() => {
                      const content = document.querySelector('.agreement-content');
                      if (content) {
                        const currentSize = parseFloat(window.getComputedStyle(content).fontSize);
                        content.style.fontSize = `${Math.max(currentSize - 1, 8)}px`;
                      }
                    }}
                  >
                    <i className="bi bi-zoom-out"></i>
                  </button>
                  <button
                    className="toolbar-btn"
                    title="Print"
                    onClick={() => {
                      const printContent = document.createElement('div');
                      printContent.innerHTML = markdownToHTML(agreementText);
                      printContent.style.padding = '20px';

                      const printWindow = window.open('', '_blank');
                      printWindow.document.write(`
                        <html>
                          <head>
                            <title>${projectData.name} - Contributor Agreement</title>
                            <style>
                              body { font-family: Arial, sans-serif; line-height: 1.6; }
                              h1, h2, h3 { color: #333; }
                              h1 { font-size: 24px; border-bottom: 1px solid #eee; padding-bottom: 10px; }
                              h2 { font-size: 20px; }
                              h3 { font-size: 16px; }
                            </style>
                          </head>
                          <body>
                            ${printContent.innerHTML}
                          </body>
                        </html>
                      `);

                      printWindow.document.close();
                      setTimeout(() => {
                        printWindow.print();
                      }, 500);
                    }}
                  >
                    <i className="bi bi-printer"></i>
                  </button>
                  <div className="toolbar-divider"></div>
                  <div className="toolbar-dropdown">
                    <button className="toolbar-btn" title="More Options">
                      <i className="bi bi-three-dots"></i>
                    </button>
                    <div className="toolbar-dropdown-menu">
                      <button
                        className="dropdown-item"
                        onClick={() => {
                          const content = document.querySelector('.agreement-content');
                          if (content) {
                            content.style.fontSize = '0.875rem';
                          }
                        }}
                      >
                        <i className="bi bi-arrow-counterclockwise me-2"></i>
                        Reset Zoom
                      </button>
                      <button
                        className="dropdown-item"
                        onClick={() => {
                          // Copy to clipboard
                          navigator.clipboard.writeText(agreementText).then(() => {
                            toast.success('Agreement copied to clipboard');
                          }).catch(err => {
                            toast.error('Failed to copy: ' + err);
                          });
                        }}
                      >
                        <i className="bi bi-clipboard me-2"></i>
                        Copy Text
                      </button>
                    </div>
                  </div>
                </div>
                <div className="document-info">
                  <span className="document-type">
                    <i className="bi bi-file-earmark-text me-1"></i>
                    Contributor Agreement
                  </span>
                  <span className="document-date">
                    <i className="bi bi-calendar-date me-1"></i>
                    {new Date().toLocaleDateString()}
                  </span>
                </div>
              </div>
              <div className="agreement-preview">
                <div className="agreement-content" dangerouslySetInnerHTML={{ __html: markdownToHTML(agreementText) }}></div>
              </div>
            </div>
          )}

          <div className="signature-container">
            <div className="signature-header">
              <i className="bi bi-pen-fill me-2"></i>
              <h4>Digital Signature</h4>
            </div>

            <div className="signature-description">
              <i className="bi bi-info-circle me-2"></i>
              <p>By signing this agreement, you acknowledge that you have read, understood, and agree to the terms and conditions outlined above.</p>
            </div>

            <div className="signature-form">
              <div className="form-group">
                <label htmlFor="signatureName">
                  Full Name <span className="required">*</span>
                </label>
                <input
                  type="text"
                  id="signatureName"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  placeholder="Enter your full legal name"
                  required
                />
                <div className="form-hint">
                  Your full legal name is required for the agreement to be valid.
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="signatureEmail">Email</label>
                <input
                  type="email"
                  id="signatureEmail"
                  value={currentUser?.email || ''}
                  readOnly
                />
              </div>

              <div className="form-group">
                <label htmlFor="signatureDate">Date</label>
                <input
                  type="text"
                  id="signatureDate"
                  value={new Date().toLocaleDateString()}
                  readOnly
                />
              </div>

              <div className="form-group">
                <label>Signature</label>
                <div className="signature-pad-wrapper">
                  <SignatureCanvas
                    onSave={setSignatureData}
                    initialValue={signatureData}
                  />
                </div>
                <div className="signature-actions">
                  <button
                    type="button"
                    className="btn-clear-signature"
                    onClick={() => setSignatureData(null)}
                  >
                    <i className="bi bi-x-lg me-1"></i>
                    Clear
                  </button>
                </div>
              </div>

              <div className="signature-submit">
                <button
                  type="button"
                  className="btn-sign-agreement"
                  onClick={signAgreement}
                  disabled={loading || !signatureData || !fullName}
                >
                  {loading ? (
                    <>
                      <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                      Signing...
                    </>
                  ) : (
                    <>
                      <i className="bi bi-pen me-2"></i>
                      Sign Agreement
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {showAgreementTester && (
        <div className="agreement-tester-container">
          <div className="tester-header">
            <h3>
              <i className="bi bi-gear-fill me-2"></i>
              Agreement Generator Tester
            </h3>
            <button
              type="button"
              className="btn-close-tester"
              onClick={() => setShowAgreementTester(false)}
              title="Close tester"
            >
              <i className="bi bi-x-lg"></i>
            </button>
          </div>
          <AgreementTester />
        </div>
      )}
    </div>
  );
};

export default ReviewAgreement;
