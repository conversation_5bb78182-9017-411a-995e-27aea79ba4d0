import React, { useState, useEffect } from 'react';
import { supabase } from '../../../utils/supabase/supabase.utils';

const RevenueDebugger = ({ projectId }) => {
  const [revenueEntries, setRevenueEntries] = useState([]);
  const [revenueView, setRevenueView] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!projectId) return;

      try {
        setLoading(true);
        setError(null);

        // Query revenue_entries table
        const { data: entriesData, error: entriesError } = await supabase
          .from('revenue_entries')
          .select('*, revenue_sources(name, category)')
          .eq('project_id', projectId);

        if (entriesError) {
          console.error('Error fetching from revenue_entries:', entriesError);
          setError(`Error fetching from revenue_entries: ${entriesError.message}`);
        } else {
          console.log('Revenue Entries Data:', entriesData);
          setRevenueEntries(entriesData || []);
        }

        // Query revenue view
        const { data: viewData, error: viewError } = await supabase
          .from('revenue')
          .select('*')
          .eq('project_id', projectId);

        if (viewError) {
          console.error('Error fetching from revenue view:', viewError);
          setError((prev) => `${prev ? prev + ' | ' : ''}Error fetching from revenue view: ${viewError.message}`);
        } else {
          console.log('Revenue View Data:', viewData);
          setRevenueView(viewData || []);
        }
      } catch (err) {
        console.error('Unexpected error:', err);
        setError(`Unexpected error: ${err.message}`);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [projectId]);

  // Function to directly insert a test entry
  const insertTestEntry = async () => {
    try {
      setLoading(true);
      
      // Create a test entry
      const { data, error } = await supabase
        .from('revenue_entries')
        .insert([
          {
            project_id: projectId,
            amount: 100,
            currency: 'USD',
            date_received: new Date().toISOString().split('T')[0],
            description: 'Test revenue entry',
            status: 'pending',
            in_escrow: true,
            escrow_reason: 'Test escrow',
            distribution_status: 'pending'
          }
        ])
        .select();
        
      if (error) {
        console.error('Error inserting test entry:', error);
        setError(`Error inserting test entry: ${error.message}`);
      } else {
        console.log('Test entry inserted:', data);
        // Refresh data
        fetchData();
      }
    } catch (err) {
      console.error('Unexpected error:', err);
      setError(`Unexpected error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };
  
  // Function to refresh data
  const fetchData = async () => {
    if (!projectId) return;

    try {
      setLoading(true);
      setError(null);

      // Query revenue_entries table
      const { data: entriesData, error: entriesError } = await supabase
        .from('revenue_entries')
        .select('*, revenue_sources(name, category)')
        .eq('project_id', projectId);

      if (entriesError) {
        console.error('Error fetching from revenue_entries:', entriesError);
        setError(`Error fetching from revenue_entries: ${entriesError.message}`);
      } else {
        console.log('Revenue Entries Data:', entriesData);
        setRevenueEntries(entriesData || []);
      }

      // Query revenue view
      const { data: viewData, error: viewError } = await supabase
        .from('revenue')
        .select('*')
        .eq('project_id', projectId);

      if (viewError) {
        console.error('Error fetching from revenue view:', viewError);
        setError((prev) => `${prev ? prev + ' | ' : ''}Error fetching from revenue view: ${viewError.message}`);
      } else {
        console.log('Revenue View Data:', viewData);
        setRevenueView(viewData || []);
      }
    } catch (err) {
      console.error('Unexpected error:', err);
      setError(`Unexpected error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="revenue-debugger">
      <h3>Revenue Debugger</h3>
      
      {error && (
        <div className="error-message" style={{ color: 'red', marginBottom: '1rem' }}>
          {error}
        </div>
      )}
      
      <div className="debug-actions" style={{ marginBottom: '1rem' }}>
        <button 
          onClick={fetchData} 
          disabled={loading}
          style={{ marginRight: '0.5rem' }}
        >
          Refresh Data
        </button>
        
        <button 
          onClick={insertTestEntry} 
          disabled={loading}
        >
          Insert Test Entry
        </button>
      </div>
      
      <div className="debug-tables" style={{ display: 'flex', gap: '1rem' }}>
        <div className="entries-table" style={{ flex: 1 }}>
          <h4>Revenue Entries Table ({revenueEntries.length})</h4>
          {loading ? (
            <p>Loading...</p>
          ) : revenueEntries.length === 0 ? (
            <p>No entries found in revenue_entries table</p>
          ) : (
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr>
                  <th style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left' }}>ID</th>
                  <th style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left' }}>Amount</th>
                  <th style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left' }}>Date</th>
                  <th style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left' }}>Status</th>
                  <th style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left' }}>In Escrow</th>
                </tr>
              </thead>
              <tbody>
                {revenueEntries.map(entry => (
                  <tr key={entry.id}>
                    <td style={{ border: '1px solid #ddd', padding: '8px' }}>{entry.id.substring(0, 8)}...</td>
                    <td style={{ border: '1px solid #ddd', padding: '8px' }}>{entry.currency} {entry.amount}</td>
                    <td style={{ border: '1px solid #ddd', padding: '8px' }}>{new Date(entry.date_received).toLocaleDateString()}</td>
                    <td style={{ border: '1px solid #ddd', padding: '8px' }}>{entry.status}</td>
                    <td style={{ border: '1px solid #ddd', padding: '8px' }}>{entry.in_escrow ? 'Yes' : 'No'}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
        
        <div className="view-table" style={{ flex: 1 }}>
          <h4>Revenue View ({revenueView.length})</h4>
          {loading ? (
            <p>Loading...</p>
          ) : revenueView.length === 0 ? (
            <p>No entries found in revenue view</p>
          ) : (
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr>
                  <th style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left' }}>ID</th>
                  <th style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left' }}>Amount</th>
                  <th style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left' }}>Date</th>
                  <th style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left' }}>Status</th>
                  <th style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left' }}>In Escrow</th>
                </tr>
              </thead>
              <tbody>
                {revenueView.map(entry => (
                  <tr key={entry.id}>
                    <td style={{ border: '1px solid #ddd', padding: '8px' }}>{entry.id.substring(0, 8)}...</td>
                    <td style={{ border: '1px solid #ddd', padding: '8px' }}>{entry.currency} {entry.amount}</td>
                    <td style={{ border: '1px solid #ddd', padding: '8px' }}>{new Date(entry.date_received).toLocaleDateString()}</td>
                    <td style={{ border: '1px solid #ddd', padding: '8px' }}>{entry.status}</td>
                    <td style={{ border: '1px solid #ddd', padding: '8px' }}>{entry.in_escrow ? 'Yes' : 'No'}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </div>
  );
};

export default RevenueDebugger;
