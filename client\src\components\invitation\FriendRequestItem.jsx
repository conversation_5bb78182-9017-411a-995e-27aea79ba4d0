import React, { useState } from 'react';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { formatDistanceToNow } from 'date-fns';

const FriendRequestItem = ({ request, onUpdate }) => {
  const [isProcessing, setIsProcessing] = useState(false);
  
  // Format the time
  const timeAgo = formatDistanceToNow(new Date(request.created_at), { addSuffix: true });
  
  // Accept friend request
  const acceptRequest = async () => {
    setIsProcessing(true);
    
    try {
      // Update request status
      const { error: updateError } = await supabase
        .from('friend_requests')
        .update({ status: 'accepted', updated_at: new Date().toISOString() })
        .eq('id', request.id);
      
      if (updateError) throw updateError;
      
      // Create friend relationship (both ways)
      const { error: friendError1 } = await supabase
        .from('friends')
        .insert({
          user_id: request.recipient_id,
          friend_id: request.sender_id
        });
      
      if (friendError1) throw friendError1;
      
      const { error: friendError2 } = await supabase
        .from('friends')
        .insert({
          user_id: request.sender_id,
          friend_id: request.recipient_id
        });
      
      if (friendError2) throw friendError2;
      
      toast.success('Friend request accepted');
      
      // Notify parent component
      if (onUpdate) {
        onUpdate(request.id, 'accepted');
      }
    } catch (error) {
      console.error('Error accepting friend request:', error);
      toast.error('Failed to accept friend request');
    } finally {
      setIsProcessing(false);
    }
  };
  
  // Reject friend request
  const rejectRequest = async () => {
    setIsProcessing(true);
    
    try {
      // Update request status
      const { error } = await supabase
        .from('friend_requests')
        .update({ status: 'rejected', updated_at: new Date().toISOString() })
        .eq('id', request.id);
      
      if (error) throw error;
      
      toast.success('Friend request declined');
      
      // Notify parent component
      if (onUpdate) {
        onUpdate(request.id, 'rejected');
      }
    } catch (error) {
      console.error('Error rejecting friend request:', error);
      toast.error('Failed to decline friend request');
    } finally {
      setIsProcessing(false);
    }
  };
  
  return (
    <div className="invitation-item">
      <div className="invitation-avatar">
        <img 
          src={request.sender?.avatar_url || '/default-avatar-specs.png'} 
          alt={request.sender?.display_name || 'User'} 
        />
      </div>
      
      <div className="invitation-content">
        <div className="invitation-header">
          <div className="invitation-title">
            <span className="sender-name">{request.sender?.display_name || request.sender_email}</span>
            <span className="invitation-type">sent you a friend request</span>
          </div>
          <div className="invitation-time">{timeAgo}</div>
        </div>
        
        {request.message && (
          <div className="invitation-message">
            "{request.message}"
          </div>
        )}
        
        {request.status === 'pending' ? (
          <div className="invitation-actions">
            <button 
              className="btn-accept"
              onClick={acceptRequest}
              disabled={isProcessing}
            >
              {isProcessing ? 'Processing...' : 'Accept'}
            </button>
            <button 
              className="btn-decline"
              onClick={rejectRequest}
              disabled={isProcessing}
            >
              {isProcessing ? 'Processing...' : 'Decline'}
            </button>
          </div>
        ) : (
          <div className={`invitation-status ${request.status}`}>
            {request.status === 'accepted' ? 'Accepted' : 'Declined'}
          </div>
        )}
      </div>
    </div>
  );
};

export default FriendRequestItem;
