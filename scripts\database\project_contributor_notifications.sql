-- Create function to create notifications
CREATE OR REPLACE FUNCTION create_notification(
  p_user_id UUID,
  p_type TEXT,
  p_title TEXT,
  p_message TEXT,
  p_action_url TEXT,
  p_related_id UUID,
  p_metadata JSONB
) RETURNS UUID AS $$
DECLARE
  notification_id UUID;
<PERSON><PERSON>IN
  INSERT INTO public.notifications (
    user_id,
    type,
    title,
    message,
    action_url,
    related_id,
    metadata
  ) VALUES (
    p_user_id,
    p_type,
    p_title,
    p_message,
    p_action_url,
    p_related_id,
    p_metadata
  ) RETURNING id INTO notification_id;
  
  RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for project contributors (invitations)
CREATE OR REPLACE FUNCTION handle_project_contributor_notification() RETURNS TRIGGER AS $$
BEGIN
  -- For new project invitations (when status is pending)
  IF (TG_OP = 'INSERT' AND NEW.status = 'pending') THEN
    -- If recipient has a user account
    IF NEW.user_id IS NOT NULL THEN
      PERFORM create_notification(
        NEW.user_id,
        'project_invitation',
        'New Project Invitation',
        'You have been invited to join a project',
        '/notifications/projects',
        NEW.id,
        jsonb_build_object(
          'project_id', NEW.project_id,
          'project_name', (SELECT name FROM public.projects WHERE id = NEW.project_id),
          'message', ''
        )
      );
    END IF;
  -- For updated project invitations
  ELSIF (TG_OP = 'UPDATE') THEN
    -- If status changed to active (accepted)
    IF NEW.status = 'active' AND OLD.status = 'pending' THEN
      -- Get the project owner to notify them
      DECLARE
        project_owner_id UUID;
      BEGIN
        SELECT user_id INTO project_owner_id
        FROM project_contributors
        WHERE project_id = NEW.project_id AND permission_level = 'Owner'
        LIMIT 1;
        
        IF project_owner_id IS NOT NULL THEN
          PERFORM create_notification(
            project_owner_id,
            'project_invitation_accepted',
            'Project Invitation Accepted',
            (SELECT display_name FROM public.users WHERE id = NEW.user_id) || ' accepted your project invitation',
            '/project/' || NEW.project_id || '/team',
            NEW.id,
            jsonb_build_object(
              'project_id', NEW.project_id,
              'project_name', (SELECT name FROM public.projects WHERE id = NEW.project_id),
              'invitee_id', NEW.user_id,
              'invitee_name', (SELECT display_name FROM public.users WHERE id = NEW.user_id)
            )
          );
        END IF;
      END;
    -- If status changed to rejected
    ELSIF NEW.status = 'rejected' AND OLD.status = 'pending' THEN
      -- Get the project owner to notify them
      DECLARE
        project_owner_id UUID;
      BEGIN
        SELECT user_id INTO project_owner_id
        FROM project_contributors
        WHERE project_id = NEW.project_id AND permission_level = 'Owner'
        LIMIT 1;
        
        IF project_owner_id IS NOT NULL THEN
          PERFORM create_notification(
            project_owner_id,
            'project_invitation_rejected',
            'Project Invitation Declined',
            'Your project invitation was declined',
            '/project/' || NEW.project_id || '/team',
            NEW.id,
            jsonb_build_object(
              'project_id', NEW.project_id,
              'project_name', (SELECT name FROM public.projects WHERE id = NEW.project_id),
              'invitee_id', NEW.user_id
            )
          );
        END IF;
      END;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for project contributors if it doesn't exist
DROP TRIGGER IF EXISTS project_contributor_notification_trigger ON public.project_contributors;
CREATE TRIGGER project_contributor_notification_trigger
AFTER INSERT OR UPDATE ON public.project_contributors
FOR EACH ROW
EXECUTE FUNCTION handle_project_contributor_notification();
