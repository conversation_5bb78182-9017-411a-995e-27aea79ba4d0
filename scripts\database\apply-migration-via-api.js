// Apply migration via Supabase API
// Use service role to apply database changes

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

const supabase = createClient(
  'https://hqqlrrqvjcetoxbdjgzx.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzgyMDU1OSwiZXhwIjoyMDU5Mzk2NTU5fQ.k5xvFBzBxNMCtPd0d8Ko9dAcT5Y3op3ZgFvlAf85LLs'
);

async function applyMigration() {
  console.log('🔧 Applying database migration...\n');
  
  try {
    // Test connection first
    console.log('Testing database connection...');
    const { data: testData, error: testError } = await supabase
      .from('teams')
      .select('count')
      .limit(1);
      
    if (testError) {
      console.log(`❌ Connection test failed: ${testError.message}`);
      return;
    }
    
    console.log('✅ Database connection successful\n');
    
    // Check if companies table exists
    console.log('Checking existing schema...');
    const { data: companiesCheck, error: companiesError } = await supabase
      .from('companies')
      .select('count')
      .limit(1);
      
    if (companiesError && companiesError.code === '42P01') {
      console.log('Companies table does not exist, creating...');
      
      // Create companies table using RPC
      const createCompaniesSQL = `
        CREATE TABLE IF NOT EXISTS public.companies (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          legal_name TEXT NOT NULL,
          tax_id TEXT NOT NULL UNIQUE,
          company_type TEXT NOT NULL,
          primary_address JSONB NOT NULL,
          primary_email TEXT NOT NULL,
          created_by UUID REFERENCES auth.users(id),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
        );
      `;
      
      const { data: createResult, error: createError } = await supabase.rpc('exec_sql', {
        sql: createCompaniesSQL
      });
      
      if (createError) {
        console.log(`❌ Failed to create companies table: ${createError.message}`);
      } else {
        console.log('✅ Companies table created');
      }
      
    } else if (companiesError) {
      console.log(`❌ Error checking companies table: ${companiesError.message}`);
    } else {
      console.log('✅ Companies table already exists');
    }
    
    // Add company fields to teams table
    console.log('\nAdding company fields to teams table...');
    
    const alterTeamsSQL = `
      DO $$
      BEGIN
          IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teams' AND column_name = 'company_id') THEN
              ALTER TABLE public.teams ADD COLUMN company_id UUID;
          END IF;
          
          IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teams' AND column_name = 'is_business_entity') THEN
              ALTER TABLE public.teams ADD COLUMN is_business_entity BOOLEAN DEFAULT false;
          END IF;
          
          IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teams' AND column_name = 'alliance_type') THEN
              ALTER TABLE public.teams ADD COLUMN alliance_type TEXT DEFAULT 'emerging';
          END IF;
      END $$;
    `;
    
    const { data: alterResult, error: alterError } = await supabase.rpc('exec_sql', {
      sql: alterTeamsSQL
    });
    
    if (alterError) {
      console.log(`❌ Failed to alter teams table: ${alterError.message}`);
    } else {
      console.log('✅ Teams table updated with company fields');
    }
    
    // Insert VRC sample data
    console.log('\nInserting VRC sample data...');
    
    const { data: vrcData, error: vrcError } = await supabase
      .from('companies')
      .upsert({
        legal_name: 'VRC Entertainment LLC',
        tax_id: '12-3456789',
        company_type: 'llc',
        primary_address: {
          street: '123 Hollywood Blvd',
          city: 'Los Angeles',
          state: 'CA',
          zip: '90028',
          country: 'US'
        },
        primary_email: '<EMAIL>'
      }, {
        onConflict: 'tax_id'
      });
      
    if (vrcError) {
      console.log(`❌ Failed to insert VRC data: ${vrcError.message}`);
    } else {
      console.log('✅ VRC sample data inserted');
    }
    
    console.log('\n🎉 Migration completed successfully!');
    
  } catch (error) {
    console.log(`❌ Migration failed: ${error.message}`);
  }
}

applyMigration();
