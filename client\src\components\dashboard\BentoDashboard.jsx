import React, { useState, useEffect } from 'react';
import { Card, CardBody, Button, Progress, Chip } from '@heroui/react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';

/**
 * Bento Grid Dashboard Component
 * 
 * A modern, card-based dashboard with beautiful bento grid layout.
 * Features dynamic content, animations, and quick actions.
 */
const BentoDashboard = ({ currentUser, displayName }) => {
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    totalProjects: 5,
    activeContributions: 12,
    totalRevenue: 2350,
    pendingRoyalties: 573
  });

  // Animation variants for cards
  const cardVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.95 },
    visible: { opacity: 1, y: 0, scale: 1 },
    hover: { scale: 1.02, y: -2 }
  };

  // Quick action cards data
  const quickActions = [
    {
      id: 'new-project',
      title: 'New Project',
      description: 'Start your next big idea',
      icon: '🚀',
      color: 'from-green-500 to-emerald-600',
      action: () => navigate('/start')
    },
    {
      id: 'browse-projects',
      title: 'Browse Projects',
      description: 'Explore existing projects',
      icon: '📊',
      color: 'from-blue-500 to-cyan-600',
      action: () => navigate('/track')
    },
    {
      id: 'track-contribution',
      title: 'Track Contribution',
      description: 'Log your work progress',
      icon: '⏱️',
      color: 'from-orange-500 to-red-600',
      action: () => navigate('/track')
    },
    {
      id: 'view-analytics',
      title: 'View Analytics',
      description: 'Check your performance',
      icon: '📈',
      color: 'from-purple-500 to-pink-600',
      action: () => navigate('/track')
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          className="mb-8 text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
            Welcome back, {displayName}
          </h1>
          <p className="text-lg text-default-600">
            Here's what's happening with your projects today
          </p>
          <Chip color="success" variant="flat" className="mt-2">
            ✨ Experimental Navigation Active
          </Chip>
        </motion.div>

        {/* Bento Grid Layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Large Welcome Card */}
          <motion.div
            className="lg:col-span-2 lg:row-span-2"
            variants={cardVariants}
            initial="hidden"
            animate="visible"
            whileHover="hover"
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <Card className="h-full bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 text-white">
              <CardBody className="p-8 flex flex-col justify-between">
                <div>
                  <h2 className="text-3xl font-bold mb-4">🎯 Your Dashboard</h2>
                  <p className="text-lg opacity-90 mb-6">
                    Track your creative journey, manage projects, and earn royalties from your contributions.
                  </p>
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="bg-white/20 rounded-lg p-3 backdrop-blur-sm">
                      <div className="text-2xl font-bold">{stats.totalProjects}</div>
                      <div className="text-sm opacity-80">Active Projects</div>
                    </div>
                    <div className="bg-white/20 rounded-lg p-3 backdrop-blur-sm">
                      <div className="text-2xl font-bold">{stats.activeContributions}</div>
                      <div className="text-sm opacity-80">Contributions</div>
                    </div>
                  </div>
                </div>
                <Button
                  size="lg"
                  className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border-white/30"
                  variant="bordered"
                  onClick={() => navigate('/track')}
                >
                  View All Projects →
                </Button>
              </CardBody>
            </Card>
          </motion.div>

          {/* Revenue Card */}
          <motion.div
            variants={cardVariants}
            initial="hidden"
            animate="visible"
            whileHover="hover"
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            <Card className="h-full">
              <CardBody className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">Total Revenue</h3>
                  <span className="text-2xl">💰</span>
                </div>
                <div className="text-3xl font-bold text-green-600 mb-2">
                  ${stats.totalRevenue.toLocaleString()}
                </div>
                <div className="text-sm text-default-600 mb-4">
                  +12% from last month
                </div>
                <Progress
                  value={75}
                  color="success"
                  className="mb-3"
                  size="sm"
                />
                <Button
                  size="sm"
                  color="success"
                  variant="flat"
                  onClick={() => navigate('/earn')}
                  className="w-full"
                >
                  View Details
                </Button>
              </CardBody>
            </Card>
          </motion.div>

          {/* Pending Royalties Card */}
          <motion.div
            variants={cardVariants}
            initial="hidden"
            animate="visible"
            whileHover="hover"
            transition={{ duration: 0.3, delay: 0.3 }}
          >
            <Card className="h-full">
              <CardBody className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">Pending Royalties</h3>
                  <span className="text-2xl">💎</span>
                </div>
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  ${stats.pendingRoyalties}
                </div>
                <div className="text-sm text-default-600 mb-4">
                  Ready for payout
                </div>
                <Button
                  size="sm"
                  color="primary"
                  variant="flat"
                  onClick={() => navigate('/earn')}
                  className="w-full"
                >
                  Claim Now
                </Button>
              </CardBody>
            </Card>
          </motion.div>

          {/* Quick Actions Grid */}
          {quickActions.map((action, index) => (
            <motion.div
              key={action.id}
              variants={cardVariants}
              initial="hidden"
              animate="visible"
              whileHover="hover"
              transition={{ duration: 0.3, delay: 0.4 + index * 0.1 }}
            >
              <Card 
                className="h-full cursor-pointer group"
                onClick={action.action}
              >
                <CardBody className={`p-6 bg-gradient-to-br ${action.color} text-white relative overflow-hidden`}>
                  {/* Background Pattern */}
                  <div className="absolute inset-0 opacity-20">
                    <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1),transparent_50%)]" />
                  </div>
                  
                  <div className="relative z-10">
                    <div className="text-3xl mb-3 group-hover:scale-110 transition-transform">
                      {action.icon}
                    </div>
                    <h3 className="text-lg font-bold mb-2">{action.title}</h3>
                    <p className="text-sm opacity-90">{action.description}</p>
                  </div>
                  
                  {/* Hover Effect */}
                  <div className="absolute inset-0 bg-white/0 group-hover:bg-white/10 transition-all duration-300" />
                </CardBody>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Recent Activity Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <Card>
            <CardBody className="p-6">
              <h3 className="text-xl font-bold mb-4">🔥 Recent Activity</h3>
              <div className="space-y-3">
                {[
                  { action: 'Created new project', project: 'Dream Project', time: '2 hours ago' },
                  { action: 'Submitted contribution', project: 'Royaltea', time: '1 day ago' },
                  { action: 'Received royalty payment', project: 'VGA', time: '3 days ago' }
                ].map((activity, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-default-100 rounded-lg">
                    <div>
                      <div className="font-medium">{activity.action}</div>
                      <div className="text-sm text-default-600">{activity.project}</div>
                    </div>
                    <div className="text-sm text-default-500">{activity.time}</div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default BentoDashboard;
