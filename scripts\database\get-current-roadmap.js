// Script to get the current roadmap data from the database
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzgyMDU1OSwiZXhwIjoyMDU5Mzk2NTU5fQ.k5xvFBzBxNMCtPd0d8Ko9dAcT5Y3op3ZgFvlAf85LLs';

console.log('Using Supabase URL:', supabaseUrl);

const supabase = createClient(supabaseUrl, supabaseKey);

// Function to get the current roadmap data
async function getCurrentRoadmap() {
  try {
    console.log('\n=== Getting current roadmap data ===');

    // Get the latest roadmap entry
    const { data: roadmapData, error: roadmapError } = await supabase
      .from('roadmap')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1);

    if (roadmapError) {
      console.error('Error fetching roadmap from Supabase:', roadmapError);
      return null;
    }

    if (!roadmapData || roadmapData.length === 0) {
      console.log('No roadmap data found in the database');
      return null;
    }

    console.log('Found roadmap data:', roadmapData[0]);

    // Calculate stats
    const roadmap = roadmapData[0].data;
    const stats = calculateStats(roadmap);

    console.log('\n=== Roadmap Stats ===');
    console.log(`Total Tasks: ${stats.totalTasks}`);
    console.log(`Completed Tasks: ${stats.completedTasks}`);
    console.log(`Progress Percentage: ${stats.progressPercentage}%`);

    console.log('\n=== Phase Progress ===');
    stats.phases.forEach(phase => {
      console.log(`Phase ${phase.id}: ${phase.title} - ${phase.progress}%`);
    });

    return { roadmap, stats };
  } catch (error) {
    console.error('Error in getCurrentRoadmap:', error);
    return null;
  }
}

// Function to calculate stats
function calculateStats(roadmapData) {
  let totalTasks = 0;
  let completedTasks = 0;
  const phases = [];

  // Filter out metadata entries
  const actualPhases = roadmapData.filter(item => !item.type || item.type !== 'metadata');

  actualPhases.forEach(phase => {
    let phaseTotal = 0;
    let phaseCompleted = 0;

    if (phase.sections) {
      phase.sections.forEach(section => {
        if (section.tasks) {
          section.tasks.forEach(task => {
            totalTasks++;
            phaseTotal++;

            if (task.completed) {
              completedTasks++;
              phaseCompleted++;
            }
          });
        }
      });
    }

    const phaseProgress = phaseTotal > 0 ? Math.round((phaseCompleted / phaseTotal) * 100) : 0;

    phases.push({
      id: phase.id,
      title: phase.title,
      timeframe: phase.timeframe,
      progress: phaseProgress
    });
  });

  const progressPercentage = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

  return {
    totalTasks,
    completedTasks,
    progressPercentage,
    phases
  };
}

// Function to update the latest feature
async function updateLatestFeature(latestFeature) {
  try {
    console.log(`\n=== Updating latest feature to: "${latestFeature}" ===`);

    // Get the latest roadmap entry
    const { data: roadmapData, error: roadmapError } = await supabase
      .from('roadmap')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1);

    if (roadmapError) {
      console.error('Error fetching roadmap from Supabase:', roadmapError);
      return false;
    }

    if (!roadmapData || roadmapData.length === 0) {
      console.log('No roadmap data found in the database');
      return false;
    }

    // Update the latest feature
    const { data: updatedData, error: updateError } = await supabase
      .from('roadmap')
      .update({ latest_feature: latestFeature })
      .eq('id', roadmapData[0].id)
      .select();

    if (updateError) {
      console.error('Error updating latest feature:', updateError);
      return false;
    }

    console.log('Updated latest feature successfully:', updatedData);
    return true;
  } catch (error) {
    console.error('Error in updateLatestFeature:', error);
    return false;
  }
}

// Function to update a task completion status
async function updateTaskStatus(phaseId, sectionId, taskId, completed) {
  try {
    console.log(`\n=== Updating task ${phaseId}.${sectionId}.${taskId} to ${completed ? 'completed' : 'not completed'} ===`);

    // Get the latest roadmap entry
    const { data: roadmapData, error: roadmapError } = await supabase
      .from('roadmap')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1);

    if (roadmapError) {
      console.error('Error fetching roadmap from Supabase:', roadmapError);
      return false;
    }

    if (!roadmapData || roadmapData.length === 0) {
      console.log('No roadmap data found in the database');
      return false;
    }

    // Clone the roadmap data
    const roadmap = JSON.parse(JSON.stringify(roadmapData[0].data));

    // Find the phase
    const phase = roadmap.find(p => p.id === phaseId);
    if (!phase) {
      console.error(`Phase ${phaseId} not found`);
      return false;
    }

    // Find the section
    const section = phase.sections.find(s => s.id === sectionId);
    if (!section) {
      console.error(`Section ${sectionId} not found in phase ${phaseId}`);
      return false;
    }

    // Find the task
    const task = section.tasks.find(t => t.id === taskId);
    if (!task) {
      console.error(`Task ${taskId} not found in section ${sectionId} of phase ${phaseId}`);
      return false;
    }

    // Update the task
    task.completed = completed;

    // Update the roadmap
    const { data: updatedData, error: updateError } = await supabase
      .from('roadmap')
      .update({ data: roadmap })
      .eq('id', roadmapData[0].id)
      .select();

    if (updateError) {
      console.error('Error updating roadmap:', updateError);
      return false;
    }

    console.log('Updated roadmap successfully');
    return true;
  } catch (error) {
    console.error('Error in updateTaskStatus:', error);
    return false;
  }
}

// Main function
async function main() {
  try {
    console.log('=== Roadmap Management ===');

    // Get the current roadmap
    const roadmapData = await getCurrentRoadmap();

    if (!roadmapData) {
      console.error('Failed to get roadmap data');
      return;
    }

    // Update the latest feature
    const latestFeature = "Enhanced revenue tracking and royalty calculation system";
    await updateLatestFeature(latestFeature);

    // Update task status for the invitation acceptance fix
    await updateTaskStatus(3, "3.2", "3.2.3", true);

    // Update task status for the contribution validation system
    await updateTaskStatus(3, "3.3", "3.3.1", true);

    console.log('\n=== Roadmap Management Complete ===');
  } catch (error) {
    console.error('Error in main function:', error);
  }
}

// Run the main function
main();
