import React, { useState, useEffect } from 'react';
import { supabase } from '../../../utils/supabase/supabase.utils';

const KanbanTaskModal = ({ isOpen, onClose, onSave, onDelete, task, projectId }) => {
  const [formData, setFormData] = useState({
    id: '',
    title: '',
    description: '',
    status: 'todo',
    assignee_id: '',
    task_type: '',
    difficulty_level: '',
    difficulty_points: 0,
    estimated_hours: 0,
    logged_hours: 0
  });
  const [contributors, setContributors] = useState([]);
  const [taskTypes, setTaskTypes] = useState([]);
  const [difficultyLevels, setDifficultyLevels] = useState([]);
  const [loading, setLoading] = useState(false);

  // Initialize form data when task changes
  useEffect(() => {
    if (task) {
      setFormData({
        id: task.id || '',
        title: task.title || '',
        description: task.description || '',
        status: task.status || 'todo',
        assignee_id: task.assignee_id || '',
        task_type: task.task_type || '',
        difficulty_level: task.difficulty_level || '',
        difficulty_points: task.difficulty_points || 0,
        estimated_hours: task.estimated_hours || 0,
        logged_hours: task.logged_hours || 0
      });
    } else {
      // Reset form for new task
      setFormData({
        id: '',
        title: '',
        description: '',
        status: 'todo',
        assignee_id: '',
        task_type: '',
        difficulty_level: '',
        difficulty_points: 0,
        estimated_hours: 0,
        logged_hours: 0
      });
    }
  }, [task]);

  // Fetch project contributors and configuration
  useEffect(() => {
    const fetchProjectData = async () => {
      try {
        setLoading(true);

        // Fetch project contributors
        let contributorsData;
        let contributorsError;

        try {
          // Try to fetch contributors with user information
          const result = await supabase
            .from('project_contributors')
            .select('user_id, users:user_id(id, display_name, avatar_url)')
            .eq('project_id', projectId);

          contributorsData = result.data;
          contributorsError = result.error;

          if (contributorsError) throw contributorsError;

          // Format contributors data
          const formattedContributors = contributorsData.map(contributor => ({
            id: contributor.user_id,
            display_name: contributor.users?.display_name || 'Unknown User',
            avatar_url: contributor.users?.avatar_url
          }));

          setContributors(formattedContributors);
        } catch (e) {
          console.log('Error fetching contributors with user join:', e);

          // Fallback: fetch just the contributors without the join
          const { data: contributorIds, error } = await supabase
            .from('project_contributors')
            .select('user_id')
            .eq('project_id', projectId);

          if (error) throw error;

          // Fetch users separately
          if (contributorIds && contributorIds.length > 0) {
            const userIds = contributorIds.map(c => c.user_id);

            const { data: usersData } = await supabase
              .from('users')
              .select('id, display_name, avatar_url')
              .in('id', userIds);

            if (usersData) {
              const formattedContributors = contributorIds.map(contributor => {
                const user = usersData.find(u => u.id === contributor.user_id);
                return {
                  id: contributor.user_id,
                  display_name: user?.display_name || 'Unknown User',
                  avatar_url: user?.avatar_url
                };
              });

              setContributors(formattedContributors);
            }
          } else {
            setContributors([]);
          }
        }

        // Fetch project contribution tracking config
        const { data: configData, error: configError } = await supabase
          .from('contribution_tracking_config')
          .select('*')
          .eq('project_id', projectId)
          .single();

        if (configError && configError.code !== 'PGRST116') {
          // PGRST116 is "no rows returned" error, which we can handle
          throw configError;
        }

        // Set task types and difficulty levels from config
        if (configData) {
          if (configData.task_types && Array.isArray(configData.task_types)) {
            setTaskTypes(configData.task_types);
          }

          if (configData.difficulty_levels && Array.isArray(configData.difficulty_levels)) {
            setDifficultyLevels(configData.difficulty_levels);
          }
        } else {
          // Use default values if no config exists
          setTaskTypes([
            { name: 'Development', value: 'development' },
            { name: 'Design', value: 'design' },
            { name: 'Documentation', value: 'documentation' },
            { name: 'Testing', value: 'testing' },
            { name: 'Research', value: 'research' },
            { name: 'Management', value: 'management' },
            { name: 'Other', value: 'other' }
          ]);

          setDifficultyLevels([
            { name: 'Easy', value: 'easy', multiplier: 1 },
            { name: 'Medium', value: 'medium', multiplier: 2 },
            { name: 'Hard', value: 'hard', multiplier: 3 },
            { name: 'Expert', value: 'expert', multiplier: 5 }
          ]);
        }

        setLoading(false);
      } catch (error) {
        console.error('Error fetching project data:', error);
        setLoading(false);
      }
    };

    if (projectId && isOpen) {
      fetchProjectData();
    }
  }, [projectId, isOpen]);

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;

    // Convert numeric values
    if (name === 'difficulty_points' || name === 'estimated_hours') {
      setFormData({
        ...formData,
        [name]: parseFloat(value) || 0
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }

    // Update difficulty points when difficulty level changes
    if (name === 'difficulty_level') {
      const selectedLevel = difficultyLevels.find(level => level.value === value);
      if (selectedLevel && selectedLevel.multiplier) {
        setFormData(prev => ({
          ...prev,
          [name]: value,
          difficulty_points: selectedLevel.multiplier
        }));
      }
    }
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  // Handle task deletion
  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this task?')) {
      onDelete(formData.id);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="kanban-modal-overlay">
      <div className="kanban-modal">
        <div className="modal-header">
          <h2>{task ? 'Edit Task' : 'Create New Task'}</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        {loading ? (
          <div className="modal-loading">Loading...</div>
        ) : (
          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="title">Title</label>
              <input
                type="text"
                id="title"
                name="title"
                value={formData.title}
                onChange={handleChange}
                required
                className="form-control"
              />
            </div>

            <div className="form-group">
              <label htmlFor="description">Description</label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                className="form-control"
                rows="3"
              />
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="status">Status</label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  className="form-control"
                >
                  <option value="todo">To Do</option>
                  <option value="in_progress">In Progress</option>
                  <option value="review">Review</option>
                  <option value="done">Done</option>
                  <option value="blocked">Blocked</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="assignee_id">Assignee</label>
                <select
                  id="assignee_id"
                  name="assignee_id"
                  value={formData.assignee_id}
                  onChange={handleChange}
                  className="form-control"
                >
                  <option value="">Unassigned</option>
                  {contributors.map(contributor => (
                    <option key={contributor.id} value={contributor.id}>
                      {contributor.display_name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="task_type">Task Type</label>
                <select
                  id="task_type"
                  name="task_type"
                  value={formData.task_type}
                  onChange={handleChange}
                  className="form-control"
                >
                  <option value="">Select Type</option>
                  {taskTypes.map(type => (
                    <option key={type.value} value={type.value}>
                      {type.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="difficulty_level">Difficulty</label>
                <select
                  id="difficulty_level"
                  name="difficulty_level"
                  value={formData.difficulty_level}
                  onChange={handleChange}
                  className="form-control"
                >
                  <option value="">Select Difficulty</option>
                  {difficultyLevels.map(level => (
                    <option key={level.value} value={level.value}>
                      {level.name} ({level.multiplier}x)
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="difficulty_points">Difficulty Points</label>
                <input
                  type="number"
                  id="difficulty_points"
                  name="difficulty_points"
                  value={formData.difficulty_points}
                  onChange={handleChange}
                  min="0"
                  step="1"
                  className="form-control"
                />
              </div>

              <div className="form-group">
                <label htmlFor="estimated_hours">Estimated Hours</label>
                <input
                  type="number"
                  id="estimated_hours"
                  name="estimated_hours"
                  value={formData.estimated_hours}
                  onChange={handleChange}
                  min="0"
                  step="0.5"
                  className="form-control"
                />
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="logged_hours">Logged Hours</label>
                <input
                  type="number"
                  id="logged_hours"
                  name="logged_hours"
                  value={formData.logged_hours}
                  onChange={handleChange}
                  min="0"
                  step="0.5"
                  className="form-control"
                />
                {task && (
                  <small className="form-text text-muted">
                    Track time spent on this task. This contributes to royalty calculations.
                  </small>
                )}
              </div>
            </div>

            <div className="modal-footer">
              {task && (
                <button
                  type="button"
                  className="btn btn-danger"
                  onClick={handleDelete}
                >
                  Delete
                </button>
              )}
              <button type="button" className="btn btn-secondary" onClick={onClose}>
                Cancel
              </button>
              <button type="submit" className="btn btn-primary">
                {task ? 'Update' : 'Create'} Task
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default KanbanTaskModal;
