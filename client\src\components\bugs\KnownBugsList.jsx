import React, { useState, useEffect } from 'react';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

// Hardcoded bugs for testing - remove when database is working properly
const HARDCODED_BUGS = [
  {
    id: 1,
    title: 'Project Invitations not working',
    description: 'Project invitations do not update notification status correctly.',
    solution: null,
    status: 'acknowledged',
    created_at: '2023-04-15T12:00:00Z',
    fixed_at: null,
    is_public: true
  },
  {
    id: 2,
    title: 'Loading indicator persists after tab switch',
    description: 'Sometimes the loading indicator continues to display after switching tabs.',
    solution: 'Refresh the page to clear the loading state.',
    status: 'fixed',
    created_at: '2023-03-15T12:00:00Z',
    fixed_at: '2023-03-17T12:00:00Z',
    is_public: true
  }
];

const KnownBugsList = () => {
  const [bugs, setBugs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [expandedBugId, setExpandedBugId] = useState(null);

  // Fetch public bugs
  useEffect(() => {
    const fetchBugs = async () => {
      try {
        setLoading(true);

        // Use hardcoded bugs for now to ensure consistent display
        setBugs(HARDCODED_BUGS);

        /* Commented out until database issues are resolved
        const { data, error } = await supabase
          .from('bug_reports')
          .select('*')
          .eq('is_public', true)
          .order('created_at', { ascending: false });

        if (error) throw error;
        setBugs(data || []);
        */
      } catch (error) {
        console.error('Error fetching known bugs:', error);
        toast.error('Failed to load known bugs');
        // Fallback to hardcoded bugs
        setBugs(HARDCODED_BUGS);
      } finally {
        setLoading(false);
      }
    };

    fetchBugs();
  }, []);

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return <div className="known-bugs-loading">Loading known bugs...</div>;
  }

  if (bugs.length === 0) {
    return <div className="known-bugs-empty">No known bugs at this time.</div>;
  }

  const toggleBugExpansion = (bugId) => {
    setExpandedBugId(expandedBugId === bugId ? null : bugId);
  };

  return (
    <div className="known-bugs-container">
      <h3 className="section-title">Known Issues</h3>
      <p className="section-description">
        We're actively working on fixing these issues. Click on any issue for more details.
      </p>

      <div className="bugs-list">
        {bugs.map(bug => {
          const isExpanded = expandedBugId === bug.id;
          return (
            <div
              key={bug.id}
              className={`bug-item ${bug.status} ${isExpanded ? 'expanded' : ''}`}
            >
              <div
                className="bug-header clickable"
                onClick={() => toggleBugExpansion(bug.id)}
              >
                <h4 className="bug-title">{bug.title}</h4>
                <div className="bug-status-and-toggle">
                  <div className="bug-status">{bug.status}</div>
                  <div className="expand-toggle">
                    <i className={`bi ${isExpanded ? 'bi-chevron-up' : 'bi-chevron-down'}`}></i>
                  </div>
                </div>
              </div>

              <div className="bug-content">
                <div className="bug-description">
                  {bug.description}
                </div>

                {bug.solution && (
                  <div className="bug-solution">
                    <strong>Solution:</strong> {bug.solution}
                  </div>
                )}

                {isExpanded && (
                  <div className="bug-details">
                    <div className="bug-details-section">
                      <h5 className="bug-details-title">Details</h5>
                      <div className="bug-details-content">
                        <p>{bug.description}</p>
                        {bug.solution && (
                          <div className="bug-solution-expanded">
                            <h6>Solution:</h6>
                            <p>{bug.solution}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                <div className="bug-meta">
                  <span className="bug-date">Reported: {formatDate(bug.created_at)}</span>
                  {bug.status === 'fixed' && (
                    <span className="bug-fixed-date">Fixed: {formatDate(bug.fixed_at)}</span>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default KnownBugsList;
