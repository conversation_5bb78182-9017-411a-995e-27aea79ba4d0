# Comprehensive Migration Instructions for Users Table

This guide provides a comprehensive solution to fix the timestamp columns in the `users` table. Follow these steps to apply the migration manually:

## Step 1: Access the SQL Editor

1. Log in to your Supabase dashboard
2. Select your project
3. Click on "SQL Editor" in the left sidebar
4. Click "New Query" to create a new SQL query

## Step 2: Check the Current Table Structure

First, let's check the current structure of the users table to understand what we're working with:

```sql
-- Check the structure of the users table
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = 'users'
ORDER BY ordinal_position;
```

## Step 3: Run the Comprehensive Migration SQL

Copy and paste the following SQL into the editor:

```sql
-- Add timestamp columns to users table
-- This migration adds both created_at and updated_at columns if they don't exist

-- Add created_at column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'users'
        AND column_name = 'created_at'
    ) THEN
        ALTER TABLE public.users
        ADD COLUMN created_at TIMESTAMPTZ DEFAULT NOW();
        
        RAISE NOTICE 'Added created_at column to users table';
    ELSE
        RAISE NOTICE 'created_at column already exists in users table';
    END IF;
END $$;

-- Add updated_at column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'users'
        AND column_name = 'updated_at'
    ) THEN
        ALTER TABLE public.users
        ADD COLUMN updated_at TIMESTAMPTZ DEFAULT NOW();
        
        RAISE NOTICE 'Added updated_at column to users table';
    ELSE
        RAISE NOTICE 'updated_at column already exists in users table';
    END IF;
END $$;
```

Alternatively, you can use this simpler version if you prefer:

```sql
-- Simple version without checking if columns exist
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ DEFAULT NOW();
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT NOW();
```

## Step 4: Execute the Query

Click the "Run" button to execute the SQL query. You should see a success message in the results panel.

## Step 5: Verify the Changes

After running the migration, check the table structure again to confirm the columns were added:

```sql
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = 'users'
ORDER BY ordinal_position;
```

## Step 6: Test the Authentication Flow

After successfully running the migration, test the authentication to ensure that:

1. New users can sign up with Google
2. User profiles are correctly created in the `users` table
3. The `created_at` and `updated_at` columns are properly populated

## Troubleshooting

If you encounter any issues:

1. Check the error messages in the SQL Editor results panel
2. Make sure you have the necessary permissions to alter the `users` table
3. Verify that the `users` table exists and has the expected structure

For more help, refer to the Supabase documentation on [schema migrations](https://supabase.com/docs/guides/database/migrations).
