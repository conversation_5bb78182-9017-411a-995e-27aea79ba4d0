// User Discovery API
// Handles user search, discovery, and profile viewing for social features
// Based on docs/design-system/systems/social-system.md

const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' };
  }

  try {
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Get user from authorization header
    const authHeader = event.headers.authorization;
    if (!authHeader) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Authorization header required' })
      };
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Invalid authentication token' })
      };
    }

    const { httpMethod, path } = event;
    const pathParts = path.split('/').filter(part => part);
    
    switch (httpMethod) {
      case 'GET':
        return await handleGet(supabase, user, pathParts, event.queryStringParameters);
      default:
        return {
          statusCode: 405,
          headers,
          body: JSON.stringify({ error: 'Method not allowed' })
        };
    }
  } catch (error) {
    console.error('API Error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error', details: error.message })
    };
  }
};

// GET handlers
async function handleGet(supabase, user, pathParts, queryParams) {
  const endpoint = pathParts[pathParts.length - 1];
  
  switch (endpoint) {
    case 'discover':
      return await discoverUsers(supabase, user.id, queryParams);
    case 'search':
      return await searchUsers(supabase, user.id, queryParams);
    default:
      // Check if it's a profile request: /users/:id/profile or /users/:id/mutual-allies
      if (pathParts.length === 3) {
        const userId = pathParts[1];
        const action = pathParts[2];
        
        if (action === 'profile') {
          return await getUserProfile(supabase, user.id, userId);
        } else if (action === 'mutual-allies') {
          return await getMutualAllies(supabase, user.id, userId);
        }
      }
      
      return {
        statusCode: 404,
        headers,
        body: JSON.stringify({ error: 'Endpoint not found' })
      };
  }
}

// Discover potential allies using AI-like recommendations
async function discoverUsers(supabase, userId, queryParams) {
  const { limit = 10, skills, location, experience_level } = queryParams || {};
  
  // Get current user's skills for matching
  const { data: userSkills } = await supabase
    .from('user_skills')
    .select('skill_name')
    .eq('user_id', userId);

  const userSkillNames = userSkills?.map(s => s.skill_name) || [];
  
  // Base query to find users excluding current user and existing allies
  let query = supabase
    .from('users')
    .select(`
      id,
      display_name,
      email,
      user_profiles(
        professional_title,
        bio,
        location,
        availability_status,
        profile_photo_url
      ),
      user_skills(skill_name, skill_level, years_experience),
      network_analytics(network_score, network_level, total_allies)
    `)
    .neq('id', userId)
    .not('id', 'in', `(
      SELECT CASE 
        WHEN user_id = '${userId}' THEN ally_id 
        ELSE user_id 
      END 
      FROM user_allies 
      WHERE (user_id = '${userId}' OR ally_id = '${userId}') 
      AND status IN ('accepted', 'pending')
    )`)
    .limit(limit * 2); // Get more to filter and rank

  const { data, error } = await query;

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to discover users', details: error.message })
    };
  }

  // Calculate recommendation scores
  const recommendations = data.map(discoveredUser => {
    let score = 0;
    const reasons = [];

    // Skill matching score
    const userSkillSet = new Set(userSkillNames.map(s => s.toLowerCase()));
    const discoveredSkills = discoveredUser.user_skills?.map(s => s.skill_name.toLowerCase()) || [];
    const skillMatches = discoveredSkills.filter(skill => userSkillSet.has(skill));
    const complementarySkills = discoveredSkills.filter(skill => !userSkillSet.has(skill));

    if (skillMatches.length > 0) {
      score += skillMatches.length * 10;
      reasons.push(`${skillMatches.length} shared skills`);
    }

    if (complementarySkills.length > 0) {
      score += Math.min(complementarySkills.length * 5, 25);
      reasons.push(`${complementarySkills.length} complementary skills`);
    }

    // Location matching
    if (discoveredUser.user_profiles?.[0]?.location && location) {
      if (discoveredUser.user_profiles[0].location.toLowerCase().includes(location.toLowerCase())) {
        score += 15;
        reasons.push('Same location');
      }
    }

    // Network score bonus
    const networkScore = discoveredUser.network_analytics?.[0]?.network_score || 0;
    score += networkScore * 2;

    // Availability bonus
    if (discoveredUser.user_profiles?.[0]?.availability_status === 'available') {
      score += 10;
      reasons.push('Currently available');
    }

    // Random factor for diversity
    score += Math.random() * 10;

    return {
      ...discoveredUser,
      recommendation_score: Math.round(score),
      match_reasons: reasons,
      skill_matches: skillMatches.length,
      complementary_skills: Math.min(complementarySkills.length, 5)
    };
  });

  // Sort by score and take top results
  const topRecommendations = recommendations
    .sort((a, b) => b.recommendation_score - a.recommendation_score)
    .slice(0, limit);

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({
      recommendations: topRecommendations,
      total: topRecommendations.length
    })
  };
}

// Search users with filters
async function searchUsers(supabase, userId, queryParams) {
  const { 
    q, 
    skills, 
    location, 
    experience_level, 
    availability_status,
    limit = 20, 
    offset = 0 
  } = queryParams || {};

  let query = supabase
    .from('users')
    .select(`
      id,
      display_name,
      email,
      user_profiles(
        professional_title,
        bio,
        location,
        availability_status,
        profile_photo_url
      ),
      user_skills(skill_name, skill_level, years_experience),
      network_analytics(network_score, network_level, total_allies)
    `)
    .neq('id', userId)
    .range(offset, offset + limit - 1);

  // Text search in name and bio
  if (q) {
    query = query.or(`display_name.ilike.%${q}%,user_profiles.bio.ilike.%${q}%`);
  }

  // Location filter
  if (location) {
    query = query.filter('user_profiles.location', 'ilike', `%${location}%`);
  }

  // Availability filter
  if (availability_status) {
    query = query.filter('user_profiles.availability_status', 'eq', availability_status);
  }

  const { data, error } = await query;

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to search users', details: error.message })
    };
  }

  // Filter by skills if provided
  let filteredData = data;
  if (skills) {
    const skillsArray = Array.isArray(skills) ? skills : [skills];
    filteredData = data.filter(user => 
      user.user_skills && 
      skillsArray.some(skill => 
        user.user_skills.some(userSkill => 
          userSkill.skill_name.toLowerCase().includes(skill.toLowerCase())
        )
      )
    );
  }

  // Filter by experience level if provided
  if (experience_level) {
    filteredData = filteredData.filter(user =>
      user.user_skills && 
      user.user_skills.some(userSkill => 
        userSkill.skill_level === experience_level
      )
    );
  }

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({
      users: filteredData,
      total: filteredData.length,
      hasMore: filteredData.length === limit
    })
  };
}

// Get detailed user profile
async function getUserProfile(supabase, currentUserId, targetUserId) {
  const { data, error } = await supabase
    .from('users')
    .select(`
      id,
      display_name,
      email,
      created_at,
      user_profiles(
        professional_title,
        bio,
        location,
        website_url,
        linkedin_url,
        github_url,
        availability_status,
        profile_photo_url
      ),
      user_skills(skill_name, skill_level, years_experience, is_verified),
      network_analytics(
        network_score,
        network_level,
        total_allies,
        endorsements_received,
        collaboration_success_rate
      )
    `)
    .eq('id', targetUserId)
    .single();

  if (error) {
    return {
      statusCode: 404,
      headers,
      body: JSON.stringify({ error: 'User not found' })
    };
  }

  // Get recent endorsements
  const { data: endorsements } = await supabase
    .from('skill_endorsements')
    .select(`
      skill_name,
      proficiency_level,
      endorsement_message,
      created_at,
      endorser:endorser_id(display_name)
    `)
    .eq('endorsed_id', targetUserId)
    .order('created_at', { ascending: false })
    .limit(5);

  // Check ally status with current user
  const { data: allyStatus } = await supabase
    .from('user_allies')
    .select('status, connection_reason')
    .or(`and(user_id.eq.${currentUserId},ally_id.eq.${targetUserId}),and(user_id.eq.${targetUserId},ally_id.eq.${currentUserId})`)
    .single();

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({
      user: {
        ...data,
        recent_endorsements: endorsements || [],
        ally_status: allyStatus?.status || 'none',
        connection_reason: allyStatus?.connection_reason
      }
    })
  };
}

// Get mutual allies between two users
async function getMutualAllies(supabase, currentUserId, targetUserId) {
  // Get current user's allies
  const { data: currentUserAllies } = await supabase
    .from('user_allies')
    .select('user_id, ally_id')
    .or(`user_id.eq.${currentUserId},ally_id.eq.${currentUserId}`)
    .eq('status', 'accepted');

  // Get target user's allies
  const { data: targetUserAllies } = await supabase
    .from('user_allies')
    .select('user_id, ally_id')
    .or(`user_id.eq.${targetUserId},ally_id.eq.${targetUserId}`)
    .eq('status', 'accepted');

  // Find mutual allies
  const currentAlliesSet = new Set();
  currentUserAllies?.forEach(connection => {
    const allyId = connection.user_id === currentUserId ? connection.ally_id : connection.user_id;
    currentAlliesSet.add(allyId);
  });

  const mutualAllyIds = [];
  targetUserAllies?.forEach(connection => {
    const allyId = connection.user_id === targetUserId ? connection.ally_id : connection.user_id;
    if (currentAlliesSet.has(allyId)) {
      mutualAllyIds.push(allyId);
    }
  });

  if (mutualAllyIds.length === 0) {
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ mutual_allies: [], count: 0 })
    };
  }

  // Get mutual ally details
  const { data: mutualAllies, error } = await supabase
    .from('users')
    .select('id, display_name, user_profiles(professional_title, profile_photo_url)')
    .in('id', mutualAllyIds);

  if (error) {
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to fetch mutual allies', details: error.message })
    };
  }

  return {
    statusCode: 200,
    headers,
    body: JSON.stringify({
      mutual_allies: mutualAllies,
      count: mutualAllies.length
    })
  };
}
