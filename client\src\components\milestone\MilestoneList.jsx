import React, { useState, useEffect, useContext } from 'react';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { toast } from 'react-hot-toast';
import { format } from 'date-fns';

const MilestoneList = ({ projectId, limit = 0, showActions = true, onEdit, onDelete, onSelect }) => {
  const { currentUser } = useContext(UserContext);
  const [milestones, setMilestones] = useState([]);
  const [loading, setLoading] = useState(true);
  const [userRole, setUserRole] = useState(null);
  const [filter, setFilter] = useState('all'); // 'all', 'pending', 'in-progress', 'completed', 'blocked'

  // Fetch milestones
  useEffect(() => {
    const fetchMilestones = async () => {
      if (!projectId) return;

      try {
        setLoading(true);

        // Fetch milestones
        let query = supabase
          .from('milestones')
          .select('*')
          .eq('project_id', projectId)
          .order('created_at', { ascending: true });

        // Apply filter if not 'all'
        if (filter !== 'all') {
          query = query.eq('status', filter);
        }

        // Apply limit if provided
        if (limit > 0) {
          query = query.limit(limit);
        }

        const { data, error } = await query;

        if (error) throw error;

        setMilestones(data || []);

        // Check user's role in the project
        if (currentUser) {
          const { data: contributorData, error: contributorError } = await supabase
            .from('project_contributors')
            .select('is_admin')
            .eq('project_id', projectId)
            .eq('user_id', currentUser.id)
            .single();

          if (!contributorError && contributorData) {
            setUserRole(contributorData.is_admin ? 'admin' : 'contributor');
          } else {
            setUserRole('viewer');
          }
        }

      } catch (error) {
        console.error('Error fetching milestones:', error);
        toast.error('Failed to load milestones');
      } finally {
        setLoading(false);
      }
    };

    fetchMilestones();
  }, [projectId, currentUser, filter, limit]);

  // Handle milestone deletion
  const handleDelete = async (id) => {
    if (!window.confirm('Are you sure you want to delete this milestone?')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('milestones')
        .delete()
        .eq('id', id);

      if (error) throw error;

      // Remove from local state
      setMilestones(prev => prev.filter(m => m.id !== id));
      toast.success('Milestone deleted successfully');

      // Call onDelete callback if provided
      if (onDelete) {
        onDelete(id);
      }
    } catch (error) {
      console.error('Error deleting milestone:', error);
      toast.error('Failed to delete milestone');
    }
  };

  // Handle milestone status update
  const handleStatusUpdate = async (id, newStatus) => {
    try {
      const { error } = await supabase
        .from('milestones')
        .update({ status: newStatus, updated_at: new Date() })
        .eq('id', id);

      if (error) throw error;

      // Update local state
      setMilestones(prev => prev.map(m =>
        m.id === id ? { ...m, status: newStatus, updated_at: new Date() } : m
      ));

      toast.success(`Milestone marked as ${newStatus}`);
    } catch (error) {
      console.error('Error updating milestone status:', error);
      toast.error('Failed to update milestone status');
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'No deadline';
    return format(new Date(dateString), 'MMM d, yyyy');
  };

  // Check if user can modify milestones
  const canModify = () => {
    return userRole === 'admin';
  };

  // Check if milestone is overdue
  const isOverdue = (milestone) => {
    if (!milestone.deadline || milestone.status === 'completed') return false;
    return new Date(milestone.deadline) < new Date();
  };

  if (loading) {
    return <div className="milestone-list-loading">Loading milestones...</div>;
  }

  if (milestones.length === 0) {
    return (
      <div className="milestone-list-empty">
        <p>No milestones found.</p>
        {filter !== 'all' && (
          <button
            className="btn btn-link"
            onClick={() => setFilter('all')}
          >
            Show all milestones
          </button>
        )}
      </div>
    );
  }

  return (
    <div className="milestone-list-container">
      {!limit && (
        <div className="milestone-filters">
          <button
            className={`filter-button ${filter === 'all' ? 'active' : ''}`}
            onClick={() => setFilter('all')}
          >
            All
          </button>
          <button
            className={`filter-button ${filter === 'pending' ? 'active' : ''}`}
            onClick={() => setFilter('pending')}
          >
            Pending
          </button>
          <button
            className={`filter-button ${filter === 'in-progress' ? 'active' : ''}`}
            onClick={() => setFilter('in-progress')}
          >
            In Progress
          </button>
          <button
            className={`filter-button ${filter === 'completed' ? 'active' : ''}`}
            onClick={() => setFilter('completed')}
          >
            Completed
          </button>
          <button
            className={`filter-button ${filter === 'blocked' ? 'active' : ''}`}
            onClick={() => setFilter('blocked')}
          >
            Blocked
          </button>
        </div>
      )}

      <div className="milestone-list">
        {milestones.map(milestone => (
          <div
            key={milestone.id}
            className={`milestone-card ${milestone.status} ${isOverdue(milestone) ? 'overdue' : ''}`}
            onClick={() => onSelect && onSelect(milestone)}
          >
            <div className="milestone-header">
              <h3 className="milestone-name">{milestone.name}</h3>
              <div className="milestone-status-badge">
                {milestone.status}
                {isOverdue(milestone) && (
                  <span className="overdue-badge">Overdue</span>
                )}
              </div>
            </div>

            {milestone.description && (
              <div className="milestone-description">
                {milestone.description}
              </div>
            )}

            <div className="milestone-meta">
              {milestone.deadline && (
                <div className="milestone-deadline">
                  <i className="bi bi-calendar"></i> {formatDate(milestone.deadline)}
                </div>
              )}

              <div className="milestone-weight">
                <i className="bi bi-star-fill"></i> Weight: {milestone.weight}
              </div>
            </div>

            {milestone.deliverables && milestone.deliverables.length > 0 && (
              <div className="milestone-deliverables">
                <h4>Deliverables:</h4>
                <ul>
                  {milestone.deliverables.map((deliverable, index) => (
                    <li key={index}>{deliverable}</li>
                  ))}
                </ul>
              </div>
            )}

            {milestone.approval_criteria && (
              <div className="milestone-criteria">
                <h4>Approval Criteria:</h4>
                <p>{milestone.approval_criteria}</p>
              </div>
            )}

            {showActions && (
              <div className="milestone-actions">
                {canModify() && (
                  <>
                    <button
                      className="btn-action edit"
                      onClick={() => onEdit && onEdit(milestone)}
                      title="Edit milestone"
                    >
                      <i className="bi bi-pencil"></i>
                    </button>
                    <button
                      className="btn-action delete"
                      onClick={() => handleDelete(milestone.id)}
                      title="Delete milestone"
                    >
                      <i className="bi bi-trash"></i>
                    </button>
                  </>
                )}

                {/* Status update buttons */}
                {showActions && milestone.status !== 'completed' && (
                  <button
                    className="btn-action complete"
                    onClick={() => handleStatusUpdate(milestone.id, 'completed')}
                    title="Mark as completed"
                  >
                    <i className="bi bi-check-circle"></i>
                  </button>
                )}

                {showActions && milestone.status === 'pending' && (
                  <button
                    className="btn-action start"
                    onClick={() => handleStatusUpdate(milestone.id, 'in-progress')}
                    title="Start milestone"
                  >
                    <i className="bi bi-play-circle"></i>
                  </button>
                )}

                {showActions && milestone.status !== 'blocked' && milestone.status !== 'completed' && (
                  <button
                    className="btn-action block"
                    onClick={() => handleStatusUpdate(milestone.id, 'blocked')}
                    title="Mark as blocked"
                  >
                    <i className="bi bi-exclamation-triangle"></i>
                  </button>
                )}

                {showActions && milestone.status === 'blocked' && (
                  <button
                    className="btn-action unblock"
                    onClick={() => handleStatusUpdate(milestone.id, 'in-progress')}
                    title="Unblock milestone"
                  >
                    <i className="bi bi-arrow-counterclockwise"></i>
                  </button>
                )}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default MilestoneList;
