// Test current database status and API functionality
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  'https://hqqlrrqvjcetoxbdjgzx.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MjA1NTksImV4cCI6MjA1OTM5NjU1OX0.e0oaDNVbA563SZJHPm6UTFOXcLbWorKn42a_brChpKQ'
);

async function testDatabaseStatus() {
  console.log('🔍 Testing current database status...\n');
  
  try {
    // Test basic tables
    console.log('Testing basic tables...');
    
    const tables = ['teams', 'team_members', 'projects', 'users'];
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1);
          
        if (error) {
          console.log(`❌ ${table}: ${error.message}`);
        } else {
          console.log(`✅ ${table}: accessible (${data?.length || 0} records)`);
        }
      } catch (err) {
        console.log(`❌ ${table}: ${err.message}`);
      }
    }
    
    // Test compliance tables
    console.log('\nTesting compliance tables...');
    
    const complianceTables = ['companies', 'financial_transactions', 'commission_payments', 'recurring_fees'];
    
    for (const table of complianceTables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1);
          
        if (error) {
          console.log(`❌ ${table}: ${error.message}`);
        } else {
          console.log(`✅ ${table}: accessible (${data?.length || 0} records)`);
        }
      } catch (err) {
        console.log(`❌ ${table}: ${err.message}`);
      }
    }
    
    // Check teams table structure
    console.log('\nChecking teams table structure...');
    
    const { data: teamsData, error: teamsError } = await supabase
      .from('teams')
      .select('*')
      .limit(1);
      
    if (teamsData && teamsData.length > 0) {
      const teamRecord = teamsData[0];
      console.log('Teams table columns:', Object.keys(teamRecord));
      
      // Check for our new columns
      const hasCompanyId = 'company_id' in teamRecord;
      const hasBusinessEntity = 'is_business_entity' in teamRecord;
      const hasAllianceType = 'alliance_type' in teamRecord;
      
      console.log(`Company ID column: ${hasCompanyId ? '✅' : '❌'}`);
      console.log(`Business entity column: ${hasBusinessEntity ? '✅' : '❌'}`);
      console.log(`Alliance type column: ${hasAllianceType ? '✅' : '❌'}`);
    }
    
    // Test authentication
    console.log('\nTesting authentication...');
    
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'TestPassword123!'
    });
    
    if (authData?.user) {
      console.log(`✅ Authentication successful for test user`);
      console.log(`   User ID: ${authData.user.id}`);
      console.log(`   Email: ${authData.user.email}`);
      
      // Test API with authentication
      console.log('\nTesting APIs with authentication...');
      
      const authToken = authData.session.access_token;
      
      // Test companies API
      try {
        const response = await fetch('https://royalty.technology/.netlify/functions/companies', {
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          }
        });
        
        console.log(`Companies API: ${response.status}`);
        
        if (response.ok) {
          const data = await response.json();
          console.log(`   Response:`, data);
        } else {
          const errorText = await response.text();
          console.log(`   Error: ${errorText.substring(0, 100)}...`);
        }
      } catch (err) {
        console.log(`❌ Companies API error: ${err.message}`);
      }
      
    } else {
      console.log(`❌ Authentication failed: ${authError?.message}`);
    }
    
  } catch (error) {
    console.log(`❌ Database test failed: ${error.message}`);
  }
}

testDatabaseStatus();
