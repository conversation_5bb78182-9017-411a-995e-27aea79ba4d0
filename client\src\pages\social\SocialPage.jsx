import React, { useState, useContext } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Tabs, Tab, Avatar, Chip, Input } from '@heroui/react';
import { UserContext } from '../../../contexts/supabase-auth.context.jsx';

/**
 * Social Page Component
 *
 * Main interface for social features including friends, chat, activity feed, and community.
 * Follows the spatial-first design philosophy with engaging social interactions.
 */
const SocialPage = () => {
  const { currentUser } = useContext(UserContext);
  const [activeTab, setActiveTab] = useState('friends');

  // Mock data for demo
  const friends = [
    { id: 1, name: '<PERSON>', avatar: '👩‍💻', status: 'online', project: 'Game Engine' },
    { id: 2, name: '<PERSON>', avatar: '👨‍🎨', status: 'away', project: 'Mobile App' },
    { id: 3, name: '<PERSON>', avatar: '👩‍🔬', status: 'offline', project: 'Research Project' }
  ];

  const activityFeed = [
    { id: 1, user: '<PERSON>', action: 'completed a task', project: 'Game Engine', time: '2 hours ago' },
    { id: 2, user: '<PERSON>', action: 'submitted a design', project: 'Mobile App', time: '4 hours ago' },
    { id: 3, user: 'Carol <PERSON>', action: 'started tracking time', project: 'Research Project', time: '6 hours ago' }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'online': return 'success';
      case 'away': return 'warning';
      case 'offline': return 'default';
      default: return 'default';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-rose-900 to-pink-900">
      {/* Header */}
      <motion.div
        className="relative z-10 pt-8 pb-6"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="container mx-auto px-6">
          <div className="text-center mb-8">
            <motion.div
              className="text-6xl mb-4"
              animate={{
                scale: [1, 1.1, 1],
                rotate: [0, 5, -5, 0]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatType: "reverse"
              }}
            >
              💬
            </motion.div>
            <h1 className="text-4xl font-bold text-white mb-2">
              Social Hub
            </h1>
            <p className="text-white/80 text-lg max-w-2xl mx-auto">
              Connect with collaborators, share progress, and build your creative community.
            </p>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="container mx-auto px-6 pb-12">
        <motion.div
          className="max-w-6xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {/* Navigation Tabs */}
          <Card className="mb-8 bg-white/5 border border-white/10">
            <CardBody className="p-6">
              <Tabs
                selectedKey={activeTab}
                onSelectionChange={setActiveTab}
                variant="underlined"
                classNames={{
                  tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
                  cursor: "w-full bg-gradient-to-r from-rose-500 to-pink-500",
                  tab: "max-w-fit px-0 h-12",
                  tabContent: "group-data-[selected=true]:text-white text-white/70"
                }}
              >
                <Tab key="friends" title="👥 Friends" />
                <Tab key="chat" title="💬 Chat" />
                <Tab key="activity" title="📈 Activity" />
                <Tab key="community" title="🌍 Community" />
              </Tabs>
            </CardBody>
          </Card>

          {/* Tab Content */}
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            {activeTab === 'friends' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Friends List */}
                <Card className="bg-white/5 border border-white/10">
                  <CardHeader className="pb-3">
                    <h3 className="text-xl font-semibold text-white">Your Friends</h3>
                  </CardHeader>
                  <CardBody className="space-y-4">
                    {friends.map((friend) => (
                      <div key={friend.id} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="relative">
                            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-lg">
                              {friend.avatar}
                            </div>
                            <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${
                              friend.status === 'online' ? 'bg-green-500' :
                              friend.status === 'away' ? 'bg-yellow-500' : 'bg-gray-500'
                            }`}></div>
                          </div>
                          <div>
                            <div className="text-white font-medium">{friend.name}</div>
                            <div className="text-white/60 text-sm">Working on {friend.project}</div>
                          </div>
                        </div>
                        <Chip size="sm" color={getStatusColor(friend.status)} variant="flat">
                          {friend.status}
                        </Chip>
                      </div>
                    ))}
                  </CardBody>
                </Card>

                {/* Friend Requests */}
                <Card className="bg-white/5 border border-white/10">
                  <CardHeader className="pb-3">
                    <h3 className="text-xl font-semibold text-white">Friend Requests</h3>
                  </CardHeader>
                  <CardBody>
                    <div className="text-center py-8">
                      <div className="text-4xl mb-4">👋</div>
                      <p className="text-white/70">No pending friend requests</p>
                      <Button className="mt-4 bg-rose-500 hover:bg-rose-600 text-white">
                        Find Friends
                      </Button>
                    </div>
                  </CardBody>
                </Card>
              </div>
            )}

            {activeTab === 'chat' && (
              <Card className="bg-white/5 border border-white/10">
                <CardBody className="p-6">
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">💬</div>
                    <h3 className="text-xl font-semibold text-white mb-2">Chat System</h3>
                    <p className="text-white/70 mb-6">Real-time messaging with your collaborators</p>
                    <Button className="bg-rose-500 hover:bg-rose-600 text-white">
                      Start Chatting
                    </Button>
                  </div>
                </CardBody>
              </Card>
            )}

            {activeTab === 'activity' && (
              <Card className="bg-white/5 border border-white/10">
                <CardHeader className="pb-3">
                  <h3 className="text-xl font-semibold text-white">Activity Feed</h3>
                </CardHeader>
                <CardBody className="space-y-4">
                  {activityFeed.map((activity) => (
                    <div key={activity.id} className="flex items-start gap-3 p-3 bg-white/5 rounded-lg">
                      <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center text-white text-sm">
                        {activity.user.charAt(0)}
                      </div>
                      <div className="flex-1">
                        <div className="text-white text-sm">
                          <span className="font-medium">{activity.user}</span> {activity.action} in{' '}
                          <span className="text-blue-400">{activity.project}</span>
                        </div>
                        <div className="text-white/60 text-xs">{activity.time}</div>
                      </div>
                    </div>
                  ))}
                </CardBody>
              </Card>
            )}

            {activeTab === 'community' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <Card className="bg-white/5 border border-white/10">
                  <CardHeader className="pb-3">
                    <h3 className="text-xl font-semibold text-white">Community Forums</h3>
                  </CardHeader>
                  <CardBody>
                    <div className="text-center py-8">
                      <div className="text-4xl mb-4">🌍</div>
                      <p className="text-white/70 mb-4">Join discussions with the Royaltea community</p>
                      <Button className="bg-rose-500 hover:bg-rose-600 text-white">
                        Browse Forums
                      </Button>
                    </div>
                  </CardBody>
                </Card>

                <Card className="bg-white/5 border border-white/10">
                  <CardHeader className="pb-3">
                    <h3 className="text-xl font-semibold text-white">Public Projects</h3>
                  </CardHeader>
                  <CardBody>
                    <div className="text-center py-8">
                      <div className="text-4xl mb-4">🚀</div>
                      <p className="text-white/70 mb-4">Discover and join public projects</p>
                      <Button className="bg-rose-500 hover:bg-rose-600 text-white">
                        Explore Projects
                      </Button>
                    </div>
                  </CardBody>
                </Card>
              </div>
            )}
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default SocialPage;
