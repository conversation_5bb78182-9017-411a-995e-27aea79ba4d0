import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, Card, CardBody, CardHeader } from '@heroui/react';

/**
 * AllianceReviewScreen Component
 * 
 * Final review screen showing alliance configuration
 * Follows wireframe design with comprehensive review
 */
const AllianceReviewScreen = ({ 
  questionData, 
  allianceData, 
  onConfirm, 
  onBack, 
  isLoading 
}) => {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4 }
    }
  };

  // Helper functions for display
  const getProjectTypeDisplay = () => {
    const typeMap = {
      'business': 'Business Project',
      'personal': 'Personal Project',
      'opensource': 'Open Source',
      'creative': 'Creative Work'
    };
    return typeMap[questionData.projectType] || 'Project';
  };

  const getSecondaryAnswerDisplay = () => {
    if (questionData.projectType === 'business') {
      const statusMap = {
        'established': 'Established company',
        'startup': 'Startup in formation',
        'informal': 'Informal partnership',
        'idea': 'Just an idea'
      };
      return statusMap[questionData.companyStatus] || '';
    } else if (questionData.projectType === 'personal') {
      const sizeMap = {
        'solo': 'Solo (just me)',
        'small': 'Small team (2-5 people)',
        'medium': 'Medium team (6-15 people)',
        'large': 'Large team (15+ people)'
      };
      return sizeMap[questionData.teamSize] || '';
    } else if (questionData.projectType === 'opensource') {
      const communityMap = {
        'public': 'Public community',
        'private': 'Private community',
        'invite': 'Invite-based'
      };
      return communityMap[questionData.communityType] || '';
    } else if (questionData.projectType === 'creative') {
      const creativeMap = {
        'collaborative': 'Collaborative creation',
        'individual': 'Individual with support',
        'mixed': 'Mixed approach'
      };
      return creativeMap[questionData.creativeType] || '';
    }
    return '';
  };

  const getStartDateDisplay = () => {
    const dateMap = {
      'now': 'Right now!',
      'specific': 'Specific date',
      'when_ready': 'When team is ready',
      'when_funded': 'When funding is secured'
    };
    return dateMap[questionData.startDate] || 'Custom timeline';
  };

  const getPaymentModelDisplay = () => {
    const paymentMap = {
      'fixed': 'Fixed payments',
      'revenue_sharing': 'Revenue sharing',
      'hybrid': 'Hybrid model',
      'volunteer': 'Volunteer/Equity only',
      'not_sure': 'Not sure yet'
    };
    return paymentMap[questionData.paymentModel] || 'Custom payment model';
  };

  const getTeamRolesDisplay = () => {
    const rolesMap = {
      'main_leader': 'I\'ll be the main leader',
      'shared': 'Shared leadership',
      'skill_based': 'Skill-based roles',
      'flexible': 'Flexible roles'
    };
    return rolesMap[questionData.teamRoles] || 'Custom roles';
  };

  const getInvitationMethodDisplay = () => {
    const inviteMap = {
      'email': 'Send email invitations',
      'links': 'Create invitation links',
      'discoverable': 'Make it discoverable',
      'later': 'Invite people later'
    };
    return inviteMap[questionData.invitationMethod] || 'Custom invitation method';
  };

  const getAllianceTypeDisplay = () => {
    const typeMap = {
      'solo': 'Solo Venture',
      'emerging': 'Emerging Alliance',
      'established': 'Established Alliance'
    };
    return typeMap[allianceData.alliance_type] || 'Alliance';
  };

  return (
    <motion.div
      className="min-h-screen flex items-center justify-center p-4"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="max-w-4xl mx-auto w-full">
        {/* Header */}
        <motion.div variants={itemVariants} className="text-center mb-8">
          <h1 className="text-5xl font-bold text-foreground mb-4">
            🏰 Review Your Alliance
          </h1>
          <p className="text-xl text-default-600">
            Everything looks great! Ready to create your alliance?
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Alliance Summary */}
          <motion.div variants={itemVariants}>
            <Card className="bg-content1 border border-default-200">
              <CardHeader>
                <h2 className="text-2xl font-semibold text-foreground">
                  Alliance Summary
                </h2>
              </CardHeader>
              <CardBody className="space-y-4 text-foreground">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{questionData.allianceIcon}</span>
                  <div>
                    <div className="font-semibold text-lg">{questionData.allianceName}</div>
                    <div className="text-default-600 text-sm">{getAllianceTypeDisplay()}</div>
                  </div>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div><strong>Project Type:</strong> {getProjectTypeDisplay()}</div>
                  {getSecondaryAnswerDisplay() && (
                    <div><strong>Details:</strong> {getSecondaryAnswerDisplay()}</div>
                  )}
                  <div><strong>Start Date:</strong> {getStartDateDisplay()}</div>
                  <div><strong>Payment Model:</strong> {getPaymentModelDisplay()}</div>
                  <div><strong>Leadership:</strong> {getTeamRolesDisplay()}</div>
                  <div><strong>Invitations:</strong> {getInvitationMethodDisplay()}</div>
                </div>

                {questionData.allianceDescription && (
                  <div className="pt-2">
                    <div className="text-sm text-default-600 mb-2">Description:</div>
                    <div className="text-sm">{questionData.allianceDescription}</div>
                  </div>
                )}
              </CardBody>
            </Card>
          </motion.div>

          {/* Configuration Details */}
          <motion.div variants={itemVariants}>
            <Card className="bg-content1 border border-default-200">
              <CardHeader>
                <h2 className="text-2xl font-semibold text-foreground">
                  Configuration
                </h2>
              </CardHeader>
              <CardBody className="space-y-4 text-foreground">
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-default-600">Alliance Type:</span>
                    <span className="font-medium">{getAllianceTypeDisplay()}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-default-600">Business Entity:</span>
                    <span className="font-medium">
                      {allianceData.is_business_entity ? 'Yes' : 'No'}
                    </span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-default-600">Public Visibility:</span>
                    <span className="font-medium">
                      {allianceData.is_public ? 'Public' : 'Private'}
                    </span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-default-600">Max Members:</span>
                    <span className="font-medium">{allianceData.max_members}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-default-600">Discoverable:</span>
                    <span className="font-medium">
                      {allianceData.is_discoverable ? 'Yes' : 'No'}
                    </span>
                  </div>
                </div>

                <div className="pt-4 border-t border-default-200">
                  <h3 className="font-semibold mb-3">Features Enabled:</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center space-x-2">
                      <span className="text-success">✅</span>
                      <span>Member invitations</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-success">✅</span>
                      <span>Shared workspace</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={allianceData.auto_venture_creation ? "text-success" : "text-default-400"}>
                        {allianceData.auto_venture_creation ? "✅" : "⭕"}
                      </span>
                      <span>Auto venture creation</span>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          </motion.div>
        </div>

        {/* Next Steps Preview */}
        <motion.div variants={itemVariants} className="mt-8">
          <Card className="bg-primary-50 border border-primary-200">
            <CardHeader>
              <h2 className="text-xl font-semibold text-primary">
                🚀 What Happens Next
              </h2>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="text-center">
                  <div className="text-2xl mb-2">🏰</div>
                  <div className="font-semibold text-foreground mb-1">Alliance Created</div>
                  <div className="text-default-600">Your alliance will be set up with all the configurations above</div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl mb-2">
                    {questionData.invitationMethod === 'email' ? '📧' : 
                     questionData.invitationMethod === 'links' ? '🔗' : 
                     questionData.invitationMethod === 'discoverable' ? '🌍' : '⏳'}
                  </div>
                  <div className="font-semibold text-foreground mb-1">
                    {questionData.invitationMethod === 'later' ? 'Ready to Go' : 'Invite Members'}
                  </div>
                  <div className="text-default-600">
                    {questionData.invitationMethod === 'later' 
                      ? 'You can invite members anytime from your alliance dashboard'
                      : 'Start building your team right away'
                    }
                  </div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl mb-2">🚀</div>
                  <div className="font-semibold text-foreground mb-1">Create Ventures</div>
                  <div className="text-default-600">Begin working on projects with your team</div>
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>

        {/* Action Buttons */}
        <motion.div variants={itemVariants} className="mt-8 flex justify-between">
          <Button
            size="lg"
            variant="bordered"
            onPress={onBack}
            disabled={isLoading}
            className="text-foreground border-default-300 hover:bg-default-100"
          >
            ← Back to Questions
          </Button>

          <Button
            size="lg"
            onPress={onConfirm}
            disabled={isLoading}
            className="bg-primary text-white font-semibold px-12 py-4 text-lg hover:bg-primary-600"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Creating Alliance...
              </>
            ) : (
              'Create Alliance'
            )}
          </Button>
        </motion.div>

        {/* Footer Note */}
        <motion.div variants={itemVariants} className="mt-6 text-center">
          <p className="text-default-500 text-sm">
            You can modify these settings anytime from your alliance management dashboard.
          </p>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default AllianceReviewScreen;
