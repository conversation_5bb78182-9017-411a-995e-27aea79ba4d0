// Plaid Webhook Handler
// Backend Specialist: Process Plaid webhook events
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Webhook event handlers
const handleTransferUpdate = async (webhookData) => {
  try {
    const { transfer_id, transfer_status, failure_reason } = webhookData;
    
    console.log('Processing transfer update:', { transfer_id, transfer_status });
    
    // Update transaction status in database
    const { data: transaction, error: updateError } = await supabase
      .from('financial_transactions')
      .update({ 
        status: mapPlaidStatusToInternal(transfer_status),
        updated_at: new Date().toISOString()
      })
      .eq('reference_number', transfer_id)
      .select()
      .single();

    if (updateError) {
      console.error('Failed to update transaction:', updateError.message);
      return false;
    }

    // If transfer failed, record failure reason
    if (transfer_status === 'failed' && failure_reason) {
      await supabase
        .from('financial_transactions')
        .update({ 
          description: `${transaction.description} - Failed: ${failure_reason}`
        })
        .eq('id', transaction.id);
    }

    // Send notification to user about status change
    await sendTransferNotification(transaction, transfer_status);
    
    return true;
  } catch (error) {
    console.error('Handle transfer update error:', error);
    return false;
  }
};

const handleAccountUpdate = async (webhookData) => {
  try {
    const { item_id, account_id, new_balance } = webhookData;
    
    console.log('Processing account update:', { item_id, account_id });
    
    // Update account balance if plaid_accounts table exists
    // For now, log the event
    console.log('Account balance updated:', new_balance);
    
    return true;
  } catch (error) {
    console.error('Handle account update error:', error);
    return false;
  }
};

const handleItemError = async (webhookData) => {
  try {
    const { item_id, error_code, error_message } = webhookData;
    
    console.log('Processing item error:', { item_id, error_code, error_message });
    
    // Log error and potentially notify user
    // For now, just log it
    console.error('Plaid item error:', { item_id, error_code, error_message });
    
    return true;
  } catch (error) {
    console.error('Handle item error error:', error);
    return false;
  }
};

// Map Plaid transfer status to internal status
const mapPlaidStatusToInternal = (plaidStatus) => {
  const statusMap = {
    'pending': 'processing',
    'posted': 'completed',
    'cancelled': 'cancelled',
    'failed': 'failed',
    'returned': 'failed',
    'reversed': 'reversed'
  };
  
  return statusMap[plaidStatus] || 'processing';
};

// Send notification about transfer status change
const sendTransferNotification = async (transaction, status) => {
  try {
    // Get user information
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, display_name, email')
      .eq('id', transaction.payee_user_id || transaction.created_by)
      .single();

    if (userError) {
      console.warn('Failed to get user for notification:', userError.message);
      return;
    }

    // Create notification record (if notifications table exists)
    const notificationData = {
      user_id: user.id,
      type: 'payment_update',
      title: `Payment ${status}`,
      message: `Your payment of $${transaction.gross_amount} has been ${status}`,
      data: {
        transaction_id: transaction.id,
        transfer_id: transaction.reference_number,
        amount: transaction.gross_amount,
        status: status
      }
    };

    console.log('Would send notification:', notificationData);
    
    // TODO: Implement actual notification sending
    // - Email notification
    // - In-app notification
    // - Push notification
    
  } catch (error) {
    console.error('Send notification error:', error);
  }
};

// Main webhook handler
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Plaid-Verification',
    'Access-Control-Allow-Methods': 'POST, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    // Parse webhook data
    const webhookData = JSON.parse(event.body);
    const { webhook_type, webhook_code } = webhookData;

    console.log('Received Plaid webhook:', { webhook_type, webhook_code });

    // Verify webhook signature (in production)
    // const signature = event.headers['plaid-verification'];
    // if (!verifyWebhookSignature(event.body, signature)) {
    //   return {
    //     statusCode: 401,
    //     headers,
    //     body: JSON.stringify({ error: 'Invalid signature' })
    //   };
    // }

    // Log webhook for debugging
    try {
      await supabase
        .from('plaid_webhooks')
        .insert([{
          webhook_type,
          webhook_code,
          item_id: webhookData.item_id || null,
          webhook_data: webhookData,
          processed: false
        }]);
    } catch (logError) {
      console.warn('Failed to log webhook:', logError.message);
    }

    // Process webhook based on type
    let processed = false;
    
    switch (webhook_type) {
      case 'TRANSFER':
        if (webhook_code === 'TRANSFER_EVENTS_UPDATE') {
          processed = await handleTransferUpdate(webhookData);
        }
        break;
        
      case 'TRANSACTIONS':
        if (webhook_code === 'TRANSACTIONS_REMOVED' || 
            webhook_code === 'DEFAULT_UPDATE') {
          processed = await handleAccountUpdate(webhookData);
        }
        break;
        
      case 'ITEM':
        if (webhook_code === 'ERROR') {
          processed = await handleItemError(webhookData);
        }
        break;
        
      default:
        console.log('Unhandled webhook type:', webhook_type);
        processed = true; // Mark as processed to avoid retries
    }

    // Update webhook processing status
    if (processed) {
      try {
        await supabase
          .from('plaid_webhooks')
          .update({ 
            processed: true, 
            processed_at: new Date().toISOString() 
          })
          .eq('webhook_type', webhook_type)
          .eq('webhook_code', webhook_code)
          .eq('webhook_data', webhookData);
      } catch (updateError) {
        console.warn('Failed to update webhook status:', updateError.message);
      }
    }

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ 
        message: 'Webhook processed successfully',
        processed: processed
      })
    };

  } catch (error) {
    console.error('Webhook processing error:', error);
    
    // Log failed webhook processing
    try {
      await supabase
        .from('plaid_webhooks')
        .update({ 
          processed: false,
          processing_error: error.message,
          processed_at: new Date().toISOString()
        })
        .eq('webhook_data', JSON.parse(event.body));
    } catch (logError) {
      console.warn('Failed to log webhook error:', logError.message);
    }

    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Webhook processing failed' })
    };
  }
};
