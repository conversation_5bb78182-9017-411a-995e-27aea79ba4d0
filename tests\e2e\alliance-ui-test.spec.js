// Alliance UI Components Test
// Day 2 - Testing alliance management interface

import { test, expect } from '@playwright/test';

const SITE_URL = 'https://royalty.technology';

test.describe('Alliance Management UI', () => {
  test('should test alliance components are loaded', async ({ page }) => {
    // Navigate to the site
    await page.goto(SITE_URL);
    await page.waitForLoadState('networkidle');
    
    // Check if AllianceManage component is in the bundle
    const scripts = await page.evaluate(() => {
      return Array.from(document.scripts).map(script => script.src);
    });
    
    console.log('Loaded scripts:', scripts.length);
    
    // Check for alliance-related JavaScript
    const hasAllianceCode = await page.evaluate(() => {
      // Check if our alliance components are in the global scope or modules
      return window.React !== undefined;
    });
    
    console.log('React loaded:', hasAllianceCode);
    
    // Try to navigate to alliance routes
    await page.goto(`${SITE_URL}/alliances`);
    await page.waitForLoadState('networkidle');
    
    // Check if page loads without JavaScript errors
    const errors = [];
    page.on('pageerror', error => {
      errors.push(error.message);
    });
    
    await page.waitForTimeout(2000);
    
    console.log('JavaScript errors:', errors);
    
    // Check if we can find any alliance-related content
    const bodyText = await page.textContent('body');
    const hasAllianceText = bodyText.includes('Alliance') || bodyText.includes('alliance');
    console.log('Has alliance text:', hasAllianceText);
    
    // Check for team-related content (since alliances extend teams)
    const hasTeamText = bodyText.includes('Team') || bodyText.includes('team');
    console.log('Has team text:', hasTeamText);
  });

  test('should test direct team management access', async ({ page }) => {
    // Try to access team management directly
    await page.goto(`${SITE_URL}/teams`);
    await page.waitForLoadState('networkidle');
    
    const pageContent = await page.textContent('body');
    console.log('Teams page content length:', pageContent.length);
    
    // Look for any interactive elements
    const buttons = await page.locator('button').count();
    const links = await page.locator('a').count();
    const inputs = await page.locator('input').count();
    
    console.log(`Interactive elements - Buttons: ${buttons}, Links: ${links}, Inputs: ${inputs}`);
    
    // Check if we can find create/manage buttons
    const createButton = page.locator('text=Create').first();
    const manageButton = page.locator('text=Manage').first();
    
    const hasCreate = await createButton.isVisible();
    const hasManage = await manageButton.isVisible();
    
    console.log('Has create button:', hasCreate);
    console.log('Has manage button:', hasManage);
    
    // If we find a create button, try clicking it
    if (hasCreate) {
      await createButton.click();
      await page.waitForLoadState('networkidle');
      
      const newPageContent = await page.textContent('body');
      console.log('After create click - content changed:', newPageContent !== pageContent);
    }
  });

  test('should check for business entity components', async ({ page }) => {
    await page.goto(`${SITE_URL}`);
    await page.waitForLoadState('networkidle');
    
    // Check if our business-related terms are in the page source
    const pageSource = await page.content();
    
    const businessTerms = [
      'CompanyRegistration',
      'CompanyDetails', 
      'AllianceManage',
      'business entity',
      'tax compliance',
      'financial transaction'
    ];
    
    for (const term of businessTerms) {
      const found = pageSource.includes(term);
      console.log(`${term}: ${found}`);
    }
    
    // Check if our CSS classes are present
    const cssClasses = [
      'alliance-manage-container',
      'company-registration',
      'business-entity-section'
    ];
    
    for (const className of cssClasses) {
      const hasClass = await page.locator(`.${className}`).count() > 0;
      console.log(`CSS class ${className}: ${hasClass}`);
    }
  });

  test('should test experimental navigation with alliances', async ({ page }) => {
    await page.goto(`${SITE_URL}`);
    await page.waitForLoadState('networkidle');
    
    // Check if experimental navigation is active
    const hasExperimentalNav = await page.evaluate(() => {
      return document.querySelector('canvas') !== null;
    });
    
    console.log('Has experimental navigation (canvas):', hasExperimentalNav);
    
    if (hasExperimentalNav) {
      // Try keyboard navigation
      await page.keyboard.press('ArrowDown');
      await page.waitForTimeout(500);
      
      await page.keyboard.press('ArrowDown');
      await page.waitForTimeout(500);
      
      // Look for teams card
      const teamsVisible = await page.locator('text=Teams').isVisible();
      console.log('Teams card visible in grid view:', teamsVisible);
      
      if (teamsVisible) {
        await page.locator('text=Teams').click();
        await page.waitForLoadState('networkidle');
        
        const currentUrl = page.url();
        console.log('Navigated to:', currentUrl);
        
        const isTeamsPage = currentUrl.includes('/teams');
        console.log('Successfully navigated to teams page:', isTeamsPage);
      }
    }
  });

  test('should verify component imports and routing', async ({ page }) => {
    // Check if our routes are properly configured
    const routes = [
      '/teams',
      '/alliances', 
      '/teams/test-id/manage',
      '/alliances/test-id/manage'
    ];
    
    for (const route of routes) {
      await page.goto(`${SITE_URL}${route}`);
      await page.waitForLoadState('networkidle');
      
      const status = page.url().includes('404') ? 404 : 200;
      console.log(`Route ${route}: ${status}`);
      
      // Check if page has content (not just a blank page)
      const hasContent = await page.locator('body *').count() > 10;
      console.log(`Route ${route} has content: ${hasContent}`);
    }
  });

  test('should test CSS loading and styling', async ({ page }) => {
    await page.goto(`${SITE_URL}/alliances`);
    await page.waitForLoadState('networkidle');
    
    // Check if our alliance styles are applied
    const hasAllianceStyles = await page.evaluate(() => {
      // Look for our specific CSS custom properties or classes
      const testElement = document.createElement('div');
      testElement.className = 'alliance-manage-container';
      document.body.appendChild(testElement);
      
      const styles = window.getComputedStyle(testElement);
      const hasCustomStyling = styles.maxWidth !== 'none' || styles.padding !== '0px';
      
      document.body.removeChild(testElement);
      return hasCustomStyling;
    });
    
    console.log('Alliance styles are applied:', hasAllianceStyles);
    
    // Check for CSS variables or custom properties
    const hasCSSVariables = await page.evaluate(() => {
      const styles = window.getComputedStyle(document.documentElement);
      return styles.getPropertyValue('--primary-color') !== '';
    });
    
    console.log('Has CSS custom properties:', hasCSSVariables);
  });
});
