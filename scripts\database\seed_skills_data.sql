-- Seed initial skill data
-- Run this after the combined_skills_migration.sql file

DO $$
BEGIN
    -- Programming & Development
    PERFORM insert_skill_hierarchy('Programming & Development', NULL, NULL, NULL, NULL, 'Skills related to programming and development in game creation');
    
    -- Game Engines category
    PERFORM insert_skill_hierarchy('Programming & Development', 'Game Engines', NULL, NULL, NULL, 'Skills related to various game engines');
    
    -- Unreal Engine skills
    PERFORM insert_skill_hierarchy('Programming & Development', 'Game Engines', 'Unreal Engine', NULL, NULL, 'Skills related to Unreal Engine development', 'bi-joystick');
    
    -- Unreal Engine micro-skills
    PERFORM insert_skill_hierarchy('Programming & Development', 'Game Engines', 'Unreal Engine', 'Blueprint Visual Scripting', NULL, 'Visual scripting system in Unreal Engine');
    PERFORM insert_skill_hierarchy('Programming & Development', 'Game Engines', 'Unreal Engine', 'C++ Programming in Unreal', NULL, 'C++ programming specific to Unreal Engine');
    PERFORM insert_skill_hierarchy('Programming & Development', 'Game Engines', 'Unreal Engine', 'Unreal Material System', NULL, 'Material creation and management in Unreal Engine');
    
    -- Blueprint Visual Scripting mastery components
    PERFORM insert_skill_hierarchy('Programming & Development', 'Game Engines', 'Unreal Engine', 'Blueprint Visual Scripting', 'Blueprint Component System', 'Creating and managing components in Blueprint');
    PERFORM insert_skill_hierarchy('Programming & Development', 'Game Engines', 'Unreal Engine', 'Blueprint Visual Scripting', 'Animation Blueprint System', 'Creating and managing animation blueprints');
    PERFORM insert_skill_hierarchy('Programming & Development', 'Game Engines', 'Unreal Engine', 'Blueprint Visual Scripting', 'UI Blueprint Integration', 'Integrating UI elements with Blueprint');
    
    -- Unity Engine skills
    PERFORM insert_skill_hierarchy('Programming & Development', 'Game Engines', 'Unity Engine', NULL, NULL, 'Skills related to Unity Engine development', 'bi-unity');
    
    -- Unity Engine micro-skills
    PERFORM insert_skill_hierarchy('Programming & Development', 'Game Engines', 'Unity Engine', 'C# Programming in Unity', NULL, 'C# programming specific to Unity Engine');
    PERFORM insert_skill_hierarchy('Programming & Development', 'Game Engines', 'Unity Engine', 'Unity Rendering', NULL, 'Rendering systems in Unity Engine');
    PERFORM insert_skill_hierarchy('Programming & Development', 'Game Engines', 'Unity Engine', 'Unity Physics', NULL, 'Physics systems in Unity Engine');
    
    -- Programming Languages category
    PERFORM insert_skill_hierarchy('Programming & Development', 'Programming Languages', NULL, NULL, NULL, 'Skills related to programming languages used in game development');
    
    -- C++ skills
    PERFORM insert_skill_hierarchy('Programming & Development', 'Programming Languages', 'C++', NULL, NULL, 'Skills related to C++ programming', 'bi-code-slash');
    
    -- C++ micro-skills
    PERFORM insert_skill_hierarchy('Programming & Development', 'Programming Languages', 'C++', 'C++ Fundamentals', NULL, 'Fundamental concepts of C++ programming');
    PERFORM insert_skill_hierarchy('Programming & Development', 'Programming Languages', 'C++', 'Modern C++ (11/14/17/20)', NULL, 'Modern C++ features and standards');
    PERFORM insert_skill_hierarchy('Programming & Development', 'Programming Languages', 'C++', 'STL Library Usage', NULL, 'Standard Template Library usage in C++');
    
    -- Art & Visual Design category
    PERFORM insert_skill_hierarchy('Art & Visual Design', NULL, NULL, NULL, NULL, 'Skills related to art and visual design in game creation');
    
    -- 2D Art category
    PERFORM insert_skill_hierarchy('Art & Visual Design', '2D Art', NULL, NULL, NULL, 'Skills related to 2D art creation');
    
    -- Concept Art skills
    PERFORM insert_skill_hierarchy('Art & Visual Design', '2D Art', 'Concept Art', NULL, NULL, 'Skills related to concept art creation', 'bi-brush');
    
    -- Concept Art micro-skills
    PERFORM insert_skill_hierarchy('Art & Visual Design', '2D Art', 'Concept Art', 'Character Concept Art', NULL, 'Creating concept art for characters');
    PERFORM insert_skill_hierarchy('Art & Visual Design', '2D Art', 'Concept Art', 'Environment Concept Art', NULL, 'Creating concept art for environments');
    PERFORM insert_skill_hierarchy('Art & Visual Design', '2D Art', 'Concept Art', 'Prop Concept Art', NULL, 'Creating concept art for props');
    
    -- Add more skills as needed...
END $$;
