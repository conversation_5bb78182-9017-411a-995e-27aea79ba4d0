// SocialNotifications - Simplified notification system
import React, { useState, useEffect, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardBody, Button, Badge } from '@heroui/react';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { supabase } from '../../utils/supabase/supabase.utils';
import socialService from '../../services/socialService';
import { 
  Bell, 
  MessageCircle, 
  UserPlus, 
  CheckCircle, 
  X,
  Users,
  Trophy
} from 'lucide-react';

const SocialNotifications = ({ isOpen, onClose }) => {
  const { currentUser } = useContext(UserContext);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (currentUser && isOpen) {
      loadNotifications();
    }
  }, [currentUser, isOpen]);

  const loadNotifications = async () => {
    try {
      setIsLoading(true);
      
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', currentUser.id)
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) throw error;

      setNotifications(data || []);
      const unread = data ? data.filter(n => !n.is_read).length : 0;
      setUnreadCount(unread);
    } catch (error) {
      console.error('Error loading notifications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const markAsRead = async (notificationId) => {
    try {
      await socialService.markNotificationAsRead(notificationId);
      
      setNotifications(prev => 
        prev.map(n => 
          n.id === notificationId ? { ...n, is_read: true } : n
        )
      );
      
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const respondToFriendRequest = async (notificationId, response) => {
    try {
      await socialService.respondToFriendRequest(notificationId, response);
      
      setNotifications(prev => 
        prev.map(n => 
          n.id === notificationId 
            ? { 
                ...n, 
                is_read: true,
                metadata: { 
                  ...n.metadata, 
                  responded: true, 
                  response 
                }
              } 
            : n
        )
      );
      
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Error responding to friend request:', error);
    }
  };

  const getNotificationIcon = (type) => {
    const iconMap = {
      message: <MessageCircle className="text-blue-500" size={20} />,
      friend_request: <UserPlus className="text-green-500" size={20} />,
      friend_request_response: <Users className="text-purple-500" size={20} />,
      achievement: <Trophy className="text-yellow-500" size={20} />
    };
    return iconMap[type] || <Bell className="text-gray-500" size={20} />;
  };

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className="fixed top-16 right-4 z-50"
    >
      <Card className="w-96 max-h-[80vh] bg-white shadow-xl">
        <CardBody className="p-0">
          {/* Header */}
          <div className="p-4 border-b">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Bell size={20} className="text-primary" />
                <h3 className="font-semibold">Notifications</h3>
                {unreadCount > 0 && (
                  <Badge content={unreadCount} color="danger" size="sm" />
                )}
              </div>
              <Button
                isIconOnly
                size="sm"
                variant="light"
                onPress={onClose}
              >
                <X size={16} />
              </Button>
            </div>
          </div>

          {/* Notifications list */}
          <div className="max-h-96 overflow-y-auto">
            {isLoading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2"></div>
                <p className="text-sm text-gray-500">Loading notifications...</p>
              </div>
            ) : notifications.length === 0 ? (
              <div className="p-8 text-center">
                <Bell size={32} className="mx-auto mb-2 text-gray-300" />
                <p className="text-sm text-gray-500">No notifications</p>
              </div>
            ) : (
              <div className="space-y-1">
                {notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`p-4 hover:bg-gray-50 transition-colors border-l-4 ${
                      notification.is_read 
                        ? 'border-transparent' 
                        : 'border-primary'
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 mt-1">
                        {getNotificationIcon(notification.type)}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <h4 className={`text-sm font-medium mb-1 ${
                          notification.is_read ? 'text-gray-700' : 'text-gray-900'
                        }`}>
                          {notification.title}
                        </h4>
                        
                        <p className={`text-sm mb-2 ${
                          notification.is_read ? 'text-gray-500' : 'text-gray-700'
                        }`}>
                          {notification.message}
                        </p>
                        
                        {/* Friend request actions */}
                        {notification.type === 'friend_request' && !notification.metadata?.responded && (
                          <div className="flex gap-2 mb-2">
                            <Button
                              size="sm"
                              color="success"
                              variant="flat"
                              onPress={() => respondToFriendRequest(notification.id, 'accept')}
                              startContent={<CheckCircle size={14} />}
                            >
                              Accept
                            </Button>
                            <Button
                              size="sm"
                              color="danger"
                              variant="flat"
                              onPress={() => respondToFriendRequest(notification.id, 'decline')}
                              startContent={<X size={14} />}
                            >
                              Decline
                            </Button>
                          </div>
                        )}
                        
                        <div className="flex items-center justify-between">
                          <p className="text-xs text-gray-500">
                            {new Date(notification.created_at).toLocaleDateString()}
                          </p>
                          
                          {!notification.is_read && (
                            <Button
                              size="sm"
                              variant="light"
                              onPress={() => markAsRead(notification.id)}
                              className="text-xs"
                            >
                              Mark as read
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardBody>
      </Card>
    </motion.div>
  );
};

export default SocialNotifications;
