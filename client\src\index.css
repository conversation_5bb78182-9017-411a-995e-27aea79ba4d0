/* Minimal base styles - let Hero<PERSON> handle everything else */
:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  overflow: hidden; /* Hide body scrollbar for immersive navigation */
}

/* Hide scrollbars globally for immersive navigation experience */
* {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

*::-webkit-scrollbar {
  display: none; /* WebKit */
}

/* Ensure html doesn't create scrollbars */
html {
  overflow: hidden;
  height: 100%;
  width: 100%;
}

/* Allow scrolling within content areas when needed */
.canvas-content {
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.canvas-content::-webkit-scrollbar {
  display: none;
}

.pointer {
  cursor: pointer;
}


