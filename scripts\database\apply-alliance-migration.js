// Apply Alliance & Venture System Migration
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_KEY);

async function applyMigrationInChunks() {
  console.log('🚀 Applying Alliance & Venture System Migration in chunks...');
  
  try {
    // Read the migration file
    const migrationSQL = fs.readFileSync('../../supabase/migrations/20250609000001_alliance_venture_system.sql', 'utf8');
    
    // Split into logical chunks by major sections
    const chunks = migrationSQL.split('-- ============================================================================');
    
    console.log(`📄 Migration split into ${chunks.length} chunks`);
    
    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i].trim();
      if (!chunk) continue;
      
      console.log(`\n🔧 Applying chunk ${i + 1}/${chunks.length}...`);
      
      try {
        // For PostgreSQL, we need to execute each statement separately
        const statements = chunk.split(';').filter(stmt => stmt.trim());
        
        for (const statement of statements) {
          const cleanStatement = statement.trim();
          if (!cleanStatement || cleanStatement.startsWith('--')) continue;
          
          console.log(`   Executing: ${cleanStatement.substring(0, 50)}...`);
          
          const { error } = await supabase.rpc('exec', { sql: cleanStatement + ';' });
          
          if (error && !error.message.includes('already exists') && !error.message.includes('does not exist')) {
            console.log(`   ⚠️ Statement warning:`, error.message);
          }
        }
        
        console.log(`   ✅ Chunk ${i + 1} completed`);
        
      } catch (chunkError) {
        console.log(`   ❌ Chunk ${i + 1} failed:`, chunkError.message);
        // Continue with next chunk
      }
    }
    
    console.log('\n🎯 Migration application completed!');
    
    // Verify the migration worked
    console.log('\n🔍 Verifying migration results...');
    await verifyMigration();
    
  } catch (err) {
    console.log('❌ Error applying migration:', err.message);
  }
}

async function verifyMigration() {
  try {
    // Check if alliance_invitations table exists
    const { data: invitations, error: invError } = await supabase
      .from('alliance_invitations')
      .select('*')
      .limit(0);
    
    if (!invError) {
      console.log('✅ alliance_invitations table created successfully');
    } else {
      console.log('❌ alliance_invitations table not found:', invError.message);
    }
    
    // Check if teams table has new columns
    const { data: teams, error: teamsError } = await supabase
      .from('teams')
      .select('*')
      .limit(1);
    
    if (!teamsError && teams.length === 0) {
      console.log('✅ teams table accessible with new structure');
    } else if (!teamsError) {
      const columns = Object.keys(teams[0]);
      const hasAllianceColumns = ['alliance_type', 'business_model', 'industry'].every(col => columns.includes(col));
      console.log(hasAllianceColumns ? '✅ teams table has alliance columns' : '❌ teams table missing alliance columns');
      console.log('Available columns:', columns);
    }
    
    // Check if projects table has venture columns
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('*')
      .limit(1);
    
    if (!projectsError && projects.length > 0) {
      const columns = Object.keys(projects[0]);
      const hasVentureColumns = ['venture_type', 'revenue_model', 'alliance_id'].every(col => columns.includes(col));
      console.log(hasVentureColumns ? '✅ projects table has venture columns' : '❌ projects table missing venture columns');
    }
    
    // Check if tasks table has mission/bounty columns
    const { data: tasks, error: tasksError } = await supabase
      .from('tasks')
      .select('*')
      .limit(1);
    
    if (!tasksError && tasks.length > 0) {
      const columns = Object.keys(tasks[0]);
      const hasMissionColumns = ['task_category', 'bounty_amount', 'is_public'].every(col => columns.includes(col));
      console.log(hasMissionColumns ? '✅ tasks table has mission/bounty columns' : '❌ tasks table missing mission/bounty columns');
    }
    
  } catch (verifyError) {
    console.log('❌ Verification failed:', verifyError.message);
  }
}

applyMigrationInChunks();
