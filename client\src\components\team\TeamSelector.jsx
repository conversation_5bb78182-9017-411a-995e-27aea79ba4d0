import React, { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { toast } from 'react-hot-toast';

/**
 * TeamSelector component for selecting a team to own a project
 * @param {Object} props - Component props
 * @param {string} props.selectedTeamId - Currently selected team ID
 * @param {Function} props.onTeamSelect - Callback when a team is selected
 * @param {boolean} props.isTeamOwned - Whether the project is owned by a team
 * @param {Function} props.onTeamOwnedChange - Callback when team ownership changes
 */
const TeamSelector = ({
  selectedTeamId,
  onTeamSelect,
  isTeamOwned,
  onTeamOwnedChange
}) => {
  const { currentUser } = useContext(UserContext);
  const [teams, setTeams] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newTeam, setNewTeam] = useState({
    name: '',
    description: ''
  });

  // Fetch teams on component mount
  useEffect(() => {
    if (currentUser) {
      fetchUserTeams();
    }
  }, [currentUser]);

  // Fetch teams where the user is an admin
  const fetchUserTeams = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get teams where the user is an admin
      const { data: teamMembers, error: memberError } = await supabase
        .from('team_members')
        .select('team_id, is_admin, role')
        .eq('user_id', currentUser.id);

      if (memberError) throw memberError;

      if (teamMembers && teamMembers.length > 0) {
        // Get team details
        const teamIds = teamMembers.map(member => member.team_id);
        const { data: teamData, error: teamError } = await supabase
          .from('teams')
          .select('*')
          .in('id', teamIds);

        if (teamError) throw teamError;

        // Combine team data with member role and admin status
        const teamsWithRoles = teamData.map(team => {
          const membership = teamMembers.find(member => member.team_id === team.id);
          return {
            ...team,
            role: membership?.role || 'member',
            is_admin: membership?.is_admin || false
          };
        });

        setTeams(teamsWithRoles);
      } else {
        setTeams([]);
      }
    } catch (err) {
      console.error('Error fetching teams:', err);
      setError('Failed to load teams');
    } finally {
      setLoading(false);
    }
  };

  // Handle creating a new team
  const handleCreateTeam = async (e) => {
    e.preventDefault();

    if (!newTeam.name.trim()) {
      toast.error('Team name is required');
      return;
    }

    try {
      setLoading(true);

      // Create the team
      const { data: teamData, error: teamError } = await supabase
        .from('teams')
        .insert([{
          name: newTeam.name,
          description: newTeam.description,
          created_by: currentUser.id
        }])
        .select()
        .single();

      if (teamError) throw teamError;

      // Add the creator as an admin member
      const { error: memberError } = await supabase
        .from('team_members')
        .insert([{
          team_id: teamData.id,
          user_id: currentUser.id,
          role: 'owner',
          is_admin: true
        }]);

      if (memberError) throw memberError;

      toast.success('Team created successfully');
      setNewTeam({ name: '', description: '' });
      setShowCreateForm(false);

      // Select the newly created team
      onTeamSelect(teamData.id);
      onTeamOwnedChange(true);

      // Refresh teams list
      fetchUserTeams();
    } catch (error) {
      console.error('Error creating team:', error);
      toast.error('Failed to create team');
    } finally {
      setLoading(false);
    }
  };

  // Handle team ownership toggle
  const handleTeamOwnedToggle = (e) => {
    const isChecked = e.target.checked;
    onTeamOwnedChange(isChecked);

    // If toggling off, clear the selected team
    if (!isChecked) {
      onTeamSelect(null);
    } else if (teams.length === 1) {
      // If there's only one team, select it automatically
      onTeamSelect(teams[0].id);
    }
  };

  // Handle team selection
  const handleTeamSelect = (e) => {
    onTeamSelect(e.target.value);
  };

  return (
    <div className="team-selector">
      <div className="team-ownership-toggle">
        <label className="toggle-label">
          <input
            type="checkbox"
            checked={isTeamOwned}
            onChange={handleTeamOwnedToggle}
          />
          <span className="toggle-text">This project is owned by a team</span>
        </label>
      </div>

      {isTeamOwned && (
        <div className="team-selection">
          {loading ? (
            <div className="loading-indicator">Loading teams...</div>
          ) : error ? (
            <div className="error-message">{error}</div>
          ) : (
            <>
              {showCreateForm ? (
                <div className="create-team-form">
                  <h3>Create New Team</h3>
                  <form onSubmit={handleCreateTeam}>
                    <div className="form-group">
                      <label htmlFor="team-name">Team Name</label>
                      <input
                        id="team-name"
                        type="text"
                        value={newTeam.name}
                        onChange={(e) => setNewTeam({ ...newTeam, name: e.target.value })}
                        placeholder="Enter team name"
                        className="form-input"
                        required
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="team-description">Description (optional)</label>
                      <textarea
                        id="team-description"
                        value={newTeam.description}
                        onChange={(e) => setNewTeam({ ...newTeam, description: e.target.value })}
                        placeholder="Brief description of the team"
                        className="form-input"
                        rows="2"
                      />
                    </div>
                    <div className="form-actions">
                      <button type="submit" className="create-team-btn">Create Team</button>
                      <button
                        type="button"
                        className="cancel-btn"
                        onClick={() => setShowCreateForm(false)}
                      >
                        Cancel
                      </button>
                    </div>
                  </form>
                </div>
              ) : (
                <>
                  {teams.length === 0 ? (
                    <div className="no-teams-message">
                      <p>You don't have any teams yet.</p>
                      <button
                        className="create-first-team-btn"
                        onClick={() => setShowCreateForm(true)}
                      >
                        Create Your First Team
                      </button>
                    </div>
                  ) : (
                    <>
                      <div className="team-select-container">
                        <label htmlFor="team-select">Select a team:</label>
                        <select
                          id="team-select"
                          value={selectedTeamId || ''}
                          onChange={handleTeamSelect}
                          className="team-select"
                        >
                          <option value="">-- Select a team --</option>
                          {teams.map(team => (
                            <option key={team.id} value={team.id}>
                              {team.name} {team.role === 'owner' ? '(Owner)' : team.is_admin ? '(Admin)' : ''}
                            </option>
                          ))}
                        </select>
                      </div>

                      {selectedTeamId && (
                        <div className="selected-team-info">
                          {teams.find(t => t.id === selectedTeamId)?.description}
                        </div>
                      )}

                      <div className="team-actions">
                        <button
                          className="create-team-link"
                          onClick={() => setShowCreateForm(true)}
                        >
                          <i className="bi bi-plus-circle"></i> Create a new team
                        </button>
                        <Link to="/teams" className="manage-teams-link">
                          <i className="bi bi-people"></i> Manage your teams
                        </Link>
                      </div>
                    </>
                  )}
                </>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default TeamSelector;
