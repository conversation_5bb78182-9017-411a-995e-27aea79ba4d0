// Test Plaid Payment APIs
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_KEY);

// Mock JWT token for testing
const createMockToken = (userId) => {
  const header = Buffer.from(JSON.stringify({ alg: 'HS256', typ: 'JWT' })).toString('base64');
  const payload = Buffer.from(JSON.stringify({ sub: userId, iat: Date.now() })).toString('base64');
  const signature = 'mock-signature';
  return `${header}.${payload}.${signature}`;
};

// Test user ID
const TEST_USER_ID = '2a033231-d173-4292-aa36-90f4d735bcf3';

async function testPlaidPaymentAPIs() {
  console.log('🧪 Testing Plaid Payment APIs...');
  
  try {
    // Test 1: Payment Method Selection Logic
    console.log('\n1️⃣ Testing Payment Method Selection...');
    
    const testCases = [
      { amount: 100, urgency: 'immediate', expected: 'rtp' },
      { amount: 50000, urgency: 'same_day', expected: 'ach_same_day' },
      { amount: 1500000, urgency: 'standard', expected: 'wire_domestic' },
      { amount: 1000, urgency: 'standard', expected: 'ach_standard' }
    ];
    
    for (const testCase of testCases) {
      const routing = determinePaymentMethod(testCase.amount, testCase.urgency, 'outbound');
      console.log(`   Amount: $${testCase.amount}, Urgency: ${testCase.urgency} → ${routing.preferred}`);
      
      if (routing.preferred === testCase.expected) {
        console.log('   ✅ Correct routing');
      } else {
        console.log(`   ❌ Expected ${testCase.expected}, got ${routing.preferred}`);
      }
    }
    
    // Test 2: Fee Calculation
    console.log('\n2️⃣ Testing Fee Calculation...');
    
    const feeTests = [
      { amount: 1000, method: 'ach_standard', expectedFixed: 0.25 },
      { amount: 1000, method: 'ach_same_day', expectedFixed: 1.50 },
      { amount: 1000, method: 'wire_domestic', expectedFixed: 15.00 },
      { amount: 100000, method: 'rtp', expectedPercentage: 100 } // 0.1% of 100k
    ];
    
    for (const feeTest of feeTests) {
      const fees = calculatePaymentFees(feeTest.amount, feeTest.method);
      console.log(`   ${feeTest.method}: $${feeTest.amount} → $${fees.total} total fee`);
      console.log(`     Fixed: $${fees.fixed}, Percentage: $${fees.percentage}`);
      
      if (feeTest.expectedFixed && Math.abs(fees.fixed - feeTest.expectedFixed) < 0.01) {
        console.log('   ✅ Fixed fee correct');
      } else if (feeTest.expectedPercentage && Math.abs(fees.percentage - feeTest.expectedPercentage) < 0.01) {
        console.log('   ✅ Percentage fee correct');
      }
    }
    
    // Test 3: Payment Transaction Creation
    console.log('\n3️⃣ Testing Payment Transaction Creation...');
    
    const paymentData = {
      company_id: null,
      transaction_type: 'payment_transfer',
      transaction_category: 'test_payment',
      gross_amount: 500.00,
      net_amount: 498.50,
      currency: 'USD',
      payee_user_id: TEST_USER_ID,
      payer_user_id: TEST_USER_ID,
      description: 'Test payment transaction',
      reference_number: `TEST-PAY-${Date.now()}`,
      payment_method: 'ach_standard',
      created_by: TEST_USER_ID
    };
    
    const { data: transaction, error: transactionError } = await supabase
      .from('financial_transactions')
      .insert([paymentData])
      .select()
      .single();
    
    if (transactionError) {
      console.log('❌ Payment transaction creation failed:', transactionError.message);
    } else {
      console.log('✅ Payment transaction created:', transaction.id);
      console.log(`   Amount: $${transaction.gross_amount}, Method: ${transaction.payment_method}`);
      
      // Test 4: Transaction Status Updates
      console.log('\n4️⃣ Testing Transaction Status Updates...');
      
      const statusUpdates = ['processing', 'completed', 'failed'];
      
      for (const status of statusUpdates) {
        const { error: updateError } = await supabase
          .from('financial_transactions')
          .update({ status: status })
          .eq('id', transaction.id);
        
        if (updateError) {
          console.log(`   ❌ Status update to ${status} failed:`, updateError.message);
        } else {
          console.log(`   ✅ Status updated to ${status}`);
        }
      }
      
      // Test 5: Payment History Query
      console.log('\n5️⃣ Testing Payment History Query...');
      
      const { data: paymentHistory, error: historyError } = await supabase
        .from('financial_transactions')
        .select('*')
        .eq('transaction_type', 'payment_transfer')
        .or(`created_by.eq.${TEST_USER_ID},payee_user_id.eq.${TEST_USER_ID}`)
        .order('created_at', { ascending: false })
        .limit(10);
      
      if (historyError) {
        console.log('❌ Payment history query failed:', historyError.message);
      } else {
        console.log(`✅ Payment history retrieved: ${paymentHistory.length} transactions`);
        
        paymentHistory.forEach((payment, index) => {
          console.log(`   ${index + 1}. $${payment.gross_amount} - ${payment.payment_method} - ${payment.status}`);
        });
      }
      
      // Cleanup test transaction
      await supabase.from('financial_transactions').delete().eq('id', transaction.id);
      console.log('🧹 Test transaction cleaned up');
    }
    
    // Test 6: Settlement Date Calculation
    console.log('\n6️⃣ Testing Settlement Date Calculation...');
    
    const settlementTests = [
      { method: 'rtp', expectedDays: 0 },
      { method: 'ach_same_day', expectedDays: 1 },
      { method: 'ach_standard', expectedDays: 3 },
      { method: 'wire_domestic', expectedDays: 1 }
    ];
    
    for (const test of settlementTests) {
      const settlement = getEstimatedSettlement(test.method);
      const today = new Date().toISOString().split('T')[0];
      const expectedDate = new Date();
      expectedDate.setDate(expectedDate.getDate() + test.expectedDays);
      const expected = expectedDate.toISOString().split('T')[0];
      
      console.log(`   ${test.method}: ${settlement} (expected: ${expected})`);
      
      if (test.expectedDays === 0 && settlement === today) {
        console.log('   ✅ Immediate settlement correct');
      } else if (settlement === expected) {
        console.log('   ✅ Settlement date correct');
      }
    }
    
    console.log('\n🎉 Plaid Payment API tests completed!');
    console.log('✅ Payment method routing working');
    console.log('✅ Fee calculation accurate');
    console.log('✅ Transaction creation and updates working');
    console.log('✅ Payment history queries working');
    console.log('✅ Settlement date calculation working');
    
  } catch (error) {
    console.error('❌ Plaid API test failed:', error);
  }
}

// Helper functions (copied from the API files for testing)
const determinePaymentMethod = (amount, urgency, direction) => {
  const PAYMENT_ROUTING = {
    immediate: { preferred: 'rtp', fallback: ['ach_same_day', 'ach_standard'] },
    same_day: { preferred: 'ach_same_day', fallback: ['ach_standard'] },
    standard: { preferred: 'ach_standard', fallback: [] },
    large_amount: { preferred: 'wire_domestic', fallback: ['ach_same_day', 'ach_standard'] }
  };

  if (amount > 1000000) return PAYMENT_ROUTING.large_amount;
  
  switch (urgency) {
    case 'immediate': return PAYMENT_ROUTING.immediate;
    case 'same_day': return PAYMENT_ROUTING.same_day;
    case 'standard':
    default: return PAYMENT_ROUTING.standard;
  }
};

const calculatePaymentFees = (amount, paymentMethod) => {
  const feeStructure = {
    ach_standard: { fixed: 0.25, percentage: 0 },
    ach_same_day: { fixed: 1.50, percentage: 0 },
    rtp: { fixed: 0.50, percentage: 0.001 },
    wire_domestic: { fixed: 15.00, percentage: 0 },
    wire_international: { fixed: 45.00, percentage: 0.002 }
  };

  const fees = feeStructure[paymentMethod] || feeStructure.ach_standard;
  const fixedFee = fees.fixed;
  const percentageFee = amount * fees.percentage;
  const total = fixedFee + percentageFee;

  return { fixed: fixedFee, percentage: percentageFee, total: total, method: paymentMethod };
};

const getEstimatedSettlement = (paymentMethod) => {
  const now = new Date();
  const settlementTimes = {
    rtp: 0,
    ach_same_day: 1,
    ach_standard: 3,
    wire_domestic: 1,
    wire_international: 5
  };

  const daysToAdd = settlementTimes[paymentMethod] || 3;
  const settlementDate = new Date(now.getTime() + (daysToAdd * 24 * 60 * 60 * 1000));
  
  return settlementDate.toISOString().split('T')[0];
};

// Run tests
testPlaidPaymentAPIs();
