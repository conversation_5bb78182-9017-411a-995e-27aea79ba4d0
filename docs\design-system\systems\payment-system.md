# Payment System
**Complete System Specification v1.0**

## 📋 Document Information
- **Last Updated**: January 16, 2025
- **Version**: 1.0
- **Implementation Status**: 🔴 Not Implemented
- **Priority**: 🔥 Critical (Revenue dependency)

---

## 🎯 System Overview

**[Design Team: Define the complete payment and financial system]**

The Payment System handles all financial transactions, revenue distribution, escrow management, and payment processing through Teller integration, supporting multiple business models and payment methods.

### **Key Features**
**[Design Team: Specify all payment features you want]**
- **Teller Integration**: Bank account linking and ACH transfers with superior developer experience
- **Escrow Management**: Secure fund holding until conditions met
- **Revenue Distribution**: Automated profit sharing based on agreements
- **Commission Tracking**: Real-time commission calculation and payment
- **Subscription Billing**: Recurring payment management
- **Multi-Payment Methods**: Support for all Teller-enabled payment types
- **Financial Reporting**: Comprehensive transaction and earnings reports

### **User Benefits**
**[Design Team: Describe what users gain from the payment system]**
- Secure, automated payment processing
- Transparent revenue sharing and tracking
- Multiple payment method options
- Real-time financial insights and reporting
- Automated tax documentation and compliance
- Instant payment notifications and confirmations

---

## 🏗️ Architecture

**[Design Team: Map out the payment system structure]**

### **Core Components**
```
Payment System
├── Teller Integration
│   ├── Bank Account Linking
│   ├── Payment Method Verification
│   ├── ACH Transfer Processing
│   └── Transaction Monitoring
├── Escrow Management
│   ├── Fund Holding
│   ├── Condition Verification
│   ├── Automated Release
│   └── Dispute Resolution
├── Revenue Distribution
│   ├── Agreement Parsing
│   ├── Share Calculation
│   ├── Automated Payments
│   └── Distribution Reports
├── Commission Engine
│   ├── Rate Configuration
│   ├── Real-time Calculation
│   ├── Payment Scheduling
│   └── Performance Tracking
├── Subscription Management
│   ├── Recurring Billing
│   ├── Plan Management
│   ├── Usage Tracking
│   └── Invoice Generation
└── Financial Reporting
    ├── Transaction History
    ├── Earnings Reports
    ├── Tax Documentation
    └── Analytics Dashboard
```

---

## 🎨 User Interface Design

**[Design Team: Design the payment interfaces]**

### **Payment Dashboard (Main View)**
```
┌─────┐ ┌─────────────────────────────────────────────────────────┐ ┌─────┐
│ 🔔  │ │                 Financial Overview                      │ │ 💳  │
│     │ │                                                         │ │Add  │
│ 📧  │ │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │ │Pay  │
│     │ │  │💰 This Month│ │🔒 Escrow    │ │📊 Analytics │       │ │Meth │
│ 📋  │ │  │ Earned: $2.4k│ │ Held: $1.2k │ │ View Trends │       │ │     │
│     │ │  │ Paid: $1.9k │ │ 3 Projects  │ │ Export Data │       │ │ 💸  │
│ 👥  │ │  └─────────────┘ └─────────────┘ └─────────────┘       │ │With │
│     │ │                                                         │ │draw │
│ ⚙️  │ │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │ │     │
│     │ │  │📋 Transactions│ │⏰ Pending   │ │🏦 Methods   │       │ │ 📄  │
│     │ │  │ 12 This Week│ │ $560 Due    │ │ 2 Connected │       │ │Rep  │
│     │ │  │ View History│ │ 3 Items     │ │ Manage All  │       │ │orts │
│     │ │  └─────────────┘ └─────────────┘ └─────────────┘       │ │     │
└─────┘ └─────────────────────────────────────────────────────────┘ └─────┘
```

### **Payment Method Setup**
```
┌─────┐ ┌─────────────────────────────────────────────────────────┐ ┌─────┐
│ 🔔  │ │                Add Payment Method                       │ │ 🔒  │
│     │ │                                                         │ │Sec  │
│ 📧  │ │  Connect with Plaid:                                   │ │ure  │
│     │ │  ┌─────────────────────────────────────────────────────┐ │ │     │
│ 📋  │ │  │ 🏦 Select Your Bank                                 │ │ │ ❓  │
│     │ │  │                                                     │ │ │Help │
│ 👥  │ │  │ [🏛️ Chase Bank]  [🏛️ Bank of America]              │ │ │     │
│     │ │  │ [🏛️ Wells Fargo] [🏛️ Citibank]                     │ │ │ 📞  │
│ ⚙️  │ │  │ [🔍 Search for your bank...]                        │ │ │Supp │
│     │ │  │                                                     │ │ │ort  │
│     │ │  │ 🔒 Secured by Plaid - Bank-level security          │ │ │     │
│     │ │  └─────────────────────────────────────────────────────┘ │ │ ⚙️  │
│     │ │                                                         │ │Alt  │
│     │ │  [💳 Debit Card] [🏦 Wire] [📱 Digital Wallet]         │ │Meth │
│     │ │                              [Cancel] [Continue]       │ │     │
└─────┘ └─────────────────────────────────────────────────────────┘ └─────┘
```

### **Escrow Status Interface**
```
┌─────────────────────────────────────────────────────┐
│ 🔒 Escrow Management                                │
│                                                     │
│ Active Escrow Accounts:                             │
│                                                     │
│ Project Alpha - Phase 1                            │
│ Amount: $1,200.00                                   │
│ Conditions: ✅ Milestone Complete ⏳ Client Review  │
│ Release Date: Jan 25, 2025                         │
│ [View Details]                                      │
│                                                     │
│ Venture Beta - Commission                           │
│ Amount: $350.00                                     │
│ Conditions: ⏳ 30-day holding period               │
│ Release Date: Feb 5, 2025                          │
│ [View Details]                                      │
│                                                     │
│ Total in Escrow: $1,550.00                         │
└─────────────────────────────────────────────────────┘
```

### **Revenue Distribution Setup**
```
┌─────────────────────────────────────────────────────┐
│ Revenue Distribution Configuration                   │
│                                                     │
│ Project: Alpha Development                          │
│ Total Revenue: $10,000.00                          │
│                                                     │
│ Distribution Model: [Percentage-based ▼]           │
│                                                     │
│ Contributors:                                       │
│ • John Doe (Lead Dev)     40% = $4,000.00         │
│ • Sarah Smith (Designer)  30% = $3,000.00         │
│ • Mike Chen (QA)         20% = $2,000.00         │
│ • Platform Fee           10% = $1,000.00         │
│                                                     │
│ Payment Schedule: [Upon milestone completion ▼]    │
│ Escrow Period: [30 days ▼]                        │
│                                                     │
│                    [Save Configuration] [Preview]  │
└─────────────────────────────────────────────────────┘
```

---

## 🔄 User Experience Flow

**[Design Team: Map out payment user journeys]**

### **Payment Method Setup Flow**
```mermaid
graph TD
    A[User Needs to Add Payment Method] --> B[Click 'Add Payment Method']
    B --> C[Choose Plaid Bank Connection]
    C --> D[Select Bank from List]
    D --> E[Enter Bank Credentials]
    E --> F[Plaid Verifies Account]
    F --> G[Account Successfully Linked]
    G --> H[Payment Method Available]
```

### **Revenue Distribution Flow**
```mermaid
graph TD
    A[Project Revenue Received] --> B[System Calculates Shares]
    B --> C[Funds Placed in Escrow]
    C --> D[Escrow Conditions Check]
    D --> E[Conditions Met?]
    E -->|Yes| F[Initiate Distribution]
    E -->|No| G[Wait for Conditions]
    F --> H[Process Individual Payments]
    H --> I[Send Payment Notifications]
    G --> D
```

---

## 📊 Data Requirements

**[Design Team: Specify payment data needs]**

### **Database Schema**
```sql
-- Payment methods table
CREATE TABLE payment_methods (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id),
    plaid_account_id VARCHAR(255),
    account_type VARCHAR(50), -- 'checking', 'savings', 'credit'
    bank_name VARCHAR(255),
    account_mask VARCHAR(10), -- Last 4 digits
    is_verified BOOLEAN DEFAULT false,
    is_primary BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Transactions table
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id),
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    transaction_type VARCHAR(50), -- 'payment', 'withdrawal', 'commission'
    status VARCHAR(20), -- 'pending', 'completed', 'failed', 'cancelled'
    plaid_transaction_id VARCHAR(255),
    source_type VARCHAR(50), -- 'project', 'subscription', 'commission'
    source_id UUID,
    description TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP
);

-- Escrow accounts table
CREATE TABLE escrow_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID,
    amount DECIMAL(10,2) NOT NULL,
    conditions JSONB, -- Conditions that must be met for release
    release_date DATE,
    status VARCHAR(20) DEFAULT 'active', -- 'active', 'released', 'disputed'
    created_at TIMESTAMP DEFAULT NOW(),
    released_at TIMESTAMP
);

-- Revenue distributions table
CREATE TABLE revenue_distributions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID,
    total_amount DECIMAL(10,2),
    distribution_rules JSONB, -- Percentage breakdown
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT NOW(),
    distributed_at TIMESTAMP
);
```

---

## 🔧 Technical Implementation

**[Design Team: You don't need to fill this out - AI will handle it]**

### **Component Structure**
```
client/src/components/payments/
├── PaymentDashboard.jsx
├── PaymentMethodSetup.jsx
├── TellerLinkComponent.jsx
├── EscrowManager.jsx
├── RevenueDistribution.jsx
├── TransactionHistory.jsx
├── FinancialReports.jsx
├── CommissionTracker.jsx
└── PaymentNotifications.jsx
```

### **Teller Integration**
```javascript
// Teller configuration
const TELLER_CONFIG = {
  environment: 'production', // or 'sandbox' for testing
  applicationId: 'app_pelk82mrrofp6upddo000',
  certificatePath: './teller/certificate.pem',
  privateKeyPath: './teller/private_key.pem',
  webhook: 'https://api.royaltea.technology/webhooks/teller'
};

// Payment processing workflow
const processPayment = async (amount, recipient, source) => {
  // 1. Validate payment details
  // 2. Check escrow conditions
  // 3. Initiate Teller transfer
  // 4. Update transaction records
  // 5. Send notifications
};
```

---

## 🧪 Testing Requirements

**[Design Team: Define what payment features should work]**

### **User Acceptance Criteria**
- [ ] Users can securely link bank accounts through Teller
- [ ] Revenue distribution calculates and pays correctly
- [ ] Escrow system holds and releases funds based on conditions
- [ ] Commission tracking updates in real-time
- [ ] Payment notifications are sent promptly
- [ ] Financial reports are accurate and comprehensive
- [ ] All transactions are properly logged and auditable

### **Security Requirements**
- [ ] All payment data is encrypted in transit and at rest
- [ ] PCI compliance maintained for card transactions
- [ ] Bank credentials never stored on Royaltea servers (Teller certificate-based auth)
- [ ] Two-factor authentication for large transactions
- [ ] Fraud detection and prevention measures active

---

## 📱 Responsive Behavior

**[Design Team: How should payments work on mobile?]**

### **Mobile Adaptations**
- Touch-optimized payment method selection
- Simplified transaction history with swipe actions
- Mobile-friendly Teller bank selection interface
- Quick payment confirmation with biometric authentication
- Responsive financial dashboard with key metrics

---

## ♿ Accessibility Features

**[Design Team: Ensure payments are accessible]**

- **Screen Reader Support**: All financial information clearly announced
- **Keyboard Navigation**: Full keyboard access to payment features
- **High Contrast**: Financial data clearly visible
- **Large Touch Targets**: Easy interaction on mobile devices
- **Clear Language**: Financial terms explained in plain language

---

## 📝 **Design Team Notes**

**[Design Team: Use this section for payment system requirements]**

### **Payment Method Priorities**
1. Bank account linking (ACH) - Primary method
2. Debit card payments - Secondary option
3. Wire transfers - For large amounts
4. Digital wallets - Future consideration

### **Revenue Models to Support**
- Percentage-based revenue sharing
- Fixed commission rates
- Milestone-based payments
- Subscription billing
- One-time project payments

### **Compliance Considerations**
- Tax reporting (1099 generation)
- International payment regulations
- Anti-money laundering (AML) requirements
- Know Your Customer (KYC) verification

### **Future Enhancements**
- Cryptocurrency payment support
- International wire transfers
- Multi-currency support
- Advanced fraud detection
- Payment scheduling and automation

---

**[Design Team: This payment system must be rock-solid reliable and secure. Focus on user trust, transparency, and ease of use. Every financial interaction should feel safe and professional.]**
