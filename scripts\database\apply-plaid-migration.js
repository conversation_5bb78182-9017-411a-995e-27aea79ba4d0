// Apply Plaid Integration Migration
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_KEY);

async function applyPlaidMigration() {
  console.log('🚀 Applying Plaid Integration Migration...');
  
  try {
    // Read the migration file
    const migrationSQL = fs.readFileSync('../../supabase/migrations/20240116000002_plaid_integration.sql', 'utf8');
    
    console.log('📄 Migration file loaded');
    
    // Split into logical chunks and execute
    const chunks = migrationSQL.split(';').filter(chunk => chunk.trim());
    
    console.log(`📋 Executing ${chunks.length} SQL statements...`);
    
    for (let i = 0; i < chunks.length; i++) {
      const statement = chunks[i].trim();
      if (!statement || statement.startsWith('--')) continue;
      
      try {
        console.log(`   ${i + 1}/${chunks.length}: ${statement.substring(0, 50)}...`);
        
        // Execute each statement individually
        const { error } = await supabase.rpc('exec', { sql: statement + ';' });
        
        if (error && !error.message.includes('already exists') && !error.message.includes('does not exist')) {
          console.log(`   ⚠️ Warning:`, error.message);
        } else if (!error) {
          console.log(`   ✅ Success`);
        }
        
      } catch (statementError) {
        console.log(`   ❌ Error:`, statementError.message);
        // Continue with next statement
      }
    }
    
    console.log('\n🎯 Migration application completed!');
    
    // Verify the migration worked
    await verifyPlaidMigration();
    
  } catch (err) {
    console.log('❌ Error applying migration:', err.message);
  }
}

async function verifyPlaidMigration() {
  console.log('\n🔍 Verifying Plaid migration...');
  
  try {
    // Check if plaid_accounts table exists
    const { data: plaidAccounts, error: plaidError } = await supabase
      .from('plaid_accounts')
      .select('*')
      .limit(0);
    
    if (!plaidError) {
      console.log('✅ plaid_accounts table created successfully');
    } else {
      console.log('❌ plaid_accounts table not found:', plaidError.message);
    }
    
    // Check if payment_transactions table exists
    const { data: paymentTrans, error: paymentError } = await supabase
      .from('payment_transactions')
      .select('*')
      .limit(0);
    
    if (!paymentError) {
      console.log('✅ payment_transactions table created successfully');
    } else {
      console.log('❌ payment_transactions table not found:', paymentError.message);
    }
    
    // Check if payment_preferences table exists
    const { data: preferences, error: prefError } = await supabase
      .from('payment_preferences')
      .select('*')
      .limit(0);
    
    if (!prefError) {
      console.log('✅ payment_preferences table created successfully');
    } else {
      console.log('❌ payment_preferences table not found:', prefError.message);
    }
    
    // Check if escrow_accounts table exists
    const { data: escrow, error: escrowError } = await supabase
      .from('escrow_accounts')
      .select('*')
      .limit(0);
    
    if (!escrowError) {
      console.log('✅ escrow_accounts table created successfully');
    } else {
      console.log('❌ escrow_accounts table not found:', escrowError.message);
    }
    
    // Check if payment_routing_rules table exists and has default data
    const { data: rules, error: rulesError } = await supabase
      .from('payment_routing_rules')
      .select('*');
    
    if (!rulesError) {
      console.log(`✅ payment_routing_rules table created with ${rules.length} default rules`);
    } else {
      console.log('❌ payment_routing_rules table not found:', rulesError.message);
    }
    
    console.log('\n🎉 Plaid migration verification completed!');
    
  } catch (verifyError) {
    console.log('❌ Verification failed:', verifyError.message);
  }
}

applyPlaidMigration();
