// <PERSON>ript to apply just the column additions to the users table
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please set SUPABASE_URL and SUPABASE_SERVICE_KEY environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);
console.log('Supabase client initialized');

// Function to check if a column exists
async function columnExists(tableName, columnName) {
  try {
    // Try to select the column
    const query = `SELECT ${columnName} FROM ${tableName} LIMIT 1`;
    const { data, error } = await supabase.rpc('query', { sql_query: query });
    
    // If there's no error, the column exists
    return !error;
  } catch (error) {
    // If there's an error, the column doesn't exist
    return false;
  }
}

// Function to add a column if it doesn't exist
async function addColumnIfNotExists(tableName, columnName, columnType, defaultValue = null) {
  try {
    console.log(`Checking if column ${columnName} exists in ${tableName}...`);
    
    // Check if the column exists
    const exists = await columnExists(tableName, columnName);
    
    if (exists) {
      console.log(`Column ${columnName} already exists in ${tableName}.`);
      return true;
    }
    
    console.log(`Adding column ${columnName} to ${tableName}...`);
    
    // Build the ALTER TABLE statement
    let sql = `ALTER TABLE ${tableName} ADD COLUMN ${columnName} ${columnType}`;
    
    // Add default value if provided
    if (defaultValue !== null) {
      sql += ` DEFAULT ${defaultValue}`;
    }
    
    // Execute the SQL
    const { data, error } = await supabase.rpc('query', { sql_query: sql });
    
    if (error) {
      console.error(`Error adding column ${columnName}:`, error.message);
      return false;
    }
    
    console.log(`Column ${columnName} added to ${tableName}.`);
    return true;
  } catch (error) {
    console.error(`Error adding column ${columnName}:`, error.message);
    return false;
  }
}

// Main function to apply the migration
async function applyMigration() {
  try {
    console.log('Starting Retro Profile migration process...');
    
    // First, check if the query function exists
    const { data: functionData, error: functionError } = await supabase.rpc('query', { sql_query: 'SELECT 1' });
    
    if (functionError) {
      console.error('The query function does not exist. Please create it manually in the Supabase SQL editor.');
      console.log('CREATE OR REPLACE FUNCTION query(sql_query TEXT) RETURNS JSONB AS $$ BEGIN RETURN (SELECT jsonb_agg(row_to_json(t)) FROM (SELECT * FROM dblink(\'dbname=postgres\', sql_query) AS t) t); END; $$ LANGUAGE plpgsql SECURITY DEFINER;');
      process.exit(1);
    }
    
    // Add columns to the users table
    const columns = [
      { name: 'headline', type: 'TEXT', default: null },
      { name: 'location', type: 'TEXT', default: null },
      { name: 'website', type: 'TEXT', default: null },
      { name: 'cover_image_url', type: 'TEXT', default: null },
      { name: 'status_message', type: 'TEXT', default: null },
      { name: 'availability_status', type: 'TEXT', default: null },
      { name: 'profile_views', type: 'INTEGER', default: '0' },
      { name: 'theme_settings', type: 'JSONB', default: '\'{"theme":"default","colors":{"background":"#f8fafc","primary":"#3b82f6","secondary":"#f0f2f5","text":"#1c1e21","accent":"#6c5ce7","links":"#3b82f6","borders":"#e2e8f0"},"fonts":{"heading":"Inter","body":"Inter"},"layout":"standard"}\'::jsonb' },
      { name: 'custom_css', type: 'TEXT', default: null },
      { name: 'profile_song_url', type: 'TEXT', default: null },
      { name: 'privacy_settings', type: 'JSONB', default: '\'{"profile_visibility":"public","section_visibility":{"personal_info":true,"contact_details":false,"skills":true,"projects":true,"contribution_details":true,"contribution_percentages":false,"royalty_info":false,"profile_song":true,"top_collaborators":true,"comments":true,"profile_visitors":false},"verification_display":"level_only"}\'::jsonb' }
    ];
    
    // Add each column
    let successCount = 0;
    let failCount = 0;
    
    for (const column of columns) {
      const success = await addColumnIfNotExists('public.users', column.name, column.type, column.default);
      
      if (success) {
        successCount++;
      } else {
        failCount++;
      }
    }
    
    // Print summary
    console.log('\n=== Migration Summary ===');
    console.log(`Total columns: ${columns.length}`);
    console.log(`Successfully added: ${successCount}`);
    console.log(`Failed: ${failCount}`);
    
    if (failCount === 0) {
      console.log('\nColumn migration completed successfully!');
      console.log('\nPlease follow the MANUAL_MIGRATION_GUIDE.md to complete the rest of the migration.');
    } else {
      console.log('\nColumn migration completed with errors. Please check the logs and try again.');
    }
  } catch (error) {
    console.error('Migration failed:', error);
  }
}

// Run the migration
applyMigration();
