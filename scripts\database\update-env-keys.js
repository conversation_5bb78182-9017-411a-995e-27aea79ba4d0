// <PERSON>ript to update environment variables in .env files
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to update a key in an .env file
function updateEnvKey(filePath, key, value) {
  try {
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      console.error(`File not found: ${filePath}`);
      return false;
    }

    // Read the file content
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const lines = fileContent.split('\n');
    
    // Check if key exists
    const keyExists = lines.some(line => line.startsWith(`${key}=`));
    
    if (keyExists) {
      // Update the key
      const updatedLines = lines.map(line => {
        if (line.startsWith(`${key}=`)) {
          return `${key}=${value}`;
        }
        return line;
      });
      
      // Write the updated content back to the file
      fs.writeFileSync(filePath, updatedLines.join('\n'), 'utf8');
      console.log(`Updated ${key} in ${filePath}`);
    } else {
      // Add the key
      fs.appendFileSync(filePath, `\n${key}=${value}`, 'utf8');
      console.log(`Added ${key} to ${filePath}`);
    }
    
    return true;
  } catch (error) {
    console.error(`Error updating ${key} in ${filePath}:`, error);
    return false;
  }
}

// Main function
function main() {
  console.log('=== Environment Variable Updater ===');
  console.log('This script will update environment variables in .env files');
  
  rl.question('Enter the Supabase service key: ', (serviceKey) => {
    if (!serviceKey) {
      console.error('Error: Service key is required');
      rl.close();
      return;
    }
    
    // Update the service key in client/.env.local
    const clientEnvPath = path.join(__dirname, 'client', '.env.local');
    updateEnvKey(clientEnvPath, 'SUPABASE_SERVICE_KEY', serviceKey);
    
    // Also update it in the root .env file if it exists
    const rootEnvPath = path.join(__dirname, '.env');
    if (fs.existsSync(rootEnvPath)) {
      updateEnvKey(rootEnvPath, 'SUPABASE_SERVICE_KEY', serviceKey);
    }
    
    console.log('\nEnvironment variables updated successfully');
    rl.close();
  });
}

// Run the main function
main();
