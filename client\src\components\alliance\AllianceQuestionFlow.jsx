import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@heroui/react';
import AllianceQuestionStep from './AllianceQuestionStep';

/**
 * AllianceQuestionFlow Component
 * 
 * Manages the 7-question adaptive flow for alliance creation
 * Questions adapt based on project type (business, personal, opensource, creative)
 * Follows the wireframe specifications exactly
 */
const AllianceQuestionFlow = ({ 
  initialData = {}, 
  onComplete, 
  onBack, 
  onCancel 
}) => {
  const [currentQuestion, setCurrentQuestion] = useState(1);
  const [answers, setAnswers] = useState({
    projectType: '',
    teamSize: '',
    companyStatus: '',
    communityType: '',
    creativeType: '',
    startDate: '',
    paymentModel: '',
    teamRoles: '',
    allianceName: '',
    allianceDescription: '',
    allianceIcon: '🏰',
    invitationMethod: '',
    ...initialData
  });

  const totalQuestions = 7;

  // Question definitions with adaptive logic
  const questions = [
    {
      id: 1,
      title: "What kind of project is this?",
      type: "project_type_selection",
      options: [
        {
          value: "business",
          icon: "💼",
          title: "Business Project",
          description: "Building something for a company or startup"
        },
        {
          value: "personal",
          icon: "👤",
          title: "Personal Project",
          description: "Working on your own idea or side project"
        },
        {
          value: "opensource",
          icon: "🌍",
          title: "Open Source",
          description: "Contributing to public, community-driven work"
        },
        {
          value: "creative",
          icon: "🎨",
          title: "Creative Work",
          description: "Art, music, writing, or other creative projects"
        }
      ]
    },
    {
      id: 2,
      title: getQuestion2Title(),
      type: "adaptive_selection",
      options: getQuestion2Options()
    },
    {
      id: 3,
      title: "When do you want to start?",
      type: "start_date_selection",
      options: [
        {
          value: "now",
          icon: "🚀",
          title: "Right now!",
          description: "We're ready to begin immediately"
        },
        {
          value: "specific",
          icon: "📅",
          title: "Specific date",
          description: "We have a planned start date"
        },
        {
          value: "when_ready",
          icon: "🤔",
          title: "When the team is ready",
          description: "Start once we have enough people"
        },
        {
          value: "when_funded",
          icon: "💰",
          title: "When funding is secured",
          description: "Waiting for budget or investment"
        }
      ]
    },
    {
      id: 4,
      title: "How will people get paid?",
      type: "payment_model_selection",
      options: [
        {
          value: "fixed",
          icon: "💰",
          title: "Fixed payments",
          description: "Pay specific amounts for completed tasks"
        },
        {
          value: "revenue_sharing",
          icon: "📊",
          title: "Revenue sharing",
          description: "Split income based on contributions"
        },
        {
          value: "hybrid",
          icon: "🎯",
          title: "Hybrid model",
          description: "Some fixed payments + some revenue sharing"
        },
        {
          value: "volunteer",
          icon: "🤝",
          title: "Volunteer/Equity only",
          description: "No immediate payment, future equity/profit share"
        },
        {
          value: "not_sure",
          icon: "❓",
          title: "Not sure yet",
          description: "We'll figure this out as we go"
        }
      ]
    },
    {
      id: 5,
      title: "What roles will your team have?",
      type: "team_roles_selection",
      options: [
        {
          value: "main_leader",
          icon: "👑",
          title: "I'll be the main leader",
          description: "I make final decisions and manage the project"
        },
        {
          value: "shared",
          icon: "🤝",
          title: "Shared leadership",
          description: "Multiple people will share decision-making"
        },
        {
          value: "skill_based",
          icon: "🎯",
          title: "Skill-based roles",
          description: "People lead their areas of expertise"
        },
        {
          value: "flexible",
          icon: "🌊",
          title: "Flexible roles",
          description: "Roles will evolve based on what's needed"
        }
      ]
    },
    {
      id: 6,
      title: "What should we call your Alliance?",
      type: "alliance_details",
      fields: [
        {
          name: "allianceName",
          label: "Alliance Name",
          type: "text",
          placeholder: "The Dream Team Alliance",
          required: true
        },
        {
          name: "allianceDescription",
          label: "Description (optional)",
          type: "textarea",
          placeholder: "A group of passionate developers building the future of creative collaboration..."
        },
        {
          name: "allianceIcon",
          label: "Choose an icon",
          type: "icon_picker",
          options: ["🏰", "🛡️", "⚔️", "🏆", "🎯", "🚀", "💎", "🌟"]
        }
      ]
    },
    {
      id: 7,
      title: "How do you want to invite people?",
      type: "invitation_method_selection",
      options: [
        {
          value: "email",
          icon: "📧",
          title: "Send email invitations",
          description: "I have their email addresses"
        },
        {
          value: "links",
          icon: "🔗",
          title: "Create invitation links",
          description: "I'll share links with people myself"
        },
        {
          value: "discoverable",
          icon: "🌍",
          title: "Make it discoverable",
          description: "Let people find and request to join"
        },
        {
          value: "later",
          icon: "⏳",
          title: "I'll invite people later",
          description: "Just create the Alliance for now"
        }
      ]
    }
  ];

  // Get adaptive question 2 title based on project type
  function getQuestion2Title() {
    switch (answers.projectType) {
      case 'business': return "Are you an official company?";
      case 'personal': return "How big will your team be?";
      case 'opensource': return "What kind of community?";
      case 'creative': return "What type of creative work?";
      default: return "Tell us more about your project";
    }
  }

  // Get adaptive question 2 options based on project type
  function getQuestion2Options() {
    switch (answers.projectType) {
      case 'business':
        return [
          {
            value: "established",
            icon: "🏢",
            title: "Yes, we're an established company",
            description: "LLC, Corp, or other official business entity"
          },
          {
            value: "startup",
            icon: "🚀",
            title: "Startup in formation",
            description: "Planning to become official, but not yet"
          },
          {
            value: "informal",
            icon: "🤝",
            title: "Informal business partnership",
            description: "Working together but no official structure"
          },
          {
            value: "idea",
            icon: "💡",
            title: "Just an idea for now",
            description: "Exploring if this could become a business"
          }
        ];
      
      case 'personal':
        return [
          {
            value: "solo",
            icon: "👤",
            title: "Just Me",
            description: "Solo project, might invite others later"
          },
          {
            value: "small",
            icon: "👥",
            title: "Small Team (2-5 people)",
            description: "Close friends or collaborators"
          },
          {
            value: "medium",
            icon: "👨‍👩‍👧‍👦",
            title: "Medium Team (6-15 people)",
            description: "Organized group with different skills"
          },
          {
            value: "large",
            icon: "🏢",
            title: "Large Team (15+ people)",
            description: "Big project with many contributors"
          }
        ];
      
      case 'opensource':
        return [
          {
            value: "public",
            icon: "🌍",
            title: "Public community",
            description: "Anyone can contribute and participate"
          },
          {
            value: "private",
            icon: "🔒",
            title: "Private community",
            description: "Invitation-only contributor group"
          },
          {
            value: "invite",
            icon: "📨",
            title: "Invite-based",
            description: "People can request to join"
          }
        ];
      
      case 'creative':
        return [
          {
            value: "collaborative",
            icon: "🤝",
            title: "Collaborative creation",
            description: "Multiple people working together"
          },
          {
            value: "individual",
            icon: "👤",
            title: "Individual with support",
            description: "Main creator with helpers/advisors"
          },
          {
            value: "mixed",
            icon: "🎭",
            title: "Mixed approach",
            description: "Different parts have different collaboration styles"
          }
        ];
      
      default:
        return [];
    }
  }

  // Handle answer selection
  const handleAnswer = (questionId, answer) => {
    setAnswers(prev => {
      const updated = { ...prev };
      
      // Handle different question types
      if (questionId === 1) {
        updated.projectType = answer;
      } else if (questionId === 2) {
        // Map to appropriate field based on project type
        if (prev.projectType === 'business') updated.companyStatus = answer;
        else if (prev.projectType === 'personal') updated.teamSize = answer;
        else if (prev.projectType === 'opensource') updated.communityType = answer;
        else if (prev.projectType === 'creative') updated.creativeType = answer;
      } else if (questionId === 3) {
        updated.startDate = answer;
      } else if (questionId === 4) {
        updated.paymentModel = answer;
      } else if (questionId === 5) {
        updated.teamRoles = answer;
      } else if (questionId === 6) {
        // Handle alliance details (multiple fields)
        Object.assign(updated, answer);
      } else if (questionId === 7) {
        updated.invitationMethod = answer;
      }
      
      return updated;
    });
  };

  // Navigate to next question
  const handleNext = () => {
    if (currentQuestion < totalQuestions) {
      setCurrentQuestion(currentQuestion + 1);
    } else {
      // Complete the flow
      onComplete(answers);
    }
  };

  // Navigate to previous question
  const handlePrevious = () => {
    if (currentQuestion > 1) {
      setCurrentQuestion(currentQuestion - 1);
    } else {
      onBack();
    }
  };

  // Get current question data
  const getCurrentQuestion = () => {
    return questions.find(q => q.id === currentQuestion);
  };

  // Check if current question is answered
  const isAnswered = () => {
    switch (currentQuestion) {
      case 1: return !!answers.projectType;
      case 2: 
        if (answers.projectType === 'business') return !!answers.companyStatus;
        if (answers.projectType === 'personal') return !!answers.teamSize;
        if (answers.projectType === 'opensource') return !!answers.communityType;
        if (answers.projectType === 'creative') return !!answers.creativeType;
        return false;
      case 3: return !!answers.startDate;
      case 4: return !!answers.paymentModel;
      case 5: return !!answers.teamRoles;
      case 6: return !!answers.allianceName;
      case 7: return !!answers.invitationMethod;
      default: return false;
    }
  };

  const currentQuestionData = getCurrentQuestion();

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      {/* Exit button */}
      {onCancel && (
        <div className="absolute top-6 right-6 z-10">
          <Button
            variant="light"
            size="lg"
            onPress={onCancel}
            isIconOnly
            className="text-foreground hover:bg-default-100"
          >
            <i className="bi bi-x-lg text-2xl"></i>
          </Button>
        </div>
      )}

      {/* Back button */}
      <div className="absolute top-6 left-6 z-10">
        <Button
          variant="light"
          size="lg"
          onPress={handlePrevious}
          className="text-foreground hover:bg-default-100"
        >
          <i className="bi bi-arrow-left text-xl mr-2"></i>
          Back
        </Button>
      </div>

      {/* Step indicator */}
      <div className="absolute top-6 right-20 z-10">
        <span className="text-foreground text-lg">
          Step {currentQuestion}/{totalQuestions}
        </span>
      </div>

      {/* Question content */}
      <div className="max-w-4xl mx-auto w-full">
        <AnimatePresence mode="wait">
          <AllianceQuestionStep
            key={currentQuestion}
            question={currentQuestionData}
            answer={answers}
            onAnswer={handleAnswer}
            onNext={handleNext}
            isAnswered={isAnswered()}
          />
        </AnimatePresence>
      </div>

      {/* Progress dots */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
        <div className="flex space-x-2">
          {Array.from({ length: totalQuestions }, (_, i) => (
            <div
              key={i}
              className={`w-3 h-3 rounded-full transition-colors ${
                i + 1 === currentQuestion
                  ? 'bg-primary'
                  : i + 1 < currentQuestion
                  ? 'bg-success'
                  : 'bg-default-300'
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default AllianceQuestionFlow;
