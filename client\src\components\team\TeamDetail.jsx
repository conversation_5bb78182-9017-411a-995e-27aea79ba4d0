import React, { useState, useEffect, useContext } from 'react';
import { useParams, Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { getUserTeamPermissions } from '../../utils/team/teamPermissions.utils';
import LoadingAnimation from '../layout/LoadingAnimation';

/**
 * TeamDetail component displays details about a team
 * including members and projects
 */
const TeamDetail = () => {
  const { teamId } = useParams();
  const { currentUser } = useContext(UserContext);
  const [team, setTeam] = useState(null);
  const [members, setMembers] = useState([]);
  const [projects, setProjects] = useState([]);
  const [userRole, setUserRole] = useState(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const [showInviteForm, setShowInviteForm] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState('member');
  const [permissions, setPermissions] = useState({});

  // Fetch team data on component mount
  useEffect(() => {
    if (teamId && currentUser) {
      fetchTeamData();
    }
  }, [teamId, currentUser]);

  // Fetch team data, members, and projects
  const fetchTeamData = async () => {
    try {
      setLoading(true);

      // Fetch team details
      const { data: teamData, error: teamError } = await supabase
        .from('teams')
        .select('*')
        .eq('id', teamId)
        .single();

      if (teamError) throw teamError;
      setTeam(teamData);

      // Fetch team members with user details
      const { data: membersData, error: membersError } = await supabase
        .from('team_members')
        .select(`
          id,
          role,
          is_admin,
          joined_at,
          user:user_id(id, email, user_metadata)
        `)
        .eq('team_id', teamId);

      if (membersError) throw membersError;

      // Process member data
      const processedMembers = membersData.map(member => ({
        id: member.id,
        userId: member.user.id,
        email: member.user.email,
        displayName: member.user.user_metadata?.full_name || member.user.email,
        role: member.role,
        isAdmin: member.is_admin,
        joinedAt: member.joined_at
      }));

      setMembers(processedMembers);

      // Check current user's role in the team
      const currentMember = processedMembers.find(member => member.userId === currentUser.id);
      if (currentMember) {
        setUserRole(currentMember.role);
        setIsAdmin(currentMember.isAdmin);

        // Fetch user's permissions for this team
        const userPermissions = await getUserTeamPermissions(currentUser.id, teamId);
        setPermissions(userPermissions);
      }

      // Fetch team projects
      const { data: projectsData, error: projectsError } = await supabase
        .from('team_projects')
        .select(`
          id,
          project:project_id(
            id,
            name,
            description,
            project_type,
            created_at
          )
        `)
        .eq('team_id', teamId);

      if (projectsError) throw projectsError;

      // Process project data
      const processedProjects = projectsData.map(item => ({
        id: item.project.id,
        name: item.project.name,
        description: item.project.description,
        projectType: item.project.project_type,
        createdAt: item.project.created_at
      }));

      setProjects(processedProjects);
    } catch (error) {
      console.error('Error fetching team data:', error);
      toast.error('Failed to load team data');
    } finally {
      setLoading(false);
    }
  };

  // Handle inviting a new member
  const handleInviteMember = async (e) => {
    e.preventDefault();

    if (!inviteEmail.trim()) {
      toast.error('Email is required');
      return;
    }

    try {
      setLoading(true);

      // Check if user exists
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('id')
        .eq('email', inviteEmail.toLowerCase())
        .maybeSingle();

      // Create invitation
      const invitation = {
        team_id: teamId,
        invited_by: currentUser.id,
        role: inviteRole,
        status: 'pending'
      };

      if (userData) {
        // User exists, add user_id
        invitation.invited_user_id = userData.id;
      } else {
        // User doesn't exist, use email
        invitation.invited_email = inviteEmail.toLowerCase();
      }

      const { error: inviteError } = await supabase
        .from('team_invitations')
        .insert([invitation]);

      if (inviteError) throw inviteError;

      // Create notification for the user if they exist
      if (userData) {
        const { error: notifError } = await supabase
          .from('notifications')
          .insert([{
            user_id: userData.id,
            type: 'team_invitation',
            title: 'Team Invitation',
            message: `You've been invited to join the team "${team.name}"`,
            action_url: `/teams/invitations`,
            related_id: teamId,
            metadata: {
              team_id: teamId,
              team_name: team.name,
              invited_by: currentUser.id,
              role: inviteRole
            }
          }]);

        if (notifError) console.error('Error creating notification:', notifError);
      }

      toast.success(`Invitation sent to ${inviteEmail}`);
      setInviteEmail('');
      setInviteRole('member');
      setShowInviteForm(false);
    } catch (error) {
      console.error('Error inviting member:', error);
      toast.error('Failed to send invitation');
    } finally {
      setLoading(false);
    }
  };

  if (loading && !team) {
    return <LoadingAnimation />;
  }

  if (!team) {
    return (
      <div className="team-not-found">
        <h2>Team Not Found</h2>
        <p>The team you're looking for doesn't exist or you don't have access to it.</p>
        <Link to="/teams" className="back-button">Back to Teams</Link>
      </div>
    );
  }

  return (
    <div className="team-detail-container">
      <div className="team-detail-header">
        <div className="team-info">
          <h2>{team.name}</h2>
          <p className="team-description">{team.description || 'No description'}</p>
        </div>
        <div className="team-actions">
          {permissions.invite_members && (
            <button
              className="invite-button"
              onClick={() => setShowInviteForm(!showInviteForm)}
            >
              {showInviteForm ? 'Cancel' : 'Invite Member'}
            </button>
          )}

          {isAdmin && (
            <Link to={`/teams/${teamId}/manage`} className="manage-button">
              Manage Team
            </Link>
          )}
        </div>
      </div>

      {showInviteForm && (
        <div className="invite-form">
          <h3>Invite New Member</h3>
          <form onSubmit={handleInviteMember}>
            <div className="form-group">
              <label htmlFor="invite-email">Email</label>
              <input
                id="invite-email"
                type="email"
                value={inviteEmail}
                onChange={(e) => setInviteEmail(e.target.value)}
                placeholder="Enter email address"
                required
              />
            </div>
            <div className="form-group">
              <label htmlFor="invite-role">Role</label>
              <select
                id="invite-role"
                value={inviteRole}
                onChange={(e) => setInviteRole(e.target.value)}
              >
                <option value="member">Member</option>
                <option value="admin">Admin</option>
              </select>
            </div>
            <div className="form-actions">
              <button type="submit" className="submit-button">Send Invitation</button>
              <button
                type="button"
                className="cancel-button"
                onClick={() => setShowInviteForm(false)}
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="team-content">
        <div className="team-section">
          <h3>Team Members ({members.length})</h3>
          <div className="members-list">
            {members.map(member => (
              <div key={member.id} className="member-card">
                <div className="member-info">
                  <div className="member-name">{member.displayName}</div>
                  <div className="member-email">{member.email}</div>
                </div>
                <div className="member-meta">
                  <span className={`role-badge ${member.role}`}>{member.role}</span>
                  {member.isAdmin && <span className="admin-badge">Admin</span>}
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="team-section">
          <h3>Team Projects ({projects.length})</h3>
          {projects.length > 0 ? (
            <div className="projects-list">
              {projects.map(project => (
                <div key={project.id} className="project-card">
                  <div className="project-info">
                    <h4>{project.name}</h4>
                    <p>{project.description || 'No description'}</p>
                  </div>
                  <div className="project-meta">
                    <span className="project-type">{project.projectType}</span>
                    <span className="project-date">Created {new Date(project.createdAt).toLocaleDateString()}</span>
                  </div>
                  <Link to={`/project/${project.id}`} className="view-project-button">
                    View Project
                  </Link>
                </div>
              ))}
            </div>
          ) : (
            <div className="no-projects">
              <p>This team doesn't have any projects yet.</p>
              {permissions.create_projects && (
                <Link to="/project/create" className="create-project-button">
                  Create Project
                </Link>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TeamDetail;
