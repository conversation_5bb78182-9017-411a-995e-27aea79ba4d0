import React, { createContext, useState, useEffect } from 'react';
import { supabase } from '../../utils/supabase/supabase.utils';

export const ThemeContext = createContext();

export const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState('light');
  const [loading, setLoading] = useState(true);

  // Load theme preferences from localStorage and system preferences
  useEffect(() => {
    const loadPreferences = async () => {
      try {
        // Check if user has saved preferences in localStorage
        const savedTheme = localStorage.getItem('theme');

        if (savedTheme) {
          setTheme(savedTheme);
        } else {
          // Check system preference for dark mode
          const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
          const systemTheme = prefersDark ? 'dark' : 'light';
          setTheme(systemTheme);
          localStorage.setItem('theme', systemTheme);
        }
      } catch (error) {
        console.error('Error loading preferences:', error);
        // Default to light theme if there's an error
        setTheme('light');
      } finally {
        setLoading(false);
      }
    };

    loadPreferences();
  }, []);

  // Apply theme to document when theme changes
  useEffect(() => {
    if (!loading) {
      if (theme === 'dark') {
        document.documentElement.classList.add('dark');
        document.documentElement.classList.remove('light');
      } else {
        document.documentElement.classList.add('light');
        document.documentElement.classList.remove('dark');
      }
    }
  }, [theme, loading]);

  // Function to toggle theme
  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    localStorage.setItem('theme', newTheme);

    // Save to database if user is logged in
    savePreferencesToDatabase({ theme: newTheme });
  };

  // Helper function to save preferences to database
  const savePreferencesToDatabase = async (preferencesToUpdate) => {
    try {
      const { data: { session } } = await supabase.auth.getSession();

      if (session?.user?.id) {
        // First get current preferences
        const { data, error } = await supabase
          .from('users')
          .select('preferences')
          .eq('id', session.user.id)
          .single();

        if (error && error.code !== 'PGRST116') {
          console.error('Error fetching user preferences:', error);
          return;
        }

        // Update preferences with new values
        const currentPreferences = data?.preferences || {};
        const updatedPreferences = {
          ...currentPreferences,
          ...preferencesToUpdate
        };

        // Save updated preferences
        await supabase
          .from('users')
          .update({ preferences: updatedPreferences })
          .eq('id', session.user.id);
      }
    } catch (error) {
      console.error('Error saving preferences to database:', error);
    }
  };

  return (
    <ThemeContext.Provider value={{
      theme,
      toggleTheme,
      loading
    }}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeProvider;
