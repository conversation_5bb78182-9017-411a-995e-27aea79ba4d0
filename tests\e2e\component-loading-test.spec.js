// Component Loading Test
// Day 2 - Testing if alliance components are properly loaded

import { test, expect } from '@playwright/test';

const SITE_URL = 'https://royalty.technology';

test.describe('Component Loading Verification', () => {
  test('should check if alliance components are in the bundle', async ({ page }) => {
    // Navigate to the site
    await page.goto(SITE_URL);
    await page.waitForLoadState('networkidle');
    
    // Check network requests for our alliance components
    const requests = [];
    page.on('request', request => {
      if (request.url().includes('AllianceManage') || request.url().includes('alliance')) {
        requests.push(request.url());
      }
    });
    
    // Try to navigate to alliance management
    await page.goto(`${SITE_URL}/alliances`);
    await page.waitForLoadState('networkidle');
    
    console.log('Alliance-related requests:', requests);
    
    // Check if the alliance CSS was loaded
    const allianceCSSLoaded = requests.some(url => url.includes('AllianceManage') && url.includes('.css'));
    console.log('Alliance CSS loaded:', allianceCSSLoaded);
    
    // Check if the alliance JS was loaded
    const allianceJSLoaded = requests.some(url => url.includes('AllianceManage') && url.includes('.js'));
    console.log('Alliance JS loaded:', allianceJSLoaded);
  });

  test('should test team management route specifically', async ({ page }) => {
    // Navigate directly to team management
    await page.goto(`${SITE_URL}/teams/test-id/manage`);
    await page.waitForLoadState('networkidle');
    
    // Check if we get our AllianceManage component
    const pageContent = await page.textContent('body');
    console.log('Team manage page content length:', pageContent.length);
    
    // Look for specific alliance management elements
    const hasManageAlliance = pageContent.includes('Manage Alliance') || pageContent.includes('Alliance Information');
    console.log('Has alliance management content:', hasManageAlliance);
    
    // Check for business entity section
    const hasBusinessEntity = pageContent.includes('Business Entity') || pageContent.includes('Register Business');
    console.log('Has business entity section:', hasBusinessEntity);
    
    // Check for permission section
    const hasPermissions = pageContent.includes('Alliance Permissions') || pageContent.includes('Business Roles');
    console.log('Has permissions section:', hasPermissions);
  });

  test('should check console errors and warnings', async ({ page }) => {
    const logs = [];
    
    page.on('console', msg => {
      logs.push({
        type: msg.type(),
        text: msg.text()
      });
    });
    
    await page.goto(`${SITE_URL}/alliances`);
    await page.waitForLoadState('networkidle');
    
    // Wait for any async operations
    await page.waitForTimeout(3000);
    
    // Filter for errors and warnings
    const errors = logs.filter(log => log.type === 'error');
    const warnings = logs.filter(log => log.type === 'warning');
    
    console.log('Console errors:', errors.length);
    errors.forEach(error => console.log('ERROR:', error.text));
    
    console.log('Console warnings:', warnings.length);
    warnings.forEach(warning => console.log('WARNING:', warning.text));
    
    // Check for specific import/module errors
    const importErrors = errors.filter(error => 
      error.text.includes('import') || 
      error.text.includes('module') || 
      error.text.includes('Alliance') ||
      error.text.includes('Component')
    );
    
    console.log('Import-related errors:', importErrors.length);
    importErrors.forEach(error => console.log('IMPORT ERROR:', error.text));
  });

  test('should verify experimental navigation is working', async ({ page }) => {
    await page.goto(SITE_URL);
    await page.waitForLoadState('networkidle');
    
    // Check if experimental navigation canvas exists
    const canvas = await page.locator('canvas').first();
    const hasCanvas = await canvas.isVisible();
    console.log('Experimental navigation canvas visible:', hasCanvas);
    
    if (hasCanvas) {
      // Get canvas dimensions
      const canvasBox = await canvas.boundingBox();
      console.log('Canvas dimensions:', canvasBox);
      
      // Try clicking on the canvas
      await canvas.click();
      await page.waitForTimeout(1000);
      
      // Try keyboard navigation
      await page.keyboard.press('ArrowDown');
      await page.waitForTimeout(1000);
      
      await page.keyboard.press('ArrowDown');
      await page.waitForTimeout(1000);
      
      // Check if we can see teams card
      const teamsCard = page.locator('text=Teams').first();
      const teamsVisible = await teamsCard.isVisible();
      console.log('Teams card visible after navigation:', teamsVisible);
    } else {
      // Check if we have regular navigation
      const navLinks = await page.locator('nav a, .nav a, [role="navigation"] a').count();
      console.log('Regular navigation links found:', navLinks);
      
      // Look for teams link
      const teamsLink = page.locator('a[href*="teams"], text=Teams').first();
      const hasTeamsLink = await teamsLink.isVisible();
      console.log('Teams link visible:', hasTeamsLink);
      
      if (hasTeamsLink) {
        await teamsLink.click();
        await page.waitForLoadState('networkidle');
        
        const currentUrl = page.url();
        console.log('Navigated to:', currentUrl);
      }
    }
  });

  test('should test direct component access', async ({ page }) => {
    // Try to access the alliance management component directly
    await page.goto(`${SITE_URL}/teams/12345/manage`);
    await page.waitForLoadState('networkidle');
    
    // Check if we get a 404 or if the component loads
    const is404 = page.url().includes('404') || await page.locator('text=404').isVisible();
    console.log('Is 404 page:', is404);
    
    if (!is404) {
      // Check if we have alliance management content
      const bodyText = await page.textContent('body');
      
      // Look for our specific component text
      const componentIndicators = [
        'Alliance Information',
        'Business Entity',
        'Alliance Permissions',
        'Manage Alliance',
        'Register Business'
      ];
      
      for (const indicator of componentIndicators) {
        const found = bodyText.includes(indicator);
        console.log(`Component indicator "${indicator}": ${found}`);
      }
      
      // Check for our CSS classes
      const cssClasses = [
        'alliance-manage-container',
        'alliance-info-section',
        'business-entity-section',
        'alliance-permissions-section'
      ];
      
      for (const className of cssClasses) {
        const hasClass = await page.locator(`.${className}`).count() > 0;
        console.log(`CSS class "${className}": ${hasClass}`);
      }
    }
  });

  test('should check if components are lazy loaded', async ({ page }) => {
    // Monitor network requests
    const jsRequests = [];
    page.on('request', request => {
      if (request.url().includes('.js') && !request.url().includes('main-')) {
        jsRequests.push(request.url());
      }
    });
    
    // Start at home page
    await page.goto(SITE_URL);
    await page.waitForLoadState('networkidle');
    
    const initialRequests = [...jsRequests];
    console.log('Initial JS requests:', initialRequests.length);
    
    // Navigate to teams page
    await page.goto(`${SITE_URL}/teams`);
    await page.waitForLoadState('networkidle');
    
    const teamsRequests = jsRequests.filter(url => !initialRequests.includes(url));
    console.log('New JS requests for teams page:', teamsRequests.length);
    teamsRequests.forEach(url => console.log('TEAMS JS:', url));
    
    // Navigate to alliance management
    await page.goto(`${SITE_URL}/teams/test/manage`);
    await page.waitForLoadState('networkidle');
    
    const manageRequests = jsRequests.filter(url => !initialRequests.includes(url) && !teamsRequests.includes(url));
    console.log('New JS requests for manage page:', manageRequests.length);
    manageRequests.forEach(url => console.log('MANAGE JS:', url));
    
    // Check if AllianceManage component was loaded
    const allianceComponentLoaded = jsRequests.some(url => url.includes('AllianceManage'));
    console.log('AllianceManage component loaded:', allianceComponentLoaded);
  });
});
