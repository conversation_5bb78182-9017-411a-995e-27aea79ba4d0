/**
 * Loading Tracker
 * 
 * This utility helps track and debug loading states across the application.
 * It logs when loading states are set and unset, and by which component.
 */

// Track all loading state changes
const loadingStateChanges = [];

// Track current loading states
const activeLoadingStates = new Map();

// Maximum number of loading state changes to keep in history
const MAX_HISTORY = 100;

/**
 * Log a loading state change
 * @param {string} component - The component name
 * @param {string} property - The loading property name
 * @param {boolean} value - The new loading state value
 * @param {string} stack - The stack trace
 */
export const trackLoadingState = (component, property, value, stack = null) => {
  const timestamp = new Date();
  const id = `${component}:${property}`;
  
  // Get the stack trace if not provided
  if (!stack) {
    try {
      throw new Error('Loading state change');
    } catch (e) {
      stack = e.stack;
    }
  }
  
  // Create a record of the change
  const record = {
    timestamp,
    component,
    property,
    value,
    stack: stack.split('\n').slice(2, 7).join('\n') // Keep only relevant part of stack
  };
  
  // Add to history
  loadingStateChanges.unshift(record);
  
  // Trim history if needed
  if (loadingStateChanges.length > MAX_HISTORY) {
    loadingStateChanges.pop();
  }
  
  // Update active loading states
  if (value) {
    activeLoadingStates.set(id, record);
  } else {
    activeLoadingStates.delete(id);
  }
  
  // Log the change
  console.log(`[LoadingTracker] ${component} ${property} = ${value}`, {
    component,
    property,
    value,
    stack: record.stack
  });
};

/**
 * Get all loading state changes
 * @returns {Array} - The loading state changes
 */
export const getLoadingStateChanges = () => {
  return [...loadingStateChanges];
};

/**
 * Get all active loading states
 * @returns {Array} - The active loading states
 */
export const getActiveLoadingStates = () => {
  return Array.from(activeLoadingStates.values());
};

/**
 * Clear all loading state history
 */
export const clearLoadingStateHistory = () => {
  loadingStateChanges.length = 0;
};

/**
 * Create a proxy to track loading state changes
 * @param {Object} target - The object to proxy
 * @param {string} componentName - The component name
 * @returns {Proxy} - A proxy that tracks loading state changes
 */
export const createLoadingTracker = (target, componentName) => {
  return new Proxy(target, {
    set(obj, prop, value) {
      // Only track properties that look like loading states
      if (typeof prop === 'string' && 
          (prop.includes('loading') || prop.includes('Loading') || prop === 'isLoading')) {
        trackLoadingState(componentName, prop, value);
      }
      
      // Set the property
      obj[prop] = value;
      return true;
    }
  });
};

export default {
  trackLoadingState,
  getLoadingStateChanges,
  getActiveLoadingStates,
  clearLoadingStateHistory,
  createLoadingTracker
};
