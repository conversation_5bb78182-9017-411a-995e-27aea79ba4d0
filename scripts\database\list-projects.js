// Simple script to list all projects in Supabase
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client with the production URL and service role key
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzgyMDU1OSwiZXhwIjoyMDU5Mzk2NTU5fQ.k5xvFBzBxNMCtPd0d8Ko9dAcT5Y3op3ZgFvlAf85LLs';
const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log('Connecting to Supabase...');

    // List all projects
    console.log('\nListing all projects:');
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('*');

    if (projectsError) {
      console.error('Error fetching projects:', projectsError);
      return;
    }

    if (!projects || projects.length === 0) {
      console.log('No projects found in the database.');
    } else {
      console.log(`Found ${projects.length} projects:`);
      projects.forEach((project, index) => {
        console.log(`\n${index + 1}. ${project.name} (ID: ${project.id})`);
        console.log(`   Type: ${project.project_type}`);
        console.log(`   Description: ${project.description || 'No description'}`);
        console.log(`   Created: ${new Date(project.created_at).toLocaleString()}`);
      });

      // For each project, check agreements
      for (const project of projects) {
        console.log(`\n-----------------------------------------`);
        console.log(`Checking agreements for project: ${project.name} (${project.id})`);

        const { data: agreements, error: agreementsError } = await supabase
          .from('contributor_agreements')
          .select('*')
          .eq('project_id', project.id);

        if (agreementsError) {
          console.error('Error fetching agreements:', agreementsError);
          continue;
        }

        if (!agreements || agreements.length === 0) {
          console.log('No agreements found for this project.');
        } else {
          console.log(`Found ${agreements.length} agreements:`);

          for (const agreement of agreements) {
            console.log(`\n  Agreement ID: ${agreement.id}`);
            console.log(`  Version: ${agreement.version || 1}`);
            console.log(`  Status: ${agreement.status || 'Unknown'}`);
            console.log(`  Created: ${new Date(agreement.created_at).toLocaleString()}`);

            // Check for Village references
            if (agreement.agreement_text && agreement.agreement_text.toLowerCase().includes('village')) {
              console.log(`  ⚠️ CONTAINS VILLAGE REFERENCE`);

              // Find the context of the reference
              const lines = agreement.agreement_text.split('\n');
              let foundCount = 0;

              for (let i = 0; i < lines.length; i++) {
                if (lines[i].toLowerCase().includes('village')) {
                  foundCount++;
                  const start = Math.max(0, i - 1);
                  const end = Math.min(lines.length - 1, i + 1);

                  console.log(`\n  Context (line ${i + 1}):`);
                  for (let j = start; j <= end; j++) {
                    console.log(`    ${j === i ? '→' : ' '} ${lines[j]}`);
                  }

                  // Only show the first few occurrences
                  if (foundCount >= 3) {
                    const remainingCount = lines.filter(line =>
                      line.toLowerCase().includes('village')).length - foundCount;
                    if (remainingCount > 0) {
                      console.log(`\n  ... and ${remainingCount} more occurrences`);
                    }
                    break;
                  }
                }
              }
            } else {
              console.log(`  ✓ No Village references found`);
            }
          }
        }

        // Check milestones
        console.log(`\nChecking milestones for project: ${project.name} (${project.id})`);

        const { data: milestones, error: milestonesError } = await supabase
          .from('milestones')
          .select('*')
          .eq('project_id', project.id);

        if (milestonesError) {
          console.error('Error fetching milestones:', milestonesError);
          continue;
        }

        if (!milestones || milestones.length === 0) {
          console.log('No milestones found for this project.');
        } else {
          console.log(`Found ${milestones.length} milestones:`);

          for (const milestone of milestones) {
            console.log(`\n  Milestone: ${milestone.name}`);
            console.log(`  Description: ${milestone.description || 'No description'}`);

            // Check for Village references
            if ((milestone.name && milestone.name.toLowerCase().includes('village')) ||
                (milestone.description && milestone.description.toLowerCase().includes('village'))) {
              console.log(`  ⚠️ CONTAINS VILLAGE REFERENCE`);
            } else {
              console.log(`  ✓ No Village references found`);
            }
          }
        }
      }
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

main();
