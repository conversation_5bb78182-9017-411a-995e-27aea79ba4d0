import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@heroui/react';

/**
 * AllianceWelcomeScreen Component
 * 
 * Welcome screen for alliance creation following wireframe specifications
 * Sets expectations and introduces the 7-question flow
 */
const AllianceWelcomeScreen = ({ 
  onStart, 
  onCancel 
}) => {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  return (
    <motion.div
      className="min-h-screen flex items-center justify-center p-4"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Exit button */}
      {onCancel && (
        <motion.div
          className="absolute top-6 right-6 z-10"
          variants={itemVariants}
        >
          <Button
            variant="light"
            size="lg"
            onPress={onCancel}
            isIconOnly
            className="text-foreground hover:bg-default-100"
          >
            <i className="bi bi-x-lg text-2xl"></i>
          </Button>
        </motion.div>
      )}

      <div className="max-w-2xl mx-auto text-center">
        {/* Main heading */}
        <motion.div variants={itemVariants} className="mb-12">
          <h1 className="text-6xl font-bold text-foreground mb-4">
            🏰 Create Your Alliance
          </h1>
          <p className="text-xl text-default-600 leading-relaxed">
            Let's set up your team in just a few simple steps. We'll ask you some 
            easy questions to make sure everything is configured perfectly for your project.
          </p>
        </motion.div>

        {/* Process overview */}
        <motion.div variants={itemVariants} className="mb-12">
          <div className="bg-content1 rounded-lg p-8 border border-default-200">
            <h2 className="text-2xl font-semibold text-foreground mb-6">
              What to Expect
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-foreground">
              <div className="text-center">
                <div className="text-4xl mb-3">❓</div>
                <h3 className="font-semibold mb-2">7 Simple Questions</h3>
                <p className="text-sm text-default-600">
                  One question at a time, no cognitive overload
                </p>
              </div>
              
              <div className="text-center">
                <div className="text-4xl mb-3">🧠</div>
                <h3 className="font-semibold mb-2">Smart Configuration</h3>
                <p className="text-sm text-default-600">
                  Questions adapt based on your project type
                </p>
              </div>
              
              <div className="text-center">
                <div className="text-4xl mb-3">⚡</div>
                <h3 className="font-semibold mb-2">Quick Setup</h3>
                <p className="text-sm text-default-600">
                  Ready to invite members in under 5 minutes
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Question preview */}
        <motion.div variants={itemVariants} className="mb-12">
          <div className="bg-primary-50 rounded-lg p-6 border border-primary-200">
            <h3 className="text-lg font-semibold text-primary mb-4">
              Questions We'll Ask:
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left text-sm">
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <span className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs font-bold">1</span>
                  <span className="text-foreground">What kind of project is this?</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs font-bold">2</span>
                  <span className="text-foreground">How big will your team be?</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs font-bold">3</span>
                  <span className="text-foreground">When do you want to start?</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs font-bold">4</span>
                  <span className="text-foreground">How will people get paid?</span>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <span className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs font-bold">5</span>
                  <span className="text-foreground">What roles will your team have?</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs font-bold">6</span>
                  <span className="text-foreground">What should we call your Alliance?</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs font-bold">7</span>
                  <span className="text-foreground">How do you want to invite people?</span>
                </div>
                <div className="flex items-center space-x-2 opacity-50">
                  <span className="w-6 h-6 bg-success text-white rounded-full flex items-center justify-center text-xs font-bold">✓</span>
                  <span className="text-foreground">Ready to launch!</span>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Time estimate */}
        <motion.div variants={itemVariants} className="mb-8">
          <div className="flex items-center justify-center space-x-2 text-default-600">
            <i className="bi bi-clock text-lg"></i>
            <span className="text-lg">
              ● ● ● ● ● ● ●  (7 simple questions - about 3 minutes)
            </span>
          </div>
        </motion.div>

        {/* Start button */}
        <motion.div variants={itemVariants}>
          <Button
            size="lg"
            className="bg-primary text-white font-semibold px-12 py-4 text-lg hover:bg-primary-600 transition-colors"
            onPress={onStart}
          >
            Let's Go!
          </Button>
        </motion.div>

        {/* Benefits reminder */}
        <motion.div variants={itemVariants} className="mt-8">
          <div className="text-center space-y-2">
            <p className="text-default-500 text-sm">
              💡 Your Alliance will be ready to:
            </p>
            <div className="flex justify-center space-x-6 text-xs text-default-600">
              <span>👥 Invite members</span>
              <span>🚀 Create ventures</span>
              <span>💰 Track contributions</span>
              <span>📊 Share revenue</span>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default AllianceWelcomeScreen;
