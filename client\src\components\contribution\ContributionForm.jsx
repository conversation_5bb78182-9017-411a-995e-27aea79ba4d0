import React, { useState, useEffect, useContext } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { toast } from 'react-hot-toast';
import DatePicker from 'react-datepicker';

const ContributionForm = ({ projectId, onSuccess, initialData = null, isEditing = false }) => {
  const { currentUser } = useContext(UserContext);
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [project, setProject] = useState(null);
  const [milestones, setMilestones] = useState([]);

  const [formData, setFormData] = useState({
    project_id: projectId || '',
    task_name: '',
    task_type: '',
    category: '',
    difficulty: 1,
    hours_spent: 1,
    description: '',
    milestone_id: '',
    date_performed: new Date(),
    status: 'pending'
  });

  // Load project details and milestones
  useEffect(() => {
    const fetchProjectDetails = async () => {
      if (!projectId) return;

      try {
        // Try to fetch project with contribution_tracking_config
        let projectData;
        let configData;

        try {
          // First try to fetch project with contribution_tracking_config
          const { data, error } = await supabase
            .from('projects')
            .select('*, contribution_tracking_config(*)')
            .eq('id', projectId)
            .single();

          if (error) throw error;
          projectData = data;
          configData = data.contribution_tracking_config;
        } catch (e) {
          console.log('Error fetching project with config:', e);

          // Fallback: fetch just the project
          const { data, error } = await supabase
            .from('projects')
            .select('*')
            .eq('id', projectId)
            .single();

          if (error) throw error;
          projectData = data;

          // Fetch config separately
          try {
            const { data: configResult, error: configError } = await supabase
              .from('contribution_tracking_config')
              .select('*')
              .eq('project_id', projectId)
              .single();

            if (!configError) {
              configData = configResult;
            }
          } catch (configErr) {
            console.log('Error fetching config separately:', configErr);
          }
        }

        // Set project data with config
        if (configData) {
          projectData.contribution_tracking_config = configData;
        }

        setProject(projectData);

        // Fetch project milestones
        const { data: milestonesData, error: milestonesError } = await supabase
          .from('milestones')
          .select('*')
          .eq('project_id', projectId)
          .order('created_at', { ascending: true });

        if (milestonesError) throw milestonesError;
        setMilestones(milestonesData);

      } catch (error) {
        console.error('Error fetching project details:', error);
        toast.error('Failed to load project details');
      }
    };

    fetchProjectDetails();
  }, [projectId]);

  // Load existing contribution data if editing
  useEffect(() => {
    if (isEditing && initialData) {
      setFormData({
        ...initialData,
        date_performed: new Date(initialData.date_performed)
      });
    }
  }, [isEditing, initialData]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleDateChange = (date) => {
    setFormData(prev => ({
      ...prev,
      date_performed: date
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!currentUser) {
      toast.error('You must be logged in to submit contributions');
      return;
    }

    setLoading(true);

    try {
      const contributionData = {
        ...formData,
        user_id: currentUser.id,
        project_id: projectId,
        difficulty: parseInt(formData.difficulty),
        hours_spent: parseFloat(formData.hours_spent)
      };

      let result;

      if (isEditing) {
        // Update existing contribution
        const { data, error } = await supabase
          .from('contributions')
          .update(contributionData)
          .eq('id', initialData.id)
          .select()
          .single();

        if (error) throw error;
        result = data;
        toast.success('Contribution updated successfully');
      } else {
        // Create new contribution
        const { data, error } = await supabase
          .from('contributions')
          .insert([contributionData])
          .select()
          .single();

        if (error) throw error;
        result = data;
        toast.success('Contribution added successfully');
      }

      // Reset form if not editing
      if (!isEditing) {
        setFormData({
          ...formData,
          task_name: '',
          description: '',
          hours_spent: 1,
          date_performed: new Date()
        });
      }

      // Call success callback
      if (onSuccess) {
        onSuccess(result);
      }

    } catch (error) {
      console.error('Error submitting contribution:', error);
      toast.error(error.message || 'Failed to submit contribution');
    } finally {
      setLoading(false);
    }
  };

  // Get task types and categories from project configuration
  const getTaskTypes = () => {
    if (!project) return [];

    // Check if contribution_tracking_config exists and has task_types
    if (project.contribution_tracking_config) {
      // Handle both array of objects and array of strings
      const taskTypes = project.contribution_tracking_config.task_types || [];

      // If it's already an array of objects with name property, return it
      if (taskTypes.length > 0 && typeof taskTypes[0] === 'object' && taskTypes[0].name) {
        return taskTypes;
      }

      // If it's an array of strings, convert to objects
      if (taskTypes.length > 0 && typeof taskTypes[0] === 'string') {
        return taskTypes.map(type => ({ name: type }));
      }
    }

    // Default task types if none found
    return [
      { name: 'Development' },
      { name: 'Design' },
      { name: 'Documentation' },
      { name: 'Testing' },
      { name: 'Research' },
      { name: 'Management' },
      { name: 'Other' }
    ];
  };

  const getCategories = () => {
    if (!project) return [];

    // Check if contribution_tracking_config exists and has categories
    if (project.contribution_tracking_config) {
      // Handle both array of objects and array of strings
      const categories = project.contribution_tracking_config.categories || [];

      // If it's an array of objects with name property, extract names
      if (categories.length > 0 && typeof categories[0] === 'object' && categories[0].name) {
        return categories.map(cat => cat.name);
      }

      // If it's already an array of strings, return it
      if (categories.length > 0 && typeof categories[0] === 'string') {
        return categories;
      }
    }

    // Default categories if none found
    return [
      'Frontend',
      'Backend',
      'Database',
      'UI/UX',
      'DevOps',
      'QA',
      'Content',
      'Other'
    ];
  };

  const getDifficultyScale = () => {
    if (!project) return [1, 2, 3, 5, 8];

    // Check if contribution_tracking_config exists and has difficulty_scale
    if (project.contribution_tracking_config) {
      const difficultyScale = project.contribution_tracking_config.difficulty_scale ||
                             project.contribution_tracking_config.difficulty_levels || [];

      // If it's an array of numbers, return it
      if (difficultyScale.length > 0 && typeof difficultyScale[0] === 'number') {
        return difficultyScale;
      }

      // If it's an array of objects with multiplier property, extract multipliers
      if (difficultyScale.length > 0 && typeof difficultyScale[0] === 'object' && difficultyScale[0].multiplier) {
        return difficultyScale.map(level => level.multiplier);
      }
    }

    // Default difficulty scale
    return [1, 2, 3, 5, 8];
  };

  return (
    <div className="contribution-form-container">
      <form onSubmit={handleSubmit} className="contribution-form">
        <div className="form-group">
          <label htmlFor="task_name">Task Name*</label>
          <input
            type="text"
            id="task_name"
            name="task_name"
            value={formData.task_name}
            onChange={handleInputChange}
            className="form-control"
            required
          />
        </div>

        <div className="form-row">
          <div className="form-group">
            <label htmlFor="category">Category*</label>
            <select
              id="category"
              name="category"
              value={formData.category}
              onChange={handleInputChange}
              className="form-control"
              required
            >
              <option value="">Select Category</option>
              {getCategories().map((category, index) => (
                <option key={index} value={category}>{category}</option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="task_type">Task Type*</label>
            <select
              id="task_type"
              name="task_type"
              value={formData.task_type}
              onChange={handleInputChange}
              className="form-control"
              required
            >
              <option value="">Select Task Type</option>
              {getTaskTypes().map((type, index) => (
                <option key={index} value={type.name}>{type.name}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="form-row">
          <div className="form-group">
            <label htmlFor="difficulty">Difficulty*</label>
            <select
              id="difficulty"
              name="difficulty"
              value={formData.difficulty}
              onChange={handleInputChange}
              className="form-control"
              required
            >
              {getDifficultyScale().map((level) => (
                <option key={level} value={level}>{level}</option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="hours_spent">Hours Spent*</label>
            <input
              type="number"
              id="hours_spent"
              name="hours_spent"
              value={formData.hours_spent}
              onChange={handleInputChange}
              className="form-control"
              min="0.1"
              step="0.1"
              required
            />
          </div>
        </div>

        <div className="form-group">
          <label htmlFor="milestone_id">Related Milestone</label>
          <select
            id="milestone_id"
            name="milestone_id"
            value={formData.milestone_id || ''}
            onChange={handleInputChange}
            className="form-control"
          >
            <option value="">None</option>
            {milestones.map((milestone) => (
              <option key={milestone.id} value={milestone.id}>{milestone.name}</option>
            ))}
          </select>
        </div>

        <div className="form-group">
          <label htmlFor="date_performed">Date Performed*</label>
          <DatePicker
            id="date_performed"
            selected={formData.date_performed}
            onChange={handleDateChange}
            className="form-control"
            dateFormat="MMMM d, yyyy"
            maxDate={new Date()}
            required
          />
        </div>

        <div className="form-group">
          <label htmlFor="description">Description</label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            className="form-control"
            rows="3"
          />
        </div>

        <div className="form-actions">
          <button
            type="button"
            className="btn btn-secondary"
            onClick={() => onSuccess ? onSuccess() : navigate(-1)}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn btn-primary"
            disabled={loading}
          >
            {loading ? 'Submitting...' : isEditing ? 'Update Contribution' : 'Add Contribution'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ContributionForm;
