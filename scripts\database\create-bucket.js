const { createClient } = require('@supabase/supabase-js');

// Create a Supabase client
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE2OTk0NzI1NzYsImV4cCI6MjAxNTA0ODU3Nn0.Wd_oYpYUQdE_RoLQdOgwcnQE4MqUCxgMjU6QKW_RQ0c';
const supabase = createClient(supabaseUrl, supabaseKey);

async function createBucket() {
  try {
    // Create the bucket
    const { data, error } = await supabase.storage.createBucket('contribution-attachments', {
      public: false,
      fileSizeLimit: 10485760, // 10MB
    });
    
    if (error) {
      console.error('Error creating bucket:', error);
      return;
    }
    
    console.log('Bucket created successfully:', data);
    
    // Set bucket policy to allow authenticated users to upload
    const { error: policyError } = await supabase.storage.from('contribution-attachments').createPolicy('authenticated-upload', {
      name: 'authenticated-upload',
      definition: {
        role: 'authenticated',
        operation: 'INSERT',
        match: {
          bucket_id: 'contribution-attachments'
        }
      }
    });
    
    if (policyError) {
      console.error('Error setting bucket policy:', policyError);
      return;
    }
    
    console.log('Bucket policy set successfully');
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

createBucket();
