// Plaid Payment Processing API
// Backend Specialist: Payment transfers and transaction management
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Payment method routing logic
const PAYMENT_ROUTING = {
  immediate: {
    preferred: 'rtp',
    fallback: ['ach_same_day', 'ach_standard']
  },
  same_day: {
    preferred: 'ach_same_day',
    fallback: ['ach_standard']
  },
  standard: {
    preferred: 'ach_standard',
    fallback: []
  },
  large_amount: {
    preferred: 'wire_domestic',
    fallback: ['ach_same_day', 'ach_standard']
  }
};

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Determine optimal payment method
const determinePaymentMethod = (amount, urgency, direction) => {
  // Large amounts (>$1M) should use wire transfers
  if (amount > 1000000) {
    return PAYMENT_ROUTING.large_amount;
  }
  
  // Route based on urgency
  switch (urgency) {
    case 'immediate':
      return PAYMENT_ROUTING.immediate;
    case 'same_day':
      return PAYMENT_ROUTING.same_day;
    case 'standard':
    default:
      return PAYMENT_ROUTING.standard;
  }
};

// Initiate Payment Transfer
const initiateTransfer = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const data = JSON.parse(event.body);
    
    // Validate required fields
    if (!data.amount || !data.recipient_id || !data.payment_method) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          error: 'amount, recipient_id, and payment_method are required' 
        })
      };
    }

    const amount = parseFloat(data.amount);
    if (amount <= 0) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Amount must be greater than 0' })
      };
    }

    // Determine optimal payment method if not specified
    const paymentRouting = determinePaymentMethod(
      amount, 
      data.urgency || 'standard',
      data.direction || 'outbound'
    );

    const selectedMethod = data.payment_method || paymentRouting.preferred;

    // Calculate fees based on payment method
    const fees = calculatePaymentFees(amount, selectedMethod);

    // Create payment transaction record
    const transactionData = {
      company_id: data.company_id || null,
      transaction_type: 'payment_transfer',
      transaction_category: data.category || 'business_payment',
      gross_amount: amount,
      net_amount: amount - fees.total,
      currency: data.currency || 'USD',
      payee_user_id: data.recipient_id,
      payer_user_id: userId,
      description: data.description || `Payment transfer via ${selectedMethod}`,
      reference_number: data.reference_id || `PAY-${Date.now()}`,
      payment_method: selectedMethod,
      created_by: userId
    };

    const { data: transaction, error: transactionError } = await supabase
      .from('financial_transactions')
      .insert([transactionData])
      .select()
      .single();

    if (transactionError) {
      throw new Error(`Failed to create transaction: ${transactionError.message}`);
    }

    // For now, simulate payment processing
    // TODO: Replace with actual Plaid Transfer API call
    const mockTransferId = `transfer-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    console.log('Payment Transfer Request:', {
      transfer_id: mockTransferId,
      amount: amount,
      method: selectedMethod,
      fees: fees,
      transaction_id: transaction.id
    });

    // Update transaction with transfer ID
    const { error: updateError } = await supabase
      .from('financial_transactions')
      .update({ 
        reference_number: mockTransferId,
        status: 'processing'
      })
      .eq('id', transaction.id);

    if (updateError) {
      console.warn('Failed to update transaction with transfer ID:', updateError.message);
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        transfer_id: mockTransferId,
        transaction_id: transaction.id,
        amount: amount,
        net_amount: amount - fees.total,
        payment_method: selectedMethod,
        fees: fees,
        status: 'processing',
        estimated_settlement: getEstimatedSettlement(selectedMethod),
        created_at: transaction.created_at
      })
    };

  } catch (error) {
    console.error('Initiate transfer error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to initiate transfer' })
    };
  }
};

// Calculate payment fees based on method
const calculatePaymentFees = (amount, paymentMethod) => {
  const feeStructure = {
    ach_standard: { fixed: 0.25, percentage: 0 },
    ach_same_day: { fixed: 1.50, percentage: 0 },
    rtp: { fixed: 0.50, percentage: 0.001 }, // 0.1%
    wire_domestic: { fixed: 15.00, percentage: 0 },
    wire_international: { fixed: 45.00, percentage: 0.002 } // 0.2%
  };

  const fees = feeStructure[paymentMethod] || feeStructure.ach_standard;
  const fixedFee = fees.fixed;
  const percentageFee = amount * fees.percentage;
  const total = fixedFee + percentageFee;

  return {
    fixed: fixedFee,
    percentage: percentageFee,
    total: total,
    method: paymentMethod
  };
};

// Get estimated settlement time
const getEstimatedSettlement = (paymentMethod) => {
  const now = new Date();
  const settlementTimes = {
    rtp: 0, // Immediate
    ach_same_day: 1, // Same day
    ach_standard: 3, // 3 business days
    wire_domestic: 1, // Same day
    wire_international: 5 // 5 business days
  };

  const daysToAdd = settlementTimes[paymentMethod] || 3;
  const settlementDate = new Date(now.getTime() + (daysToAdd * 24 * 60 * 60 * 1000));
  
  return settlementDate.toISOString().split('T')[0]; // Return date only
};

// Get Transfer Status
const getTransferStatus = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const transferId = event.path.split('/').pop();
    
    // Look up transaction by reference number (transfer ID)
    const { data: transaction, error: transactionError } = await supabase
      .from('financial_transactions')
      .select('*')
      .eq('reference_number', transferId)
      .eq('created_by', userId)
      .single();

    if (transactionError) {
      return {
        statusCode: 404,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Transfer not found' })
      };
    }

    // For mock purposes, simulate status progression
    const mockStatus = simulateTransferStatus(transaction.created_at);

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        transfer_id: transferId,
        transaction_id: transaction.id,
        status: mockStatus,
        amount: transaction.gross_amount,
        payment_method: transaction.payment_method,
        created_at: transaction.created_at,
        estimated_settlement: getEstimatedSettlement(transaction.payment_method),
        description: transaction.description
      })
    };

  } catch (error) {
    console.error('Get transfer status error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to get transfer status' })
    };
  }
};

// Simulate transfer status progression
const simulateTransferStatus = (createdAt) => {
  const now = new Date();
  const created = new Date(createdAt);
  const minutesElapsed = (now - created) / (1000 * 60);

  if (minutesElapsed < 5) return 'processing';
  if (minutesElapsed < 30) return 'sent';
  if (minutesElapsed < 60) return 'in_transit';
  return 'completed';
};

// List User Transfers
const listTransfers = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const limit = parseInt(queryParams.get('limit')) || 50;
    const offset = parseInt(queryParams.get('offset')) || 0;
    const status = queryParams.get('status');

    let query = supabase
      .from('financial_transactions')
      .select('*')
      .or(`created_by.eq.${userId},payee_user_id.eq.${userId}`)
      .eq('transaction_type', 'payment_transfer')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (status) {
      query = query.eq('status', status);
    }

    const { data: transactions, error: transactionError } = await query;

    if (transactionError) {
      throw new Error(`Failed to fetch transfers: ${transactionError.message}`);
    }

    const transfers = transactions.map(transaction => ({
      transfer_id: transaction.reference_number,
      transaction_id: transaction.id,
      amount: transaction.gross_amount,
      net_amount: transaction.net_amount,
      payment_method: transaction.payment_method,
      status: simulateTransferStatus(transaction.created_at),
      direction: transaction.created_by === userId ? 'outbound' : 'inbound',
      description: transaction.description,
      created_at: transaction.created_at,
      estimated_settlement: getEstimatedSettlement(transaction.payment_method)
    }));

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        transfers,
        pagination: {
          limit,
          offset,
          total: transfers.length
        }
      })
    };

  } catch (error) {
    console.error('List transfers error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to list transfers' })
    };
  }
};

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/plaid-payments', '');

  try {
    let response;

    if (event.httpMethod === 'POST') {
      if (path === '/transfer' || path === '') {
        response = await initiateTransfer(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'GET') {
      if (path === '/transfers' || path === '/') {
        response = await listTransfers(event);
      } else if (path.startsWith('/transfer/')) {
        response = await getTransferStatus(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('Plaid Payments API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
