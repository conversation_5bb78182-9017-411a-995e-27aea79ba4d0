import React, { useState, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { Button } from '@heroui/react';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

// Import question flow components
import AllianceWelcomeScreen from './AllianceWelcomeScreen';
import AllianceQuestionFlow from './AllianceQuestionFlow';
import AllianceReviewScreen from './AllianceReviewScreen';

/**
 * ImmersiveAllianceWizard Component
 * 
 * Enhanced alliance creation wizard that follows the 7-question immersive flow
 * One question at a time with adaptive logic based on project type
 * Integrates with existing teams/contributors system
 */
const ImmersiveAllianceWizard = ({ 
  mode = 'create', // 'create' or 'edit'
  allianceId = null,
  onComplete,
  onCancel 
}) => {
  const { currentUser } = useContext(UserContext);
  const navigate = useNavigate();
  
  // Wizard state management
  const [currentPhase, setCurrentPhase] = useState('welcome'); // 'welcome', 'questions', 'review'
  const [isLoading, setIsLoading] = useState(false);
  
  // Question flow data (7 questions)
  const [questionData, setQuestionData] = useState({
    projectType: '', // Q1: business, personal, opensource, creative
    teamSize: '', // Q2A: solo, small, medium, large (personal path)
    companyStatus: '', // Q2B: established, startup, informal, idea (business path)
    communityType: '', // Q2C: public, private, invite (opensource path)
    creativeType: '', // Q2D: collaborative, individual, mixed (creative path)
    startDate: '', // Q3: now, specific, when_ready, when_funded
    paymentModel: '', // Q4: fixed, revenue_sharing, hybrid, volunteer, not_sure
    teamRoles: '', // Q5: main_leader, shared, skill_based, flexible
    allianceName: '', // Q6: name
    allianceDescription: '', // Q6: description
    allianceIcon: '🏰', // Q6: icon
    invitationMethod: '' // Q7: email, links, discoverable, later
  });
  
  // Alliance data structure (maps to teams table)
  const [allianceData, setAllianceData] = useState({
    // Basic alliance info
    name: '',
    description: '',
    alliance_type: 'emerging', // solo, emerging, established
    created_by: currentUser?.id,
    
    // Configuration based on questions
    is_business_entity: false,
    is_public: true,
    max_members: 10,
    
    // Team preferences
    invite_members_enabled: true,
    shared_workspace_enabled: true,
    auto_venture_creation: false,
    
    // Payment and role configuration
    default_payment_model: 'revenue_sharing',
    leadership_structure: 'single_leader',
    
    // Invitation settings
    invitation_method: 'email',
    is_discoverable: false
  });

  // Load existing alliance data if editing
  React.useEffect(() => {
    if (mode === 'edit' && allianceId) {
      loadExistingAlliance();
    }
  }, [mode, allianceId]);

  const loadExistingAlliance = async () => {
    setIsLoading(true);
    try {
      const { data: alliance, error } = await supabase
        .from('teams')
        .select('*')
        .eq('id', allianceId)
        .single();

      if (error) throw error;

      if (alliance) {
        setAllianceData(alliance);
        // Skip question flow for existing alliances
        setCurrentPhase('review');
      }
    } catch (error) {
      console.error('Error loading alliance:', error);
      toast.error('Failed to load alliance data');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle question flow completion
  const handleQuestionsComplete = (answers) => {
    setQuestionData(answers);
    
    // Map question answers to alliance data
    const mappedAllianceData = mapQuestionsToAllianceData(answers);
    setAllianceData(prev => ({ ...prev, ...mappedAllianceData }));
    
    // Transition to review
    setCurrentPhase('review');
  };

  // Map question answers to alliance data structure
  const mapQuestionsToAllianceData = (answers) => {
    const mapped = {
      name: answers.allianceName,
      description: answers.allianceDescription,
      alliance_type: mapProjectTypeToAllianceType(answers.projectType, answers.teamSize),
      is_business_entity: answers.projectType === 'business' && 
                         (answers.companyStatus === 'established' || answers.companyStatus === 'startup'),
      is_public: mapToPublicSetting(answers.projectType, answers.communityType, answers.invitationMethod),
      max_members: mapTeamSizeToMaxMembers(answers.teamSize),
      default_payment_model: answers.paymentModel,
      leadership_structure: answers.teamRoles,
      invitation_method: answers.invitationMethod,
      is_discoverable: answers.invitationMethod === 'discoverable'
    };

    return mapped;
  };

  // Helper functions for mapping
  const mapProjectTypeToAllianceType = (projectType, teamSize) => {
    if (projectType === 'business') return 'established';
    if (teamSize === 'solo') return 'solo';
    return 'emerging';
  };

  const mapToPublicSetting = (projectType, communityType, invitationMethod) => {
    if (projectType === 'opensource') return communityType === 'public';
    return invitationMethod === 'discoverable';
  };

  const mapTeamSizeToMaxMembers = (teamSize) => {
    const mapping = {
      'solo': 1,
      'small': 5,
      'medium': 15,
      'large': 50
    };
    return mapping[teamSize] || 10;
  };

  // Handle alliance creation
  const handleAllianceCreate = async () => {
    setIsLoading(true);
    try {
      // Create the alliance in teams table
      const { data: alliance, error: allianceError } = await supabase
        .from('teams')
        .insert([{
          ...allianceData,
          created_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (allianceError) throw allianceError;

      // Add creator as founder/admin
      const { error: memberError } = await supabase
        .from('team_members')
        .insert([{
          team_id: alliance.id,
          user_id: currentUser.id,
          role: allianceData.leadership_structure === 'main_leader' ? 'founder' : 'admin',
          status: 'active',
          is_admin: true,
          joined_at: new Date().toISOString()
        }]);

      if (memberError) throw memberError;

      // Create team preferences if needed
      if (allianceData.invite_members_enabled || allianceData.shared_workspace_enabled) {
        const { error: prefsError } = await supabase
          .from('team_preferences')
          .insert([{
            team_id: alliance.id,
            invite_members_enabled: allianceData.invite_members_enabled,
            shared_workspace_enabled: allianceData.shared_workspace_enabled,
            auto_venture_creation: allianceData.auto_venture_creation,
            created_at: new Date().toISOString()
          }]);

        if (prefsError) console.warn('Could not set alliance preferences:', prefsError);
      }

      toast.success('Alliance created successfully!');
      
      if (onComplete) {
        onComplete({
          type: 'alliance_created',
          alliance,
          action: getNextAction(),
          redirectTo: `/alliance/${alliance.id}`
        });
      } else {
        navigate(`/alliance/${alliance.id}`);
      }
    } catch (error) {
      console.error('Error creating alliance:', error);
      toast.error('Failed to create alliance. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Determine next action based on invitation method
  const getNextAction = () => {
    switch (questionData.invitationMethod) {
      case 'email': return 'invite_members';
      case 'links': return 'create_invite_links';
      case 'discoverable': return 'setup_discovery';
      default: return 'create_venture';
    }
  };

  // Render current phase
  const renderCurrentPhase = () => {
    switch (currentPhase) {
      case 'welcome':
        return (
          <AllianceWelcomeScreen
            onStart={() => setCurrentPhase('questions')}
            onCancel={onCancel}
          />
        );
      
      case 'questions':
        return (
          <AllianceQuestionFlow
            initialData={questionData}
            onComplete={handleQuestionsComplete}
            onBack={() => setCurrentPhase('welcome')}
            onCancel={onCancel}
          />
        );
      
      case 'review':
        return (
          <AllianceReviewScreen
            questionData={questionData}
            allianceData={allianceData}
            onConfirm={handleAllianceCreate}
            onBack={() => setCurrentPhase('questions')}
            isLoading={isLoading}
          />
        );
      
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50">
      <AnimatePresence mode="wait">
        {renderCurrentPhase()}
      </AnimatePresence>
    </div>
  );
};

export default ImmersiveAllianceWizard;
