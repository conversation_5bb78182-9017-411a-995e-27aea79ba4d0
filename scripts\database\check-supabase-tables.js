// Script to check Supabase tables and structure
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Get Supabase credentials from .env file
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key available:', !!supabaseKey);

const supabase = createClient(supabaseUrl, supabaseKey);

async function listTables() {
  try {
    // Query to list all tables in the public schema
    const { data, error } = await supabase.rpc('list_tables');
    
    if (error) {
      console.error('Error listing tables:', error);
      return;
    }
    
    console.log('Tables in database:');
    console.log(data);
    
    // Check for specific tables
    const tables = ['project_settings', 'royalty_models', 'projects'];
    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1);
        
        if (error) {
          console.error(`Error querying ${table} table:`, error);
          console.log(`${table} table might not exist`);
        } else {
          console.log(`${table} table exists`);
          console.log('Sample data:', data);
        }
      } catch (err) {
        console.error(`Error checking ${table} table:`, err);
      }
    }
    
    // Check a specific project to see how royalty model is stored
    try {
      const { data: projects, error: projectsError } = await supabase
        .from('projects')
        .select('*')
        .limit(1);
      
      if (projectsError) {
        console.error('Error querying projects table:', projectsError);
      } else if (projects && projects.length > 0) {
        const projectId = projects[0].id;
        console.log(`Found project with ID: ${projectId}`);
        
        // Check if this project has a royalty model in royalty_models table
        const { data: projectRoyaltyModel, error: projectRoyaltyModelError } = await supabase
          .from('royalty_models')
          .select('*')
          .eq('project_id', projectId)
          .single();
        
        if (projectRoyaltyModelError) {
          console.log(`No royalty model found in royalty_models table for project ${projectId}`);
        } else {
          console.log('Royalty model from royalty_models table:', projectRoyaltyModel);
        }
      }
    } catch (err) {
      console.error('Error checking project royalty model:', err);
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

listTables();
