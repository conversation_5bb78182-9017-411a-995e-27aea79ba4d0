import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>, CardB<PERSON>, CardHeader, Button } from '@heroui/react';
import ProjectWizard from '../project/ProjectWizard';
import { motion, AnimatePresence } from 'framer-motion';

const StartPage = () => {
  const navigate = useNavigate();
  const [showProjectCreator, setShowProjectCreator] = useState(false);

  const handleStartProject = () => {
    setShowProjectCreator(true);
    // Alternative: navigate directly to the create page
    // navigate('/project/create');
  };

  const handleStartTraining = () => {
    navigate('/learn');
  };

  const handleBackToOptions = () => {
    setShowProjectCreator(false);
  };

  return (
    <div className="min-h-screen py-12 px-4 sm:px-6 lg:px-8">
      <AnimatePresence mode="wait">
        {!showProjectCreator ? (
          <motion.div
            key="options"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="max-w-4xl mx-auto text-center mb-12">
              <h1 className="text-4xl font-bold mb-4">Start Your Journey</h1>
              <p className="text-xl leading-relaxed">
                Begin your Royaltea experience by creating a new project or learning about our platform.
              </p>
            </div>

            <div className="max-w-4xl mx-auto grid md:grid-cols-2 gap-8">
              <Card className="h-full">
                <CardBody className="p-8 flex flex-col h-full">
                  <div className="text-center mb-6">
                    <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center">
                      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <h3 className="text-2xl font-bold text-center mb-3">Start a Project</h3>
                  </div>
                  <p className="text-center mb-6 flex-grow">
                    Create a new project in just a few simple steps. We'll set up smart defaults so you can get started right away.
                  </p>
                  <Button
                    className="w-full"
                    size="lg"
                    onClick={handleStartProject}
                  >
                    Create New Project
                  </Button>
                </CardBody>
              </Card>

              <Card className="h-full">
                <CardBody className="p-8 flex flex-col h-full">
                  <div className="text-center mb-6">
                    <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center">
                      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"/>
                      </svg>
                    </div>
                    <h3 className="text-2xl font-bold text-center mb-3">Start Training</h3>
                  </div>
                  <p className="text-center mb-6 flex-grow">
                    Learn how to use Royaltea effectively with our comprehensive training resources.
                    Understand royalty models, contribution tracking, and best practices.
                  </p>
                  <Button
                    variant="bordered"
                    className="w-full"
                    size="lg"
                    onClick={handleStartTraining}
                  >
                    Access Training
                  </Button>
                </CardBody>
              </Card>
            </div>
          </motion.div>
        ) : (
          <motion.div
            key="creator"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="project-creator-container max-w-6xl mx-auto"
          >
            <Button
              variant="bordered"
              className="mb-6"
              onClick={handleBackToOptions}
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Back to options
            </Button>
            <ProjectWizard />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default StartPage;
