import { supabase } from '../../../utils/supabase/supabase.utils';

/**
 * Fetch all skills from the database
 * @returns {Promise<Array>} Array of skills
 */
export const getAllSkills = async () => {
  const { data, error } = await supabase
    .from('skills')
    .select('*')
    .order('category', { ascending: true })
    .order('area', { ascending: true })
    .order('name', { ascending: true })
    .order('micro_skill', { ascending: true })
    .order('mastery_component', { ascending: true });

  if (error) {
    console.error('Error fetching skills:', error);
    throw error;
  }

  return data || [];
};

/**
 * Fetch skills for a specific user
 * @param {string} userId - The user ID to fetch skills for
 * @returns {Promise<Array>} Array of user skills with verification details
 */
export const getUserSkills = async (userId) => {
  const { data, error } = await supabase
    .from('user_skills')
    .select(`
      *,
      skill:skills(*),
      verifications:skill_verifications(*)
    `)
    .eq('user_id', userId);

  if (error) {
    console.error('Error fetching user skills:', error);
    throw error;
  }

  return data || [];
};

/**
 * Add a skill to a user's profile
 * @param {string} userId - The user ID
 * @param {string} skillId - The skill ID to add
 * @param {Object} options - Additional options for the skill
 * @returns {Promise<Object>} The created user skill
 */
export const addUserSkill = async (userId, skillId, options = {}) => {
  const { data, error } = await supabase
    .from('user_skills')
    .insert([
      {
        user_id: userId,
        skill_id: skillId,
        proficiency_score: options.proficiency_score || 0,
        verification_level: options.verification_level || 0,
        is_public: options.is_public !== undefined ? options.is_public : true
      }
    ])
    .select();

  if (error) {
    console.error('Error adding user skill:', error);
    throw error;
  }

  return data[0];
};

/**
 * Update a user's skill
 * @param {string} userSkillId - The user skill ID to update
 * @param {Object} updates - The updates to apply
 * @returns {Promise<Object>} The updated user skill
 */
export const updateUserSkill = async (userSkillId, updates) => {
  const { data, error } = await supabase
    .from('user_skills')
    .update(updates)
    .eq('id', userSkillId)
    .select();

  if (error) {
    console.error('Error updating user skill:', error);
    throw error;
  }

  return data[0];
};

/**
 * Remove a skill from a user's profile
 * @param {string} userSkillId - The user skill ID to remove
 * @returns {Promise<void>}
 */
export const removeUserSkill = async (userSkillId) => {
  const { error } = await supabase
    .from('user_skills')
    .delete()
    .eq('id', userSkillId);

  if (error) {
    console.error('Error removing user skill:', error);
    throw error;
  }
};

/**
 * Add a verification to a user skill
 * @param {string} userSkillId - The user skill ID
 * @param {string} verificationType - The type of verification
 * @param {Object} verificationData - Additional verification data
 * @returns {Promise<Object>} The created verification
 */
export const addSkillVerification = async (userSkillId, verificationType, verificationData = {}) => {
  const { data, error } = await supabase
    .from('skill_verifications')
    .insert([
      {
        user_skill_id: userSkillId,
        verification_type: verificationType,
        verification_source: verificationData.source || null,
        verification_data: verificationData.data || {},
        verified_by: verificationData.verified_by || null,
        expires_at: verificationData.expires_at || null
      }
    ])
    .select();

  if (error) {
    console.error('Error adding skill verification:', error);
    throw error;
  }

  return data[0];
};

/**
 * Get the display name for a verification level
 * @param {number} level - The verification level (0-5)
 * @returns {string} The display name for the level
 */
export const getVerificationLevelName = (level) => {
  const levels = {
    0: 'Unverified',
    1: 'Learning Verified',
    2: 'Peer Verified',
    3: 'Project Verified',
    4: 'Expert Verified',
    5: 'Industry Recognized'
  };
  
  return levels[level] || 'Unknown';
};

/**
 * Get the icon for a verification level
 * @param {number} level - The verification level (0-5)
 * @returns {string} The Bootstrap icon class for the level
 */
export const getVerificationLevelIcon = (level) => {
  const icons = {
    0: 'bi-person-fill',
    1: 'bi-book-fill',
    2: 'bi-people-fill',
    3: 'bi-briefcase-fill',
    4: 'bi-award-fill',
    5: 'bi-trophy-fill'
  };
  
  return icons[level] || 'bi-question-circle-fill';
};

/**
 * Calculate the overall skill score based on various metrics
 * @param {Object} userSkill - The user skill object
 * @returns {number} The calculated overall score (0-100)
 */
export const calculateSkillScore = (userSkill) => {
  const {
    proficiency_score = 0,
    currency_score = 0,
    depth_score = 0,
    breadth_score = 0,
    relevance_score = 0,
    verification_level = 0
  } = userSkill;
  
  // Base weight for each component
  const weights = {
    proficiency: 0.4,
    currency: 0.1,
    depth: 0.2,
    breadth: 0.1,
    relevance: 0.1,
    verification: 0.1
  };
  
  // Normalize verification level to 0-100 scale
  const normalizedVerification = (verification_level / 5) * 100;
  
  // Calculate weighted score
  const score = 
    (proficiency_score * weights.proficiency) +
    (currency_score * weights.currency) +
    (depth_score * weights.depth) +
    (breadth_score * weights.breadth) +
    (relevance_score * weights.relevance) +
    (normalizedVerification * weights.verification);
  
  return Math.round(score);
};

/**
 * Organize skills into a hierarchical structure
 * @param {Array} skills - Flat array of skills
 * @returns {Array} Hierarchical structure of skills
 */
export const organizeSkillHierarchy = (skills) => {
  const skillMap = {};
  const rootSkills = [];
  
  // First pass: create a map of all skills
  skills.forEach(skill => {
    skillMap[skill.id] = {
      ...skill,
      children: []
    };
  });
  
  // Second pass: build the hierarchy
  skills.forEach(skill => {
    if (skill.parent_id && skillMap[skill.parent_id]) {
      skillMap[skill.parent_id].children.push(skillMap[skill.id]);
    } else if (!skill.parent_id) {
      rootSkills.push(skillMap[skill.id]);
    }
  });
  
  return rootSkills;
};
