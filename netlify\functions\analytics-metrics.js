// Analytics & Metrics API
// Backend Specialist: Collaboration analytics and performance metrics system
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Get Collaboration Metrics
const getCollaborationMetrics = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const targetUserId = queryParams.get('user_id') || userId;
    const period = queryParams.get('period') || 'monthly';
    const limit = parseInt(queryParams.get('limit') || '12');

    // Get collaboration metrics for the specified period
    const { data: metrics, error: metricsError } = await supabase
      .from('collaboration_metrics')
      .select('*')
      .eq('user_id', targetUserId)
      .eq('metric_period', period)
      .order('period_start', { ascending: false })
      .limit(limit);

    if (metricsError) {
      throw new Error(`Failed to fetch collaboration metrics: ${metricsError.message}`);
    }

    // Calculate trends and aggregates
    const currentMetrics = metrics?.[0];
    const previousMetrics = metrics?.[1];
    
    const trends = {};
    if (currentMetrics && previousMetrics) {
      trends.collaboration_success_rate = calculateTrend(
        currentMetrics.collaboration_success_rate, 
        previousMetrics.collaboration_success_rate
      );
      trends.network_growth_rate = calculateTrend(
        currentMetrics.network_growth_rate, 
        previousMetrics.network_growth_rate
      );
      trends.activity_score = calculateTrend(
        currentMetrics.activity_score, 
        previousMetrics.activity_score
      );
      trends.on_time_delivery_rate = calculateTrend(
        currentMetrics.on_time_delivery_rate, 
        previousMetrics.on_time_delivery_rate
      );
    }

    // Calculate aggregated statistics
    const aggregates = {
      total_collaborations: metrics?.reduce((sum, m) => sum + (m.total_collaborations || 0), 0) || 0,
      average_success_rate: calculateAverage(metrics?.map(m => m.collaboration_success_rate)),
      average_response_time: calculateAverage(metrics?.map(m => m.response_time_avg)),
      total_projects_completed: metrics?.reduce((sum, m) => sum + (m.projects_completed || 0), 0) || 0,
      total_files_shared: metrics?.reduce((sum, m) => sum + (m.files_shared || 0), 0) || 0,
      network_size: currentMetrics?.total_active_connections || 0
    };

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        metrics: metrics || [],
        current_metrics: currentMetrics,
        trends,
        aggregates,
        period
      })
    };

  } catch (error) {
    console.error('Get collaboration metrics error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch collaboration metrics' })
    };
  }
};

// Get Network Analytics
const getNetworkAnalytics = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const targetUserId = queryParams.get('user_id') || userId;
    const period = queryParams.get('period') || 'monthly';

    // Get latest network analytics
    const { data: analytics, error: analyticsError } = await supabase
      .from('network_analytics')
      .select('*')
      .eq('user_id', targetUserId)
      .eq('analysis_period', period)
      .order('period_start', { ascending: false })
      .limit(6); // Last 6 periods for trend analysis

    if (analyticsError) {
      throw new Error(`Failed to fetch network analytics: ${analyticsError.message}`);
    }

    const currentAnalytics = analytics?.[0];
    
    // Calculate network insights
    const insights = {
      network_strength: calculateNetworkStrength(currentAnalytics),
      influence_level: calculateInfluenceLevel(currentAnalytics),
      diversity_score: currentAnalytics?.skill_diversity_in_network || 0,
      growth_trajectory: calculateGrowthTrajectory(analytics),
      connection_quality: calculateConnectionQuality(currentAnalytics)
    };

    // Get network composition data
    const networkComposition = {
      strong_ties_percentage: currentAnalytics ? 
        (currentAnalytics.strong_ties / currentAnalytics.total_connections * 100) : 0,
      weak_ties_percentage: currentAnalytics ? 
        (currentAnalytics.weak_ties / currentAnalytics.total_connections * 100) : 0,
      bridge_connections_percentage: currentAnalytics ? 
        (currentAnalytics.bridge_connections / currentAnalytics.total_connections * 100) : 0
    };

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        analytics: analytics || [],
        current_analytics: currentAnalytics,
        insights,
        network_composition,
        period
      })
    };

  } catch (error) {
    console.error('Get network analytics error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch network analytics' })
    };
  }
};

// Generate Analytics Report
const generateAnalyticsReport = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const data = JSON.parse(event.body);
    const reportType = data.report_type || 'comprehensive';
    const period = data.period || 'monthly';
    const targetUserId = data.user_id || userId;

    // Get all relevant data for the report
    const [
      collaborationMetrics,
      networkAnalytics,
      achievements,
      endorsements,
      rankings
    ] = await Promise.all([
      getCollaborationMetricsData(targetUserId, period),
      getNetworkAnalyticsData(targetUserId, period),
      getUserAchievementsData(targetUserId),
      getUserEndorsementsData(targetUserId),
      getUserRankingsData(targetUserId, period)
    ]);

    // Generate comprehensive report
    const report = {
      report_id: `report_${Date.now()}`,
      generated_at: new Date().toISOString(),
      report_type: reportType,
      period: period,
      user_id: targetUserId,
      
      // Executive summary
      executive_summary: generateExecutiveSummary({
        collaborationMetrics,
        networkAnalytics,
        achievements,
        rankings
      }),
      
      // Detailed sections
      collaboration_performance: {
        metrics: collaborationMetrics,
        key_insights: generateCollaborationInsights(collaborationMetrics),
        recommendations: generateCollaborationRecommendations(collaborationMetrics)
      },
      
      network_analysis: {
        analytics: networkAnalytics,
        network_health: calculateNetworkHealth(networkAnalytics),
        growth_opportunities: identifyGrowthOpportunities(networkAnalytics)
      },
      
      recognition_achievements: {
        achievements: achievements,
        achievement_score: calculateAchievementScore(achievements),
        next_milestones: identifyNextMilestones(achievements)
      },
      
      peer_validation: {
        endorsements: endorsements,
        credibility_score: calculateCredibilityScore(endorsements),
        skill_gaps: identifySkillGaps(endorsements)
      },
      
      competitive_position: {
        rankings: rankings,
        percentile_performance: calculatePercentilePerformance(rankings),
        improvement_areas: identifyImprovementAreas(rankings)
      }
    };

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ report })
    };

  } catch (error) {
    console.error('Generate analytics report error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to generate analytics report' })
    };
  }
};

// Calculate Performance Score
const calculatePerformanceScore = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const targetUserId = queryParams.get('user_id') || userId;

    // Get latest metrics for score calculation
    const [
      collaborationMetrics,
      networkAnalytics,
      achievements,
      endorsements
    ] = await Promise.all([
      getLatestCollaborationMetrics(targetUserId),
      getLatestNetworkAnalytics(targetUserId),
      getUserAchievementsData(targetUserId),
      getUserEndorsementsData(targetUserId)
    ]);

    // Calculate component scores (0-100 scale)
    const scores = {
      collaboration_score: calculateCollaborationScore(collaborationMetrics),
      network_score: calculateNetworkScore(networkAnalytics),
      achievement_score: calculateAchievementScore(achievements),
      endorsement_score: calculateEndorsementScore(endorsements)
    };

    // Calculate weighted overall score
    const weights = {
      collaboration_score: 0.35,
      network_score: 0.25,
      achievement_score: 0.25,
      endorsement_score: 0.15
    };

    const overall_score = Object.keys(scores).reduce((total, key) => {
      return total + (scores[key] * weights[key]);
    }, 0);

    // Determine performance level
    const performance_level = getPerformanceLevel(overall_score);
    
    // Generate insights and recommendations
    const insights = generatePerformanceInsights(scores, overall_score);
    const recommendations = generatePerformanceRecommendations(scores);

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        overall_score: Math.round(overall_score),
        performance_level,
        component_scores: scores,
        weights,
        insights,
        recommendations,
        calculated_at: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('Calculate performance score error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to calculate performance score' })
    };
  }
};

// Helper functions for calculations
const calculateTrend = (current, previous) => {
  if (!previous || previous === 0) return 0;
  return ((current - previous) / previous) * 100;
};

const calculateAverage = (values) => {
  const validValues = values?.filter(v => v != null && !isNaN(v)) || [];
  return validValues.length > 0 ? validValues.reduce((sum, v) => sum + v, 0) / validValues.length : 0;
};

const calculateNetworkStrength = (analytics) => {
  if (!analytics) return 0;
  const density = analytics.network_density || 0;
  const clustering = analytics.clustering_coefficient || 0;
  return (density * 0.6 + clustering * 0.4) * 100;
};

const calculateInfluenceLevel = (analytics) => {
  if (!analytics) return 0;
  const influence = analytics.network_influence_score || 0;
  const centrality = (analytics.betweenness_centrality + analytics.closeness_centrality) / 2;
  return (influence * 0.7 + centrality * 0.3) * 100;
};

const calculateGrowthTrajectory = (analytics) => {
  if (!analytics || analytics.length < 2) return 'stable';
  const recent = analytics[0];
  const previous = analytics[1];
  const growthRate = recent.connection_growth_rate || 0;
  
  if (growthRate > 10) return 'rapid_growth';
  if (growthRate > 5) return 'steady_growth';
  if (growthRate > 0) return 'slow_growth';
  if (growthRate === 0) return 'stable';
  return 'declining';
};

const calculateConnectionQuality = (analytics) => {
  if (!analytics) return 0;
  const strongTiesRatio = analytics.strong_ties / (analytics.total_connections || 1);
  const collaborationRatio = analytics.collaboration_partners / (analytics.total_connections || 1);
  return (strongTiesRatio * 0.6 + collaborationRatio * 0.4) * 100;
};

const getPerformanceLevel = (score) => {
  if (score >= 90) return 'exceptional';
  if (score >= 80) return 'excellent';
  if (score >= 70) return 'good';
  if (score >= 60) return 'average';
  if (score >= 50) return 'below_average';
  return 'needs_improvement';
};

// Data fetching helper functions
const getCollaborationMetricsData = async (userId, period) => {
  const { data } = await supabase
    .from('collaboration_metrics')
    .select('*')
    .eq('user_id', userId)
    .eq('metric_period', period)
    .order('period_start', { ascending: false })
    .limit(6);
  return data || [];
};

const getNetworkAnalyticsData = async (userId, period) => {
  const { data } = await supabase
    .from('network_analytics')
    .select('*')
    .eq('user_id', userId)
    .eq('analysis_period', period)
    .order('period_start', { ascending: false })
    .limit(6);
  return data || [];
};

const getUserAchievementsData = async (userId) => {
  const { data } = await supabase
    .from('user_achievements')
    .select('*')
    .eq('user_id', userId)
    .eq('is_public', true)
    .order('earned_at', { ascending: false });
  return data || [];
};

const getUserEndorsementsData = async (userId) => {
  const { data } = await supabase
    .from('user_endorsements')
    .select('*')
    .eq('endorsed_user_id', userId)
    .eq('is_public', true)
    .eq('status', 'active')
    .order('created_at', { ascending: false });
  return data || [];
};

const getUserRankingsData = async (userId, period) => {
  const { data } = await supabase
    .from('recognition_rankings')
    .select('*')
    .eq('user_id', userId)
    .eq('ranking_period', period)
    .order('calculated_at', { ascending: false });
  return data || [];
};

// Insight generation functions
const generateExecutiveSummary = (data) => {
  return {
    overview: "Performance analysis based on collaboration metrics, network analytics, and peer recognition.",
    key_strengths: [],
    improvement_areas: [],
    overall_trend: "stable"
  };
};

const generateCollaborationInsights = (metrics) => {
  return {
    performance_trend: "improving",
    key_metrics: [],
    notable_achievements: []
  };
};

const generatePerformanceInsights = (scores, overallScore) => {
  const insights = [];
  
  if (scores.collaboration_score > 80) {
    insights.push("Strong collaboration performance with high success rates");
  }
  
  if (scores.network_score > 75) {
    insights.push("Well-connected network with good influence metrics");
  }
  
  return insights;
};

const generatePerformanceRecommendations = (scores) => {
  const recommendations = [];
  
  if (scores.collaboration_score < 60) {
    recommendations.push("Focus on improving collaboration success rates and project delivery");
  }
  
  if (scores.network_score < 50) {
    recommendations.push("Expand your professional network and increase connection quality");
  }
  
  return recommendations;
};

// Additional helper functions for performance calculations
const getLatestCollaborationMetrics = async (targetUserId) => {
  const { data } = await supabase
    .from('collaboration_metrics')
    .select('*')
    .eq('user_id', targetUserId)
    .order('period_start', { ascending: false })
    .limit(1)
    .single();
  return data;
};

const getLatestNetworkAnalytics = async (targetUserId) => {
  const { data } = await supabase
    .from('network_analytics')
    .select('*')
    .eq('user_id', targetUserId)
    .order('period_start', { ascending: false })
    .limit(1)
    .single();
  return data;
};

const calculateCollaborationScore = (metrics) => {
  if (!metrics) return 0;
  const successRate = metrics.collaboration_success_rate || 0;
  const onTimeRate = metrics.on_time_delivery_rate || 0;
  const qualityScore = metrics.quality_score || 0;
  return (successRate * 0.4 + onTimeRate * 0.3 + qualityScore * 0.3);
};

const calculateNetworkScore = (analytics) => {
  if (!analytics) return 0;
  const influence = analytics.network_influence_score || 0;
  const connections = Math.min(analytics.total_connections / 100, 1) * 100; // Cap at 100 connections
  const diversity = analytics.skill_diversity_in_network || 0;
  return (influence * 0.5 + connections * 0.3 + diversity * 0.2);
};

const calculateEndorsementScore = (endorsements) => {
  if (!endorsements || endorsements.length === 0) return 0;
  const totalEndorsements = endorsements.length;
  const averageLevel = endorsements.reduce((sum, e) => sum + (e.endorsement_level || 0), 0) / totalEndorsements;
  const verifiedCount = endorsements.filter(e => e.is_verified).length;
  const verificationRate = verifiedCount / totalEndorsements;

  return Math.min((totalEndorsements * 2) + (averageLevel * 10) + (verificationRate * 30), 100);
};

// Stub functions for report generation (to be implemented)
const calculateNetworkHealth = () => ({ status: 'healthy', score: 85 });
const identifyGrowthOpportunities = () => ['Expand into new skill areas', 'Increase collaboration frequency'];
const identifyNextMilestones = () => ['Reach 50 collaborations', 'Earn skill expert badge'];
const calculateCredibilityScore = () => 78;
const identifySkillGaps = () => ['Advanced project management', 'Leadership skills'];
const calculatePercentilePerformance = () => ({ overall: 75, collaboration: 80, network: 70 });
const identifyImprovementAreas = () => ['Network diversity', 'Response time'];
const generateCollaborationRecommendations = () => ['Focus on project completion rates'];

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/analytics-metrics', '');

  try {
    let response;

    if (event.httpMethod === 'GET') {
      if (path === '/collaboration' || path === '/collaboration/') {
        response = await getCollaborationMetrics(event);
      } else if (path === '/network' || path === '/network/') {
        response = await getNetworkAnalytics(event);
      } else if (path === '/performance-score' || path === '/performance-score/') {
        response = await calculatePerformanceScore(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else if (event.httpMethod === 'POST') {
      if (path === '/report' || path === '/report/') {
        response = await generateAnalyticsReport(event);
      } else {
        response = {
          statusCode: 404,
          body: JSON.stringify({ error: 'Endpoint not found' })
        };
      }
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('Analytics Metrics API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
