import React, { useState, useEffect, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Chip, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, Input, Textarea, Select, SelectItem } from '@heroui/react';
import { UserContext } from '../../../contexts/supabase-auth.context.jsx';
import { useDataSync } from '../../contexts/DataSyncContext';
import { supabase } from '../../../utils/supabase/supabase.utils.js';
import { toast } from 'react-hot-toast';

/**
 * Drag and Drop Task Board Component
 *
 * Provides intuitive task management with drag-and-drop functionality.
 * Supports task creation, status updates, and real-time synchronization.
 */

const DragDropTaskBoard = ({ projectId, className = "" }) => {
  const { currentUser } = useContext(UserContext);
  const { syncTriggers, triggerProjectSync } = useDataSync();

  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [draggedTask, setDraggedTask] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingTask, setEditingTask] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    status: 'todo',
    priority: 'medium',
    difficulty_rating: 3
  });

  // Task status columns
  const columns = [
    { id: 'todo', title: 'To Do', color: 'default' },
    { id: 'in_progress', title: 'In Progress', color: 'primary' },
    { id: 'review', title: 'Review', color: 'warning' },
    { id: 'done', title: 'Done', color: 'success' }
  ];

  // Load tasks
  const loadTasks = async () => {
    if (!projectId) return;

    try {
      setLoading(true);

      const { data, error } = await supabase
        .from('tasks')
        .select('*')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setTasks(data || []);
    } catch (error) {
      console.error('Error loading tasks:', error);
      toast.error('Failed to load tasks');
    } finally {
      setLoading(false);
    }
  };

  // Save task
  const saveTask = async () => {
    if (!formData.title.trim()) {
      toast.error('Task title is required');
      return;
    }

    try {
      const taskData = {
        ...formData,
        project_id: projectId,
        assigned_to: currentUser.id,
        updated_at: new Date().toISOString()
      };

      if (editingTask) {
        // Update existing task
        const { error } = await supabase
          .from('tasks')
          .update(taskData)
          .eq('id', editingTask.id);

        if (error) throw error;
        toast.success('Task updated successfully');
      } else {
        // Create new task
        taskData.created_at = new Date().toISOString();

        const { error } = await supabase
          .from('tasks')
          .insert([taskData]);

        if (error) throw error;
        toast.success('Task created successfully');
      }

      triggerProjectSync();
      loadTasks();
      closeModal();
    } catch (error) {
      console.error('Error saving task:', error);
      toast.error('Failed to save task');
    }
  };

  // Update task status
  const updateTaskStatus = async (taskId, newStatus) => {
    try {
      const { error } = await supabase
        .from('tasks')
        .update({
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', taskId);

      if (error) throw error;

      triggerProjectSync();
      loadTasks();
    } catch (error) {
      console.error('Error updating task status:', error);
      toast.error('Failed to update task status');
    }
  };

  // Delete task
  const deleteTask = async (taskId) => {
    if (!confirm('Are you sure you want to delete this task?')) return;

    try {
      const { error } = await supabase
        .from('tasks')
        .delete()
        .eq('id', taskId);

      if (error) throw error;

      toast.success('Task deleted successfully');
      triggerProjectSync();
      loadTasks();
    } catch (error) {
      console.error('Error deleting task:', error);
      toast.error('Failed to delete task');
    }
  };

  // Drag and drop handlers
  const handleDragStart = (e, task) => {
    setDraggedTask(task);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e, newStatus) => {
    e.preventDefault();

    if (draggedTask && draggedTask.status !== newStatus) {
      updateTaskStatus(draggedTask.id, newStatus);
    }

    setDraggedTask(null);
  };

  // Modal handlers
  const openModal = (task = null) => {
    if (task) {
      setEditingTask(task);
      setFormData({
        title: task.title || '',
        description: task.description || '',
        status: task.status || 'todo',
        priority: task.priority || 'medium',
        difficulty_rating: task.difficulty_rating || 3
      });
    } else {
      setEditingTask(null);
      setFormData({
        title: '',
        description: '',
        status: 'todo',
        priority: 'medium',
        difficulty_rating: 3
      });
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setEditingTask(null);
    setFormData({
      title: '',
      description: '',
      status: 'todo',
      priority: 'medium',
      difficulty_rating: 3
    });
  };

  // Get priority color
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'danger';
      case 'medium': return 'warning';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  // Load data on mount and when sync triggers change
  useEffect(() => {
    loadTasks();
  }, [projectId, syncTriggers.projects]);

  if (loading) {
    return (
      <div className={`p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-default-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="space-y-3">
                <div className="h-4 bg-default-200 rounded"></div>
                {[1, 2].map(j => (
                  <div key={j} className="h-20 bg-default-200 rounded"></div>
                ))}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold">Task Board</h3>
        <Button
          color="primary"
          onClick={() => openModal()}
          size="sm"
        >
          Add Task
        </Button>
      </div>

      {/* Task Board */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {columns.map((column) => (
          <div
            key={column.id}
            className="space-y-4"
            onDragOver={handleDragOver}
            onDrop={(e) => handleDrop(e, column.id)}
          >
            {/* Column Header */}
            <div className="flex items-center justify-between">
              <h4 className="font-medium text-foreground">{column.title}</h4>
              <Chip size="sm" color={column.color} variant="flat">
                {tasks.filter(task => task.status === column.id).length}
              </Chip>
            </div>

            {/* Tasks */}
            <div className="space-y-3 min-h-[200px]">
              <AnimatePresence>
                {tasks
                  .filter(task => task.status === column.id)
                  .map((task) => (
                    <motion.div
                      key={task.id}
                      layout
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.2 }}
                      draggable
                      onDragStart={(e) => handleDragStart(e, task)}
                      className="cursor-move"
                    >
                      <Card className="border border-divider hover:shadow-md transition-shadow">
                        <CardBody className="p-4">
                          <div className="flex items-start justify-between mb-2">
                            <h5 className="font-medium text-sm">{task.title}</h5>
                            <div className="flex gap-1">
                              <Button
                                size="sm"
                                variant="light"
                                onClick={() => openModal(task)}
                                className="min-w-0 px-2"
                              >
                                ✏️
                              </Button>
                              <Button
                                size="sm"
                                variant="light"
                                color="danger"
                                onClick={() => deleteTask(task.id)}
                                className="min-w-0 px-2"
                              >
                                🗑️
                              </Button>
                            </div>
                          </div>

                          {task.description && (
                            <p className="text-xs text-muted-foreground mb-3 line-clamp-2">
                              {task.description}
                            </p>
                          )}

                          <div className="flex items-center justify-between">
                            <Chip
                              size="sm"
                              color={getPriorityColor(task.priority)}
                              variant="flat"
                            >
                              {task.priority}
                            </Chip>
                            <span className="text-xs text-muted-foreground">
                              D{task.difficulty_rating}
                            </span>
                          </div>
                        </CardBody>
                      </Card>
                    </motion.div>
                  ))}
              </AnimatePresence>
            </div>
          </div>
        ))}
      </div>

      {/* Task Modal */}
      <Modal isOpen={isModalOpen} onClose={closeModal} size="lg">
        <ModalContent>
          <ModalHeader>
            {editingTask ? 'Edit Task' : 'Create Task'}
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <Input
                label="Task Title"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                placeholder="Enter task title"
                isRequired
              />

              <Textarea
                label="Description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Describe the task"
              />

              <div className="grid grid-cols-2 gap-4">
                <Select
                  label="Status"
                  selectedKeys={[formData.status]}
                  onSelectionChange={(keys) => setFormData({ ...formData, status: Array.from(keys)[0] })}
                >
                  {columns.map((column) => (
                    <SelectItem key={column.id} value={column.id}>
                      {column.title}
                    </SelectItem>
                  ))}
                </Select>

                <Select
                  label="Priority"
                  selectedKeys={[formData.priority]}
                  onSelectionChange={(keys) => setFormData({ ...formData, priority: Array.from(keys)[0] })}
                >
                  <SelectItem key="low" value="low">Low</SelectItem>
                  <SelectItem key="medium" value="medium">Medium</SelectItem>
                  <SelectItem key="high" value="high">High</SelectItem>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Difficulty: {formData.difficulty_rating}
                </label>
                <input
                  type="range"
                  min="1"
                  max="5"
                  value={formData.difficulty_rating}
                  onChange={(e) => setFormData({ ...formData, difficulty_rating: parseInt(e.target.value) })}
                  className="w-full"
                />
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onClick={closeModal}>
              Cancel
            </Button>
            <Button color="primary" onClick={saveTask}>
              {editingTask ? 'Update' : 'Create'}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default DragDropTaskBoard;
