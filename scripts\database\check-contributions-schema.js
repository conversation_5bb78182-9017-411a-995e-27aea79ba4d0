const { createClient } = require('@supabase/supabase-js');

// Create a Supabase client
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE2OTk0NzI1NzYsImV4cCI6MjAxNTA0ODU3Nn0.Wd_oYpYUQdE_RoLQdOgwcnQE4MqUCxgMjU6QKW_RQ0c';
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkContributionsSchema() {
  try {
    // Get the schema for the contributions table
    const { data, error } = await supabase.rpc('get_table_definition', { table_name: 'contributions' });
    
    if (error) {
      console.error('Error getting table definition:', error);
      return;
    }
    
    console.log('Contributions table schema:', data);
    
    // Get a sample contribution to see the data structure
    const { data: sampleData, error: sampleError } = await supabase
      .from('contributions')
      .select('*')
      .limit(1);
      
    if (sampleError) {
      console.error('Error getting sample contribution:', sampleError);
      return;
    }
    
    console.log('Sample contribution data:', sampleData);
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

checkContributionsSchema();
