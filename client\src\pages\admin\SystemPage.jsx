import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardBody, CardHeader, Button, Tabs, Tab, Progress, Chip } from '@heroui/react';

/**
 * System Management Page Component
 * 
 * Admin interface for system monitoring, database management, migrations, and logs.
 * Provides comprehensive system health and maintenance tools.
 */
const SystemPage = () => {
  const [activeTab, setActiveTab] = useState('health');

  // Mock system data
  const systemHealth = {
    status: 'healthy',
    uptime: '99.9%',
    responseTime: '120ms',
    activeUsers: 1247,
    dbConnections: 45,
    memoryUsage: 68,
    cpuUsage: 23,
    diskUsage: 42
  };

  const migrations = [
    {
      id: 1,
      name: 'Add contribution tracking fields',
      status: 'completed',
      date: '2024-01-15',
      duration: '2.3s'
    },
    {
      id: 2,
      name: 'Update user permissions schema',
      status: 'completed',
      date: '2024-01-14',
      duration: '1.8s'
    },
    {
      id: 3,
      name: 'Create escrow tables',
      status: 'pending',
      date: '2024-01-16',
      duration: '-'
    }
  ];

  const logs = [
    {
      id: 1,
      level: 'info',
      message: 'User authentication successful',
      timestamp: '2024-01-15 14:30:25',
      source: 'auth-service'
    },
    {
      id: 2,
      level: 'warning',
      message: 'High memory usage detected',
      timestamp: '2024-01-15 14:25:10',
      source: 'monitoring'
    },
    {
      id: 3,
      level: 'error',
      message: 'Database connection timeout',
      timestamp: '2024-01-15 14:20:05',
      source: 'database'
    }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy': return 'success';
      case 'warning': return 'warning';
      case 'error': return 'danger';
      case 'completed': return 'success';
      case 'pending': return 'warning';
      case 'failed': return 'danger';
      default: return 'default';
    }
  };

  const getLogLevelColor = (level) => {
    switch (level) {
      case 'info': return 'primary';
      case 'warning': return 'warning';
      case 'error': return 'danger';
      default: return 'default';
    }
  };

  const getUsageColor = (usage) => {
    if (usage > 80) return 'danger';
    if (usage > 60) return 'warning';
    return 'success';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-slate-800">
      {/* Header */}
      <motion.div
        className="relative z-10 pt-8 pb-6"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="container mx-auto px-6">
          <div className="text-center mb-8">
            <motion.div
              className="text-6xl mb-4"
              animate={{ 
                scale: [1, 1.1, 1],
                rotate: [0, 5, -5, 0]
              }}
              transition={{ 
                duration: 2, 
                repeat: Infinity,
                repeatType: "reverse"
              }}
            >
              🖥️
            </motion.div>
            <h1 className="text-4xl font-bold text-white mb-2">
              System Management
            </h1>
            <p className="text-white/80 text-lg max-w-2xl mx-auto">
              Monitor system health, manage database operations, and maintain platform stability.
            </p>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="container mx-auto px-6 pb-12">
        <motion.div
          className="max-w-6xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {/* Navigation Tabs */}
          <Card className="mb-8 bg-white/5 border border-white/10">
            <CardBody className="p-6">
              <Tabs
                selectedKey={activeTab}
                onSelectionChange={setActiveTab}
                variant="underlined"
                classNames={{
                  tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
                  cursor: "w-full bg-gradient-to-r from-gray-500 to-slate-500",
                  tab: "max-w-fit px-0 h-12",
                  tabContent: "group-data-[selected=true]:text-white text-white/70"
                }}
              >
                <Tab key="health" title="💚 Health" />
                <Tab key="database" title="🗄️ Database" />
                <Tab key="migrations" title="🔄 Migrations" />
                <Tab key="logs" title="📋 Logs" />
              </Tabs>
            </CardBody>
          </Card>

          {/* Tab Content */}
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            {activeTab === 'health' && (
              <div className="space-y-6">
                {/* System Status Overview */}
                <Card className="bg-white/5 border border-white/10">
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-center w-full">
                      <h3 className="text-xl font-semibold text-white">System Status</h3>
                      <Chip color={getStatusColor(systemHealth.status)} variant="flat">
                        {systemHealth.status.toUpperCase()}
                      </Chip>
                    </div>
                  </CardHeader>
                  <CardBody className="space-y-6">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-400">{systemHealth.uptime}</div>
                        <div className="text-white/70 text-sm">Uptime</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-400">{systemHealth.responseTime}</div>
                        <div className="text-white/70 text-sm">Response Time</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-400">{systemHealth.activeUsers}</div>
                        <div className="text-white/70 text-sm">Active Users</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-orange-400">{systemHealth.dbConnections}</div>
                        <div className="text-white/70 text-sm">DB Connections</div>
                      </div>
                    </div>
                  </CardBody>
                </Card>

                {/* Resource Usage */}
                <Card className="bg-white/5 border border-white/10">
                  <CardHeader className="pb-3">
                    <h3 className="text-xl font-semibold text-white">Resource Usage</h3>
                  </CardHeader>
                  <CardBody className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span className="text-white/70">Memory Usage</span>
                        <span className="text-white">{systemHealth.memoryUsage}%</span>
                      </div>
                      <Progress
                        value={systemHealth.memoryUsage}
                        color={getUsageColor(systemHealth.memoryUsage)}
                        className="max-w-full"
                      />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span className="text-white/70">CPU Usage</span>
                        <span className="text-white">{systemHealth.cpuUsage}%</span>
                      </div>
                      <Progress
                        value={systemHealth.cpuUsage}
                        color={getUsageColor(systemHealth.cpuUsage)}
                        className="max-w-full"
                      />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span className="text-white/70">Disk Usage</span>
                        <span className="text-white">{systemHealth.diskUsage}%</span>
                      </div>
                      <Progress
                        value={systemHealth.diskUsage}
                        color={getUsageColor(systemHealth.diskUsage)}
                        className="max-w-full"
                      />
                    </div>
                  </CardBody>
                </Card>
              </div>
            )}

            {activeTab === 'database' && (
              <Card className="bg-white/5 border border-white/10">
                <CardBody className="p-6">
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">🗄️</div>
                    <h3 className="text-xl font-semibold text-white mb-2">Database Management</h3>
                    <p className="text-white/70 mb-6">
                      Advanced database operations and maintenance tools
                    </p>
                    <div className="flex gap-4 justify-center">
                      <Button className="bg-blue-500 hover:bg-blue-600 text-white">
                        Backup Database
                      </Button>
                      <Button className="bg-orange-500 hover:bg-orange-600 text-white">
                        Optimize Tables
                      </Button>
                    </div>
                  </div>
                </CardBody>
              </Card>
            )}

            {activeTab === 'migrations' && (
              <Card className="bg-white/5 border border-white/10">
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-center w-full">
                    <h3 className="text-xl font-semibold text-white">Database Migrations</h3>
                    <Button className="bg-green-500 hover:bg-green-600 text-white">
                      Run Pending
                    </Button>
                  </div>
                </CardHeader>
                <CardBody className="space-y-4">
                  {migrations.map((migration) => (
                    <div key={migration.id} className="flex items-center justify-between p-4 bg-white/5 rounded-lg">
                      <div>
                        <div className="text-white font-medium">{migration.name}</div>
                        <div className="text-white/60 text-sm">
                          {migration.date} • Duration: {migration.duration}
                        </div>
                      </div>
                      <Chip size="sm" color={getStatusColor(migration.status)} variant="flat">
                        {migration.status}
                      </Chip>
                    </div>
                  ))}
                </CardBody>
              </Card>
            )}

            {activeTab === 'logs' && (
              <Card className="bg-white/5 border border-white/10">
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-center w-full">
                    <h3 className="text-xl font-semibold text-white">System Logs</h3>
                    <Button className="bg-gray-500 hover:bg-gray-600 text-white">
                      Download Logs
                    </Button>
                  </div>
                </CardHeader>
                <CardBody className="space-y-3">
                  {logs.map((log) => (
                    <div key={log.id} className="flex items-start gap-3 p-3 bg-white/5 rounded-lg">
                      <Chip size="sm" color={getLogLevelColor(log.level)} variant="flat">
                        {log.level.toUpperCase()}
                      </Chip>
                      <div className="flex-1">
                        <div className="text-white text-sm">{log.message}</div>
                        <div className="text-white/60 text-xs">
                          {log.timestamp} • {log.source}
                        </div>
                      </div>
                    </div>
                  ))}
                </CardBody>
              </Card>
            )}
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default SystemPage;
