# Form Components Wireframe
**Complete Form Component Library**

## 📋 Component Information
- **Component Type**: Form Input and Validation Components
- **Usage**: Universal form elements across all platform features
- **Pattern**: Consistent form design language with validation and accessibility
- **Priority**: 🔥 Critical - Required by all features
- **Implementation**: Reusable component library

---

## 🎯 **Design Philosophy**

### **User-Centered Form Design**
- **Clear Visual Hierarchy** - Logical flow and grouping of form elements
- **Immediate Feedback** - Real-time validation and helpful error messages
- **Accessibility First** - Screen reader support and keyboard navigation
- **Mobile Optimization** - Touch-friendly inputs with appropriate keyboards
- **Progressive Enhancement** - Works without JavaScript, enhanced with it

### **Consistency Standards**
- **Unified Styling** - Consistent appearance across all platform forms
- **Predictable Behavior** - Standard interaction patterns users can learn
- **Error Prevention** - Smart defaults and validation to prevent mistakes
- **Recovery Support** - Clear paths to fix errors and complete tasks

---

## 📝 **Input Field Components**

### **Text Input Field**
```
┌─────────────────────────────────────────────────────────────┐
│ Field Label *                                    [?] Help   │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Placeholder text...                                     │ │
│ └─────────────────────────────────────────────────────────┘ │
│ Helper text or validation message                           │
└─────────────────────────────────────────────────────────────┘

States:
┌─────────────────────────────────────────────────────────────┐
│ Default State                                               │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Enter your project name...                              │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│ Focused State                                               │
│ ┌═════════════════════════════════════════════════════════┐ │
│ ║ My Awesome Project|                                     ║ │
│ └═════════════════════════════════════════════════════════┘ │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│ Error State                                                 │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ My Awesome Project                                      │ │
│ └─────────────────────────────────────────────────────────┘ │
│ ❌ Project name must be at least 3 characters              │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│ Success State                                               │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ My Awesome Project                                      │ │
│ └─────────────────────────────────────────────────────────┘ │
│ ✅ Great project name!                                      │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│ Disabled State                                              │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Cannot edit this field                                  │ │
│ └─────────────────────────────────────────────────────────┘ │
│ This field is automatically generated                      │
└─────────────────────────────────────────────────────────────┘
```

### **Textarea Component**
```
┌─────────────────────────────────────────────────────────────┐
│ Description *                                    [?] Help   │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Describe your project in detail...                     │ │
│ │                                                         │ │
│ │                                                         │ │
│ │                                                         │ │
│ └─────────────────────────────────────────────────────────┘ │
│ 0/500 characters • Markdown supported                      │
└─────────────────────────────────────────────────────────────┘

With Content:
┌─────────────────────────────────────────────────────────────┐
│ Project Description *                            [?] Help   │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ This is an innovative web application that will         │ │
│ │ revolutionize how creative teams collaborate on         │ │
│ │ projects. We're looking for experienced developers      │ │
│ │ who are passionate about **fair revenue sharing**.     │ │
│ └─────────────────────────────────────────────────────────┘ │
│ 187/500 characters • Markdown preview available           │
└─────────────────────────────────────────────────────────────┘
```

### **Select Dropdown Component**
```
┌─────────────────────────────────────────────────────────────┐
│ Project Category *                               [?] Help   │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Choose a category...                                 ▼ │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘

Expanded State:
┌─────────────────────────────────────────────────────────────┐
│ Project Category *                               [?] Help   │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Web Development                                      ▲ │ │
│ ├─────────────────────────────────────────────────────────┤ │
│ │ ✓ Web Development                                       │ │
│ │   Mobile App Development                                │ │
│ │   Game Development                                      │ │
│ │   Design & Creative                                     │ │
│ │   Marketing & Content                                   │ │
│ │   Business & Strategy                                   │ │
│ │   Other                                                 │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘

Multi-Select Variant:
┌─────────────────────────────────────────────────────────────┐
│ Required Skills *                                [?] Help   │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ [React] [TypeScript] [Node.js]                      ▼ │ │
│ └─────────────────────────────────────────────────────────┘ │
│ Selected: 3 skills                                          │
└─────────────────────────────────────────────────────────────┘
```

### **Checkbox Component**
```
Single Checkbox:
┌─────────────────────────────────────────────────────────────┐
│ ☑ I agree to the Terms of Service and Privacy Policy       │
│   Required for account creation                             │
└─────────────────────────────────────────────────────────────┘

Checkbox Group:
┌─────────────────────────────────────────────────────────────┐
│ Notification Preferences                         [?] Help   │
├─────────────────────────────────────────────────────────────┤
│ ☑ Email notifications for new ally requests                │
│ ☑ Push notifications for direct messages                   │
│ ☐ Weekly newsletter with platform updates                  │
│ ☑ SMS notifications for urgent project updates             │
└─────────────────────────────────────────────────────────────┘

States:
☐ Unchecked    ☑ Checked    ☒ Indeterminate    ☐ Disabled
```

### **Radio Button Component**
```
┌─────────────────────────────────────────────────────────────┐
│ Project Type *                                   [?] Help   │
├─────────────────────────────────────────────────────────────┤
│ ● Fixed Price Project                                       │
│   One-time project with agreed upon deliverables           │
│                                                             │
│ ○ Hourly Rate Project                                       │
│   Ongoing work billed by the hour                          │
│                                                             │
│ ○ Revenue Share Project                                     │
│   Earn percentage of project revenue over time             │
└─────────────────────────────────────────────────────────────┘
```

### **File Upload Component**
```
Default State:
┌─────────────────────────────────────────────────────────────┐
│ Project Files                                    [?] Help   │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │                    📁 Upload Files                      │ │
│ │                                                         │ │
│ │         Drag and drop files here or click to browse    │ │
│ │                                                         │ │
│ │     Supported: PDF, DOC, XLS, PNG, JPG (Max 10MB)     │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘

With Files:
┌─────────────────────────────────────────────────────────────┐
│ Project Files (3)                                [?] Help   │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 📄 project-requirements.pdf        2.3MB        [×]    │ │
│ │ 🖼️ mockup-design.png              1.8MB        [×]    │ │
│ │ 📊 budget-breakdown.xlsx           456KB        [×]    │ │
│ └─────────────────────────────────────────────────────────┘ │
│ [+ Add More Files]                                          │
└─────────────────────────────────────────────────────────────┘

Upload Progress:
┌─────────────────────────────────────────────────────────────┐
│ ⏳ Uploading presentation.pptx...                           │
│ ████████████████████████████████████████████████████ 85%   │
│ 8.5MB of 10MB • 3 seconds remaining                        │
└─────────────────────────────────────────────────────────────┘
```

### **Number Input Component**
```
┌─────────────────────────────────────────────────────────────┐
│ Budget Range *                                   [?] Help   │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────┐    to    ┌─────────────────────────┐ │
│ │ $ 5,000            │          │ $ 15,000               │ │
│ └─────────────────────┘          └─────────────────────────┘ │
│ Minimum budget: $1,000 • Maximum budget: $100,000          │
└─────────────────────────────────────────────────────────────┘

With Stepper Controls:
┌─────────────────────────────────────────────────────────────┐
│ Team Size *                                      [?] Help   │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ [-]                    5                         [+]   │ │
│ └─────────────────────────────────────────────────────────┘ │
│ Recommended: 3-7 team members for optimal collaboration    │
└─────────────────────────────────────────────────────────────┘
```

### **Date/Time Input Component**
```
Date Picker:
┌─────────────────────────────────────────────────────────────┐
│ Project Deadline *                               [?] Help   │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ MM/DD/YYYY                                           📅 │ │
│ └─────────────────────────────────────────────────────────┘ │
│ Must be at least 2 weeks from today                        │
└─────────────────────────────────────────────────────────────┘

Date Range Picker:
┌─────────────────────────────────────────────────────────────┐
│ Project Timeline *                               [?] Help   │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────┐    to    ┌─────────────────────────┐ │
│ │ 01/20/2025       📅 │          │ 03/15/2025           📅 │ │
│ └─────────────────────┘          └─────────────────────────┘ │
│ Duration: 8 weeks • Estimated completion: March 15, 2025    │
└─────────────────────────────────────────────────────────────┘

Time Picker:
┌─────────────────────────────────────────────────────────────┐
│ Meeting Time                                     [?] Help   │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 02:30 PM                                             🕐 │ │
│ └─────────────────────────────────────────────────────────┘ │
│ Time zone: PST (UTC-8)                                      │
└─────────────────────────────────────────────────────────────┘
```

### **Slider/Range Component**
```
Single Value Slider:
┌─────────────────────────────────────────────────────────────┐
│ Difficulty Level *                               [?] Help   │
├─────────────────────────────────────────────────────────────┤
│ Beginner    ●━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ Expert │
│             ├─────┼─────┼─────┼─────┼─────┼─────┼─────┤     │
│             1     2     3     4     5     6     7     8     │
│                                                             │
│ Current: Level 3 (Intermediate)                             │
└─────────────────────────────────────────────────────────────┘

Range Slider:
┌─────────────────────────────────────────────────────────────┐
│ Experience Range                                 [?] Help   │
├─────────────────────────────────────────────────────────────┤
│ 0 years   ●━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━●   15+ years │
│           ├─────┼─────┼─────┼─────┼─────┼─────┼─────┤       │
│           0     2     4     6     8     10    12    15      │
│                                                             │
│ Range: 3-8 years experience                                 │
└─────────────────────────────────────────────────────────────┘
```

### **Toggle Switch Component**
```
┌─────────────────────────────────────────────────────────────┐
│ Public Project                                   [?] Help   │
├─────────────────────────────────────────────────────────────┤
│ Make this project visible to all platform users            │
│                                                             │
│ ●━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━○ │
│ ON                                                      OFF │
│                                                             │
│ ✅ Project will appear in public bounty board               │
└─────────────────────────────────────────────────────────────┘

OFF State:
┌─────────────────────────────────────────────────────────────┐
│ Email Notifications                              [?] Help   │
├─────────────────────────────────────────────────────────────┤
│ Receive email updates for project activities               │
│                                                             │
│ ○━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━● │
│ ON                                                      OFF │
│                                                             │
│ You will not receive email notifications                   │
└─────────────────────────────────────────────────────────────┘
```

---

## 🔍 **Form Validation Components**

### **Inline Validation**
```
Real-time Validation:
┌─────────────────────────────────────────────────────────────┐
│ Email Address *                                  [?] Help   │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ <EMAIL>                                        │ │
│ └─────────────────────────────────────────────────────────┘ │
│ ✅ Valid email format                                       │
└─────────────────────────────────────────────────────────────┘

Password Strength:
┌─────────────────────────────────────────────────────────────┐
│ Password *                                       [?] Help   │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ••••••••••••••••                                        │ │
│ └─────────────────────────────────────────────────────────┘ │
│ ████████████████████████████████████████████████████ Strong │
│ ✅ 8+ chars ✅ Uppercase ✅ Number ✅ Special character     │
└─────────────────────────────────────────────────────────────┘

Field Dependencies:
┌─────────────────────────────────────────────────────────────┐
│ Confirm Password *                               [?] Help   │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ••••••••••••••••                                        │ │
│ └─────────────────────────────────────────────────────────┘ │
│ ✅ Passwords match                                          │
└─────────────────────────────────────────────────────────────┘
```

### **Form Error Summary**
```
┌─────────────────────────────────────────────────────────────┐
│ ❌ Please fix the following errors:                         │
├─────────────────────────────────────────────────────────────┤
│ • Project name is required                                  │
│ • Budget must be at least $1,000                           │
│ • Please select at least one required skill                │
│ • Project deadline must be in the future                   │
└─────────────────────────────────────────────────────────────┘
```

---

## 📱 **Mobile Adaptations**

### **Mobile Form Layout**
```
┌─────────────────────────┐
│ Project Details         │
├─────────────────────────┤
│                         │
│ Project Name *          │
│ ┌─────────────────────┐ │
│ │ Enter name...       │ │
│ └─────────────────────┘ │
│                         │
│ Category *              │
│ ┌─────────────────────┐ │
│ │ Web Development   ▼ │ │
│ └─────────────────────┘ │
│                         │
│ Budget Range *          │
│ ┌─────────────────────┐ │
│ │ $ 5,000             │ │
│ └─────────────────────┘ │
│ to                      │
│ ┌─────────────────────┐ │
│ │ $ 15,000            │ │
│ └─────────────────────┘ │
│                         │
│ [Continue]              │
│                         │
└─────────────────────────┘
```

### **Mobile Input Optimizations**
- **Appropriate Keyboards** - Numeric keypad for numbers, email keyboard for email
- **Touch Targets** - Minimum 44px height for all interactive elements
- **Simplified Layouts** - Single column layout with clear spacing
- **Gesture Support** - Swipe to navigate between form sections
- **Auto-focus** - Automatic focus on first field when form loads
