import React from 'react';
import { But<PERSON> } from '../../components/ui/heroui';

const ButtonTest = () => {
  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-foreground mb-8">Button Test Page</h1>

        <div className="space-y-8">
          <div>
            <h2 className="text-xl font-semibold text-foreground mb-4">Default Buttons</h2>
            <div className="flex gap-4 flex-wrap">
              <Button>Default Button</Button>
              <Button variant="primary">Primary Button</Button>
              <Button variant="secondary">Secondary Button</Button>
              <Button variant="destructive">Destructive Button</Button>
              <Button variant="outline">Outline Button</Button>
              <Button variant="ghost">Ghost But<PERSON></Button>
              <Button variant="link">Link Button</Button>
            </div>
          </div>

          <div>
            <h2 className="text-xl font-semibold text-foreground mb-4">Button Sizes</h2>
            <div className="flex gap-4 items-center flex-wrap">
              <Button size="sm">Small Button</Button>
              <Button size="default">Default Button</Button>
              <Button size="lg">Large Button</Button>
            </div>
          </div>

          <div>
            <h2 className="text-xl font-semibold text-foreground mb-4">Start Page Buttons (Exact Replicas)</h2>
            <div className="flex gap-4 flex-wrap">
              <Button className="w-full" size="lg">
                Create New Project
              </Button>
              <Button variant="secondary" className="w-full" size="lg">
                Access Training
              </Button>
            </div>
          </div>

          <div>
            <h2 className="text-xl font-semibold text-foreground mb-4">CSS Variables Test</h2>
            <div className="space-y-2">
              <div className="p-4 bg-primary text-primary-foreground rounded">
                Primary Background (should be dark)
              </div>
              <div className="p-4 bg-secondary text-secondary-foreground rounded">
                Secondary Background (should be light)
              </div>
              <div className="p-4 bg-background text-foreground border rounded">
                Background (should match page background)
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ButtonTest;
