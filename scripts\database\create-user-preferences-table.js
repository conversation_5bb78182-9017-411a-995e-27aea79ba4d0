// <PERSON><PERSON><PERSON> to create the user_preferences table in Supabase
require('dotenv').config({ path: './client/.env.local' });
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseKey) {
  console.error('Error: SUPABASE_SERVICE_KEY environment variable is not set');
  process.exit(1);
}

console.log('Using Supabase URL:', supabaseUrl);

const supabase = createClient(supabaseUrl, supabaseKey);

// SQL to create the user_preferences table
const createTableSQL = `
-- Create the user_preferences table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.user_preferences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  theme VARCHAR(50) DEFAULT 'light',
  sound_enabled BOOLEAN DEFAULT true,
  feature_flags JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(user_id)
);

-- Add RLS policies if they don't exist
DO $$
BEGIN
  -- Enable RLS
  ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;
  
  -- Policy for users to read their own preferences
  IF NOT EXISTS (
    SELECT FROM pg_policies 
    WHERE tablename = 'user_preferences' 
    AND policyname = 'Users can read their own preferences'
  ) THEN
    CREATE POLICY "Users can read their own preferences" 
    ON public.user_preferences 
    FOR SELECT 
    USING (auth.uid() = user_id);
  END IF;
  
  -- Policy for users to update their own preferences
  IF NOT EXISTS (
    SELECT FROM pg_policies 
    WHERE tablename = 'user_preferences' 
    AND policyname = 'Users can update their own preferences'
  ) THEN
    CREATE POLICY "Users can update their own preferences" 
    ON public.user_preferences 
    FOR UPDATE 
    USING (auth.uid() = user_id);
  END IF;
  
  -- Policy for users to insert their own preferences
  IF NOT EXISTS (
    SELECT FROM pg_policies 
    WHERE tablename = 'user_preferences' 
    AND policyname = 'Users can insert their own preferences'
  ) THEN
    CREATE POLICY "Users can insert their own preferences" 
    ON public.user_preferences 
    FOR INSERT 
    WITH CHECK (auth.uid() = user_id);
  END IF;
END
$$;
`;

// Function to execute SQL
async function executeSQL() {
  try {
    console.log('Creating user_preferences table...');
    
    const { error } = await supabase.rpc('exec_sql', { sql: createTableSQL });
    
    if (error) {
      console.error('Error creating table:', error);
      
      // Try an alternative approach if the RPC method fails
      console.log('Trying alternative approach...');
      
      // First create the table
      const createTableResult = await supabase
        .from('user_preferences')
        .select('id')
        .limit(1);
      
      if (createTableResult.error) {
        console.log('Table does not exist, creating it...');
        
        // Execute raw SQL using the REST API
        const response = await fetch(`${supabaseUrl}/rest/v1/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${supabaseKey}`,
            'apikey': supabaseKey
          },
          body: JSON.stringify({
            query: createTableSQL
          })
        });
        
        if (!response.ok) {
          console.error('Error creating table via REST API:', await response.text());
          return;
        }
        
        console.log('Table created successfully via REST API');
      } else {
        console.log('Table already exists');
      }
    } else {
      console.log('Table created successfully');
    }
    
    // Test the table by inserting a record
    console.log('Testing table by inserting a record...');
    
    const { data: users, error: usersError } = await supabase
      .from('auth.users')
      .select('id')
      .limit(1);
    
    if (usersError) {
      console.error('Error fetching users:', usersError);
      return;
    }
    
    if (users && users.length > 0) {
      const userId = users[0].id;
      
      const { data, error: insertError } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: userId,
          feature_flags: {
            'new-ui': false,
            'tremor-charts': false,
            'tanstack-query': false,
            'new-forms': false,
            'new-auth': false
          },
          updated_at: new Date()
        }, {
          onConflict: 'user_id'
        });
      
      if (insertError) {
        console.error('Error inserting test record:', insertError);
      } else {
        console.log('Test record inserted successfully');
      }
    } else {
      console.log('No users found to test with');
    }
    
    console.log('Done');
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the function
executeSQL();
