import React, { useState, useEffect } from 'react';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

const MilestoneProgress = ({ projectId }) => {
  const [milestones, setMilestones] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    total: 0,
    completed: 0,
    inProgress: 0,
    pending: 0,
    blocked: 0,
    overdue: 0,
    completionPercentage: 0,
    weightedCompletionPercentage: 0
  });
  
  // Fetch milestones
  useEffect(() => {
    const fetchMilestones = async () => {
      if (!projectId) return;
      
      try {
        setLoading(true);
        
        const { data, error } = await supabase
          .from('milestones')
          .select('*')
          .eq('project_id', projectId)
          .order('created_at', { ascending: true });
          
        if (error) throw error;
        
        setMilestones(data || []);
        calculateStats(data || []);
      } catch (error) {
        console.error('Error fetching milestones:', error);
        toast.error('Failed to load milestone progress');
      } finally {
        setLoading(false);
      }
    };
    
    fetchMilestones();
  }, [projectId]);
  
  // Calculate milestone statistics
  const calculateStats = (milestoneData) => {
    if (!milestoneData || milestoneData.length === 0) {
      setStats({
        total: 0,
        completed: 0,
        inProgress: 0,
        pending: 0,
        blocked: 0,
        overdue: 0,
        completionPercentage: 0,
        weightedCompletionPercentage: 0
      });
      return;
    }
    
    const now = new Date();
    
    // Count by status
    const completed = milestoneData.filter(m => m.status === 'completed').length;
    const inProgress = milestoneData.filter(m => m.status === 'in-progress').length;
    const pending = milestoneData.filter(m => m.status === 'pending').length;
    const blocked = milestoneData.filter(m => m.status === 'blocked').length;
    
    // Count overdue milestones
    const overdue = milestoneData.filter(m => 
      m.deadline && 
      new Date(m.deadline) < now && 
      m.status !== 'completed'
    ).length;
    
    // Calculate simple completion percentage
    const total = milestoneData.length;
    const completionPercentage = total > 0 ? Math.round((completed / total) * 100) : 0;
    
    // Calculate weighted completion percentage
    const totalWeight = milestoneData.reduce((sum, m) => sum + (parseInt(m.weight) || 1), 0);
    const completedWeight = milestoneData
      .filter(m => m.status === 'completed')
      .reduce((sum, m) => sum + (parseInt(m.weight) || 1), 0);
    
    const weightedCompletionPercentage = totalWeight > 0 
      ? Math.round((completedWeight / totalWeight) * 100) 
      : 0;
    
    setStats({
      total,
      completed,
      inProgress,
      pending,
      blocked,
      overdue,
      completionPercentage,
      weightedCompletionPercentage
    });
  };
  
  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'var(--success-color)';
      case 'in-progress': return 'var(--primary-color)';
      case 'pending': return 'var(--info-color)';
      case 'blocked': return 'var(--danger-color)';
      default: return 'var(--text-secondary)';
    }
  };
  
  if (loading) {
    return <div className="milestone-progress-loading">Loading progress...</div>;
  }
  
  if (milestones.length === 0) {
    return <div className="milestone-progress-empty">No milestones found for this project.</div>;
  }
  
  return (
    <div className="milestone-progress-container">
      <div className="progress-overview">
        <div className="progress-header">
          <h3>Project Milestone Progress</h3>
          <div className="progress-percentage">
            <span className="percentage-value">{stats.weightedCompletionPercentage}%</span>
            <span className="percentage-label">Complete</span>
          </div>
        </div>
        
        <div className="progress-bar-container">
          <div 
            className="progress-bar" 
            style={{ width: `${stats.weightedCompletionPercentage}%` }}
          ></div>
        </div>
        
        <div className="progress-stats">
          <div className="stat-item">
            <div className="stat-value">{stats.total}</div>
            <div className="stat-label">Total</div>
          </div>
          <div className="stat-item completed">
            <div className="stat-value">{stats.completed}</div>
            <div className="stat-label">Completed</div>
          </div>
          <div className="stat-item in-progress">
            <div className="stat-value">{stats.inProgress}</div>
            <div className="stat-label">In Progress</div>
          </div>
          <div className="stat-item pending">
            <div className="stat-value">{stats.pending}</div>
            <div className="stat-label">Pending</div>
          </div>
          <div className="stat-item blocked">
            <div className="stat-value">{stats.blocked}</div>
            <div className="stat-label">Blocked</div>
          </div>
          {stats.overdue > 0 && (
            <div className="stat-item overdue">
              <div className="stat-value">{stats.overdue}</div>
              <div className="stat-label">Overdue</div>
            </div>
          )}
        </div>
      </div>
      
      <div className="milestone-timeline">
        <h3>Milestone Timeline</h3>
        <div className="timeline-container">
          {milestones.map((milestone, index) => (
            <div 
              key={milestone.id} 
              className={`timeline-item ${milestone.status}`}
              style={{ 
                '--milestone-color': getStatusColor(milestone.status),
                '--milestone-weight': milestone.weight || 1
              }}
            >
              <div className="timeline-marker"></div>
              <div className="timeline-content">
                <div className="timeline-header">
                  <h4 className="timeline-title">{milestone.name}</h4>
                  <div className="timeline-status">{milestone.status}</div>
                </div>
                {milestone.deadline && (
                  <div className="timeline-date">
                    {new Date(milestone.deadline).toLocaleDateString()}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default MilestoneProgress;
