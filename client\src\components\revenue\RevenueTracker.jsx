import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import RevenueEntryForm from './RevenueEntryForm';
import RoyaltyDistributionPreview from './RoyaltyDistributionPreview';
import { Card, CardBody, CardHeader, Button } from '../ui/heroui';

const RevenueTracker = ({ projectId }) => {
  const { currentUser } = useContext(UserContext);
  const [revenue, setRevenue] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingRevenue, setEditingRevenue] = useState(null);
  const [selectedRevenue, setSelectedRevenue] = useState(null);
  const [showDistribution, setShowDistribution] = useState(false);
  const [project, setProject] = useState(null);
  const [userRole, setUserRole] = useState(null);
  const [stats, setStats] = useState({
    totalRevenue: 0,
    totalEntries: 0,
    byCategory: {},
    bySource: {},
    byCurrency: {}
  });
  const [filter, setFilter] = useState('all'); // 'all', 'pending', 'approved', 'distributed'
  const [sortBy, setSortBy] = useState('date_desc'); // 'date_desc', 'date_asc', 'amount_desc', 'amount_asc'

  // Fetch project data and check user role
  useEffect(() => {
    const fetchProjectAndRole = async () => {
      if (!projectId || !currentUser) return;

      try {
        setLoading(true);

        // Fetch project data
        const { data: projectData, error: projectError } = await supabase
          .from('projects')
          .select('*')
          .eq('id', projectId)
          .single();

        if (projectError) throw projectError;

        setProject(projectData);

        // Check user's role in the project
        const { data: contributorData, error: contributorError } = await supabase
          .from('project_contributors')
          .select('*')
          .eq('project_id', projectId)
          .eq('user_id', currentUser.id)
          .single();

        if (contributorError && contributorError.code !== 'PGRST116') {
          // PGRST116 is "no rows returned" error, which is expected if user is not a contributor
          throw contributorError;
        }

        // Set user role
        if (contributorData) {
          setUserRole(contributorData.is_admin ? 'admin' : 'contributor');
        } else if (projectData.created_by === currentUser.id) {
          setUserRole('admin'); // Project creator is always an admin
        } else {
          setUserRole('viewer');
        }
      } catch (error) {
        console.error('Error fetching project data:', error);
        toast.error('Failed to load project data');
      } finally {
        setLoading(false);
      }
    };

    fetchProjectAndRole();
  }, [projectId, currentUser]);

  // Simplified function to fetch revenue data directly
  const fetchRevenueData = async (projectId, filterOptions, sortOptions) => {
    try {
      console.log(`Fetching revenue data for project ${projectId}`);

      // Direct query to revenue_entries table
      let query = supabase
        .from('revenue_entries')
        .select('*')
        .eq('project_id', projectId);

      console.log('Query:', query);

      // Apply filter
      if (filterOptions.filter !== 'all') {
        if (filterOptions.filter === 'distributed') {
          query = query.eq('distribution_status', 'completed');
        } else {
          query = query.eq('status', filterOptions.filter);
        }
      }

      // Apply sorting
      switch (sortOptions.sortBy) {
        case 'date_asc':
          query = query.order('date_received', { ascending: true });
          break;
        case 'amount_desc':
          query = query.order('amount', { ascending: false });
          break;
        case 'amount_asc':
          query = query.order('amount', { ascending: true });
          break;
        case 'date_desc':
        default:
          query = query.order('date_received', { ascending: false });
          break;
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching revenue data:', error);
        throw error;
      }

      console.log(`Found ${data ? data.length : 0} entries in revenue_entries`);

      // Process the data to ensure all required fields exist
      const processedData = (data || []).map(entry => {
        return {
          ...entry,
          // Ensure these fields exist with default values if missing
          source: entry.source || 'Unknown',
          category: entry.category || 'Other',
          distribution_status: entry.distribution_status || 'pending',
          in_escrow: entry.in_escrow || false
        };
      });

      return { data: processedData, error: null };
    } catch (error) {
      console.error('Error in fetchRevenueData:', error);
      return { data: [], error };
    }
  };

  // Fetch revenue data
  useEffect(() => {
    const fetchRevenue = async () => {
      if (!projectId) return;

      try {
        setLoading(true);

        // Fetch revenue data directly
        const { data, error } = await fetchRevenueData(
          projectId,
          { filter },
          { sortBy }
        );

        if (error) throw error;

        console.log('Setting revenue data:', data);
        setRevenue(data || []);

        // Calculate stats
        calculateStats(data);
      } catch (error) {
        console.error('Error fetching revenue data:', error);
        toast.error('Failed to load revenue data');
      } finally {
        setLoading(false);
      }
    };

    fetchRevenue();
  }, [projectId, filter, sortBy]);

  // Calculate revenue statistics
  const calculateStats = (revenueData) => {
    if (!revenueData || revenueData.length === 0) {
      setStats({
        totalRevenue: 0,
        totalEntries: 0,
        byCategory: {},
        bySource: {},
        byCurrency: {}
      });
      return;
    }

    const totalEntries = revenueData.length;

    // Group by currency
    const byCurrency = revenueData.reduce((acc, entry) => {
      const currency = entry.currency;
      if (!acc[currency]) {
        acc[currency] = 0;
      }

      acc[currency] += parseFloat(entry.amount);

      return acc;
    }, {});

    // Group by category
    const byCategory = revenueData.reduce((acc, entry) => {
      const category = entry.category;
      if (!acc[category]) {
        acc[category] = {
          count: 0,
          amount: 0,
          currency: entry.currency
        };
      }

      acc[category].count += 1;
      acc[category].amount += parseFloat(entry.amount);

      return acc;
    }, {});

    // Group by source
    const bySource = revenueData.reduce((acc, entry) => {
      const source = entry.source;
      if (!acc[source]) {
        acc[source] = {
          count: 0,
          amount: 0,
          currency: entry.currency
        };
      }

      acc[source].count += 1;
      acc[source].amount += parseFloat(entry.amount);

      return acc;
    }, {});

    // Calculate total revenue (simplified - just sum up USD for now)
    const totalRevenue = revenueData
      .filter(entry => entry.currency === 'USD')
      .reduce((sum, entry) => sum + parseFloat(entry.amount), 0);

    setStats({
      totalRevenue,
      totalEntries,
      byCategory,
      bySource,
      byCurrency
    });
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Format currency for display
  const formatCurrency = (amount, currency = 'USD') => {
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });

    return formatter.format(amount);
  };

  // Handle form submission success
  const handleFormSuccess = () => {
    setShowForm(false);
    setEditingRevenue(null);

    // Refresh revenue data
    fetchRevenue();
  };

  // Handle edit button click
  const handleEdit = (revenueEntry) => {
    setEditingRevenue(revenueEntry);
    setShowForm(true);

    // Scroll to form
    setTimeout(() => {
      document.getElementById('revenue-form-section')?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  // Handle delete button click
  const handleDelete = async (revenueId) => {
    if (!confirm('Are you sure you want to delete this revenue entry?')) return;

    try {
      console.log(`Deleting revenue entry with ID: ${revenueId}`);

      // Delete directly from revenue_entries
      const { error } = await supabase
        .from('revenue_entries')
        .delete()
        .eq('id', revenueId);

      if (error) {
        console.error('Error deleting revenue entry:', error);
        throw error;
      }

      toast.success('Revenue entry deleted successfully');

      // Refresh revenue data
      setRevenue(prev => prev.filter(r => r.id !== revenueId));

      // Recalculate stats
      calculateStats(revenue.filter(r => r.id !== revenueId));
    } catch (error) {
      console.error('Error deleting revenue entry:', error);
      toast.error('Failed to delete revenue entry');
    }
  };

  // Handle status update
  const handleStatusUpdate = async (revenueId, newStatus) => {
    try {
      console.log(`Updating status for revenue entry ${revenueId} to ${newStatus}`);

      // Update directly in revenue_entries
      const { error } = await supabase
        .from('revenue_entries')
        .update({ status: newStatus })
        .eq('id', revenueId);

      if (error) {
        console.error('Error updating status:', error);
        throw error;
      }

      toast.success(`Revenue status updated to ${newStatus}`);

      // Update local state
      setRevenue(prev => prev.map(r =>
        r.id === revenueId ? { ...r, status: newStatus } : r
      ));
    } catch (error) {
      console.error('Error updating revenue status:', error);
      toast.error('Failed to update revenue status');
    }
  };

  // Handle distribution status update
  const handleDistributionUpdate = async (revenueId, newStatus) => {
    try {
      console.log(`Updating distribution status for revenue entry ${revenueId} to ${newStatus}`);

      const updateData = {
        distribution_status: newStatus,
        ...(newStatus === 'completed' ? { distributed_at: new Date().toISOString() } : {})
      };

      // Update directly in revenue_entries
      const { error } = await supabase
        .from('revenue_entries')
        .update(updateData)
        .eq('id', revenueId);

      if (error) {
        console.error('Error updating distribution status:', error);
        throw error;
      }

      toast.success(`Distribution status updated to ${newStatus}`);

      // Update local state
      setRevenue(prev => prev.map(r =>
        r.id === revenueId ? {
          ...r,
          distribution_status: newStatus,
          ...(newStatus === 'completed' ? { distributed_at: new Date().toISOString() } : {})
        } : r
      ));
    } catch (error) {
      console.error('Error updating distribution status:', error);
      toast.error('Failed to update distribution status');
    }
  };

  // Handle distribution calculation
  const handleDistributionCalculation = (revenueEntry) => {
    setSelectedRevenue(revenueEntry);
    setShowDistribution(true);

    // Scroll to distribution section
    setTimeout(() => {
      document.getElementById('distribution-section')?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  // Handle distribution save
  const handleDistributionSave = () => {
    // Refresh revenue data
    fetchRevenue();

    // Hide distribution form after a delay
    setTimeout(() => {
      setShowDistribution(false);
      setSelectedRevenue(null);
    }, 2000);
  };

  // Fetch revenue data (for refreshing)
  const fetchRevenue = async () => {
    if (!projectId) return;

    try {
      setLoading(true);

      // Fetch revenue data directly
      const { data, error } = await fetchRevenueData(
        projectId,
        { filter },
        { sortBy }
      );

      if (error) throw error;

      console.log('Refreshing revenue data:', data);
      setRevenue(data || []);

      // Calculate stats
      calculateStats(data);
    } catch (error) {
      console.error('Error fetching revenue data:', error);
      toast.error('Failed to load revenue data');
    } finally {
      setLoading(false);
    }
  };

  if (loading && revenue.length === 0) {
    return <div className="loading-container">Loading revenue data...</div>;
  }

  return (
    <div className="revenue-tracker">
      <div className="revenue-tracker-header">
        <h2 className="section-title">Revenue Tracking</h2>

        {userRole === 'admin' && (
          <button
            className="add-revenue-button"
            onClick={() => {
              setShowForm(!showForm);
              setEditingRevenue(null);
            }}
          >
            {showForm ? (
              <>
                <i className="bi bi-x-lg"></i> Cancel
              </>
            ) : (
              <>
                <i className="bi bi-plus-lg"></i> Add Revenue
              </>
            )}
          </button>
        )}
      </div>

      {/* Revenue Stats */}
      <div className="revenue-stats">
        <div className="stat-card">
          <div className="stat-value">{formatCurrency(stats.totalRevenue)}</div>
          <div className="stat-label">Total Revenue (USD)</div>
        </div>

        <div className="stat-card">
          <div className="stat-value">{stats.totalEntries}</div>
          <div className="stat-label">Total Entries</div>
        </div>

        {Object.entries(stats.byCurrency).map(([currency, amount]) => (
          <div className="stat-card" key={currency}>
            <div className="stat-value">{formatCurrency(amount, currency)}</div>
            <div className="stat-label">Total in {currency}</div>
          </div>
        ))}
      </div>

      {/* Revenue Form */}
      {showForm && userRole === 'admin' && (
        <div id="revenue-form-section" className="revenue-form-section">
          <h3 className="section-subtitle">
            {editingRevenue ? 'Edit Revenue Entry' : 'Add New Revenue Entry'}
          </h3>

          <RevenueEntryForm
            projectId={projectId}
            onSuccess={handleFormSuccess}
            onCancel={() => {
              setShowForm(false);
              setEditingRevenue(null);
            }}
            initialData={editingRevenue}
          />
        </div>
      )}

      {/* Revenue List */}
      <div className="revenue-list-section">
        <div className="revenue-list-header">
          <h3 className="section-subtitle">Revenue Entries</h3>

          <div className="revenue-filters">
            <div className="filter-group">
              <label htmlFor="filter">Filter:</label>
              <select
                id="filter"
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
                className="filter-select"
              >
                <option value="all">All Entries</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="distributed">Distributed</option>
              </select>
            </div>

            <div className="filter-group">
              <label htmlFor="sortBy">Sort By:</label>
              <select
                id="sortBy"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="filter-select"
              >
                <option value="date_desc">Date (Newest First)</option>
                <option value="date_asc">Date (Oldest First)</option>
                <option value="amount_desc">Amount (Highest First)</option>
                <option value="amount_asc">Amount (Lowest First)</option>
              </select>
            </div>
          </div>
        </div>

        {revenue.length === 0 ? (
          <div className="no-revenue">
            <p>No revenue entries found. {userRole === 'admin' ? 'Add your first revenue entry to get started!' : 'Check back later for updates.'}</p>
          </div>
        ) : (
          <div className="revenue-list">
            {revenue.map(entry => (
              <div key={entry.id} className={`revenue-card ${entry.status}`}>
                <div className="revenue-header">
                  <div className="revenue-amount">{formatCurrency(entry.amount, entry.currency)}</div>
                  <div className="revenue-meta">
                    <span className="revenue-date">{formatDate(entry.date_received)}</span>
                    <span className={`revenue-status ${entry.status}`}>{entry.status}</span>
                  </div>
                </div>

                <div className="revenue-details">
                  <div className="revenue-source-category">
                    <span className="revenue-source">{entry.source}</span>
                    <span className="revenue-category">{entry.category}</span>
                  </div>

                  <div className="revenue-distribution-status">
                    <span className="distribution-label">Distribution:</span>
                    <span className={`distribution-value ${entry.distribution_status}`}>
                      {entry.distribution_status}
                    </span>
                  </div>

                  {entry.in_escrow && (
                    <div className="revenue-escrow-status">
                      <span className="escrow-label">Status:</span>
                      <span className="escrow-value">
                        In Escrow
                      </span>
                    </div>
                  )}
                </div>

                {entry.description && (
                  <div className="revenue-description">
                    {entry.description}
                  </div>
                )}

                <div className="revenue-footer">
                  {entry.has_receipt && (
                    <a
                      href={entry.receipt_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="receipt-link"
                    >
                      <i className="bi bi-file-earmark-text"></i>
                      <span>View Receipt</span>
                    </a>
                  )}

                  {/* Actions - only show for admins */}
                  {userRole === 'admin' && (
                    <div className="revenue-actions">
                      <button
                        className="action-button edit"
                        onClick={() => handleEdit(entry)}
                        title="Edit"
                      >
                        <i className="bi bi-pencil"></i>
                      </button>

                      {entry.status === 'pending' && (
                        <button
                          className="action-button approve"
                          onClick={() => handleStatusUpdate(entry.id, 'approved')}
                          title="Approve"
                        >
                          <i className="bi bi-check-lg"></i>
                        </button>
                      )}

                      {entry.status === 'approved' && entry.distribution_status === 'pending' && (
                        <button
                          className="action-button distribute"
                          onClick={() => handleDistributionUpdate(entry.id, 'in_progress')}
                          title="Start Distribution"
                        >
                          <i className="bi bi-cash-coin"></i>
                        </button>
                      )}

                      {entry.status === 'approved' && entry.distribution_status === 'pending' && (
                        <button
                          className="action-button distribute"
                          onClick={() => handleDistributionCalculation(entry)}
                          title="Quick Distribution"
                        >
                          <i className="bi bi-calculator"></i>
                        </button>
                      )}

                      {entry.status === 'approved' && entry.distribution_status === 'pending' && (
                        <a
                          href={`/project/${projectId}/royalty-calculator/${entry.id}`}
                          className="action-button advanced-calculate"
                          title="Advanced Calculator"
                        >
                          <i className="bi bi-gear-fill"></i>
                        </a>
                      )}

                      {entry.distribution_status === 'in_progress' && (
                        <button
                          className="action-button complete"
                          onClick={() => handleDistributionUpdate(entry.id, 'completed')}
                          title="Complete Distribution"
                        >
                          <i className="bi bi-check-circle"></i>
                        </button>
                      )}

                      <button
                        className="action-button delete"
                        onClick={() => handleDelete(entry.id)}
                        title="Delete"
                      >
                        <i className="bi bi-trash"></i>
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Royalty Distribution Preview */}
      {showDistribution && selectedRevenue && (
        <div id="distribution-section" className="distribution-section">
          <h3 className="section-subtitle">
            Calculate Distribution for {formatCurrency(selectedRevenue.amount, selectedRevenue.currency)}
          </h3>

          <RoyaltyDistributionPreview
            projectId={projectId}
            revenueId={selectedRevenue.id}
            revenueAmount={selectedRevenue.amount}
            revenueCurrency={selectedRevenue.currency}
            onDistributionSave={handleDistributionSave}
          />
        </div>
      )}
    </div>
  );
};

export default RevenueTracker;
