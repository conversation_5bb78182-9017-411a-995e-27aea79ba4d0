import React, { useState, useEffect, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Card,
  CardBody,
  CardHeader,
  Progress,
  Chip,
  Button,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Input,
  Textarea,
  DatePicker,
  Select,
  SelectItem
} from '@heroui/react';
import { UserContext } from '../../../contexts/supabase-auth.context.jsx';
import { useDataSync } from '../../contexts/DataSyncContext';
import { supabase } from '../../../utils/supabase/supabase.utils.js';
import { toast } from 'react-hot-toast';

/**
 * Milestone Tracker Component
 *
 * Provides comprehensive milestone tracking with progress indicators,
 * deadline management, and real-time updates.
 */

const MilestoneTracker = ({ projectId, className = "" }) => {
  const { currentUser } = useContext(UserContext);
  const { syncTriggers, triggerProjectSync } = useDataSync();

  const [milestones, setMilestones] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingMilestone, setEditingMilestone] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    deadline: null,
    status: 'pending',
    completion_percentage: 0
  });

  // Load milestones
  const loadMilestones = async () => {
    if (!projectId) return;

    try {
      setLoading(true);

      const { data, error } = await supabase
        .from('milestones')
        .select('*')
        .eq('project_id', projectId)
        .order('deadline', { ascending: true });

      if (error) throw error;
      setMilestones(data || []);
    } catch (error) {
      console.error('Error loading milestones:', error);
      toast.error('Failed to load milestones');
    } finally {
      setLoading(false);
    }
  };

  // Save milestone
  const saveMilestone = async () => {
    if (!formData.name.trim()) {
      toast.error('Milestone name is required');
      return;
    }

    try {
      const milestoneData = {
        ...formData,
        project_id: projectId,
        updated_at: new Date().toISOString()
      };

      if (editingMilestone) {
        // Update existing milestone
        const { error } = await supabase
          .from('milestones')
          .update(milestoneData)
          .eq('id', editingMilestone.id);

        if (error) throw error;
        toast.success('Milestone updated successfully');
      } else {
        // Create new milestone
        milestoneData.created_at = new Date().toISOString();

        const { error } = await supabase
          .from('milestones')
          .insert([milestoneData]);

        if (error) throw error;
        toast.success('Milestone created successfully');
      }

      triggerProjectSync();
      loadMilestones();
      closeModal();
    } catch (error) {
      console.error('Error saving milestone:', error);
      toast.error('Failed to save milestone');
    }
  };

  // Delete milestone
  const deleteMilestone = async (milestoneId) => {
    if (!confirm('Are you sure you want to delete this milestone?')) return;

    try {
      const { error } = await supabase
        .from('milestones')
        .delete()
        .eq('id', milestoneId);

      if (error) throw error;

      toast.success('Milestone deleted successfully');
      triggerProjectSync();
      loadMilestones();
    } catch (error) {
      console.error('Error deleting milestone:', error);
      toast.error('Failed to delete milestone');
    }
  };

  // Open modal for editing/creating
  const openModal = (milestone = null) => {
    if (milestone) {
      setEditingMilestone(milestone);
      setFormData({
        name: milestone.name || '',
        description: milestone.description || '',
        deadline: milestone.deadline ? new Date(milestone.deadline) : null,
        status: milestone.status || 'pending',
        completion_percentage: milestone.completion_percentage || 0
      });
    } else {
      setEditingMilestone(null);
      setFormData({
        name: '',
        description: '',
        deadline: null,
        status: 'pending',
        completion_percentage: 0
      });
    }
    setIsModalOpen(true);
  };

  // Close modal
  const closeModal = () => {
    setIsModalOpen(false);
    setEditingMilestone(null);
    setFormData({
      name: '',
      description: '',
      deadline: null,
      status: 'pending',
      completion_percentage: 0
    });
  };

  // Calculate overall progress
  const calculateOverallProgress = () => {
    if (milestones.length === 0) return 0;

    const totalProgress = milestones.reduce((sum, m) => sum + (m.completion_percentage || 0), 0);
    return totalProgress / milestones.length;
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'in_progress': return 'primary';
      case 'overdue': return 'danger';
      case 'pending': return 'default';
      default: return 'default';
    }
  };

  // Check if milestone is overdue
  const isOverdue = (milestone) => {
    if (!milestone.deadline || milestone.status === 'completed') return false;
    return new Date(milestone.deadline) < new Date();
  };

  // Load data on mount and when sync triggers change
  useEffect(() => {
    loadMilestones();
  }, [projectId, syncTriggers.projects]);

  if (loading) {
    return (
      <div className={`p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-default-200 rounded w-1/3"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-16 bg-default-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <Card>
        <CardHeader className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-semibold">Project Milestones</h3>
            <p className="text-sm text-muted-foreground">
              Track progress and deadlines
            </p>
          </div>
          <Button
            color="primary"
            onClick={() => openModal()}
            size="sm"
          >
            Add Milestone
          </Button>
        </CardHeader>
        <CardBody>
          {/* Overall Progress */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium">Overall Progress</span>
              <span className="text-sm text-muted-foreground">
                {calculateOverallProgress().toFixed(0)}%
              </span>
            </div>
            <Progress
              value={calculateOverallProgress()}
              color="primary"
              className="mb-2"
            />
            <p className="text-xs text-muted-foreground">
              {milestones.filter(m => m.status === 'completed').length} of {milestones.length} milestones completed
            </p>
          </div>

          {/* Milestones List */}
          <div className="space-y-4">
            <AnimatePresence>
              {milestones.map((milestone) => (
                <motion.div
                  key={milestone.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card className="border border-divider">
                    <CardBody className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h4 className="font-medium">{milestone.name}</h4>
                            <Chip
                              size="sm"
                              color={getStatusColor(isOverdue(milestone) ? 'overdue' : milestone.status)}
                              variant="flat"
                            >
                              {isOverdue(milestone) ? 'Overdue' : milestone.status}
                            </Chip>
                          </div>

                          {milestone.description && (
                            <p className="text-sm text-muted-foreground mb-3">
                              {milestone.description}
                            </p>
                          )}

                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            {milestone.deadline && (
                              <span>
                                📅 {new Date(milestone.deadline).toLocaleDateString()}
                              </span>
                            )}
                            <span>
                              {milestone.completion_percentage || 0}% complete
                            </span>
                          </div>

                          {/* Progress Bar */}
                          <Progress
                            value={milestone.completion_percentage || 0}
                            color={getStatusColor(milestone.status)}
                            size="sm"
                            className="mt-3"
                          />
                        </div>

                        <div className="flex items-center gap-2 ml-4">
                          <Button
                            size="sm"
                            variant="light"
                            onClick={() => openModal(milestone)}
                          >
                            Edit
                          </Button>
                          <Button
                            size="sm"
                            variant="light"
                            color="danger"
                            onClick={() => deleteMilestone(milestone.id)}
                          >
                            Delete
                          </Button>
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>

            {milestones.length === 0 && (
              <div className="text-center py-8">
                <p className="text-muted-foreground mb-4">No milestones yet</p>
                <Button
                  color="primary"
                  variant="flat"
                  onClick={() => openModal()}
                >
                  Create your first milestone
                </Button>
              </div>
            )}
          </div>
        </CardBody>
      </Card>

      {/* Milestone Modal */}
      <Modal isOpen={isModalOpen} onClose={closeModal} size="lg">
        <ModalContent>
          <ModalHeader>
            {editingMilestone ? 'Edit Milestone' : 'Create Milestone'}
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <Input
                label="Milestone Name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Enter milestone name"
                isRequired
              />

              <Textarea
                label="Description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Describe this milestone"
              />

              <DatePicker
                label="Deadline"
                value={formData.deadline}
                onChange={(date) => setFormData({ ...formData, deadline: date })}
              />

              <Select
                label="Status"
                selectedKeys={[formData.status]}
                onSelectionChange={(keys) => setFormData({ ...formData, status: Array.from(keys)[0] })}
              >
                <SelectItem key="pending" value="pending">Pending</SelectItem>
                <SelectItem key="in_progress" value="in_progress">In Progress</SelectItem>
                <SelectItem key="completed" value="completed">Completed</SelectItem>
              </Select>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Completion: {formData.completion_percentage}%
                </label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={formData.completion_percentage}
                  onChange={(e) => setFormData({ ...formData, completion_percentage: parseInt(e.target.value) })}
                  className="w-full"
                />
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onClick={closeModal}>
              Cancel
            </Button>
            <Button color="primary" onClick={saveMilestone}>
              {editingMilestone ? 'Update' : 'Create'}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default MilestoneTracker;
