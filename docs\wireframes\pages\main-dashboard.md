# Main Dashboard Wireframe
**Primary User Interface - Command Center**

## 📋 Page Information
- **Page Type**: Primary Dashboard
- **User Access**: All authenticated users
- **Navigation**: Entry point after login
- **Purpose**: Central hub for all platform activities
- **Layout**: Spatial navigation with dual-view system
- **Gamification**: Optional elements with professional toggle
- **Navigation**: Standard input priority with experimental spatial enhancement

---

## 🎯 **Dashboard Overview**

The main dashboard serves as the **command center** for the Royaltea platform, implementing the dual-view navigation system (grid view ↔ overworld view) that replaces traditional page navigation.

### **Core Functionality**
- **Spatial Navigation**: 3D-style movement through content areas
- **Dual View System**: Grid view (zoomed out) ↔ Overworld view (zoomed in)
- **Contextual Information**: Section details fade in on zoom
- **Drag Navigation**: Move view without triggering selection
- **Memory System**: Remembers origin for proper back navigation

---

## 📱 **Desktop Layout Wireframe**

### **Bento Grid Dashboard (Varied Widget Sizes)**
```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│  ← Spatial Nav                                          🏰 ROYALTEA KINGDOM         │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│  ┌─────────────────────────────────────────────────────┐  ┌─────────────────────┐ │
│  │                  🚀 ACTIVE VENTURES (4×2)            │  │   💰 TREASURY (2×2) │ │
│  ├─────────────────────────────────────────────────────┤  ├─────────────────────┤ │
│  │                                                     │  │                     │ │
│  │  ⚔️ TaskMaster Pro                                  │  │  $2,847.50 USDC     │ │
│  │  ████████████████████░░ 85% • 8 members             │  │  15,420 ORB Points  │ │
│  │  💰 $18,400 revenue • 🎯 Beta Testing               │  │                     │ │
│  │                                                     │  │  📈 +$1,240 month   │ │
│  │  🎨 Creative Studio                                 │  │  💎 +2,400 ORB      │ │
│  │  ████████████░░░░░░░░░░ 60% • 6 members             │  │                     │ │
│  │  💰 $8,200 revenue • 🎯 Development                 │  │  [Wallet]           │ │
│  │                                                     │  │  [Trade ORB]        │ │
│  │  🧪 Testing Tool                                    │  │                     │ │
│  │  ████████░░░░░░░░░░░░░░ 40% • 4 members             │  │                     │ │
│  │  💰 $3,600 revenue • 🎯 Early Stage                 │  │                     │ │
│  │                                                     │  │                     │ │
│  │  [View All (5)] [Create] [Analytics]                │  │                     │ │
│  │                                                     │  │                     │ │
│  └─────────────────────────────────────────────────────┘  └─────────────────────┘ │
│                                                                                     │
│  ┌─────────────────────────┐  ┌─────────────────────────┐  ┌─────────────────────┐ │
│  │   👥 ALLIANCES (2×1)    │  │    ⚔️ MISSIONS (2×1)    │  │   🎯 BOUNTIES (2×1) │ │
│  ├─────────────────────────┤  ├─────────────────────────┤  ├─────────────────────┤ │
│  │                         │  │                         │  │                     │ │
│  │  3 Active Teams         │  │  12 Active Tasks        │  │  8 Open Tasks       │ │
│  │  🔥 Dream Team (4/5)    │  │  ⚡ 3 Due Today         │  │  💰 $500 React Bug  │ │
│  │  ⭐ Code Wizards (3/8)  │  │  🎯 2 In Review         │  │  🎨 $300 Logo Fix   │ │
│  │  [View All] [Create]    │  │  [View All] [Start]     │  │  [Browse] [Post]    │ │
│  │                         │  │                         │  │                     │ │
│  └─────────────────────────┘  └─────────────────────────┘  └─────────────────────┘ │
│                                                                                     │
│  ┌─────────────────────────────────────────────────────┐  ┌─────────┐ ┌─────────┐ │
│  │              🤝 ALLY NETWORK (4×1)                  │  │🏆 RANK  │ │⚡ STREAK │ │
│  ├─────────────────────────────────────────────────────┤  │ (1×1)   │ │ (1×1)   │ │
│  │                                                     │  ├─────────┤ ├─────────┤ │
│  │  23 Connections • 🌟 Sarah_Dev (5.0★) • 🔥 Mike_PM  │  │         │ │         │ │
│  │  (4.8★) • ⚡ Alex_UI (4.9★) • +20 more allies       │  │  #47    │ │12 days  │ │
│  │                                                     │  │         │ │         │ │
│  │  [Network] [Invite] [Messages] [Directory]          │  │         │ │         │ │
│  │                                                     │  │         │ │         │ │
│  └─────────────────────────────────────────────────────┘  └─────────┘ └─────────┘ │
│                                                                                     │
│  ┌─────────────────────────┐  ┌─────────────────────────┐  ┌─────────────────────┐ │
│  │   📊 ANALYTICS (2×1)    │  │    🎓 LEARNING (2×1)    │  │   ⚡ ACTIONS (2×1)   │ │
│  ├─────────────────────────┤  ├─────────────────────────┤  ├─────────────────────┤ │
│  │                         │  │                         │  │                     │ │
│  │  ████████░░ 82% Score   │  │  3 Courses Active       │  │  📝 Create Project  │ │
│  │  🎯 94% success rate    │  │  🚀 React Advanced 65%  │  │  👥 Invite Ally     │ │
│  │  💰 +15% revenue MoM    │  │  🎓 TypeScript Pro 40%  │  │  🎯 Browse Missions │ │
│  │  [Deep Dive] [Reports]  │  │  [Continue] [Browse]    │  │  📊 View Reports    │ │
│  │                         │  │                         │  │                     │ │
│  └─────────────────────────┘  └─────────────────────────┘  └─────────────────────┘ │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

### **Overworld View (Zoomed In)**
```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│  ← Grid View                                                           [?] Help      │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│                              VENTURES REALM                                        │
│                                                                                     │
│  ┌─────────────────────────────────────────────────────────────────────────────┐  │
│  │                                                                             │  │
│  │    🏰 Project Alpha                    🏗️ Project Beta                     │  │
│  │    ├─ 5 Active Missions                ├─ 3 Active Missions                │  │
│  │    ├─ 8 Contributors                   ├─ 12 Contributors                  │  │
│  │    └─ $1,200 Revenue                   └─ $850 Revenue                     │  │
│  │                                                                             │  │
│  │                        🌟 New Venture                                      │  │
│  │                        [Create Project]                                    │  │
│  │                                                                             │  │
│  │    🏛️ Project Gamma                   🎯 Project Delta                     │  │
│  │    ├─ 2 Active Missions                ├─ 7 Active Missions                │  │
│  │    ├─ 6 Contributors                   ├─ 15 Contributors                  │  │
│  │    └─ $400 Revenue                     └─ $2,100 Revenue                  │  │
│  │                                                                             │  │
│  └─────────────────────────────────────────────────────────────────────────────┘  │
│                                                                                     │
│  Navigation: Drag to move • Click to enter • Double-click for details             │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

---

## 🎮 **Navigation System Details**

### **Spatial Navigation Controls**
```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                     │
│  ┌─────────┐                                                        ┌─────────┐   │
│  │    ↑    │  Vertical Helper Bar (Left)                           │    ↑    │   │
│  │ ZOOM IN │  • Quick Actions                                       │ CONTEXT │   │
│  │    ↓    │  • Navigation Controls                                 │ ACTIONS │   │
│  │ ZOOM OUT│  • View Toggles                                        │    ↓    │   │
│  └─────────┘                                                        └─────────┘   │
│                                                                                     │
│                              CONTENT AREA                                          │
│                         (Draggable Navigation)                                     │
│                                                                                     │
│  Controls:                                                                          │
│  • Drag: Move view without selection                                               │
│  • Click: Select/Enter section                                                     │
│  • Double-click: Deep dive into content                                            │
│  • Scroll: Zoom in/out                                                             │
│  • ESC: Return to previous view                                                     │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

### **View Transition Animation**
```mermaid
stateDiagram-v2
    [*] --> GridView
    GridView --> OverworldView : Zoom In / Click Section
    OverworldView --> ContentView : Click Item
    ContentView --> OverworldView : Back Button / ESC
    OverworldView --> GridView : Zoom Out / Grid Button
    ContentView --> GridView : Home Button
    
    note right of GridView
        Shows all 8 main sections
        Bento grid layout
        High-level overview
    end note
    
    note right of OverworldView
        Shows section details
        Spatial card layout
        Interactive elements
    end note
    
    note right of ContentView
        Detailed work interface
        Full functionality
        Remembers origin
    end note
```

---

## 🧩 **Component Breakdown**

### **Section Cards (Grid View)**
```
┌─────────────┐
│   SECTION   │  ← Section Title
│             │
│    [ICON]   │  ← Visual Identifier
│             │
│  Status Info│  ← Key Metrics
└─────────────┘
```

**Card States:**
- **Default**: Normal section display
- **Hover**: Subtle highlight with preview info
- **Active**: Currently selected section
- **Loading**: Shimmer effect during data fetch
- **Error**: Error state with retry option

### **Project Cards (Overworld View)**
```
┌─────────────────────────────┐
│ 🏰 Project Name             │  ← Project Icon + Name
├─────────────────────────────┤
│ ├─ 5 Active Missions        │  ← Mission Count
│ ├─ 8 Contributors           │  ← Team Size
│ └─ $1,200 Revenue           │  ← Financial Status
├─────────────────────────────┤
│ [View] [Edit] [Analytics]   │  ← Quick Actions
└─────────────────────────────┘
```

### **Helper Bars**
```
Left Bar (Navigation):          Right Bar (Context):
┌─────────┐                    ┌─────────┐
│ [Home]  │                    │ [Help]  │
│ [Back]  │                    │ [User]  │
│ [Grid]  │                    │ [Notif] │
│ [Search]│                    │ [Theme] │
└─────────┘                    └─────────┘
```

---

## 📱 **Mobile Responsive Layout**

### **Mobile Grid View**
```
┌─────────────────────────┐
│ ROYALTEA           [≡]  │
├─────────────────────────┤
│                         │
│  ┌─────────┐ ┌─────────┐│
│  │ALLIANCES│ │VENTURES ││
│  │   [👥]  │ │  [🏗️]  ││
│  │3 Active │ │5 Active ││
│  └─────────┘ └─────────┘│
│                         │
│  ┌─────────┐ ┌─────────┐│
│  │MISSIONS │ │TREASURY ││
│  │  [⚔️]   │ │  [💰]  ││
│  │12 Active│ │$2,450.00││
│  └─────────┘ └─────────┘│
│                         │
│  ┌─────────┐ ┌─────────┐│
│  │ ALLIES  │ │BOUNTIES ││
│  │  [🤝]   │ │  [🎯]  ││
│  │23 Allies│ │ 8 Open  ││
│  └─────────┘ └─────────┘│
│                         │
│  ┌─────────┐ ┌─────────┐│
│  │ANALYTICS│ │LEARNING ││
│  │  [📊]   │ │  [🎓]  ││
│  │Metrics  │ │3 Courses││
│  └─────────┘ └─────────┘│
│                         │
└─────────────────────────┘
```

### **Mobile Navigation**
- **Hamburger menu** for additional options
- **Swipe gestures** for navigation
- **Touch-optimized** card sizes
- **Bottom navigation** for quick access

---

## 🎨 **Visual Design Requirements**

### **Color System**
- **Primary**: Crown gold (#FFD700) for highlights
- **Secondary**: Royal purple (#6B46C1) for accents
- **Background**: Dark theme with light theme toggle
- **Cards**: Subtle gradients with hover effects

### **Typography**
- **Headers**: Bold, clear hierarchy
- **Body**: Readable, accessible fonts
- **Icons**: Consistent icon family
- **Status**: Color-coded information

### **Animations**
- **Smooth transitions** between views (300ms)
- **Hover effects** on interactive elements
- **Loading animations** for data fetching
- **Zoom transitions** with easing

---

## 🔧 **Technical Implementation**

### **State Management**
- **Current view** (grid/overworld/content)
- **Selected section** tracking
- **Navigation history** for back functionality
- **User preferences** (view settings)

### **Performance**
- **Lazy loading** for section data
- **Virtualization** for large lists
- **Caching** for frequently accessed data
- **Optimistic updates** for user actions

### **Accessibility**
- **Keyboard navigation** support
- **Screen reader** compatibility
- **Focus management** during view transitions
- **High contrast** mode support

---

**This dashboard serves as the central nervous system of the Royaltea platform, providing intuitive access to all platform features through an innovative spatial navigation system.**
