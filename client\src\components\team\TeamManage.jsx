import React, { useState, useEffect, useContext } from 'react';
import { useParams, Link, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { supabase } from '../../../utils/supabase/supabase.utils';
import LoadingAnimation from '../layout/LoadingAnimation';
import TeamPermissions from './TeamPermissions';

/**
 * TeamManage component for managing team settings and members
 */
const TeamManage = () => {
  const { teamId } = useParams();
  const { currentUser } = useContext(UserContext);
  const navigate = useNavigate();
  const [team, setTeam] = useState(null);
  const [members, setMembers] = useState([]);
  const [invitations, setInvitations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isOwner, setIsOwner] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [teamData, setTeamData] = useState({
    name: '',
    description: ''
  });

  // Fetch team data on component mount
  useEffect(() => {
    if (teamId && currentUser) {
      fetchTeamData();
    }
  }, [teamId, currentUser]);

  // Fetch team data, members, and invitations
  const fetchTeamData = async () => {
    try {
      setLoading(true);

      // Fetch team details
      const { data: teamData, error: teamError } = await supabase
        .from('teams')
        .select('*')
        .eq('id', teamId)
        .single();

      if (teamError) throw teamError;
      setTeam(teamData);
      setTeamData({
        name: teamData.name,
        description: teamData.description || ''
      });

      // Fetch team members with user details
      const { data: membersData, error: membersError } = await supabase
        .from('team_members')
        .select(`
          id,
          role,
          is_admin,
          joined_at,
          user:user_id(id, email, user_metadata)
        `)
        .eq('team_id', teamId);

      if (membersError) throw membersError;

      // Process member data
      const processedMembers = membersData.map(member => ({
        id: member.id,
        userId: member.user.id,
        email: member.user.email,
        displayName: member.user.user_metadata?.full_name || member.user.email,
        role: member.role,
        isAdmin: member.is_admin,
        joinedAt: member.joined_at
      }));

      setMembers(processedMembers);

      // Check current user's role in the team
      const currentMember = processedMembers.find(member => member.userId === currentUser.id);
      if (currentMember) {
        setIsAdmin(currentMember.isAdmin);
        setIsOwner(currentMember.role === 'owner');
      } else {
        // If user is not a member, redirect to team detail page
        toast.error('You do not have permission to manage this team');
        navigate(`/teams/${teamId}`);
      }

      // Fetch team invitations
      const { data: invitationsData, error: invitationsError } = await supabase
        .from('team_invitations')
        .select('*')
        .eq('team_id', teamId)
        .order('created_at', { ascending: false });

      if (invitationsError) throw invitationsError;
      setInvitations(invitationsData);
    } catch (error) {
      console.error('Error fetching team data:', error);
      toast.error('Failed to load team data');
      navigate('/teams');
    } finally {
      setLoading(false);
    }
  };

  // Handle updating team information
  const handleUpdateTeam = async (e) => {
    e.preventDefault();

    if (!teamData.name.trim()) {
      toast.error('Team name is required');
      return;
    }

    try {
      setLoading(true);

      const { error } = await supabase
        .from('teams')
        .update({
          name: teamData.name,
          description: teamData.description,
          updated_at: new Date()
        })
        .eq('id', teamId);

      if (error) throw error;

      toast.success('Team updated successfully');
      setEditMode(false);
      fetchTeamData();
    } catch (error) {
      console.error('Error updating team:', error);
      toast.error('Failed to update team');
    } finally {
      setLoading(false);
    }
  };

  // Handle changing member role
  const handleChangeMemberRole = async (memberId, isAdmin) => {
    try {
      setLoading(true);

      const { error } = await supabase
        .from('team_members')
        .update({
          is_admin: isAdmin,
          updated_at: new Date()
        })
        .eq('id', memberId);

      if (error) throw error;

      toast.success(`Member ${isAdmin ? 'promoted to admin' : 'demoted from admin'}`);
      fetchTeamData();
    } catch (error) {
      console.error('Error updating member role:', error);
      toast.error('Failed to update member role');
    } finally {
      setLoading(false);
    }
  };

  // Handle removing a member
  const handleRemoveMember = async (memberId, memberName) => {
    if (!confirm(`Are you sure you want to remove ${memberName} from the team?`)) {
      return;
    }

    try {
      setLoading(true);

      const { error } = await supabase
        .from('team_members')
        .delete()
        .eq('id', memberId);

      if (error) throw error;

      toast.success(`${memberName} removed from team`);
      fetchTeamData();
    } catch (error) {
      console.error('Error removing member:', error);
      toast.error('Failed to remove member');
    } finally {
      setLoading(false);
    }
  };

  // Handle canceling an invitation
  const handleCancelInvitation = async (invitationId) => {
    if (!confirm('Are you sure you want to cancel this invitation?')) {
      return;
    }

    try {
      setLoading(true);

      const { error } = await supabase
        .from('team_invitations')
        .delete()
        .eq('id', invitationId);

      if (error) throw error;

      toast.success('Invitation canceled');
      fetchTeamData();
    } catch (error) {
      console.error('Error canceling invitation:', error);
      toast.error('Failed to cancel invitation');
    } finally {
      setLoading(false);
    }
  };

  if (loading && !team) {
    return <LoadingAnimation />;
  }

  return (
    <div className="team-manage-container">
      <div className="team-manage-header">
        <div className="header-content">
          <h2>Manage Team</h2>
          <Link to={`/teams/${teamId}`} className="back-button">
            Back to Team
          </Link>
        </div>
      </div>

      <div className="team-manage-content">
        <div className="team-info-section">
          <div className="section-header">
            <h3>Team Information</h3>
            {!editMode && (
              <button
                className="edit-button"
                onClick={() => setEditMode(true)}
              >
                Edit
              </button>
            )}
          </div>

          {editMode ? (
            <form onSubmit={handleUpdateTeam} className="edit-team-form">
              <div className="form-group">
                <label htmlFor="team-name">Team Name</label>
                <input
                  id="team-name"
                  type="text"
                  value={teamData.name}
                  onChange={(e) => setTeamData({ ...teamData, name: e.target.value })}
                  placeholder="Enter team name"
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="team-description">Description (optional)</label>
                <textarea
                  id="team-description"
                  value={teamData.description}
                  onChange={(e) => setTeamData({ ...teamData, description: e.target.value })}
                  placeholder="Enter team description"
                  rows="3"
                />
              </div>
              <div className="form-actions">
                <button type="submit" className="save-button">Save Changes</button>
                <button
                  type="button"
                  className="cancel-button"
                  onClick={() => {
                    setEditMode(false);
                    setTeamData({
                      name: team.name,
                      description: team.description || ''
                    });
                  }}
                >
                  Cancel
                </button>
              </div>
            </form>
          ) : (
            <div className="team-info">
              <div className="info-item">
                <span className="info-label">Name:</span>
                <span className="info-value">{team?.name}</span>
              </div>
              <div className="info-item">
                <span className="info-label">Description:</span>
                <span className="info-value">{team?.description || 'No description'}</span>
              </div>
              <div className="info-item">
                <span className="info-label">Created:</span>
                <span className="info-value">{new Date(team?.created_at).toLocaleDateString()}</span>
              </div>
            </div>
          )}
        </div>

        <div className="team-members-section">
          <h3>Team Members ({members.length})</h3>
          <div className="members-list">
            {members.map(member => (
              <div key={member.id} className="member-card">
                <div className="member-info">
                  <div className="member-name">{member.displayName}</div>
                  <div className="member-email">{member.email}</div>
                </div>
                <div className="member-meta">
                  <span className={`role-badge ${member.role}`}>{member.role}</span>
                  {member.isAdmin && <span className="admin-badge">Admin</span>}
                </div>
                <div className="member-actions">
                  {isOwner && member.userId !== currentUser.id && (
                    <>
                      <button
                        className="role-button"
                        onClick={() => handleChangeMemberRole(member.id, !member.isAdmin)}
                      >
                        {member.isAdmin ? 'Remove Admin' : 'Make Admin'}
                      </button>
                      <button
                        className="remove-button"
                        onClick={() => handleRemoveMember(member.id, member.displayName)}
                      >
                        Remove
                      </button>
                    </>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="team-invitations-section">
          <h3>Pending Invitations ({invitations.length})</h3>
          {invitations.length > 0 ? (
            <div className="invitations-list">
              {invitations.map(invitation => (
                <div key={invitation.id} className="invitation-card">
                  <div className="invitation-info">
                    <div className="invitation-email">
                      {invitation.invited_email || 'Unknown email'}
                    </div>
                    <div className="invitation-meta">
                      <span className="invitation-date">
                        Sent: {new Date(invitation.created_at).toLocaleDateString()}
                      </span>
                      <span className="invitation-status">
                        Status: {invitation.status}
                      </span>
                    </div>
                  </div>
                  <div className="invitation-actions">
                    <button
                      className="cancel-invitation-button"
                      onClick={() => handleCancelInvitation(invitation.id)}
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="no-invitations">
              <p>No pending invitations</p>
            </div>
          )}
        </div>

        {/* Team Permissions Section */}
        <div className="team-permissions-section">
          <TeamPermissions
            teamId={teamId}
            isOwner={isOwner}
            onPermissionsUpdated={fetchTeamData}
          />
        </div>
      </div>
    </div>
  );
};

export default TeamManage;
