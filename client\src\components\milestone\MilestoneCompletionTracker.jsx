import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { format } from 'date-fns';

const MilestoneCompletionTracker = ({ milestone, onUpdate }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(false);
  const [deliverableStatus, setDeliverableStatus] = useState([]);
  const [completionEvidence, setCompletionEvidence] = useState([]);
  const [completionPercentage, setCompletionPercentage] = useState(0);
  const [showEvidenceForm, setShowEvidenceForm] = useState(false);
  const [newEvidence, setNewEvidence] = useState({
    title: '',
    description: '',
    link: '',
    type: 'link' // 'link', 'image', 'document'
  });
  const [userRole, setUserRole] = useState(null);

  // Initialize deliverable status from milestone data
  useEffect(() => {
    if (!milestone) return;

    // Check user's role in the project
    const checkUserRole = async () => {
      if (!currentUser) {
        setUserRole('viewer');
        return;
      }

      try {
        const { data, error } = await supabase
          .from('project_contributors')
          .select('is_admin')
          .eq('project_id', milestone.project_id)
          .eq('user_id', currentUser.id)
          .single();
          
        if (!error && data) {
          setUserRole(data.is_admin ? 'admin' : 'contributor');
        } else {
          setUserRole('viewer');
        }
      } catch (error) {
        console.error('Error checking user role:', error);
        setUserRole('viewer');
      }
    };

    checkUserRole();

    // Initialize deliverable status
    if (milestone.deliverables && Array.isArray(milestone.deliverables)) {
      // If deliverable_status exists, use it
      if (milestone.deliverable_status && Array.isArray(milestone.deliverable_status)) {
        setDeliverableStatus(milestone.deliverable_status);
      } else {
        // Otherwise, create default status for each deliverable
        const initialStatus = milestone.deliverables.map(deliverable => ({
          deliverable,
          completed: false,
          completedAt: null,
          completedBy: null
        }));
        setDeliverableStatus(initialStatus);
      }
    }

    // Initialize completion evidence
    if (milestone.completion_evidence && Array.isArray(milestone.completion_evidence)) {
      setCompletionEvidence(milestone.completion_evidence);
    }

    // Initialize completion percentage
    if (typeof milestone.completion_percentage === 'number') {
      setCompletionPercentage(milestone.completion_percentage);
    } else {
      // Calculate from deliverable status
      calculateCompletionPercentage();
    }
  }, [milestone, currentUser]);

  // Calculate completion percentage based on deliverable status
  const calculateCompletionPercentage = () => {
    if (!deliverableStatus || deliverableStatus.length === 0) {
      setCompletionPercentage(0);
      return;
    }

    const completedCount = deliverableStatus.filter(item => item.completed).length;
    const percentage = Math.round((completedCount / deliverableStatus.length) * 100);
    setCompletionPercentage(percentage);
  };

  // Update deliverable status
  useEffect(() => {
    calculateCompletionPercentage();
  }, [deliverableStatus]);

  // Toggle deliverable completion
  const toggleDeliverable = async (index) => {
    if (!currentUser || userRole === 'viewer') return;
    
    try {
      setLoading(true);
      
      const updatedStatus = [...deliverableStatus];
      const isCompleted = !updatedStatus[index].completed;
      
      updatedStatus[index] = {
        ...updatedStatus[index],
        completed: isCompleted,
        completedAt: isCompleted ? new Date().toISOString() : null,
        completedBy: isCompleted ? currentUser.id : null
      };
      
      setDeliverableStatus(updatedStatus);
      
      // Calculate new completion percentage
      const completedCount = updatedStatus.filter(item => item.completed).length;
      const newPercentage = Math.round((completedCount / updatedStatus.length) * 100);
      setCompletionPercentage(newPercentage);
      
      // Update milestone in database
      const { error } = await supabase
        .from('milestones')
        .update({
          deliverable_status: updatedStatus,
          completion_percentage: newPercentage,
          // If all deliverables are completed, update status to completed
          ...(newPercentage === 100 && milestone.status !== 'completed' ? {
            status: 'completed',
            completed_at: new Date().toISOString()
          } : {}),
          updated_at: new Date().toISOString()
        })
        .eq('id', milestone.id);
        
      if (error) throw error;
      
      // Call onUpdate callback
      if (onUpdate) {
        onUpdate({
          ...milestone,
          deliverable_status: updatedStatus,
          completion_percentage: newPercentage,
          ...(newPercentage === 100 && milestone.status !== 'completed' ? {
            status: 'completed',
            completed_at: new Date().toISOString()
          } : {})
        });
      }
      
      toast.success(`Deliverable ${isCompleted ? 'completed' : 'marked as incomplete'}`);
    } catch (error) {
      console.error('Error updating deliverable status:', error);
      toast.error('Failed to update deliverable status');
    } finally {
      setLoading(false);
    }
  };

  // Add completion evidence
  const addCompletionEvidence = async (e) => {
    e.preventDefault();
    
    if (!currentUser || userRole === 'viewer') return;
    if (!newEvidence.title.trim()) {
      toast.error('Please enter a title for the evidence');
      return;
    }
    
    try {
      setLoading(true);
      
      const evidenceItem = {
        ...newEvidence,
        id: Date.now().toString(),
        addedAt: new Date().toISOString(),
        addedBy: currentUser.id
      };
      
      const updatedEvidence = [...completionEvidence, evidenceItem];
      setCompletionEvidence(updatedEvidence);
      
      // Update milestone in database
      const { error } = await supabase
        .from('milestones')
        .update({
          completion_evidence: updatedEvidence,
          updated_at: new Date().toISOString()
        })
        .eq('id', milestone.id);
        
      if (error) throw error;
      
      // Call onUpdate callback
      if (onUpdate) {
        onUpdate({
          ...milestone,
          completion_evidence: updatedEvidence
        });
      }
      
      // Reset form
      setNewEvidence({
        title: '',
        description: '',
        link: '',
        type: 'link'
      });
      setShowEvidenceForm(false);
      
      toast.success('Evidence added successfully');
    } catch (error) {
      console.error('Error adding completion evidence:', error);
      toast.error('Failed to add evidence');
    } finally {
      setLoading(false);
    }
  };

  // Remove completion evidence
  const removeEvidence = async (evidenceId) => {
    if (!currentUser || userRole === 'viewer') return;
    if (!window.confirm('Are you sure you want to remove this evidence?')) return;
    
    try {
      setLoading(true);
      
      const updatedEvidence = completionEvidence.filter(item => item.id !== evidenceId);
      setCompletionEvidence(updatedEvidence);
      
      // Update milestone in database
      const { error } = await supabase
        .from('milestones')
        .update({
          completion_evidence: updatedEvidence,
          updated_at: new Date().toISOString()
        })
        .eq('id', milestone.id);
        
      if (error) throw error;
      
      // Call onUpdate callback
      if (onUpdate) {
        onUpdate({
          ...milestone,
          completion_evidence: updatedEvidence
        });
      }
      
      toast.success('Evidence removed successfully');
    } catch (error) {
      console.error('Error removing completion evidence:', error);
      toast.error('Failed to remove evidence');
    } finally {
      setLoading(false);
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    return format(new Date(dateString), 'MMM d, yyyy');
  };

  if (!milestone) {
    return <div className="milestone-completion-empty">No milestone selected</div>;
  }

  return (
    <div className="milestone-completion-tracker">
      <div className="completion-header">
        <h3>{milestone.name}</h3>
        <div className="completion-percentage-badge">
          {completionPercentage}% Complete
        </div>
      </div>
      
      <div className="completion-progress">
        <div className="progress-bar-container">
          <div 
            className="progress-bar" 
            style={{ width: `${completionPercentage}%` }}
          ></div>
        </div>
      </div>
      
      {milestone.description && (
        <div className="milestone-description">
          {milestone.description}
        </div>
      )}
      
      {milestone.deadline && (
        <div className="milestone-deadline">
          <i className="bi bi-calendar"></i> Deadline: {formatDate(milestone.deadline)}
        </div>
      )}
      
      {/* Deliverables Checklist */}
      {deliverableStatus && deliverableStatus.length > 0 && (
        <div className="deliverables-section">
          <h4>Deliverables</h4>
          <div className="deliverables-checklist">
            {deliverableStatus.map((item, index) => (
              <div key={index} className="deliverable-item">
                <label className="deliverable-checkbox">
                  <input 
                    type="checkbox" 
                    checked={item.completed} 
                    onChange={() => toggleDeliverable(index)}
                    disabled={loading || userRole === 'viewer'}
                  />
                  <span className="deliverable-text">{item.deliverable}</span>
                </label>
                {item.completed && item.completedAt && (
                  <div className="deliverable-meta">
                    Completed on {formatDate(item.completedAt)}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
      
      {/* Completion Evidence */}
      <div className="evidence-section">
        <div className="evidence-header">
          <h4>Completion Evidence</h4>
          {(userRole === 'admin' || userRole === 'contributor') && (
            <button 
              className="btn btn-sm btn-primary"
              onClick={() => setShowEvidenceForm(!showEvidenceForm)}
              disabled={loading}
            >
              {showEvidenceForm ? 'Cancel' : 'Add Evidence'}
            </button>
          )}
        </div>
        
        {/* Evidence Form */}
        {showEvidenceForm && (
          <form className="evidence-form" onSubmit={addCompletionEvidence}>
            <div className="form-group">
              <label htmlFor="evidence-title">Title*</label>
              <input 
                type="text"
                id="evidence-title"
                value={newEvidence.title}
                onChange={(e) => setNewEvidence({...newEvidence, title: e.target.value})}
                className="form-control"
                required
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="evidence-description">Description</label>
              <textarea 
                id="evidence-description"
                value={newEvidence.description}
                onChange={(e) => setNewEvidence({...newEvidence, description: e.target.value})}
                className="form-control"
                rows="2"
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="evidence-type">Type</label>
              <select 
                id="evidence-type"
                value={newEvidence.type}
                onChange={(e) => setNewEvidence({...newEvidence, type: e.target.value})}
                className="form-control"
              >
                <option value="link">Link</option>
                <option value="image">Image</option>
                <option value="document">Document</option>
              </select>
            </div>
            
            <div className="form-group">
              <label htmlFor="evidence-link">Link*</label>
              <input 
                type="text"
                id="evidence-link"
                value={newEvidence.link}
                onChange={(e) => setNewEvidence({...newEvidence, link: e.target.value})}
                className="form-control"
                placeholder="https://"
                required
              />
            </div>
            
            <div className="form-actions">
              <button 
                type="button"
                className="btn btn-secondary"
                onClick={() => setShowEvidenceForm(false)}
              >
                Cancel
              </button>
              <button 
                type="submit"
                className="btn btn-primary"
                disabled={loading}
              >
                {loading ? 'Adding...' : 'Add Evidence'}
              </button>
            </div>
          </form>
        )}
        
        {/* Evidence List */}
        {completionEvidence.length > 0 ? (
          <div className="evidence-list">
            {completionEvidence.map((evidence) => (
              <div key={evidence.id} className="evidence-item">
                <div className="evidence-content">
                  <div className="evidence-title">
                    <i className={`bi ${evidence.type === 'link' ? 'bi-link' : evidence.type === 'image' ? 'bi-image' : 'bi-file-earmark'}`}></i>
                    <a href={evidence.link} target="_blank" rel="noopener noreferrer">
                      {evidence.title}
                    </a>
                  </div>
                  
                  {evidence.description && (
                    <div className="evidence-description">
                      {evidence.description}
                    </div>
                  )}
                  
                  <div className="evidence-meta">
                    Added on {formatDate(evidence.addedAt)}
                  </div>
                </div>
                
                {(userRole === 'admin' || (userRole === 'contributor' && evidence.addedBy === currentUser?.id)) && (
                  <button 
                    className="btn-action delete"
                    onClick={() => removeEvidence(evidence.id)}
                    title="Remove evidence"
                    disabled={loading}
                  >
                    <i className="bi bi-trash"></i>
                  </button>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="evidence-empty">
            No evidence has been added yet.
          </div>
        )}
      </div>
      
      {/* Approval Criteria */}
      {milestone.approval_criteria && (
        <div className="approval-section">
          <h4>Approval Criteria</h4>
          <div className="approval-criteria">
            {milestone.approval_criteria}
          </div>
        </div>
      )}
    </div>
  );
};

export default MilestoneCompletionTracker;
