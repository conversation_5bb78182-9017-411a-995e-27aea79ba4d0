# Data Display Components Wireframe
**Complete Data Visualization Component Library**

## 📋 Component Information
- **Component Type**: Data Display and Visualization Components
- **Usage**: Information presentation across all platform features
- **Pattern**: Consistent data visualization following design system principles
- **Priority**: 🔥 Critical - Required for all data-driven features
- **Implementation**: Reusable data display component library

---

## 🎯 **Design Philosophy**

### **Information Design Principles**
- **Clear Hierarchy** - Logical information organization with visual emphasis
- **Scannable Content** - Easy to scan and find relevant information quickly
- **Contextual Actions** - Relevant actions available where users need them
- **Progressive Disclosure** - Show essential information first, details on demand
- **Responsive Design** - Optimal display across all device sizes

### **Data Visualization Standards**
- **Accessibility First** - Screen reader support and keyboard navigation
- **Performance Optimized** - Efficient rendering of large datasets
- **Interactive Elements** - Hover states, sorting, filtering, and selection
- **Consistent Styling** - Unified appearance across all data displays
- **Mobile Adaptation** - Touch-friendly interactions and responsive layouts

---

## 👤 **User Card Components**

### **Basic User Card**
```
┌─────────────────────────────────────────────────────────────┐
│ ┌─────┐ Sarah Chen                                    [⋯]   │
│ │ SC  │ Senior Frontend Developer                           │
│ └─────┘ ⭐ 4.9 rating • 🏆 Level 8 • 🤝 47 allies          │
│                                                             │
│ Skills: React, TypeScript, Node.js, UI/UX                  │
│ Available: Part-time • Rate: $75/hour                      │
│                                                             │
│ [View Profile] [Send Message] [Add Ally]                   │
└─────────────────────────────────────────────────────────────┘
```

### **Compact User Card**
```
┌─────────────────────────────────────────────────────────────┐
│ ┌───┐ Sarah Chen                                      [⋯]   │
│ │SC │ Senior Frontend Developer • ⭐ 4.9 • Available        │
│ └───┘ [Message] [Profile]                                  │
└─────────────────────────────────────────────────────────────┘
```

### **User Card with Status**
```
┌─────────────────────────────────────────────────────────────┐
│ ┌─────┐ Sarah Chen                              🟢 Online   │
│ │ SC  │ Senior Frontend Developer                           │
│ └─────┘ ⭐ 4.9 rating • 🏆 Level 8 • 🤝 47 allies          │
│                                                             │
│ Currently working on: E-commerce Platform                  │
│ Last active: 2 minutes ago                                 │
│                                                             │
│ [View Profile] [Send Message] [Add Ally]                   │
└─────────────────────────────────────────────────────────────┘
```

### **User Card with Collaboration History**
```
┌─────────────────────────────────────────────────────────────┐
│ ┌─────┐ Sarah Chen                                    [⋯]   │
│ │ SC  │ Senior Frontend Developer                           │
│ └─────┘ ⭐ 4.9 rating • 🏆 Level 8 • 🤝 47 allies          │
│                                                             │
│ 🤝 Collaborated on 3 projects together                     │
│ 💰 Total earnings: $12,400 • Success rate: 100%           │
│ 📅 Last project: Mobile App (completed 2 weeks ago)       │
│                                                             │
│ [View Profile] [Collaborate Again] [Endorse Skills]        │
└─────────────────────────────────────────────────────────────┘
```

---

## 🎯 **Project Card Components**

### **Venture Card**
```
┌─────────────────────────────────────────────────────────────┐
│ 🎯 E-commerce Platform                                [⋯]   │
│ Web Development • Active                                    │
│                                                             │
│ 👥 Team: 5 members • 📅 Timeline: 8 weeks                  │
│ 💰 Budget: $15,000 • 📊 Progress: 65%                      │
│                                                             │
│ ████████████████████████████████████████████████████ 65%    │
│                                                             │
│ Recent Activity:                                            │
│ • API Integration completed by Mike                         │
│ • UI Design review scheduled for tomorrow                  │
│ • Payment system testing in progress                       │
│                                                             │
│ [View Details] [Join Team] [Track Progress]                │
└─────────────────────────────────────────────────────────────┘
```

### **Mission Card**
```
┌─────────────────────────────────────────────────────────────┐
│ 📋 API Integration                                    [⋯]   │
│ Backend Development • High Priority                         │
│                                                             │
│ 👤 Assigned: Mike Johnson • ⏰ Due: Jan 25, 2025           │
│ 💰 Value: $1,200 • 🎖️ Difficulty: Level 6                 │
│                                                             │
│ Description:                                                │
│ Integrate payment processing API with user authentication  │
│ and order management system. Includes error handling and   │
│ comprehensive testing.                                      │
│                                                             │
│ Requirements:                                               │
│ ✅ Node.js, Express.js  ✅ API Integration  ⏳ Testing     │
│                                                             │
│ [View Details] [Start Work] [Ask Question]                 │
└─────────────────────────────────────────────────────────────┘
```

### **Bounty Card**
```
┌─────────────────────────────────────────────────────────────┐
│ 🎯 Mobile App UI Design                               [⋯]   │
│ Design & Creative • Open Bounty                             │
│                                                             │
│ 💰 Reward: $2,500 • ⏰ Deadline: Feb 15, 2025              │
│ 🏆 Skill Level: Intermediate • 👥 Applicants: 12           │
│                                                             │
│ Looking for a talented UI/UX designer to create modern,    │
│ intuitive mobile app interfaces for our fintech startup.   │
│                                                             │
│ Required Skills:                                            │
│ • Figma/Sketch  • Mobile UI  • User Research  • Prototyping│
│                                                             │
│ Posted by: VRC Entertainment • ⭐ 4.8 rating               │
│                                                             │
│ [Apply Now] [View Details] [Save Bounty]                   │
└─────────────────────────────────────────────────────────────┘
```

---

## 💰 **Revenue Card Components**

### **Earnings Summary Card**
```
┌─────────────────────────────────────────────────────────────┐
│ 💰 Total Earnings                                     [⋯]   │
│                                                             │
│ $18,450                                                     │
│ +$1,200 this month (+7.0%)                                 │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │     ████                                                │ │
│ │   ██    ██                                              │ │
│ │ ██        ████                                          │ │
│ │ Jan  Feb  Mar  Apr  May  Jun                            │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ Breakdown:                                                  │
│ • Active projects: $12,200                                 │
│ • Completed bounties: $4,800                               │
│ • Skill endorsements: $1,450                               │
│                                                             │
│ [View Details] [Withdraw Funds] [Tax Report]               │
└─────────────────────────────────────────────────────────────┘
```

### **Project Revenue Card**
```
┌─────────────────────────────────────────────────────────────┐
│ 💰 E-commerce Platform Revenue                        [⋯]   │
│                                                             │
│ Total Revenue: $15,000                                      │
│ Your Share: $3,750 (25%)                                   │
│                                                             │
│ Revenue Distribution:                                       │
│ ████████████████████████████████████████████████████ 100%   │
│ │ Sarah (25%) │ Mike (30%) │ Team (35%) │ Platform (10%) │ │
│                                                             │
│ Payment Status:                                             │
│ ✅ Milestone 1: $5,000 (paid)                              │
│ ✅ Milestone 2: $5,000 (paid)                              │
│ ⏳ Milestone 3: $5,000 (in escrow)                         │
│                                                             │
│ [View Breakdown] [Request Payment] [Dispute]               │
└─────────────────────────────────────────────────────────────┘
```

### **Payment History Card**
```
┌─────────────────────────────────────────────────────────────┐
│ 💳 Recent Payments                                    [⋯]   │
│                                                             │
│ Jan 15, 2025  +$1,200  API Integration (completed)         │
│ Jan 10, 2025  +$800    UI Design Review (milestone)        │
│ Jan 05, 2025  +$450    Bug Fix Bounty (completed)          │
│ Dec 28, 2024  +$2,100  Mobile App (final payment)          │
│ Dec 20, 2024  +$600    Code Review (completed)             │
│                                                             │
│ [View All Payments] [Download Statement] [Tax Documents]   │
└─────────────────────────────────────────────────────────────┘
```

---

## 📊 **Data Table Components**

### **Standard Data Table**
```
┌─────────────────────────────────────────────────────────────┐
│ Team Members (5)                    [🔍 Search] [+ Add]     │
├─────────────────────────────────────────────────────────────┤
│ Name              Role           Status    Earnings   Actions│
├─────────────────────────────────────────────────────────────┤
│ Sarah Chen        Frontend Dev   🟢 Active  $3,750    [⋯]   │
│ Mike Johnson      Backend Dev    🟢 Active  $4,200    [⋯]   │
│ Lisa Wang         UI/UX Designer 🟡 Busy    $2,800    [⋯]   │
│ Tom Rodriguez     QA Engineer    🟢 Active  $1,950    [⋯]   │
│ Alex Kim          DevOps         🔴 Away    $2,300    [⋯]   │
├─────────────────────────────────────────────────────────────┤
│ Showing 5 of 5 members                                     │
└─────────────────────────────────────────────────────────────┘
```

### **Sortable Data Table**
```
┌─────────────────────────────────────────────────────────────┐
│ Mission Board (12)                  [🔍 Search] [+ Create]  │
├─────────────────────────────────────────────────────────────┤
│ Mission Name ↓    Assignee     Priority  Status    Value    │
├─────────────────────────────────────────────────────────────┤
│ API Integration   Mike Johnson  🔴 High   ⏳ Progress $1,200│
│ Database Setup    Sarah Chen    🟡 Medium ✅ Complete $800  │
│ UI Components     Lisa Wang     🟢 Low    📋 Todo    $600  │
│ Testing Suite     Tom Rodriguez 🟡 Medium 📋 Todo    $400  │
│ Documentation     Unassigned    🟢 Low    📋 Todo    $200  │
├─────────────────────────────────────────────────────────────┤
│ [Previous] Page 1 of 3 [Next]                              │
└─────────────────────────────────────────────────────────────┘
```

### **Filterable Data Table**
```
┌─────────────────────────────────────────────────────────────┐
│ Project History                     [🔍 Search] [🔧 Filter] │
│ Filters: [Status: All ▼] [Date: Last 6 months ▼] [Clear]   │
├─────────────────────────────────────────────────────────────┤
│ Project           Client        Status     Earnings  Date   │
├─────────────────────────────────────────────────────────────┤
│ E-commerce App    TechCorp      ✅ Complete $3,750   Jan 15 │
│ Mobile Dashboard  StartupXYZ    ⏳ Progress $1,200   Jan 10 │
│ Website Redesign  LocalBiz      ✅ Complete $2,100   Dec 28 │
│ API Integration   DevAgency     ✅ Complete $800     Dec 20 │
├─────────────────────────────────────────────────────────────┤
│ Total: 4 projects • Total earnings: $7,850                 │
└─────────────────────────────────────────────────────────────┘
```

### **Mobile Data Table**
```
┌─────────────────────────┐
│ Team Members        [+] │
├─────────────────────────┤
│ ┌─────────────────────┐ │
│ │ Sarah Chen          │ │
│ │ Frontend Developer  │ │
│ │ 🟢 Active • $3,750  │ │
│ │ [View] [Edit]       │ │
│ └─────────────────────┘ │
│                         │
│ ┌─────────────────────┐ │
│ │ Mike Johnson        │ │
│ │ Backend Developer   │ │
│ │ 🟢 Active • $4,200  │ │
│ │ [View] [Edit]       │ │
│ └─────────────────────┘ │
│                         │
│ [Load More]             │
└─────────────────────────┘
```

---

## 📈 **Chart & Graph Components**

### **Line Chart**
```
┌─────────────────────────────────────────────────────────────┐
│ 📈 Earnings Over Time                                 [⋯]   │
│                                                             │
│ $5K ┤                                                       │
│     │                                              ●        │
│ $4K ┤                                          ●            │
│     │                                      ●                │
│ $3K ┤                                  ●                    │
│     │                              ●                        │
│ $2K ┤                          ●                            │
│     │                      ●                                │
│ $1K ┤                  ●                                    │
│     │              ●                                        │
│  $0 └──────┬──────┬──────┬──────┬──────┬──────┬──────┬──────│
│           Jan    Feb    Mar    Apr    May    Jun    Jul    Aug│
│                                                             │
│ Total Growth: +$4,200 (350%) • Average: $525/month         │
└─────────────────────────────────────────────────────────────┘
```

### **Bar Chart**
```
┌─────────────────────────────────────────────────────────────┐
│ 📊 Skills Distribution                               [⋯]   │
│                                                             │
│ React      ████████████████████████████████████████ 85%     │
│ TypeScript ████████████████████████████████████ 75%         │
│ Node.js    ████████████████████████████ 60%                 │
│ Python     ████████████████████ 45%                         │
│ Design     ████████████ 30%                                 │
│ DevOps     ████████ 20%                                     │
│                                                             │
│ Based on 47 skill endorsements from allies                 │
└─────────────────────────────────────────────────────────────┘
```

### **Pie Chart**
```
┌─────────────────────────────────────────────────────────────┐
│ 🥧 Revenue Sources                                    [⋯]   │
│                                                             │
│                    ████████                                │
│                ████        ████                            │
│              ██              ██                            │
│            ██    Fixed (45%)   ██                          │
│          ██                      ██                        │
│          ██  Hourly (30%)          ██                      │
│          ██                      ██                        │
│            ██    Revenue (25%)   ██                        │
│              ██              ██                            │
│                ████        ████                            │
│                    ████████                                │
│                                                             │
│ • Fixed Projects: $8,100 (45%)                             │
│ • Hourly Work: $5,400 (30%)                                │
│ • Revenue Share: $4,500 (25%)                              │
└─────────────────────────────────────────────────────────────┘
```

### **Progress Ring**
```
┌─────────────────────────────────────────────────────────────┐
│ 🎯 Mission Completion                                 [⋯]   │
│                                                             │
│                        ████████                            │
│                    ████        ████                        │
│                  ██                ██                      │
│                ██                    ██                    │
│              ██                        ██                  │
│              ██           75%            ██                │
│              ██        9 of 12           ██                │
│                ██                    ██                    │
│                  ██                ██                      │
│                    ████        ████                        │
│                        ████████                            │
│                                                             │
│ ✅ 9 completed • ⏳ 2 in progress • 📋 1 pending           │
└─────────────────────────────────────────────────────────────┘
```

---

## 📱 **Mobile Data Display Adaptations**

### **Mobile Card Stack**
```
┌─────────────────────────┐
│ 👤 Sarah Chen           │
│ Frontend Developer      │
│ ⭐ 4.9 • 🏆 Level 8     │
│ [Message] [Profile]     │
├─────────────────────────┤
│ 👤 Mike Johnson         │
│ Backend Developer       │
│ ⭐ 4.7 • 🏆 Level 7     │
│ [Message] [Profile]     │
├─────────────────────────┤
│ [Load More]             │
└─────────────────────────┘
```

### **Mobile Chart Adaptations**
- **Simplified Charts** - Reduced data points for mobile screens
- **Touch Interactions** - Tap to view details, pinch to zoom
- **Horizontal Scrolling** - For wide charts and tables
- **Collapsible Sections** - Expandable chart details
- **Gesture Navigation** - Swipe between chart views
