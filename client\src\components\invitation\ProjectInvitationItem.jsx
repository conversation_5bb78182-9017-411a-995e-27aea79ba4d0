import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { formatDistanceToNow } from 'date-fns';

const ProjectInvitationItem = ({ invitation, onUpdate }) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const navigate = useNavigate();

  // Format the time
  const timeAgo = formatDistanceToNow(new Date(invitation.created_at), { addSuffix: true });

  // Accept project invitation
  const acceptInvitation = async () => {
    setIsProcessing(true);

    try {
      // Update invitation status in project_contributors table
      const { error: updateError } = await supabase
        .from('project_contributors')
        .update({
          status: 'active',
          updated_at: new Date().toISOString(),
          joined_at: new Date().toISOString()
        })
        .eq('id', invitation.id);

      if (updateError) throw updateError;

      toast.success('Project invitation accepted');

      // Notify parent component
      if (onUpdate) {
        onUpdate(invitation.id, 'accepted');
      }

      // Navigate to project
      navigate(`/project/${invitation.project_id}`);
    } catch (error) {
      console.error('Error accepting project invitation:', error);
      toast.error('Failed to accept project invitation');
    } finally {
      setIsProcessing(false);
    }
  };

  // Reject project invitation
  const rejectInvitation = async () => {
    setIsProcessing(true);

    try {
      // Update invitation status in project_contributors table
      const { error } = await supabase
        .from('project_contributors')
        .update({ status: 'rejected', updated_at: new Date().toISOString() })
        .eq('id', invitation.id);

      if (error) throw error;

      toast.success('Project invitation declined');

      // Notify parent component
      if (onUpdate) {
        onUpdate(invitation.id, 'rejected');
      }
    } catch (error) {
      console.error('Error rejecting project invitation:', error);
      toast.error('Failed to decline project invitation');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="invitation-item">
      <div className="invitation-project-icon">
        <i className="bi bi-folder2-open"></i>
      </div>

      <div className="invitation-content">
        <div className="invitation-header">
          <div className="invitation-title">
            <span className="project-name">{invitation.project?.name || 'Project'}</span>
            <span className="invitation-type">invitation from</span>
            <span className="sender-name">{invitation.inviter?.display_name || invitation.invited_by_email}</span>
          </div>
          <div className="invitation-time">{timeAgo}</div>
        </div>

        {invitation.message && (
          <div className="invitation-message">
            "{invitation.message}"
          </div>
        )}

        {invitation.status === 'pending' ? (
          <div className="invitation-actions">
            <button
              className="btn-accept"
              onClick={acceptInvitation}
              disabled={isProcessing}
            >
              {isProcessing ? 'Processing...' : 'Accept'}
            </button>
            <button
              className="btn-decline"
              onClick={rejectInvitation}
              disabled={isProcessing}
            >
              {isProcessing ? 'Processing...' : 'Decline'}
            </button>
          </div>
        ) : (
          <div className={`invitation-status ${invitation.status}`}>
            {invitation.status === 'accepted' ? 'Accepted' : 'Declined'}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectInvitationItem;
